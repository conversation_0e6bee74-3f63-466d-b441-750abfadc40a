import org.aspectj.bridge.IMessage
import org.aspectj.bridge.MessageHandler
import org.aspectj.tools.ajc.Main
import java.nio.file.Files

buildscript {
    repositories {
        mavenCentral()
    }
    dependencies {
        classpath "org.aspectj:aspectjtools:$aspectj_version"
        classpath "org.aspectj:aspectjweaver:$aspectj_version"
    }
}

apply plugin: AspectjPlugin

class AspectjPlugin implements Plugin<Project> {

    private String sourceJDK = "1.8"
    private String targetJDK = "1.8"

    @Override
    void apply(Project project) {

        project.extensions.create("aspectjConfigs", AspectjConfigs)

        if (project.hasProperty('android') && project.android != null) {
            AspectjConfigs configs = project.aspectjConfigs

            if (project.android.hasProperty('applicationVariants')
                    && project.android.applicationVariants != null) {
                project.android.applicationVariants.all { variant ->
                    if (enableBuildType(project,configs,variant.buildType.name)) {
//                        println("---------application variant---------")
                        println("application aspectj: "+variant.buildType.name)
                        // WARNING: API 'variant.getJavaCompiler()' is obsolete and
                        // has been replaced with 'variant.getJavaCompileProvider()'.
                        // doLast(variant.getJavaCompiler())
                        doFirst(variant.getJavaCompileProvider().get())
                        doLast(variant.getJavaCompileProvider().get(), variant.buildType.name)
                    }
                }
            }
            if (project.android.hasProperty('libraryVariants')
                    && project.android.libraryVariants != null) {
                project.android.libraryVariants.all { variant ->
                    if (enableBuildType(project,configs,variant.buildType.name)) {
//                        println("---------library variant---------")
                        println("library aspectj: "+variant.buildType.name)
                        doFirst(variant.getJavaCompileProvider().get())
                        doLast(variant.getJavaCompileProvider().get(), variant.buildType.name)
                    }
                }
            }
        }
    }

    private static boolean enableBuildType(Project project, AspectjConfigs configs, String buildType) {
        // Not a assemble task, no need to apply aspectj
        if (project.getGradle().startParameter.taskNames.isEmpty()) return false
        // If this buildType is in excludedBuildTypes, ignore it
        if (!configs.excludedBuildTypes.isEmpty() && configs.excludedBuildTypes.contains(buildType)) return false
        // If onlyCurrentBuildType, check if gradle assemble buildType equals this buildType
        if (configs.onlyCurrentBuildType) {
            return project.getGradle().startParameter.taskNames.any {
                it.toLowerCase().contains("assemble$buildType".toLowerCase())
            }
        }
        return true
    }

    private void doFirst(Task javaCompile) {
        javaCompile.doFirst {
            if (project.hasProperty('android') && project.android != null) {
                if (project.android.hasProperty('compileOptions') && project.android.compileOptions != null) {
                    if (project.android.compileOptions.hasProperty('targetCompatibility') && project.android.compileOptions.targetCompatibility != null) {
                        targetJDK = project.android.compileOptions.properties.get('targetCompatibility')
                    }
                    if (project.android.compileOptions.hasProperty('sourceCompatibility') && project.android.compileOptions.sourceCompatibility != null) {
                        sourceJDK = project.android.compileOptions.properties.get('sourceCompatibility')
                    }
                }
            }
        }
    }

    private void doLast(Task javaCompile, String buildType) {
        javaCompile.doLast {

            MessageHandler handler = new MessageHandler(true)
            String aspectPath = javaCompile.classpath.asPath
            String inPath = javaCompile.destinationDir.toString()
            String dPath = javaCompile.destinationDir.toString();
            String classpath = javaCompile.classpath.asPath

            // 配置 kotlin 相关参数
            String kotlinInPath = ""
            if (dPath.contains("debug${File.separator}classes")) {
                kotlinInPath = javaCompile.temporaryDir.getParentFile().path + "${File.separator}kotlin-classes${File.separator}debug"
            } else {
                kotlinInPath = javaCompile.temporaryDir.getParentFile().path + "${File.separator}kotlin-classes${File.separator}${buildType}"
            }
            String pathSplit = ";"
            if (kotlinInPath.startsWith("/")) {
                // 说明是 linux 或者 mac 环境
                pathSplit = ":"
            }
            // java 的 class 文件实现 aop
            String[] javacArgs = ["-showWeaveInfo",
                                  "-source", sourceJDK,
                                  "-target", targetJDK,
                                  "-inpath", kotlinInPath + pathSplit + inPath,
                                  "-aspectpath", aspectPath,
                                  "-d", dPath,
                                  "-classpath", classpath,
                                  "-bootclasspath", project.android.bootClasspath.join(File.pathSeparator)]
            new Main().run(javacArgs, handler)
            File[] kotlinClassFiles = FileUtil.listFiles(kotlinInPath, true)
            File javacKotlinFile
            for (File temp : kotlinClassFiles) {
                if (temp.isFile() && temp.getName().endsWith(".class")) {
                    javacKotlinFile = new File(inPath + File.separator + temp.absolutePath.replace(kotlinInPath, ""))
                    if (null != javacKotlinFile && javacKotlinFile.exists()) {
                        FileUtil.delete(temp)
                        FileUtil.copyFile(javacKotlinFile, temp)
                        FileUtil.delete(javacKotlinFile)
                    }
                }
            }

            def log = project.logger
            for (IMessage message : handler.getMessages(null, true)) {
                switch (message.getKind()) {
                    case IMessage.ABORT:
                    case IMessage.ERROR:
                    case IMessage.FAIL:
                        log.error message.message, message.thrown
                        break;
                    case IMessage.WARNING:
                    case IMessage.INFO:
                        log.info message.message, message.thrown
                        break;
                    case IMessage.DEBUG:
                        log.debug message.message, message.thrown
                        break;
                }
            }
        }
    }
}

/**
 * <AUTHOR> @date 2021/10
 * Configs of AspectJ Plugin
 */
class AspectjConfigs {

    /**
     * Exclude "debug' by default.
     */
    List<String> excludedBuildTypes = ["debug"]

    /**
     * Only enable aspectj for current assemble buildType.
     * eg: If you use 'assembleDebug', only debug variant will enable aspectj.
     */
    boolean onlyCurrentBuildType = true

    /**
     * Set exclude BuildTypes. Those buildTypes contained in [excludedBuildTypes] will be ignored.
     * eg1: To enable all buildTypes:
     * `aspectjConfigs {
     * `    excludeBuildTypes null
     * `}
     * eg2: To exclude stage and debug:
     * `aspectjConfigs {
     * `    excludeBuildTypes 'stage','debug'
     * `}
     * @param filters
     */
    AspectjConfigs excludeBuildTypes(String... filters) {
        this.excludedBuildTypes.clear()
        if (filters != null) {
            excludedBuildTypes.addAll(filters)
        }
        return this
    }
}


class FileUtil {

    static List<File> listFiles(File dir, boolean recursion) {
        List<File> files = new ArrayList<>()
        if (dir != null && dir.exists()) {
            if (dir.isDirectory()) {
                if (recursion) {
                    for (File temp : dir.listFiles()) {
                        files.addAll(listFiles(temp, recursion))
                    }
                }
            } else {
                files.add(dir)
            }
        }
        return files
    }

    static List<File> listFiles(String dirPath , boolean recursion) {
        return listFiles(new File(dirPath) , recursion)
    }

    static boolean copyFile(File srcFile, File destFile) {
        if (null == srcFile || destFile == null) {
            return false
        }
        try {
            Files.copy(srcFile.toPath(), destFile.toPath())
            return true
        } catch (Exception e) {
            e.printStackTrace()
        }
        return false
    }

    static boolean delete(File file) {
        if (file.exists()) {
            if (!file.delete()) {
                throw new Exception("file cannot delete:" + file.getAbsolutePath())
            }
            return true
        } else {
            return true
        }
    }
}