ext {
    support_version = "28.0.0"
    retrofit_version = "2.9.0"
    coroutines_version = "1.6.1"
    aspectj_version = "1.9.6"

    android = [compileSdkVersion: 30,
               buildToolsVersion: "30.0.3",
               minSdkVersion    : 23,
               targetSdkVersion : 24]

    dependencies = ["legacy-support-v4"            : 'androidx.legacy:legacy-support-v4:1.0.0',
                    "appcompat"                    : 'androidx.appcompat:appcompat:1.0.0',
                    "design"                       : 'com.google.android.material:material:1.0.0',
                    "cardview"                     : 'androidx.cardview:cardview:1.0.0',
                    "recyclerview"                 : 'androidx.recyclerview:recyclerview:1.0.0',
                    "leakcanary"                   : "com.squareup.leakcanary:leakcanary-android:1.3.1",
                    "PhotoView"                    : "com.commit451:PhotoView:1.2.5",
                    "rxjava"                       : "io.reactivex:rxjava:1.1.6",
                    "rxandroid"                    : "io.reactivex:rxandroid:1.2.1",
                    "support-annotations"          : 'androidx.annotation:annotation:1.0.0',
                    "retrofit"                     : "com.squareup.retrofit2:retrofit:$retrofit_version",
                    "converter-gson"               : "com.squareup.retrofit2:converter-gson:$retrofit_version",
                    "adapter-rxjava"               : "com.squareup.retrofit2:adapter-rxjava:$retrofit_version",
                    "logger"                       : "com.orhanobut:logger:1.11",
                    "leakcanary-android"           : "com.squareup.leakcanary:leakcanary-android:1.5.1",
                    "leakcanary-android-no-op"     : "com.squareup.leakcanary:leakcanary-android-no-op:1.5.1",
                    "gson"                         : "com.google.code.gson:gson:2.6.2",
                    "annotation-api"               : "javax.annotation:javax.annotation-api:1.2",
                    "okhttp3"                      : "com.squareup.okhttp3:okhttp:3.6.0",
                    "junit"                        : "junit:junit:4.12",
                    "test-runner"                  : 'androidx.test.ext:junit:1.1.1',
                    "test-rules"                   : 'androidx.test:rules:1.1.1',
                    "fresco"                       : "com.facebook.fresco:fresco:1.2.0",
                    "imagepipeline-okhttp3"        : "com.facebook.fresco:imagepipeline-okhttp3:1.2.0",
                    "zxing"                        : "com.google.zxing:core:3.3.3",
                    "firebase-core"                : "com.google.firebase:firebase-core:16.0.7",
                    "firebase-storage"             : "com.google.firebase:firebase-storage:16.0.1",
                    "firebase-database"            : "com.google.firebase:firebase-database:16.0.1",
                    "firebase-messaging"           : "com.google.firebase:firebase-messaging:17.3.4",
                    "firebase-config"              : "com.google.firebase:firebase-config:16.0.0",
                    "play-services-maps"           : "com.google.android.gms:play-services-maps:16.1.0",  // please make sure use the same version of firebase, otherwise gis will crash
                    "play-services-location"       : "com.google.android.gms:play-services-location:16.0.0",
                    "play-services-analytics"      : "com.google.android.gms:play-services-analytics:16.0.7",
                    "play-services-places"         : "com.google.android.gms:play-services-places:16.0.0",
                    "play-services-gcm"            : "com.google.android.gms:play-services-gcm:16.0.0",
                    "play-services-base"           : "com.google.android.gms:play-services-base:16.1.0",
                    "google-maps-utils"            : "com.google.maps.android:android-maps-utils:0.5",
                    "stream"                       : "com.annimon:stream:1.1.9",
                    "advrecyclerview"              : "com.h6ah4i.android.widget.advrecyclerview:advrecyclerview:0.11.0",
                    "multidex"                     : 'androidx.multidex:multidex:2.0.0',
                    "searchview"                   : "com.lapism:searchview:4.0",
                    "ahbottomnavigation"           : "com.aurelhubert:ahbottomnavigation:2.2.0",
                    "litepal"                      : "org.litepal.android:core:1.5.1",
                    "AvatarImageView"              : "cn.carbs.android:AvatarImageView:1.0.3",
                    "textdrawable"                 : "com.amulyakhare:com.amulyakhare.textdrawable:1.0.1",
                    "stetho"                       : "com.facebook.stetho:stetho:1.4.2",
                    "stetho-okhttp3"               : "com.facebook.stetho:stetho-okhttp3:1.4.2",
                    "constraint-layout"            : 'androidx.constraintlayout:constraintlayout:1.1.3',
                    "circleindicator"              : "me.relex:circleindicator:1.2.2@aar",
                    "BaseRecyclerViewAdapterHelper": "com.github.CymChad:BaseRecyclerViewAdapterHelper:2.9.26",
                    "RxBinding"                    : "com.jakewharton.rxbinding:rxbinding:0.4.0",
                    "qmui-android"                 : "com.qmuiteam:qmui:1.0.4",
                    "EventBus"                     : "org.greenrobot:eventbus:3.0.0",
                    "magicprogresswidget"          : "com.liulishuo.magicprogresswidget:library:1.0.9",
                    "SubsamplingScaleImageView"    : "com.davemorrissey.labs:subsampling-scale-image-view:3.6.0",
                    "PhotoDraweeView"              : "me.relex:photodraweeview:1.1.2",
                    "GroupedRecyclerViewAdapter"   : "com.github.donkingliang:GroupedRecyclerViewAdapter:1.4.1",
                    "WheelPicker"                  : "cn.aigestudio.wheelpicker:WheelPicker:1.1.2",
                    "lifecycle"                    : 'androidx.lifecycle:lifecycle-extensions:2.0.0',
                    "flexbox"                      : "com.google.android:flexbox:1.0.0",
                    "buggly"                       : "com.tencent.bugly:crashreport:3.2.422",
                    "kotlin-stdlib"                : "org.jetbrains.kotlin:kotlin-stdlib-jdk8:${kotlin_version}",
                    "kotlin-reflect"               : "org.jetbrains.kotlin:kotlin-reflect:${kotlin_version}",
                    "coroutines-core"              : "org.jetbrains.kotlinx:kotlinx-coroutines-core:${coroutines_version}",
                    "coroutines-android"           : "org.jetbrains.kotlinx:kotlinx-coroutines-android:${coroutines_version}",
                    "material-calendarview"        : "com.github.prolificinteractive:material-calendarview:2.0.1",
                    "title-bar"                    : "com.wuhenzhizao:titlebar:1.1.4",
                    "threeTenAbp"                  : 'com.jakewharton.threetenabp:threetenabp:1.1.1',//Calendar Date
                    "ios-switch"                   : 'com.github.iielse:switchbutton:1.0.4',//ios style switch
                    "swipemenu-recyclerview"       : 'com.tubb.smrv:swipemenu-recyclerview:5.4.8',//Swipe layout
                    "aspectJ"                      : "org.aspectj:aspectjrt:${aspectj_version}",//Aspectj
                    "work-runtime"                 : 'androidx.work:work-runtime-ktx:2.0.1',//workManager
                    "FreeReflection"               : "com.github.tiann:FreeReflection:3.1.0",
                    "GSYVideoPlayer"               : "com.github.CarGuo.GSYVideoPlayer:GSYVideoPlayer:v6.0.3",
                    "io-socket"                    : "io.socket:socket.io-client:2.1.0",
                    "google-auto-service"          : "com.google.auto.service:auto-service:1.0-rc4"

    ]
}
