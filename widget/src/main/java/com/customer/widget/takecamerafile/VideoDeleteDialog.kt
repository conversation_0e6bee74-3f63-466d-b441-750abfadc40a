package com.customer.widget.takecamerafile

import android.os.Bundle
import androidx.fragment.app.DialogFragment
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.customer.widget.R

class VideoDeleteDialog : DialogFragment() {

    companion object {
        private const val VIDEO_PATH = "video_path"

        @JvmStatic
        fun newInstance(
            path: String? = null,
        ): VideoDeleteDialog =
            VideoDeleteDialog().apply {
                arguments = Bundle().apply {
                    putString(VIDEO_PATH, path)
                }
            }
    }

    private var videoPreview: VideoPreview? = null
    private var onDeleteClick: (() -> Unit)? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, android.R.style.Theme_Black_NoTitleBar_Fullscreen)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View {
        val rootView = inflater.inflate(R.layout.dialog_delete_video, container, false)
        initView(rootView)
        return rootView
    }

    private fun initView(rootView: View) {
        videoPreview = rootView.findViewById(R.id.video_preview)
        videoPreview?.apply {
            arguments?.getString(VIDEO_PATH)?.let {
                setVideoPath(it)
            }
        }
        rootView.findViewById<AppCompatImageView>(R.id.btn_back).setOnClickListener { dismiss() }
        rootView.findViewById<AppCompatTextView>(R.id.tv_delete).setOnClickListener {
            onDeleteClick?.invoke()
            dismiss()
        }
    }

    fun setOnDeleteClick(onDeleteClick: (() -> Unit)?) {
        this.onDeleteClick = onDeleteClick
    }

}