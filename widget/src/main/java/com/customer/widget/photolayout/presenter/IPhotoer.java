package com.customer.widget.photolayout.presenter;


import com.customer.widget.photolayout.model.PhotoModel;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by dexter on 17/8/14.
 */

public interface IPhotoer {

    /**
     * 先创建ID,再更新上传照片
     */
    void upLoad(String url);

    /**
     * 直接上传照片获取图片ID
     */
    void upLoadPhoto(String url);

    /**
     * 删除本地照片
     */
    void deleteLocal(String url);

    /**
     * 更新照片
     */
    void updatePhoto(String fileId, String url);

    void setEntryId(String entryId);

    /**
     * 查询照片列表
     */
//    void query(String entryId);

    /**
     * 获取已上传照片ID列表
     */
    ArrayList<String> getPhotoIdList();

    /**
     * 获取已上传照片URL列表
     */
    ArrayList<String> getPhotoUrlList();

    /**
     * 获取已上传照片列表
     */
    ArrayList<PhotoModel> getPhotoList();

    /**
     * 设置照片列表
     */
    void setPhotoList(List<String> urls);

}
