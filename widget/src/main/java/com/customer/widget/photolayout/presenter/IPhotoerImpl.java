package com.customer.widget.photolayout.presenter;

import android.content.Context;
import android.text.TextUtils;

import com.annimon.stream.Stream;
import com.customer.widget.photolayout.ImageLoadUtil;
import com.customer.widget.photolayout.model.PhotoModel;
import com.linc.platform.fileapp.FileOperatePresenterImpl;
import com.linc.platform.fileapp.FileUploadResponseEntry;
import com.linc.platform.http.BaseSubscriber;
import com.linc.platform.utils.CollectionUtil;
import com.linc.platform.utils.ToastUtil;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

import retrofit2.Response;


/**
 * Created by dexter on 17/8/23.
 */

public class IPhotoerImpl implements IPhotoer {

    public static ArrayList<PhotoModel> unLoadList = new ArrayList<PhotoModel>();

    FileOperatePresenterImpl fileImpl;

    String app;
    String module;
    String service;
    String entryId;
    Context mContext;

    public ArrayList<PhotoModel> photos = new ArrayList<>();


    public IPhotoerImpl(Context mContext, String app, String module, String service) {
        this.mContext = mContext;
        this.app = app;
        this.module = module;
        this.service = service;
        fileImpl = new FileOperatePresenterImpl(null);
    }

    @Override
    public void setEntryId(String entryId) {
        this.entryId = entryId;
    }

    @Override
    public void upLoad(String url) {
        File file = getFile(url);
        PhotoModel model = new PhotoModel(app, module, service, entryId, url);
        photos.add(model);
        int index = photos.indexOf(model);
        fileImpl.uploadFileRawApi(app, module, service, file, new BaseSubscriber<Response<FileUploadResponseEntry>>() {
            @Override
            public void onSuccess(Response<FileUploadResponseEntry> response) {
                if (CollectionUtil.isNotNullOrEmpty(response.body().filesId)) {
                    model.fileId = response.body().filesId.get(0);
                    photos.set(index, model);
                } else {
                    //未生成id的图片存入到未上传列表
                    unLoadList.add(model);
                }

            }

            @Override
            public void onFailed(String message) {
                ToastUtil.showErrorToast(mContext, message);
            }

            @Override
            public void onDone() {

            }
        });
    }

    @Override
    public void updatePhoto(String fileId, String url) {
        //2.1 然后再根据fileId更新上传文件
        ImageLoadUtil.update(fileId, app, module, service, getFile(url), new ImageLoadUtil.IUpCallback() {
            @Override
            public void onSuccess(String id) {
                //2.1.1 上传成功
//                ToastUtil.showToast("更新上传成功:" + id);
            }

            @Override
            public void onFailed(String message) {
                //2.1.2 上传失败，存入未上传成功的列表
                PhotoModel model = new PhotoModel(app, module, service, entryId, url);
                model.fileId = fileId;
                unLoadList.add(model);
            }

            @Override
            public void onDone() {

            }
        });
    }


    @Override
    public void upLoadPhoto(String url) {
        ImageLoadUtil.up(app, module, service, getFile(url), new ImageLoadUtil.IUpCallback() {
            @Override
            public void onSuccess(String id) {
                PhotoModel model = new PhotoModel(app, module, service, entryId, url);
                model.fileId = id;
                photos.add(model);
            }

            @Override
            public void onFailed(String message) {

            }

            @Override
            public void onDone() {

            }
        });
    }

    @Override
    public void deleteLocal(String url) {
        //2017-09-29 物理上不删除，直到整个业务流程完成再删除
//        File file = getFile(url);
//        if (file.exists()) {
//            file.delete();
//        }
        //删除已上传列表里的照片(不能用foreach)
        for (int i = 0; i < photos.size(); i++) {
            if (!TextUtils.isEmpty(url) && photos.get(i).url.equals(url)) {
                photos.remove(photos.get(i));
            }
        }
        //删除未上传列表里的照片
        for (int i = 0; i < unLoadList.size(); i++) {
            if (!TextUtils.isEmpty(url) && unLoadList.get(i).url.equals(url)) {
                unLoadList.remove(unLoadList.get(i));
            }
        }

    }

    public File getFile(String url) {
        if (url.startsWith("file://")) {
            url = url.replace("file://", "");
        }
        File file = new File(url);
        return file;
    }


    @Override
    public ArrayList<String> getPhotoIdList() {
        ArrayList<String> list = new ArrayList<>();
        Stream.of(photos)
                .forEach(model -> list.add(model.fileId));
        return list;
    }

    @Override
    public ArrayList<String> getPhotoUrlList() {
        ArrayList<String> list = new ArrayList<>();
        Stream.of(photos)
                .forEach(model -> list.add(model.url));
        return list;
    }

    @Override
    public ArrayList<PhotoModel> getPhotoList() {
        return photos;
    }

    @Override
    public void setPhotoList(List<String> urls) {
        photos.clear();
        if (urls == null || urls.size() == 0) {
            return;
        }
        Stream.of(urls).forEach(url ->
                photos.add(new PhotoModel(app, module, service, entryId, url)));
    }

}