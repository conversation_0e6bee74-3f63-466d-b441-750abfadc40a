<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="48dp"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_gravity="top"
    android:background="?attr/selectableItemBackground"
    android:gravity="center"
    android:orientation="horizontal">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/content_txt"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_weight="1"
        android:textColor="@color/white"
        tools:text="00.00.01"/>

    <LinearLayout
        android:id="@+id/status_entry_layout"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="horizontal">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/status_txt"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:text="AVAILABLE"
            android:textColor="@color/white"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/entry_txt"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            tools:text="(12321)"
            android:textColor="@color/white"/>

    </LinearLayout>

    <CheckBox
        android:id="@+id/select_chk"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginEnd="@dimen/content_padding_right"
        android:layout_marginStart="@dimen/content_padding_left"
        android:button="@drawable/checkbox_selector_white_blue"/>
</LinearLayout>