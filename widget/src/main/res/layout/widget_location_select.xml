<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_262626"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/title_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/black"
        android:orientation="horizontal">

        <Button
            android:id="@+id/back_btn"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_gravity="center"
            android:layout_marginStart="@dimen/content_padding_left"
            android:background="@drawable/ic_back_white"
            android:visibility="gone" />

        <TextView
            android:id="@+id/widget_title_txt"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/activity_horizontal_margin"
            android:layout_marginTop="@dimen/content_padding_top"
            android:layout_marginBottom="@dimen/content_padding_bottom"
            android:text="@string/title_location_selector"
            android:textColor="@color/white"
            android:textSize="20sp"
            android:textStyle="bold" />
    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@+id/ok_cancel_btn_layout"
        android:layout_below="@+id/title_layout"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="8dp"
        android:orientation="vertical">

        <RadioGroup
            android:id="@+id/view_radio_group"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:orientation="horizontal">

            <RadioButton
                android:id="@+id/location_btn"
                style="@style/tabRadioButtonStyleV1"
                android:layout_width="match_parent"
                android:layout_height="30dp"
                android:layout_weight="1"
                android:button="@null"
                android:checked="true"
                android:text="@string/text_location" />

            <RadioButton
                android:id="@+id/staging_btn"
                style="@style/tabRadioButtonStyleV1"
                android:layout_width="match_parent"
                android:layout_height="30dp"
                android:layout_weight="1"
                android:button="@null"
                android:text="@string/text_staging" />

            <RadioButton
                android:id="@+id/dock_btn"
                style="@style/tabRadioButtonStyleV1"
                android:layout_width="match_parent"
                android:layout_height="30dp"
                android:layout_weight="1"
                android:button="@null"
                android:text="@string/text_dock" />

            <RadioButton
                android:id="@+id/rework_btn"
                style="@style/tabRadioButtonStyleV1"
                android:layout_width="match_parent"
                android:layout_height="30dp"
                android:layout_weight="1"
                android:button="@null"
                android:text="@string/text_rework" />

            <RadioButton
                android:id="@+id/yms_btn"
                style="@style/tabRadioButtonStyleV1"
                android:layout_width="match_parent"
                android:layout_height="30dp"
                android:layout_weight="1"
                android:button="@null"
                android:visibility="gone" />

            <RadioButton
                android:id="@+id/yms_dock_btn"
                style="@style/tabRadioButtonStyleV1"
                android:layout_width="match_parent"
                android:layout_height="30dp"
                android:layout_weight="1"
                android:button="@null"
                android:text="@string/text_dock"
                android:visibility="gone" />

        </RadioGroup>

        <com.customer.widget.locationselector.LocationItem
            android:id="@+id/location_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <com.customer.widget.locationselector.StagingItem
            android:id="@+id/staging_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <com.customer.widget.locationselector.ReworkItem
            android:id="@+id/rework_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <com.customer.widget.locationselector.DockItem
            android:id="@+id/dock_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <com.customer.widget.locationselector.YmsItem
            android:id="@+id/yms_item_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <com.customer.widget.locationselector.YmsDockItem
            android:id="@+id/yms_dock_view"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/ok_cancel_btn_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:orientation="horizontal">

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/cancel_btn"
            style="@style/raisedButtonStyle"
            android:backgroundTint="@color/accent_blue_v1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/content_padding_right"
            android:layout_marginEnd="@dimen/content_padding_left"
            android:layout_weight="1"
            android:text="@string/label_cancel" />

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/ok_btn"
            style="@style/raisedButtonStyle"
            android:backgroundTint="@color/accent_blue_v1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/content_padding_right"
            android:layout_marginEnd="4dp"
            android:layout_weight="1"
            android:text="@string/label_ok" />
    </LinearLayout>
</RelativeLayout>
