package com.unis.autotrackdispatcher.action;

import android.text.TextUtils;
import android.util.Log;
import android.view.View;

import com.annimon.stream.Stream;

public class ActionUtils {

    public static String getViewId(View view) {
        String target = view.getResources().getResourceName(view.getId());
        if (!TextUtils.isEmpty(target)) {
            String id = "";
            String[] targetItems = target.split("/");
            if (targetItems.length > 1) {
                id = targetItems[targetItems.length - 1];
            } else {
                id = targetItems[0];
            }
            return id;
        }
        return "";
    }

    public static String getViewActionId(View view){
        String id = getViewId(view);
        StringBuffer actionId = new StringBuffer();
        String[] ids = id.split("_");
        int length = ids.length;
        for(int i = 0;i<length-1;i++){
            actionId.append(ids[i]).append(" ");
        }
        return actionId.toString();
    }


}
