package com.lt.linc.load.orderlist;

import android.text.TextUtils;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.linc.platform.load.model.LoadOrderPickedItemLineEntry;
import com.linc.platform.utils.StringUtil;
import com.lt.linc.R;

/**
 * Created by Gavin
 */

public class OrderListItemAdapter extends BaseQuickAdapter<LoadOrderPickedItemLineEntry, BaseViewHolder> {

    public OrderListItemAdapter() {
        super(R.layout.load_order_list_item_view);
    }

    @Override
    protected void convert(BaseViewHolder helper, LoadOrderPickedItemLineEntry item) {
        helper.setText(R.id.item_name_txt, item.itemSpecName + " | " + (!TextUtils.isEmpty(item.itemSpecDesc) ? item.itemSpecDesc : item.shortDescription))
                .setText(R.id.item_qty, StringUtil.ignorePointZero(item.qty) + "(" + item.unitName + ")")
                .setText(R.id.title_txt, item.titleName)
                .setText(R.id.plts_qty, StringUtil.ignorePointZero(item.palletQty))
                .setText(R.id.location_txt, item.locationName);
    }
}
