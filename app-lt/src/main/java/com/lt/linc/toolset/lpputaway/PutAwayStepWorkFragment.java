package com.lt.linc.toolset.lpputaway;

import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.widget.AppCompatButton;
import androidx.appcompat.widget.AppCompatEditText;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;

import com.annimon.stream.Collectors;
import com.annimon.stream.Stream;
import com.customer.widget.CircleProgress;
import com.customer.widget.GeneralAlertDialog;
import com.customer.widget.ItemDividerDecoration;
import com.customer.widget.ScanEditText;
import com.customer.widget.core.LincBaseFragment;
import com.customer.widget.locationselector.LocationSelectorDialog;
import com.linc.platform.baseapp.model.LocationEntry;
import com.linc.platform.baseapp.model.LocationItemCheckEntry;
import com.linc.platform.baseapp.model.LocationTypeEntry;
import com.linc.platform.common.lp.SupportEquipmentEntry;
import com.linc.platform.common.step.StepBaseEntry;
import com.linc.platform.common.step.StepTypeEntry;
import com.linc.platform.foundation.model.organization.common.facility.FacilityEntry;
import com.linc.platform.localconfig.FacilityConfigPresenterImpl;
import com.linc.platform.toolset.lpputaway.model.PutAwayLocationSuggestionEntry;
import com.linc.platform.toolset.lpputaway.model.PutAwayTaskEntry;
import com.linc.platform.toolset.lpputaway.presenter.PutAwayStepWorkPresenter;
import com.linc.platform.toolset.lpputaway.presenter.impl.PutAwayStepWorkPresenterImpl;
import com.linc.platform.toolset.lpputaway.view.OperateWorkView;
import com.linc.platform.utils.CollectionUtil;
import com.linc.platform.utils.ToastUtil;
import com.lt.linc.R;
import com.lt.linc.util.LocationCheckNoDialog;
import com.lt.linc.util.LocationItemCheckConfirmDialog;

/**
 * <AUTHOR>
 */
public class PutAwayStepWorkFragment extends LincBaseFragment implements OperateWorkView {
    private AppCompatTextView locationNameTxt;
    private AppCompatTextView supportEquipmentTxt;
    private AppCompatButton takeOverBtn;
    private RelativeLayout workLayout;
    private RelativeLayout takeOverLayout;
    private RelativeLayout startLayout;
    private LinearLayout doneLayout;
    private ScanEditText lpEdit;
    private PutAwayStepWorkPresenter presenter;
    private CircleProgress circleProgress;
    private LpAdapter adapter;

    private String facilityId;
    private FacilityEntry facilityEntry;
    private PutAwayTaskEntry taskEntry;
    private LocationEntry selectedLocation;
    private LocationSelectorDialog locationSelectorDialog;

    public PutAwayStepWorkFragment() {
    }

    public static LincBaseFragment newInstance(@NonNull PutAwayTaskEntry taskEntry) {
        LincBaseFragment fragment = new PutAwayStepWorkFragment();
        Bundle bundle = new Bundle();
        bundle.putSerializable(PutAwayTaskEntry.TAG, taskEntry);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    public void onResume() {
        super.onResume();
        presenter.loadData();
        presenter.loadFacility(facilityId);
    }

    public void onSubmitClick() {
        if (selectedLocation == null || adapter.getItemCount() == 0) {
            GeneralAlertDialog.createAlertDialog(getContext(), getString(R.string.title_alarm),
                    getString(R.string.text_put_away_alarm_message)).show();
            return;
        }

        if (LocationTypeEntry.PICK.equals(selectedLocation.type)) {
            GeneralAlertDialog.createAlertDialog(getContext(), getString(R.string.title_alarm),
                    getString(R.string.text_can_not_put_away_to_pick_location)).show();
            return;
        }

        if (facilityEntry != null && facilityEntry.useDockCheckingNo
                && !TextUtils.isEmpty(selectedLocation.checkingNo)) {
            showCheckNoDialog();
        } else {
            checkLocationItem();
        }
    }

    private void checkLocationItem() {
        LocationItemCheckEntry checkEntry = new LocationItemCheckEntry();
        checkEntry.lpIds = adapter.getlpIds();
        presenter.checkLocationItem(taskEntry.id, selectedLocation, checkEntry);
    }

    private void showCheckNoDialog() {
        LocationCheckNoDialog dialog = new LocationCheckNoDialog();
        dialog.setArgument(isPass -> {
            if (isPass) {
                checkLocationItem();
            }
        }, selectedLocation.checkingNo);
        dialog.show(getFragmentManager(), "checkingNo");
    }

    public void onDoneBtnClick() {
        new AlertDialog.Builder(getContext()).setTitle(R.string.title_alarm)
                .setCancelable(true)
                .setMessage(R.string.label_confirm_closing_the_task)
                .setPositiveButton(R.string.label_ok, (dialog, which) -> presenter.closeStep())
                .setNegativeButton(R.string.label_cancel, (dialog, which) -> {
                }).show();

    }

    @Override
    public void showProgress(boolean show) {
        if (getActivity() == null || getActivity().isFinishing()) {
            return;
        }
        if (show) {
            circleProgress.show();
        } else {
            circleProgress.dismiss();
        }
    }

    @Override
    public void onTakeOverSuccess(boolean success) {
        if (success) {
            takeOverBtn.setClickable(false);
            ToastUtil.showToast(getContext(), R.string.take_over_success_wait_confirm);
        }
    }

    @Override
    public void onPutAwaySuccess() {
        ToastUtil.showToast(getContext(), R.string.put_away_success);
        adapter.clearAll();
        lpEdit.setText("");
        locationNameTxt.setText("");
        supportEquipmentTxt.setText("");
        selectedLocation = null;
    }

    @Override
    public void onAssignSuccess(boolean success) {
        if (success) {
            switchViewByStepStatus();
            ToastUtil.showToast(getString(R.string.msg_step_take_over_success));
        }
    }

    @Override
    public void showForceCloseDialog(String errorMessage) {
        String forceClose = getContext().getResources().getString(R.string.msg_force_close);
        new AlertDialog.Builder(getContext())
                .setMessage(errorMessage + forceClose)
                .setCancelable(true)
                .setPositiveButton(R.string.label_ok, (dialog, which) -> presenter.forceCloseStep())
                .setNegativeButton(R.string.label_cancel, (dialog, which) -> dialog.dismiss())
                .show();
    }

    @Override
    public void refreshFacility(FacilityEntry facilityEntry) {
        this.facilityEntry = facilityEntry;
    }

    @Override
    public void submitPutAway() {
        presenter.doPutAway(adapter.getlpIds(), selectedLocation.id);
    }

    @Override
    public void showPutAwayConfirmDialog(String errorMessage) {
        LocationItemCheckConfirmDialog confirmDialog = LocationItemCheckConfirmDialog.newInstance(getString(R.string.title_confirmation_needed), errorMessage);
        confirmDialog.setOnClickEvent(new LocationItemCheckConfirmDialog.OnClickEvent() {
            @Override
            public void onCancelClick() {

            }

            @Override
            public void onContinueClick() {
                submitPutAway();
            }
        });
        confirmDialog.show(getFragmentManager(), "");
    }

    @Override
    public void onCloseSuccess() {
        doneLayout.setVisibility(View.VISIBLE);
        takeOverLayout.setVisibility(View.GONE);
        workLayout.setVisibility(View.GONE);
        startLayout.setVisibility(View.GONE);
    }

    @Override
    public void onStartSuccess() {
        takeOverLayout.setVisibility(View.GONE);
        workLayout.setVisibility(View.VISIBLE);
        doneLayout.setVisibility(View.GONE);
        startLayout.setVisibility(View.GONE);
    }

    @Override
    public void onLoadTaskDone(PutAwayTaskEntry putAwayTaskEntry) {
        showViewByStepAndAssignee();
    }

    @Override
    public void updateLocationSuggestion(PutAwayLocationSuggestionEntry suggestionEntry) {
        selectedLocation = suggestionEntry == null ? null : suggestionEntry.location;
        setLocationText();
    }

    private void setLocationText() {
        if (selectedLocation == null) {
            locationNameTxt.setText("");
            supportEquipmentTxt.setText("");
        } else {
            if (CollectionUtil.isNullOrEmpty(selectedLocation.supportEquipments)) {
                supportEquipmentTxt.setText("");
            } else {
                String equipment = Stream.of(selectedLocation.supportEquipments)
                        .map(e -> SupportEquipmentEntry.FORKLIFT == e
                                ? getResources().getString(R.string.text_forklift)
                                : getResources().getString(R.string.text_pallet_jack))
                        .distinct()
                        .collect(Collectors.joining(" | "));
                supportEquipmentTxt.setText("(" + equipment + ")");
            }
            locationNameTxt.setText(selectedLocation.name);
        }
    }

    @Override
    public int getTabTitleResource() {
        return R.string.title_task_detail_tab_work;
    }

    @Override
    protected int getLayoutId() {
        taskEntry = (PutAwayTaskEntry) getArguments().getSerializable(PutAwayTaskEntry.TAG);
        presenter = new PutAwayStepWorkPresenterImpl(this, taskEntry, getIdmUserId());
        circleProgress = CircleProgress.create(this.getContext());
        return R.layout.fragment_put_away_step_work;
    }

    @Override
    protected void initView() {
        workLayout = findViewById(R.id.work_layout);
        takeOverLayout = findViewById(R.id.take_over_layout);
        doneLayout = findViewById(R.id.step_done_layout);
        startLayout = findViewById(R.id.start_layout);

        AppCompatButton startBtn = findViewById(R.id.start_btn);
        startBtn.setOnClickListener(v -> presenter.startStep());
        facilityEntry = FacilityConfigPresenterImpl.getInstance().getFacility(getIdmUserId());
        facilityId = facilityEntry == null ? null : facilityEntry.id;

        initWorkLayout();
        takeOverBtn = findViewById(R.id.take_over_btn);
        takeOverBtn.setOnClickListener(v -> assignOrTaskOverStep());
    }

    private void initWorkLayout() {
        initLpScanEdt();
        RecyclerView recyclerView = findViewById(R.id.lp_recyclerView);
        locationNameTxt = findViewById(R.id.location_name_txt);
        supportEquipmentTxt = findViewById(R.id.support_equipment_txt);

        AppCompatButton submitBtn = findViewById(R.id.submit_btn);
        submitBtn.setOnClickListener(v -> onSubmitClick());

        AppCompatButton doneBtn = findViewById(R.id.done_btn);
        doneBtn.setOnClickListener(v -> onDoneBtnClick());

        AppCompatButton selectLocationBtn = findViewById(R.id.select_location_btn);
        selectLocationBtn.setOnClickListener(v -> toSelectLocation());

        AppCompatButton getSuggestLocationBtn = findViewById(R.id.get_suggestion_btn);
        getSuggestLocationBtn.setOnClickListener(view -> {
            if (CollectionUtil.isNullOrEmpty(adapter.getlpIds())) {
                ToastUtil.showToast(getContext(), R.string.msg_please_scan_or_input_lp);
            } else {
                presenter.loadLocationSuggestion(adapter.getlpIds());
            }
        });

        adapter = new LpAdapter();
        recyclerView.setAdapter(adapter);
        recyclerView.addItemDecoration(new ItemDividerDecoration(this.getContext()));

        locationSelectorDialog = new LocationSelectorDialog(getActivity());
        locationSelectorDialog.setSelectListener(locationEntry -> {
            selectedLocation = locationEntry;
            setLocationText();
        });
    }

    private void toSelectLocation() {
        locationSelectorDialog.showLocationByLpItemLocationGroup(lpEdit.getText(),
                taskEntry.companyId);
    }

    private void initLpScanEdt() {
        lpEdit = findViewById(R.id.lp_barcode_edt);
        lpEdit.setHintText(getResources().getString(R.string.text_input_scan_lp));
        lpEdit.setOnActionListener(new ScanEditText.OnActionListener() {
            @Override
            public void onAddScanDone(String data) {
                if (!TextUtils.isEmpty(data)) {
                    adapter.addLp(data.toUpperCase());
                    lpEdit.setContentEdtFocus();
                    lpEdit.setText("");
                }
            }

            @Override
            public void onRemoveScanDone(String data) {

            }

            @Override
            public void onSearch(String data) {

            }

            @Override
            public void onClearText() {

            }
        });

    }

    private void assignOrTaskOverStep() {
        StepBaseEntry stepBaseEntry = taskEntry.getStep(StepTypeEntry.PUT_AWAY);
        if (taskEntry != null && !TextUtils.isEmpty(taskEntry.assigneeUserId)
                && taskEntry.assigneeUserId.equals(getIdmUserId())) {
            presenter.doAssignStep();
        } else if (CollectionUtil.isNullOrEmpty(stepBaseEntry.assigneeUserIds)) {
            presenter.doAssignStep();
        } else {
            AppCompatEditText descEdt = new AppCompatEditText(getContext());
            descEdt.setHint(R.string.send_message_to_owner);
            AlertDialog.Builder builder = new AlertDialog.Builder(getContext())
                    .setTitle(R.string.title_take_over)
                    .setView(descEdt)
                    .setPositiveButton(R.string.text_ok, (dialog, which)
                            -> presenter.doTakeOver(descEdt.getText().toString()))
                    .setNegativeButton(R.string.text_cancel, ((dialog, which) -> dialog.dismiss()));
            builder.create().show();
        }
    }

    private void showViewByStepAndAssignee() {
        if (presenter.isStepDone()) {
            startLayout.setVisibility(View.GONE);
            doneLayout.setVisibility(View.VISIBLE);
            workLayout.setVisibility(View.GONE);
            takeOverLayout.setVisibility(View.GONE);
        } else if (presenter.isOwner()) {
            switchViewByStepStatus();
        } else {
            startLayout.setVisibility(View.GONE);
            doneLayout.setVisibility(View.GONE);
            workLayout.setVisibility(View.GONE);
            takeOverLayout.setVisibility(View.VISIBLE);
        }
    }

    private void switchViewByStepStatus() {
        if (presenter.isStepNew()) {
            startLayout.setVisibility(View.VISIBLE);
            doneLayout.setVisibility(View.GONE);
            workLayout.setVisibility(View.GONE);
            takeOverLayout.setVisibility(View.GONE);
        } else if (presenter.isStepDone()) {
            doneLayout.setVisibility(View.VISIBLE);
            workLayout.setVisibility(View.GONE);
            startLayout.setVisibility(View.GONE);
            takeOverLayout.setVisibility(View.GONE);
        } else {
            doneLayout.setVisibility(View.GONE);
            workLayout.setVisibility(View.VISIBLE);
            startLayout.setVisibility(View.GONE);
            takeOverLayout.setVisibility(View.GONE);
        }
    }
}

