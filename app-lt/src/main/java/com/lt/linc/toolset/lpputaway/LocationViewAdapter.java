package com.lt.linc.toolset.lpputaway;

import androidx.recyclerview.widget.RecyclerView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import com.annimon.stream.Collectors;
import com.annimon.stream.Stream;
import com.linc.platform.toolset.lpputaway.model.LocationViewEntry;
import com.linc.platform.utils.CollectionUtil;
import com.lt.linc.R;
import com.lt.linc.util.Constant;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
class LocationViewAdapter extends
        RecyclerView.Adapter<LocationViewAdapter.ReceiveLocationViewHolder> {
    private List<LocationViewEntry> locationViewEntryList = new ArrayList<>();

    LocationViewAdapter() {
    }

    @Override
    public LocationViewAdapter.ReceiveLocationViewHolder onCreateViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_put_away_locationview, parent, false);
        return new ReceiveLocationViewHolder(view);
    }

    @Override
    public void onBindViewHolder(ReceiveLocationViewHolder holder, int position) {
        LocationViewEntry locationViewEntry = locationViewEntryList.get(position);
        holder.locationTxt.setText(locationViewEntryList.get(position).location);

        String needPutAwayLp = "";
        if (!CollectionUtil.isNullOrEmpty(locationViewEntry.lps)) {
            needPutAwayLp = Stream.of(locationViewEntry.lps)
                    .filter(locationEntry -> !locationEntry.lpId.startsWith(Constant.PREFIX_HLP))
                    .map(lp -> lp.lpId)
                    .collect(Collectors.joining(" | "));
        }
        holder.lpCountTxt.setText(needPutAwayLp);
    }


    @Override
    public int getItemCount() {
        return locationViewEntryList.size();
    }

    public void setEntryList(List<LocationViewEntry> entry) {
        locationViewEntryList = entry;
        notifyDataSetChanged();
    }

    class ReceiveLocationViewHolder extends RecyclerView.ViewHolder {
        private TextView locationTxt;
        private TextView lpCountTxt;

        ReceiveLocationViewHolder(View itemView) {
            super(itemView);
            locationTxt = (TextView) itemView.findViewById(R.id.location_txt);
            lpCountTxt = (TextView) itemView.findViewById(R.id.lp_count_txt);
        }
    }
}
