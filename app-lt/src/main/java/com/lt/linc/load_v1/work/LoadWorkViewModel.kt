package com.lt.linc.load_v1.work

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.text.TextUtils
import android.util.ArrayMap
import com.customer.widget.util.ImageUtils
import com.linc.platform.baseapp.model.LocationEntry
import com.linc.platform.common.help.FunctionHelpPresenterImpl
import com.linc.platform.common.lp.LpApi
import com.linc.platform.common.step.StepStatusEntry
import com.linc.platform.common.task.TaskStatusEntry
import com.linc.platform.core.PermissionManager
import com.linc.platform.foundation.api.CustomerApi
import com.linc.platform.foundation.api.FileEntryApi
import com.linc.platform.foundation.api.OrderApi
import com.linc.platform.foundation.api.CarrierApi
import com.linc.platform.foundation.api.configurationmap.ConfigurationApi
import com.linc.platform.foundation.model.ConfigurationMapSearchEntry
import com.linc.platform.foundation.model.CustomerViewEntry
import com.linc.platform.foundation.model.OrderBatchUpdateEntry
import com.linc.platform.foundation.model.OrderEntry
import com.linc.platform.foundation.model.OrderStatusEntry
import com.linc.platform.foundation.model.OrderTypeEntry
import com.linc.platform.foundation.model.OrderUpdateEntry
import com.linc.platform.foundation.model.file.FileCategory
import com.linc.platform.foundation.model.file.FileCreateEntry
import com.linc.platform.foundation.model.file.FileEntryViewEntry
import com.linc.platform.foundation.model.file.FileScenario
import com.linc.platform.foundation.model.file.FileSearchEntry
import com.linc.platform.generaltask.model.GeneralTaskViewEntry
import com.linc.platform.http.ErrorCode
import com.linc.platform.http.ErrorResponse
import com.linc.platform.idm.api.IdmApi
import com.linc.platform.idm.model.PermissionEntry
import com.linc.platform.idm.model.mapToUserLevel
import com.linc.platform.inventory.model.LPUpdate
import com.linc.platform.load.api.LoadTaskApi
import com.linc.platform.load.api.LoadWorkApi
import com.linc.platform.load.model.LoadDetailEntry
import com.linc.platform.load.model.LoadPhotoEntry
import com.linc.platform.load.model.LoadStatusEntry
import com.linc.platform.load.model.LoadUpdateEntry
import com.linc.platform.load.model.LoadingRequestEntry
import com.linc.platform.load.model.OrderProNoUpdate
import com.linc.platform.load.model.getPalletTypeIds
import com.linc.platform.load.v1.model.LpViewEntry
import com.linc.platform.load.v1.model.UpdatePhotosRequestEntry
import com.linc.platform.pack.api.PackTaskApi
import com.linc.platform.pick.model.LPBatchUpdateEntry
import com.linc.platform.print.commonprintlp.PrintData
import com.linc.platform.print.commonprintlp.PrintView
import com.linc.platform.print.model.LabelSizeEntry
import com.linc.platform.print.model.PrinterEntry
import com.linc.platform.utils.ConfigurationMapUtil
import com.linc.platform.utils.PrintUtil
import com.linc.platform.utils.ResUtil
import com.lt.linc.R
import com.lt.linc.common.Constant
import com.lt.linc.common.extensions.addToNewList
import com.lt.linc.common.extensions.deepCopy
import com.lt.linc.common.extensions.toException
import com.lt.linc.common.mvi.StepProcessReactiveViewModel
import com.lt.linc.common.mvi.subscribe
import com.lt.linc.common.mvvm.kotlin.BaseRepository
import com.lt.linc.common.mvvm.kotlin.OneShot
import com.lt.linc.common.mvvm.kotlin.androidx.lifecycle.viewmodel_ktx.viewModelScope
import com.lt.linc.common.mvvm.kotlin.extensions.apiServiceLazy
import com.lt.linc.common.mvvm.kotlin.extensions.collectIn
import com.lt.linc.common.mvvm.kotlin.extensions.getString
import com.lt.linc.common.mvvm.kotlin.extensions.launch
import com.lt.linc.common.mvvm.kotlin.extensions.requestAllAwait
import com.lt.linc.common.mvvm.kotlin.extensions.showLoading
import com.lt.linc.common.mvvm.kotlin.extensions.showToast
import com.lt.linc.common.mvvm.kotlin.extensions.speakAndToast
import com.lt.linc.common.mvvm.kotlin.shoot
import com.lt.linc.home.more.material.MaterialCenterActivity
import com.lt.linc.load.detail.TaskDetailActivity
import com.lt.linc.load.operate.AddProNoActivity
import com.lt.linc.load.operate.AddSealOrCountingSheetActivity
import com.lt.linc.load.orderlist.LoadOrderListActivity
import com.lt.linc.load_v1.config.LoadTaskUserLevelConfig
import com.lt.linc.load_v1.work.LoadWorkUiState.PageProcess
import com.lt.linc.load_v1.work.LoadWorkUiState.PageWorkStatus
import com.lt.linc.load_v1.work.LoadWorkUiState.WorkOnLoadLevelStatus.Load
import com.lt.linc.load_v1.work.LoadWorkUiState.WorkOnLoadLevelStatus.UnLoad
import com.lt.linc.load_v1.work.LoadWorkUiState.WorkOnLoadLevelStatusSwitch
import com.lt.linc.load_v1.work.LoadWorkUiState.WorkProgress
import com.lt.linc.load_v1.work.mbol.MbolListV1Activity
import com.lt.linc.load_v1.work.mbol.MbolListV1Activity.Companion.KEY_LOAD_ID
import com.lt.linc.load_v1.work.orderwork.LoadLevelWorkPageAction
import com.lt.linc.load_v1.work.orderwork.OrderListPageAction
import com.lt.linc.load_v1.work.orderwork.UnDoProgress
import com.lt.linc.toolset.print.setting.PrintSettingActivity
import com.lt.linc.util.durationFlow
import com.lt.linc.util.v1widget.FileRepository
import com.lt.linc.util.v1widget.ItemType
import com.lt.linc.util.v1widget.UploadPhotoBean
import com.lt.linc.util.v1widget.UploadStatus
import com.lt.linc.util.v1widget.filteredDeleteItems
import com.lt.linc.util.v1widget.getAvailablePhotoIds
import com.lt.linc.util.v1widget.isSame
import com.lt.linc.util.v1widget.mergeToNewList
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.withContext
import java.io.File
import java.io.Serializable
import com.linc.platform.foundation.model.organization.common.carrier.CarrierSearchEntry
import com.lt.linc.common.mvvm.kotlin.extensions.getFormattedString


/**
 * <AUTHOR>
 * @Date 2022/6/23
 */
class LoadWorkViewModel(
    private val userId: String,
    private val facilityName: String,
    private val intentFlag: Int = 0,
    private val repository: LoadWorkRepo = LoadWorkRepo(),
    initDataState: LoadWorkState,
    initUiState: LoadWorkUiState = LoadWorkUiState(),
    private val orderListPageUndoProgress: UnDoProgress = UnDoProgress(),
    private val loadModeUndoProgress: UnDoProgress = UnDoProgress(),
    private val unLoadModeUndoProgress: UnDoProgress = UnDoProgress(),
) : StepProcessReactiveViewModel<LoadWorkState, LoadWorkUiState>(initDataState,
    initUiState,
    initDataState.stepEntry,
    autoReportProcessTime = shouldReportStepProcessTime(intentFlag)) {

    private val functionHelpPresenterImpl by lazy { FunctionHelpPresenterImpl() }

    init {
        initStateSubscribe()
        //load data
        loadLoadDetail()
    }

    fun initStateSubscribe() {
        // Calculate process time, update every seconds
        durationFlow(dataState.stepEntry.startTime).collectIn(viewModelScope) {
            setUiState { copy(processDuration = it) }
        }

        subscribe(LoadWorkState::loadId) {
            setUiState { copy(loadId = it) }
        }

        subscribe(LoadWorkState::dockName) {
            setUiState { copy(dockName = it) }
        }

        subscribe(LoadWorkState::userLevelConfig, LoadWorkState::workOnLoadLevelStatus) { userLevelConfig, workOnLoadLevelStatus ->
            setUiState {
                copy(palletInputScanAllowManualInput = userLevelConfig?.allowManualInputPallet ?: false,
                    loadAllButtonVisible = userLevelConfig?.showLoadAllButton ?: false,
                    loadLevelUndoButtonVisible = if (workOnLoadLevelStatus == Load) userLevelConfig?.displayUndoButtonInLoadMode ?: false
                    else userLevelConfig?.displayUndoButtonInUnLoadMode ?: false)
            }
        }

        subscribe(LoadWorkState::loadOrderList, LoadWorkState::pageWorkStatus) { loadOrderList, pageWorkStatus ->
            setUiState {
                copy(takePhotoToLoadBtnVisible = PageWorkStatus.OnLoadLevel == pageWorkStatus,
                    pageWorkStatusSwitchVisible = loadOrderList.let { !it.isNullOrEmpty() && !it.isAllHasSequence() },
                    pageWorkStatusSwitch = OneShot(pageWorkStatus))
            }
        }

        subscribe(LoadWorkState::loadOrderList,
            LoadWorkState::loadPhotoList,
            LoadWorkState::customerEntry,
            LoadWorkState::userLevelConfig,
            LoadWorkState::carrierList
        ) { loadOrderList, loadPhotoList, customerEntry, userLevelConfig, carrierList ->
            //check is need pro no
            val isNeedProNo = customerEntry?.loadCollectFields?.contains(CustomerViewEntry.LOAD_COLLECT_FIELD_PRO_NO) ?: false
            val orderWrapperList = loadOrderList?.map { orderEntry ->
                //filter order photos
                val orderPhotos = loadPhotoList?.filter { fileEntry -> fileEntry.fileEntryViewEntry.tags?.last() == orderEntry.id }
                val carrier = carrierList?.find { it.id == orderEntry.carrierId }
                OrderEntryWrapper(
                    orderEntry = orderEntry,
                    isRequireProNumber = isNeedProNo,
                    isAllowUpdateProNo = carrier?.isAutoCreateProNo != true,
                    isAllowManualProNumber = userLevelConfig?.allowManualProNumber == true,
                    orderPhotos = orderPhotos,
                    maxTaskVideoLengthInSeconds = customerEntry?.maxTaskVideoLengthInSeconds,
                    allowTakeVideo = customerEntry?.allowTakeVideo
                )
            }
            setUiState {
                copy(orderListForShowing = OneShot(orderWrapperList))
            }
        }

        //this load ptl progress
        subscribe(LoadWorkState::loadedOrderAmount, LoadWorkState::totalOrderByLoad) { loadedOrderAmount, totalOrderByLoad ->
            setUiState {
                copy(loadedOrderProgressByLoadEvent = WorkProgress(loadedProgress = loadedOrderAmount, totalProgress = totalOrderByLoad))
            }
        }

        //this load ptl progress
        subscribe(LoadWorkState::loadedPltByLoad, LoadWorkState::totalPltByLoad) { loadedPltByLoad, totalPltByLoad ->
            setUiState {
                copy(loadedPltProgressByLoadEvent = WorkProgress(loadedProgress = loadedPltByLoad, totalProgress = totalPltByLoad))
            }
        }

        //this task plt progress
        subscribe(LoadWorkState::loadedPltByLoad,
            LoadWorkState::loadedPltByOtherLoadsByTask,
            LoadWorkState::totalPltByTask) { loadedPltByLoad, loadedPltByOtherLoadsByTask, totalPltByTask ->
            setUiState {
                copy(loadedPltProgressByTaskEvent = WorkProgress(loadedPltByLoad + loadedPltByOtherLoadsByTask, totalPltByTask))
            }
        }

        //this task load progress
        subscribe(LoadWorkState::loadedLoadByTask, LoadWorkState::totalLoadByTask) { loadedLoadByTask, totalLoadByTask ->
            setUiState {
                copy(loadedLoadProgressByTaskEvent = WorkProgress(loadedLoadByTask, totalLoadByTask))
            }
        }

        //update pageStatus load or unload switch
        subscribe(LoadWorkState::loadedPltByLoad, LoadWorkState::workOnLoadLevelStatus) { loadedPltByLoad, workOnLoadLevelStatus ->
            setUiState {
                copy(workOnLoadLevelStatusSwitch = WorkOnLoadLevelStatusSwitch(isEnable = loadedPltByLoad > 0,
                    workOnLoadLevelStatus = workOnLoadLevelStatus))
            }
        }

        //lp list showing
        subscribe(LoadWorkState::loadedPltList,
            LoadWorkState::unLoadedPltList,
            LoadWorkState::userLevelConfig,
            LoadWorkState::workOnLoadLevelStatus) { loadedPltList, unLoadedPltList, userLevelConfig, workOnLoadLevelStatus ->
            val allowDisplayUnloadedLPOnList = userLevelConfig?.allowDisplayUnloadedLPOnList ?: false
            val pltListShowing = if (Load == workOnLoadLevelStatus) {
                //Move the lp of load to the first index
                val newLoadedPltList = loadedPltList?.toMutableList()?.apply {
                    find { !dataState.lastHandlerLpId.isNullOrEmpty() && it.plt.id.contains(dataState.lastHandlerLpId!!) }?.let {
                        remove(it)
                        add(0, it)
                    }
                }
                //showing unloaded lp by user level config
                if (userLevelConfig?.allowDisplayUnloadedLPOnList == true) {
                    newLoadedPltList.addToNewList(unLoadedPltList)
                } else {
                    newLoadedPltList
                }
            } else {
                loadedPltList
            }
            setUiState { copy(pltListForShowing = OneShot(pltListShowing)) }

            if (!dataState.lastHandlerLpId.isNullOrEmpty() && Load == workOnLoadLevelStatus) {
                setUiState { copy(pltListSelectPosition = OneShot(0)) }
            }
            setDataState { copy(lastHandlerLpId = null) }
        }
        //complete or update button enable
        subscribe(LoadWorkState::workOnLoadLevelStatus,
            LoadWorkState::loadedPltList,
            LoadWorkState::isAllPltLoadByLoad) { workOnLoadLevelStatus, loadedPltList, isAllPltLoadByLoad ->
            setUiState {
                copy(completeBtnEnable = if (workOnLoadLevelStatus == Load) isAllPltLoadByLoad
                else loadedPltList?.count { it.isScannedWaitToUnload } ?: 0 > 0)
            }
        }
    }

    fun loadLoadDetail(isSwitchToLoad: Boolean = false, isNeedDoAutoCc: Boolean = true) {
        launch {
            awaitDataState()
            requestAwait(repository.loadLoadDetail(dataState.loadId)).onSuccess {
                setDataState { copy(loadViewEntry = it) }
                loadOrderList(isSwitchToLoad, isNeedDoAutoCc)
            }
        }
    }

    private fun loadOrderList(isSwitchToLoad: Boolean = false, isNeedDoAutoCc: Boolean = true) {
        launch {
            val result = requestAllAwait(
                repository.getOrderListByLoad(dataState.loadId),
                repository.getCustomer(dataState.loadDetailEntry.customerId),
                repository.searchEntryPhoto(listOf(dataState.taskId, dataState.loadId))
            )
            //user level config is null to request
            val userLevelConfig = dataState.userLevelConfig ?: requestAwait(
                repository.getUserLevelControl(
                    dataState.stepEntry.id,
                    dataState.loadDetailEntry.customerId
                ), showLoading = false
            ).getOrNull()
            val orderList = result.first.getOrNull()?.orderEntries
            orderList ?: return@launch
            val customer = result.second.getOrNull()
            val loadPhotos = result.third.getOrNull()
            
            // 过滤出所有订单的 carrierId，并查询 carrier 信息
            val carrierSearchEntry = CarrierSearchEntry().apply {
                ids = orderList.mapNotNull { it.carrierId }.distinct().takeIf { it.isNotEmpty() }?: emptyList()
            }

            val carrierList = requestAwait(repository.searchCarrier(carrierSearchEntry)).getOrNull()

            //sort by sequence
            orderList.sortBy { orderEntry -> orderEntry.sequence?.toDoubleOrNull() }

            //filter this load unUpload photo
            val unUploadLoadPhotos = dataState.loadPhotoList?.filter {
                it.uploadPhotoBean.run { !filePath.isNullOrEmpty() && uploadStatus != UploadStatus.UPLOADED }
            }
            val newLoadPhotos = loadPhotos?.map {
                FileEntryViewEntryWrapper(
                    it, UploadPhotoBean(
                        photoServerId = it.fileId, videoName = it.fileName, itemType = if (
                            it.fileType == FileSearchEntry.FILE_TYPE_PHOTO) ItemType.PHOTO_DATA else ItemType.VIDEO_DATA
                    )
                )
            }.addToNewList(unUploadLoadPhotos)

            //filter loaded or unload plt
            val (loadedLpList, UnloadedLpList) = orderList.filter { orderEntry ->
                !orderEntry.orderLps.isNullOrEmpty()
            }.flatMap { orderEntry ->
                orderEntry.orderLps
            }.run {
                filter { lpEntry ->
                    lpEntry.isLoaded()
                }.map { slpEntry ->
                    //filter this lp unUpload photoBean
                    val unUploadLpPhotos =
                        dataState.loadedPltList?.find { lpWrapper -> lpWrapper.plt.id == slpEntry.id }?.palletUploadPhotoBeans?.filter { photoBean ->
                            !photoBean.filePath.isNullOrEmpty() && photoBean.uploadStatus != UploadStatus.UPLOADED
                        }
                    val newPhotoBeanList =
                        slpEntry.photoIds?.map { photoId -> UploadPhotoBean(photoServerId = photoId) }.addToNewList(unUploadLpPhotos)
                    LoadWorkPalletWrapper(plt = slpEntry,
                        isRequireTakePhoto = customer.isRequireTakePhotoForEachPalletInLoading(),
                        palletUploadPhotoBeans = newPhotoBeanList)
                } to filter { lpEntry ->
                    !lpEntry.isLoaded()
                }.map { slpEntry ->
                    LoadWorkPalletWrapper(plt = slpEntry)
                }
            }
            setDataStateAwait {
                copy(userLevelConfig = userLevelConfig,
                    customerEntry = customer,
                    loadOrderList = orderList,
                    loadPhotoList = newLoadPhotos,
                    loadedPltList = loadedLpList,
                    unLoadedPltList = UnloadedLpList,
                    //reset Switch to load
                    workOnLoadLevelStatus = if (isSwitchToLoad) Load else workOnLoadLevelStatus,
                    carrierList = carrierList
                )
            }
            //autoCc
            if (isNeedDoAutoCc) {
                doAutoCcByOrders()
            }
        }
    }

    private fun doAutoCcByOrders() {
        val orderIds = dataState.loadOrderList?.run {
            filter { orderEntry -> orderEntry.loadedQty == 0 }.map { orderEntry -> orderEntry.id }.toList()
        }

        request(repository.doAutoCcByOrders(orderIds), error = {
            if (ErrorCode.NEED_COLLECT_SN_BEFORE_LOAD == it.code || ErrorCode.CHECK_CAN_SKIP_AUTO_CC == it.code) {
                setUiState { copy(showAutoCCErrorDialog = OneShot(it.error)) }
            } else {
                showToast(it.errorMessage)
            }
        }, success = {
            it ?: return@request
            it.apply {

                var error = ""

                if (!noLPConfigOrderIds.isNullOrEmpty()) {
                    error += "${noLPConfigOrderIds.joinToString { s -> s }} ${
                        ResUtil.getString(R.string.text_need_config_lp)
                    }\n"
                }

                if (!TextUtils.isEmpty(error)) {
                    setUiState { copy(showAutoCCErrorDialog = OneShot(error)) }
                } else {
                    loadLoadDetail(isNeedDoAutoCc = false)
                }
            }
        })
    }

    //update page status
    fun setPageWorkStatusSwitch(isOpen: Boolean) {
        setDataState {
            copy(pageWorkStatus = if (isOpen) PageWorkStatus.OnLoadLevel else PageWorkStatus.OnOrderList)
        }
    }

    //update work on load status
    fun setLoadOrUnloadSwitch(isOpen: Boolean) {
        setDataState {
            if (isOpen) {
                copy(workOnLoadLevelStatus = UnLoad,
                    loadedPltList = loadedPltList?.map { it.copy(isSwitchToLoad = false, isScannedWaitToUnload = false) })
            } else {
                copy(workOnLoadLevelStatus = Load,
                    loadedPltList = loadedPltList?.map { it.copy(isSwitchToLoad = true, isScannedWaitToUnload = false) })
            }
        }
    }

    fun completeLoadOrUpdateToUnload(
        onCompleteLoad: (String) -> Flow<Boolean?>, onShowRequireCountingSheet: (String) -> Flow<Boolean?>) {
        //complete load
        if (dataState.workOnLoadLevelStatus == Load) {
            completeLoad(onCompleteLoad, onShowRequireCountingSheet)
        } else {
            submitUpdateBatchUnLoadsLp()
        }
    }

    private fun submitUpdateBatchUnLoadsLp() {
        launch {
            awaitDataState()

            runCatching { validateLoadOrUnloadSubmit() }.onFailure {
                speakAndToast(it.message!!)
                return@launch
            }

            val requests = dataState.loadedPltList?.filter {
                !it.isSwitchToLoad && it.isScannedWaitToUnload
            }?.map { palletEntry ->
                val slpOrderId = dataState.loadOrderList?.find { it.lps?.contains(palletEntry.plt.id) == true }?.id
                LoadingRequestEntry().apply {
                    loadId = dataState.loadId
                    orderId = slpOrderId
                    slpId = palletEntry.plt.id
                }
            }

            requestAwait(repository.batchUnLoadSlp(dataState.stepEntry.id, requests)).onSuccess {

                //batch update lp packagingTypeSpecId to ""
                requests?.map { loadRequest ->
                    LPUpdate().apply {
                        id = loadRequest.slpId
                        packagingTypeSpecId = ""
                    }
                }?.let { lpUpdateList ->
                    LPBatchUpdateEntry().apply { lpUpdates = lpUpdateList }
                }?.let { lpBatchUpdateEntry ->
                    requestAwait(repository.updateLpBatch(lpBatchUpdateEntry))
                }

                speakAndToast(R.string.msg_unload_success)
                //refresh data
                loadLoadDetail(true)
            }
        }
    }

    fun completeLoad(
        onCompleteLoad: (String) -> Flow<Boolean?>, onShowRequireCountingSheet: (String) -> Flow<Boolean?>) {
        if (dataState.isLoadCompleted) {
            completedLoadSuccessFinishPage()
            return
        }
        if (dataState.customerEntry.isRequireTakePhotoForEachPalletInLoading() && dataState.loadedPltList?.any { it.plt.photoIds.isNullOrEmpty() } == true) {
            showToast(R.string.msg_take_photo_for_each_pallet)
            return
        }
        launch {
            //check has need count sheet
            requestAwait(repository.checkLoadCountingSheet(dataState.loadId)).onSuccess {

                //countingSheetPhotos is empty can not complete load
                it?.customerViewEntry?.requiredCountingSheetFacilities?.apply {
                    val hasFacility = any { name -> name.lowercase() == facilityName.lowercase() }
                    if (hasFacility && it.countingSheetPhotos.isNullOrEmpty()) {
                        val isRequireCountingSheet =
                            onShowRequireCountingSheet(ResUtil.getString(R.string.hint_query_counting_sheet)).firstOrNull() ?: false
                        if (isRequireCountingSheet) {
                            setUiState { copy(pageProcess = OneShot(PageProcess.GoToAddSeal)) }
                        }
                        return@launch
                    }
                }
                //start complete load
                requestAwait(repository.completeLoad(dataState.taskId, dataState.loadId), error = { error ->
                    //do force complete load
                    if (ErrorCode.FORCE_COMPLETE_LOAD == error.code && PermissionManager.getInstance()
                            .hasPermission(PermissionEntry.Outbound_Load_ForceCloseTask)
                    ) {
                        doForceCompleteLoad(error.errorMessage, onCompleteLoad)
                    } else if (ErrorCode.LOAD_COMPLETE_ERROR_NEED_ADD_MATERIAL == error.code) {
                        setUiState { copy(pageProcess = OneShot(PageProcess.NeedAddMaterialDialog(error.error))) }
                    } else {
                        showToast(error.errorMessage)
                    }

                }).onSuccess {
                    completedLoadSuccessToNextLoad(onCompleteLoad)
                }
            }
        }
    }

    private fun doForceCompleteLoad(error: String, onCompleteLoad: (String) -> Flow<Boolean?>) {
        launch {
            //show force complete load confirm dialog
            val isForceComplete = onCompleteLoad(error + ResUtil.getString(R.string.msg_force_complete_load)).firstOrNull() ?: false

            if (isForceComplete) {
                requestAwait(repository.forceCompleteLoad(dataState.taskId, dataState.loadId)).onSuccess {
                    completedLoadSuccessToNextLoad(onCompleteLoad)
                }
            }
        }
    }

    //complete load success
    private fun completedLoadSuccessToNextLoad(onCompleteLoad: (String) -> Flow<Boolean?>) {
        launch {
            if (dataState.userLevelConfig?.autoProceedToLoadNextOnComplete == true) {
                //check has next load to work
                dataState.loadTaskEntry.loadList?.sortedBy { it.sequence }?.apply {
                    val index = indexOfFirst { it.id == dataState.loadId }
                    //find next load when status unCompleted
                    var nextLoadDetail = if (index in 0 until lastIndex) subList(index + 1, count()).find { !it.isLoadCompleted } else null
                    if (nextLoadDetail == null) {
                        nextLoadDetail = find { it.id != dataState.loadId && !it.isLoadCompleted }
                    }

                    nextLoadDetail?.let {
                        val isToNextLoad = onCompleteLoad(ResUtil.getString(R.string.proceed_to_next_load)).firstOrNull() ?: false
                        //proceed to next load
                        if (isToNextLoad) {
                            //change load status to loaded
                            dataState.loadTaskEntry.loadList?.find { detail ->
                                detail.id == dataState.loadId
                            }?.apply { status = LoadStatusEntry.LOADED }
                            //save curLoad lp status
                            getOrNull(index)?.orderList?.flatMap { orderEntry -> orderEntry.orderLps }?.map { lp ->
                                dataState.loadedPltList?.find { loadedLp ->
                                    lp.id == loadedLp.plt.id
                                }?.let { loadedLp ->
                                    lp.loaded = loadedLp.plt.loaded
                                }
                            }
                            //start to next load
                            setDataState {
                                copy(loadDetailEntry = it,
                                    loadViewEntry = null,
                                    loadOrderList = null,
                                    loadedPltList = null,
                                    unLoadedPltList = null,
                                    pageWorkStatus = PageWorkStatus.OnOrderList,
                                    workOnLoadLevelStatus = Load)
                            }
                            loadLoadDetail()
                            return@launch
                        }
                    }
                }
            }
            completedLoadSuccessFinishPage()
        }
    }

    //complete success finish page
    private fun completedLoadSuccessFinishPage() {
        showToast(R.string.text_load_complete)
        setUiState {
            copy(pageProcess = OneShot(if (isFormDirectLoad(intentFlag)) {
                PageProcess.StartLoadTaskDetailActivity(dataState.loadTaskEntry, dataState.stepEntry, intentFlag)
            } else PageProcess.Finish))
        }
    }

    fun getWithoutCollectMaterialOrderIdToAddMaterial() {
        request(repository.getWithoutCollectMaterialOrderId(dataState.loadId)) {
            //find order to addMaterial
            dataState.loadOrderList?.find { orderEntry -> orderEntry.id == it?.firstOrNull() }?.let {
                setUiState { copy(pageProcess = PageProcess.GoToAddMaterial(it).shoot()) }
            }
        }
    }

    fun loadAllOrUnLoadAll(onConfirmLoadAll: (String) -> Flow<Boolean?>) {
        if (dataState.workOnLoadLevelStatus == Load) {
            //load all
            launch {
                val isLoadAll = onConfirmLoadAll(ResUtil.getString(R.string.text_load_all).uppercase()).firstOrNull() ?: false
                if (!isLoadAll) return@launch
                //filter unloaded lp
                val requests = dataState.loadOrderList?.filter { orderEntry ->
                    !orderEntry.orderLps.isNullOrEmpty()
                }?.flatMap { orderEntry ->
                    orderEntry.orderLps
                }?.filter { lpEntry ->
                    !lpEntry.isLoaded()
                }?.map { slpEntry ->
                    val slpOrderId = dataState.loadOrderList?.find { it.lps?.contains(slpEntry.id) == true }?.id
                    LoadingRequestEntry().apply {
                        loadId = dataState.loadId
                        orderId = slpOrderId
                        slpId = slpEntry.id
                    }
                }
                if (requests.isNullOrEmpty()) {
                    showToast(R.string.msg_no_pallet_available)
                    return@launch
                }
                requestAwait(repository.batchLoadSlp(dataState.stepEntry.id, requests)).onSuccess {
                    showToast(R.string.msg_load_all_success)
                    //refresh data
                    loadLoadDetail()
                }
            }
        } else { //unload all
            launch {
                val isUnloadAll = onConfirmLoadAll(ResUtil.getString(R.string.text_unload_all).uppercase()).firstOrNull() ?: false
                if (isUnloadAll) {
                    //update unload all state
                    setDataState { copy(loadedPltList = loadedPltList?.map { it.copy(isScannedWaitToUnload = true) }) }
                }
            }
        }
    }

    fun handlerInputOrScanResult(input: String?) {
        input ?: return
        request(repository.lpAutoComplete(input)) {
            it ?: return@request
            if (it.count() != 1) return@request
            val slp = it.first()
            setDataState { copy(lastHandlerLpId = slp) }

            //load start load
            if (dataState.workOnLoadLevelStatus == Load) {
                findOrderByLpToLoadsLp(slp)
                return@request
            }

            //unload scanned wait to update
            if (dataState.loadedPltList?.any { entry -> entry.plt.id == slp } == true) {
                unLoadModeUndoProgress.push(LoadLevelWorkPageAction.UnLoadInputPallet(slp))
                setDataState { updatePltByUnloadMode(inputPallet = slp, isUndo = false) }
            } else {
                speakAndToast(R.string.msg_invalid_lp)
            }
            resetInputFocus()
        }
    }

    private fun findOrderByLpToLoadsLp(slp: String) {
        val findOderList = dataState.loadOrderList?.filter { it.lps?.contains(slp) == true }

        if (findOderList.isNullOrEmpty()) {
            speakAndToast(ResUtil.format(R.string.msg_order_not_found, slp))
            resetInputFocus()
            return
        }

        val loadOrderIds = findOderList.map { it.id }
        val retailerIds = findOderList.map { it.retailerId }

        if (isNeedValidateUcc(loadOrderIds, retailerIds)) {
            setUiState { copy(pageProcess = OneShot(PageProcess.ScanUccDialogSubmitLpState(loadOrderIds, slp))) }
        } else {
            checkSequenceAndLpTemplateToSubmitSlp(slp)
        }
    }

    fun checkSequenceAndLpTemplateToSubmitSlp(slp: String) {
        val curOrder = dataState.loadOrderList?.firstOrNull { it.lps?.contains(slp) == true }
        curOrder ?: return

        //check order sequence
        if (canStartLoadOrder(curOrder)) {
            if (dataState.customerEntry.isAllowAutoCc()) {
                doCheckLpTemplateToLoadSlp(slp, curOrder)
                return
            }

            doSubmitLoadSlp(slp, curOrder)
        } else {
            speakAndToast(ResUtil.format(R.string.msg_order_is_locked_please_load_by_load_sequence, curOrder.id))
            resetInputFocus()
        }
    }

    private fun doCheckLpTemplateToLoadSlp(slp: String, curOrder: OrderEntry) {
        request(repository.orderLpTemplateCheck(curOrder.id), error = {
            if (ErrorCode.LP_CONFIGURATION_NOT_MATCH_WITH_ORDER_ITEMLINE == it.code) {
                doAutoCcToLoadSlp(slp, curOrder)
            } else {
                showToast(it.errorMessage)
            }
        }, success = {
            doSubmitLoadSlp(slp, curOrder)
        })
    }

    private fun doAutoCcToLoadSlp(slp: String, curOrder: OrderEntry) {
        request(repository.autoCc(orderId = curOrder.id)) {
            doSubmitLoadSlp(slp, curOrder)
        }
    }

    //check and load plt
    private fun doSubmitLoadSlp(slp: String, curOrder: OrderEntry) {
        launch {
            runCatching { validateLoadOrUnloadSubmit() }.onFailure {
                speakAndToast(it.message!!)
                resetInputFocus()
                return@launch
            }

            loadModeUndoProgress.push(LoadLevelWorkPageAction.LoadInputPallet(slp, curOrder))
            val palletTypeIds = getPalletTypeIds(curOrder)
            if (palletTypeIds.isNullOrEmpty()) {
                doLoadSlp(slp, curOrder)
                return@launch
            }
            setUiState { copy(pageProcess = OneShot(PageProcess.AddPalletDialogState(palletTypeIds, slp, curOrder))) }
        }
    }

    private suspend fun getPalletTypeIds(order: OrderEntry): List<String>? {
        val configurationMaps = requestAwait(repository.searchConfiguration(order.customerId,
            ConfigurationMapUtil.TABLE_SUGGEST_PLT_TYPE_ON_LOAD_AND_ROUTE_856)).getOrNull()
        val pltTypeOnLoadConfigurations = configurationMaps?.filter { it.customerId == order.customerId}
        if (pltTypeOnLoadConfigurations.isNullOrEmpty()) {
            return getPalletTypeIds(dataState.customerEntry, order)
        }
        val pltTypeOnLoadConfiguration = pltTypeOnLoadConfigurations.first()
        val applyToAllRetailers = pltTypeOnLoadConfiguration.valueMapping?.get(ConfigurationMapUtil.KEY_APPLY_TO_ALL_RETAILERS)
        return if (applyToAllRetailers == "true") {
            pltTypeOnLoadConfiguration.valueMapping?.get(ConfigurationMapUtil.KEY_RETAILER_PALLET_TYPES)?.split(",")
        } else {
            getPalletTypeIds(dataState.customerEntry, order)
        }
    }

    fun doLoadSlp(slp: String, curOrder: OrderEntry, palletId: String? = null) {
        val loadRequest = LoadingRequestEntry().apply {
            loadId = dataState.loadId
            orderId = curOrder.id
            slpId = slp
        }
        launch {
            requestAwait(repository.loadSlp(dataState.stepEntry.id, loadRequest)).onSuccess {
                palletId?.let {
                    // Add palletTypeId to packagingTypeSpecId of slp after loaded
                    val lpRequest: MutableMap<String, Any> = ArrayMap()
                    lpRequest["packagingTypeSpecId"] = it
                    requestAwait(repository.updateLp(slp, lpRequest))
                }
                speakAndToast(R.string.msg_load_success)
                resetInputFocus()
                //refresh data
                loadLoadDetail()
            }.onFailure {
                resetInputFocus()
            }
        }
    }

    private fun doUnloadSlp(slp: String, orderEntry: OrderEntry) {
        val loadRequest = LoadingRequestEntry().apply {
            loadId = dataState.loadDetailEntry.id
            orderId = orderEntry.id
            slpId = slp
        }
        launch {
            requestAwait(repository.unloadSlp(dataState.stepEntry.id, loadRequest)).onSuccess {
                // Always clear the packagingTypeSpecId of slp after unloaded
                val lpRequest: MutableMap<String, Any> = ArrayMap()
                lpRequest["packagingTypeSpecId"] = ""
                requestAwait(repository.updateLp(slp, lpRequest))

                speakAndToast(R.string.msg_unload_success)
                resetInputFocus()
                //refresh data
                loadLoadDetail()
            }.onFailure {
                resetInputFocus()
            }
        }
    }

    fun updateProNo(order: OrderEntry, proNumber: String, isUndo: Boolean = false) {
        if (!isUndo) {
            orderListPageUndoProgress.push(OrderListPageAction.UpdateProNo(orderEntry = order, inputProNo = proNumber))
        }
        if (dataState.userLevelConfig?.allowAutofillProNumber == true) {
            launch {
                val isNeedGenerateUniqueProNOToOrder = requestAwait(repository.searchConfiguration(order.customerId,
                    ConfigurationMapUtil.TABLE_GENERATE_UNIQUE_PRO_NO_TO_ORDER)).getOrNull()?.let {
                    ConfigurationMapUtil.generateUniqueProNOToOrder(it, dataState.loadOrderList?.firstOrNull()?.retailerName)
                } ?: false
                val isApplyProNoToOrdersOnSameLoad =
                    dataState.customerEntry?.applyProNoToOrdersOnSameLoad == true && OrderTypeEntry.DS != order.orderType

                if (isNeedGenerateUniqueProNOToOrder || isApplyProNoToOrdersOnSameLoad) {
                    //update load proNo
                    requestAwait(repository.updateLoadProNo(dataState.loadId, proNumber)).onSuccess {
                        //batch update order proNo request entries
                        val batchUpdateEntries = dataState.loadOrderList?.run {
                            if (isNeedGenerateUniqueProNOToOrder) {
                                sortedBy { orderEntry ->
                                    orderEntry.id?.filter { it.isDigit() }?.toLongOrNull()
                                }.mapIndexed { index, orderEntry ->
                                    OrderBatchUpdateEntry().apply {
                                        this.orderId = orderEntry.id
                                        this.update = OrderUpdateEntry().apply {
                                            this.proNo = if (count() > 1) "$proNumber-${index + 1}" else proNumber
                                        }
                                    }
                                }
                            } else {
                                filter { OrderTypeEntry.DS != it.orderType && it.proNo.isNullOrEmpty() }.map {
                                    OrderBatchUpdateEntry().apply {
                                        this.orderId = it.id
                                        this.update = OrderUpdateEntry().apply {
                                            this.proNo = proNumber
                                        }
                                    }
                                }
                            }
                        }
                        if (batchUpdateEntries.isNullOrEmpty() || requestAwait(repository.batchUpdateOrders(batchUpdateEntries)).onSuccess { loadLoadDetail() }.isSuccess) {
                            showToast(R.string.text_pro_no_submit_success)
                        }
                    }
                } else {
                    updateProNoByOrder(order.id, proNumber)
                }
            }
        } else {
            updateProNoByOrder(order.id, proNumber)
        }
    }

    private fun updateProNoByOrder(orderId: String, proNumber: String) {
        request(repository.updateProNoByOrder(orderId, proNumber)) {
            showToast(R.string.text_pro_no_submit_success)
            loadLoadDetail()
        }
    }

    fun addOrUpdatePalletPhotos(lpEntry: LpViewEntry, uploadPhotoBeanList: List<UploadPhotoBean>) {
        launch {
            awaitDataState()
            dataState.loadedPltList?.find { it.plt.id == lpEntry.id }?.let { palletWrapper ->
                uploadPhotoBeanList.filter { uploadPhotoBean ->
                    //photoBean is deleted so not updated to PalletPhotos
                    val palletPhotoExist =
                        palletWrapper.palletUploadPhotoBeans?.any { bean -> uploadPhotoBean.filePath == bean.filePath } ?: false
                    uploadPhotoBean.uploadStatus != UploadStatus.UPLOADED || (uploadPhotoBean.uploadStatus == UploadStatus.UPLOADED && palletPhotoExist)
                }.apply {
                    val newPhotoFileIds = palletWrapper.palletUploadPhotoBeans.getAvailablePhotoIds()
                        .addToNewList(filter { it.uploadStatus == UploadStatus.UPLOADED }.mapNotNull { it.photoServerId })?.distinct()
                    //do update pallet photos
                    if (!newPhotoFileIds.isNullOrEmpty() && !updatePalletPhotos(lpEntry, newPhotoFileIds)) {
                        forEach { it.uploadStatus = UploadStatus.PARENT_UPDATE_FAIL }
                    }
                }
            }?.let { newUploadPhotoBeanList ->
                val newLoadedPltList = dataState.loadedPltList?.toMutableList()?.apply {
                    indexOfFirst { it.plt.id == lpEntry.id }.let {
                        if (it < 0) return@apply
                        val oldPalletWrapper = this[it]
                        val newPalletUploadPhotoBeans = oldPalletWrapper.palletUploadPhotoBeans.mergeToNewList(newUploadPhotoBeanList)
                        val newPlt = oldPalletWrapper.plt.copy(photoIds = newPalletUploadPhotoBeans.filter { palletUploadPhotoBeans ->
                            palletUploadPhotoBeans.uploadStatus == UploadStatus.UPLOADED
                        }.getAvailablePhotoIds())
                        set(it, oldPalletWrapper.copy(plt = newPlt, palletUploadPhotoBeans = newPalletUploadPhotoBeans))
                    }
                }
                setDataState { copy(loadedPltList = newLoadedPltList) }
            }
        }
    }

    fun deletePalletPhotos(lpEntry: LpViewEntry, delPhotoList: List<UploadPhotoBean>?, isRemoveAll: Boolean) {
        launch {
            awaitDataState()
            dataState.loadedPltList?.find { it.plt.id == lpEntry.id }?.let {
                val lastPalletUploadPhotoBeans = it.palletUploadPhotoBeans?.filteredDeleteItems(delPhotoList, isRemoveAll)
                //request update pallet photos
                if (updatePalletPhotos(lpEntry, lastPalletUploadPhotoBeans.getAvailablePhotoIds())) {
                    return@let it.copy(palletUploadPhotoBeans = lastPalletUploadPhotoBeans)
                }
                it
            }?.let { palletWrapper ->
                val newLoadedPltList = dataState.loadedPltList?.toMutableList()?.apply {
                    indexOfFirst { it.plt.id == lpEntry.id }.let {
                        if (it < 0) return@apply
                        set(it, palletWrapper)
                    }
                }
                setDataState { copy(loadedPltList = newLoadedPltList) }
            }
        }
    }

    private suspend fun updatePalletPhotos(lpEntry: LpViewEntry, photoIds: List<String>?): Boolean =
        requestAwait(repository.updateTruckPhotos(UpdatePhotosRequestEntry(lpEntry.id, photoIds))).onSuccess {
            showToast(R.string.msg_update_success)
        }.isSuccess

    fun enterOrderWorkOperate(orderEntry: OrderEntry) {
        if (OrderStatusEntry.LOCKED == orderEntry.status) {
            showToast(ResUtil.format(R.string.msg_order_is_locked_please_load_by_load_sequence, orderEntry.id))
            return
        }
        if (isNeedValidateUcc(listOf(orderEntry.id), listOf(orderEntry.retailerId))) {
            setUiState { copy(pageProcess = OneShot(PageProcess.ScanUccDialogEnterOrderState(listOf(orderEntry.id), orderEntry))) }
        } else {
            enterOrderByCheckLpTemplate(orderEntry)
        }
    }

    fun enterOrderByCheckLpTemplate(orderEntry: OrderEntry) {
        val isCanStartLoadOrder = canStartLoadOrder(orderEntry)
        val previousUnCompleteOrder = findPreviousUnCompleteOrder(orderEntry)
        when {
            isCanStartLoadOrder || orderEntry.loadedQty == orderEntry.requestQty -> {
                if (dataState.customerEntry.isAllowAutoCc()) {
                    request(repository.orderLpTemplateCheck(orderEntry.id), error = {
                        if (ErrorCode.LP_CONFIGURATION_NOT_MATCH_WITH_ORDER_ITEMLINE == it.code) {
                            //do autoCc
                            request(repository.autoCc(orderId = orderEntry.id)) {
                                updateUiStateToEnterOrder(orderEntry)
                            }
                        } else {
                            showToast(it.errorMessage)
                        }
                    }, success = {
                        updateUiStateToEnterOrder(orderEntry)
                    })
                    return
                }
                updateUiStateToEnterOrder(orderEntry)
            }
            //allow override dn Load Sequence
            !isCanStartLoadOrder && previousUnCompleteOrder != null && dataState.userLevelConfig?.allowOverrideDNLoadSequence == true -> launch {
                val askContent = "${previousUnCompleteOrder.id} ${ResUtil.getString(R.string.hint_is_not_complete)}, ${
                    ResUtil.getString(R.string.hint_do_you_want_to_start)
                } ${orderEntry.id}?"
                if (awaitEvent { LoadTaskWorkUiEvent.AskOverrideDNSequence(askContent) } == true) {
                    updateUiStateToEnterOrder(orderEntry)
                }
            }
            else -> showToast(R.string.msg_please_operate_in_sequence)
        }
    }

    fun uploadTruckPhoto(fileDirectory: String, data: ByteArray?) {
        data ?: return
        launch {
            showLoading(true)
            withContext(Dispatchers.IO) {
                ImageUtils.savePicture(fileDirectory, data)
            }?.let {
                uploadTruckPhoto(it)
            }
            showLoading(false)
        }
    }

    fun uploadTruckPhoto(path: String) {
        launch {
            showLoading(true)
            requestAwait(
                repository.uploadFile(path),
                showLoading = false
            ).getOrNull()?.filesId?.run {
                if (isNotEmpty()) first() else null
            }?.let { fileId ->
                val newTruckPhotos = dataState.loadTaskEntry.truckPhotoIds.addToNewList(fileId)
                requestAwait(
                    repository.updateTruckPhotos(
                        UpdatePhotosRequestEntry(
                            dataState.taskId,
                            newTruckPhotos
                        )
                    ),
                    showLoading = false
                ).onSuccess {
                    showToast(R.string.msg_update_success)
                    updateTaskTruckPhotos(newTruckPhotos)
                }
            }
            showLoading(false)
        }
    }

    fun updateTaskTruckPhotos(photos: List<String>?) {
        dataState.loadTaskEntry.truckPhotoIds = photos
    }

    fun uploadOrderPhotos(orderEntry: OrderEntry, uploadPhotoBeanList: List<UploadPhotoBean>) {
        launch {
            awaitDataState()
            val tags = listOf(dataState.taskId, dataState.loadId, orderEntry.id)
            uploadPhotoBeanList.mapNotNull { uploadPhotoBean ->
                val fileEntryViewEntry = FileEntryViewEntry().apply {
                    this.tags = tags
                    this.fileType = FileSearchEntry.FILE_TYPE_PHOTO
                    if (uploadPhotoBean.uploadStatus == UploadStatus.UPLOADED) {
                        val photoBeanExist =
                            dataState.loadPhotoList?.any { uploadPhotoBean.filePath == it.uploadPhotoBean.filePath }
                                ?: false
                        //photoBean is deleted so not updated to OrderPhotos
                        if (!photoBeanExist) return@mapNotNull null
                        //do upload order photo
                        val fileType =
                            if (uploadPhotoBean.itemType == ItemType.PHOTO_DATA) FileSearchEntry.FILE_TYPE_PHOTO else FileSearchEntry.FILE_TYPE_VIDEO
                        requestAwait(
                            repository.uploadEntryPhoto(
                                uploadPhotoBean.photoServerId!!,
                                fileType,
                                tags
                            )
                        ).getOrNull()?.let {
                            this.id = it.id
                            this.fileId = it.fileId
                        } ?: let {
                            uploadPhotoBean.uploadStatus = UploadStatus.PARENT_UPDATE_FAIL
                        }
                    }
                }
                FileEntryViewEntryWrapper(fileEntryViewEntry, uploadPhotoBean)
            }.let { newFileEntryViewEntryWrapperList ->
                val newLoadPhotoList = dataState.loadPhotoList?.toMutableList()?.apply {
                    newFileEntryViewEntryWrapperList.forEach { newFileEntryViewEntryWrapper ->
                        val index = indexOfFirst { it.uploadPhotoBean.isSame(newFileEntryViewEntryWrapper.uploadPhotoBean) }
                        if (index < 0) {
                            add(newFileEntryViewEntryWrapper)
                        } else {
                            set(index, newFileEntryViewEntryWrapper)
                        }
                    }
                } ?: newFileEntryViewEntryWrapperList
                setDataStateAwait { copy(loadPhotoList = newLoadPhotoList) }
                if (uploadPhotoBeanList.any { it.uploadStatus == UploadStatus.UPLOADED }) {
                    updateLoadPhotos()
                }
            }
        }
    }

    fun deleteOrderPhotos(deletePhotoList: List<UploadPhotoBean>?) {
        deletePhotoList ?: return
        launch {
            awaitDataState()
            val deleteSucList = arrayListOf<UploadPhotoBean>()
            deletePhotoList.forEach { deletePhotoBean ->
                dataState.loadPhotoList?.find {
                    !it.fileEntryViewEntry.id.isNullOrEmpty() && it.fileEntryViewEntry.fileId == deletePhotoBean.photoServerId
                }?.apply { //do disable entry photo
                    requestAwait(repository.disableEntryPhotos(fileEntryViewEntry.id)).onSuccess { deleteSucList.add(deletePhotoBean) }
                } ?: let { deleteSucList.add(deletePhotoBean) }
            }
            val lastPhotoList = dataState.loadPhotoList?.filter { fileEntry ->
                !deleteSucList.any {
                    val isSameFilePath = !it.filePath.isNullOrEmpty() && fileEntry.uploadPhotoBean.filePath == it.filePath
                    isSameFilePath || it.photoServerId == fileEntry.uploadPhotoBean.photoServerId
                }
            }
            setDataStateAwait { copy(loadPhotoList = lastPhotoList) }
            updateLoadPhotos()
        }
    }

    private fun updateLoadPhotos() {
        request(repository.updateLoadPhotos(dataState.loadId,
            dataState.loadPhotoList?.filter { it.fileEntryViewEntry.fileId != null }?.map { it.fileEntryViewEntry.fileId }))
    }

    private fun updateUiStateToEnterOrder(orderEntry: OrderEntry) {
        setUiState {
            copy(pageProcess = OneShot(PageProcess.GoToLoadOrderWork(dataState.loadTaskEntry,
                dataState.stepEntry,
                dataState.loadDetailEntry.deepCopy()!!.apply {
                    orderList = dataState.loadOrderList
                },
                dataState.customerEntry,
                dataState.userLevelConfig,
                orderEntry)))
        }
    }

    fun orderListPageUndo() {
        (orderListPageUndoProgress.pop() as? OrderListPageAction.UpdateProNo)?.apply {
            updateProNo(orderEntry, inputProNo, true)
        }
    }

    fun loadWorkPageUndo() {
        if (dataState.workOnLoadLevelStatus == Load) {
            (loadModeUndoProgress.pop() as? LoadLevelWorkPageAction.LoadInputPallet)?.apply {
                doUnloadSlp(inputPallet, orderEntry)
            }
        } else {
            (unLoadModeUndoProgress.pop() as? LoadLevelWorkPageAction.UnLoadInputPallet)?.apply {
                setDataState { updatePltByUnloadMode(inputPallet = inputPallet, isUndo = true) }
            }
        }
    }

    @Throws
    private fun validateLoadOrUnloadSubmit() = dataState.apply {
        if (TaskStatusEntry.CLOSED == loadTaskEntry.status || TaskStatusEntry.FORCE_CLOSED == loadTaskEntry.status) {
            throw getString(R.string.text_task_closed).toException
        }
        if (stepEntry.status == StepStatusEntry.DONE) {
            throw getString(R.string.text_step_closed).toException
        }
    }

    private fun resetInputFocus() {
        setUiState { copy(resetInputFocus = OneShot(true)) }
    }

    //save verified order id list
    fun saveValidatedUccOrderId(idList: List<String>) {
        val newList: ArrayList<String> = arrayListOf()

        dataState.verifiedUccOrderIdList?.let { newList.addAll(it) }
        newList.addAll(idList)

        setDataState { copy(verifiedUccOrderIdList = newList.distinct()) }
    }

    //check need Validate Ucc
    private fun isNeedValidateUcc(loadOrderIds: List<String>?, retailerIds: List<String>?): Boolean = dataState.customerEntry?.run {
        if (!validateUCCAtLoading) return@run false

        val isVerified = dataState.verifiedUccOrderIdList?.any { loadOrderIds?.contains(it) == true } ?: false
        if (isVerified) return@run false

        return@run validateUCCAtLoadingRetailerIds?.any { retailerIds?.contains(it) == true }

    } ?: false

    //check order sequence
    private fun canStartLoadOrder(curOrder: OrderEntry?): Boolean = dataState.loadOrderList?.run {
        curOrder ?: return@run false

        if (!isAllHasSequence()) return@run true

        val index = indexOfFirst { it.id == curOrder.id }
        if (index <= 0) return@run true

        return@run subList(0, index).all { it.loadedQty == it.requestQty }
    } ?: false

    private fun findPreviousUnCompleteOrder(curOrder: OrderEntry): OrderEntry? = dataState.loadOrderList?.run {
        val index = indexOfFirst { it.id == curOrder.id }
        if (index > 0) {
            return@run subList(0, index).firstOrNull { it.loadedQty != it.requestQty }
        }
        return@run null
    }

    private fun CustomerViewEntry?.isAllowAutoCc(): Boolean = this?.allowAutoCc ?: false

    private fun List<OrderEntry>?.isAllHasSequence(): Boolean = if (this.isNullOrEmpty()) false else all { !it.sequence.isNullOrEmpty() }

    fun getDock(): LocationEntry? = dataState.loadTaskEntry.dock

    fun toPrintCountSheet(activity: Activity, facilityName: String?) {
        val printData = PrintData.basic(userId, facilityName, LabelSizeEntry.A4)
        if (!PrintUtil.hasPrinter(printData)) {
            showToast(R.string.msg_please_select_pdf_printer)
            activity.startActivity(Intent(activity, PrintSettingActivity::class.java))
            return
        }
        val viewDelegate = object : PrintView {
            override fun onPrinterNotSelect() {
                showToast(R.string.msg_please_select_pdf_printer)
            }

            override fun onPrintSuccess(data: PrintData, printerEntry: PrinterEntry) {
                showToast(R.string.print_success)
            }

            override fun onPrintFailed(response: ErrorResponse, printerEntry: PrinterEntry?) {
                showToast(response.errorMessage)
            }

            override fun showProgress(show: Boolean) {
                showLoading(show)
            }
        }

        request(repository.createCountSheet(dataState.loadId, dataState.loadTaskEntry.entryId)) {
            printData.jobData = PrintData.JobData.PDF(fileId = it?.fileId)
            PrintUtil.newInstance(viewDelegate).print(printData)
        }
    }

    fun reopenLoad() {
        request(repository.reopenLoad(dataState.taskId, dataState.stepEntry.id, dataState.loadId)) {
            showToast(R.string.text_reopen_success)
            loadLoadDetail()
        }
    }

    fun goToMbolListActivity(activity: Activity) {
        val intent = Intent()
        intent.putExtra(KEY_LOAD_ID, dataState.loadId)
        intent.setClass(activity, MbolListV1Activity::class.java)
        activity.startActivity(intent)
    }

    fun goToAddProNoActivity(activity: Activity) {
        val carrier = dataState.carrierList?.find { it.id == dataState.loadDetailEntry.carrierId }
        if (carrier?.isAutoCreateProNo == true) {
            showToast(getFormattedString(R.string.error_not_allow_create_pro_no, carrier.name))
            return
        }
        activity.startActivity(Intent(activity, AddProNoActivity::class.java).apply {
            putExtra(AddProNoActivity.TASK_ID, dataState.taskId)
            putExtra(LoadDetailEntry.TAG, dataState.loadDetailEntry)
            putExtra("hintTxt", getString(R.string.hint_comfirm_load_complete))
            putExtra(LoadOrderListActivity.LOAD_ORDERS, dataState.loadViewEntry?.orderEntries as Serializable)
        })
    }

    fun goToMaterialCenterActivity(activity: Activity) {
        activity.startActivity(Intent(activity, MaterialCenterActivity::class.java).apply {
            val customerIds: ArrayList<String> = arrayListOf()
            dataState.loadTaskEntry.loadList?.map { it.customerId }?.distinct()?.let {
                customerIds.addAll(it)
            }
            putExtra(GeneralTaskViewEntry.TAG, dataState.loadTaskEntry).putStringArrayListExtra(MaterialCenterActivity.CUSTOMER_IDS,
                customerIds)
        })
    }

    fun goToCountingSheetActivity(activity: Activity) {
        activity.startActivity(Intent(activity, AddSealOrCountingSheetActivity::class.java).apply {
            putExtra("id", dataState.taskId)
            putExtra(AddSealOrCountingSheetActivity.MODE, AddSealOrCountingSheetActivity.COUNTING_SHEET_MODE)
            putExtra(TaskDetailActivity.COMPANY_ID, dataState.loadDetailEntry.companyId)
            putExtra(AddSealOrCountingSheetActivity.ENTRY_ID, dataState.loadDetailEntry.entryId)
            putExtra(AddSealOrCountingSheetActivity.LOAD_ID, dataState.loadDetailEntry.id)
        })
    }

    fun getAndOpenHelpPage(context: Context?, helpPageKey: String?, facilityId: String?) {
        functionHelpPresenterImpl.getAndOpenHelpPage(context, helpPageKey, "", facilityId)
    }

}

private fun isFormDirectLoad(intentFlag: Int) = Constant.FLAG_FROM_DIRECT_LOAD == intentFlag

/**
 * Report Step process time for Direct Load.
 * For normal Load, it will be handled by  [com.lt.linc.load_v1.detail.LoadTaskDetailViewModel].
 */
private fun shouldReportStepProcessTime(intentFlag: Int) = isFormDirectLoad(intentFlag)

fun CustomerViewEntry?.isRequireTakePhotoForEachPalletInLoading(): Boolean = this?.requireTakePhotoForEachPalletInLoading ?: false

class LoadWorkRepo : BaseRepository() {

    private val idmApi by apiServiceLazy<IdmApi>()
    private val loadTaskApi by apiServiceLazy<LoadTaskApi>()
    private val customerApi by apiServiceLazy<CustomerApi>()
    private val loadWorkApi by apiServiceLazy<LoadWorkApi>()
    private val lpApi by apiServiceLazy<LpApi>()
    private val packTaskApi by apiServiceLazy<PackTaskApi>()
    private val orderApi by apiServiceLazy<OrderApi>()
    private val fileEntryApi by apiServiceLazy<FileEntryApi>()
    private val configurationApi by apiServiceLazy<ConfigurationApi>()
    private val fileRepository by lazy { FileRepository(paramModule = "load") }
    private val carrierApi by apiServiceLazy<CarrierApi>()

    fun getUserLevelControl(stepId: String, customerId: String?) =
        rxRequest(idmApi.getUserLevelControl(stepId, customerId)).mapToUserLevel<LoadTaskUserLevelConfig>()

    fun loadLoadDetail(loadId: String) = rxRequest(loadTaskApi.getLoad(loadId))

    fun getOrderListByLoad(loadId: String) = rxRequest(loadTaskApi.getOrderListByLoad(loadId))

    fun getCustomer(customerId: String?) = rxRequest(customerApi.getCustomer(customerId))

    fun doAutoCcByOrders(orderIds: List<String>?) =
        rxRequest(loadWorkApi.doAutoCcByOrders(orderIds))

    fun loadSlp(stepId: String, requestEntry: LoadingRequestEntry) = rxRequest(loadWorkApi.loadSlp(stepId, requestEntry))

    fun unloadSlp(stepId: String, requestEntry: LoadingRequestEntry) = rxRequest(loadWorkApi.unLoadSlp(stepId, requestEntry))

    fun batchUnLoadSlp(stepId: String, requests: List<LoadingRequestEntry>?) = rxRequest(loadWorkApi.batchUnLoadSlp(stepId, requests))

    fun batchLoadSlp(stepId: String, requests: List<LoadingRequestEntry>?) = rxRequest(loadWorkApi.batchLoadSlp(stepId, requests))

    fun completeLoad(taskId: String, loadId: String) = rxRequest(loadWorkApi.completeLoad(taskId, loadId, OrderProNoUpdate()))

    fun forceCompleteLoad(taskId: String, loadId: String) = rxRequest(loadWorkApi.forceCompleteLoad(taskId, loadId, OrderProNoUpdate()))

    fun createCountSheet(loadId: String, entryId: String?) = rxRequest(loadWorkApi.createCountSheet(loadId, entryId))

    fun checkLoadCountingSheet(loadId: String) = rxRequest(loadWorkApi.getBamLoad(loadId))

    fun updateTruckPhotos(requestEntry: UpdatePhotosRequestEntry) = rxRequest(loadWorkApi.updatePhotos(requestEntry))

    fun getWithoutCollectMaterialOrderId(loadId: String) = rxRequest(loadWorkApi.getWithoutCollectMaterialOrderId(loadId))

    fun updateLoadPhotos(loadId: String, photos: List<String>?) =
        rxRequest(loadWorkApi.uploadLoadPhotos(loadId, LoadPhotoEntry().apply { this.photos = photos }))

    fun reopenLoad(taskId: String, stepId: String, loadId: String) = rxRequest(loadTaskApi.reopenLoad(taskId, stepId, loadId))

    fun updateLoadProNo(loadId: String, proNumber: String) =
        rxRequest(loadWorkApi.updateLoad(loadId, LoadUpdateEntry().apply { proNo = proNumber }))

    fun lpAutoComplete(slp: String) = rxRequest(lpApi.idAutoComplete(listOf(slp)))

    fun updateLp(slp: String, map: Map<String, Any>) = rxRequest(lpApi.updateLp(slp, map))

    fun updateLpBatch(updateEntry: LPBatchUpdateEntry) = rxRequest(lpApi.updateLpBatch(updateEntry))

    fun orderLpTemplateCheck(orderId: String) = rxRequest(packTaskApi.orderLpTemplateCheck(orderId))

    fun autoCc(orderId: String) = rxRequest(packTaskApi.autoCc(orderId))

    fun updateProNoByOrder(orderId: String, proNumber: String) =
        rxRequest(orderApi.update(orderId, OrderUpdateEntry().apply { proNo = proNumber }))

    fun batchUpdateOrders(batchEntries: List<OrderBatchUpdateEntry>) =
        rxRequest(orderApi.batchUpdate(batchEntries))

    fun searchEntryPhoto(tags: List<String>) =
        rxRequest(fileEntryApi.search(FileSearchEntry().apply {
            this.ids = null
            this.fileTags = null
            this.status = FileSearchEntry.FILE_STATUS_ACTIVE
            this.fileCategory = FileCategory.LOAD
            this.tags = tags
        }))

    fun uploadEntryPhoto(photoId: String, fileType: String, tags: List<String>) =
        rxRequest(fileEntryApi.createV1(FileCreateEntry().apply {
            this.fileId = photoId
//        this.fileType = FileSearchEntry.FILE_TYPE_PHOTO
            this.fileType = fileType
            this.fileScenario = FileScenario.OTHER
            this.fileCategory = FileCategory.LOAD
            this.tags = tags
        }))

    fun disableEntryPhotos(photoId: String) = rxRequest(fileEntryApi.disable(photoId))

    fun uploadFile(path: String) = rxRequest(fileRepository.uploadFile(File(path)))

    fun searchConfiguration(customerId: String?, tableName: String) =
        rxRequest(configurationApi.search(ConfigurationMapSearchEntry().apply {
            this.customerId = customerId
            this.tableName = tableName
        }))

    fun searchCarrier(search: CarrierSearchEntry) = rxRequest(carrierApi.search(search))
}