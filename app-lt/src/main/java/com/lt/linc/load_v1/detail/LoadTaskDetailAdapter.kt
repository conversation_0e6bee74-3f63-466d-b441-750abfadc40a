package com.lt.linc.load_v1.detail

import android.annotation.SuppressLint
import com.customer.widget.takecamerafile.TakeCameraFileTag
import com.linc.platform.load.model.LoadDetailEntry
import com.linc.platform.load.model.LoadStatusEntry
import com.linc.platform.utils.ResUtil
import com.lt.linc.R
import com.lt.linc.common.extensions.setGone
import com.lt.linc.common.extensions.setVisible
import com.lt.linc.common.extensions.setVisibleOrGone
import com.lt.linc.common.mvvm.kotlin.BaseBindingDifferQuickAdapter
import com.lt.linc.common.mvvm.kotlin.BaseBindingViewHolder
import com.lt.linc.databinding.ItemLoadTaskDetailV1Binding
import com.lt.linc.util.v1widget.*

/**
 * <AUTHOR>
 * @Date 2022/6/22
 */
class LoadTaskDetailAdapter(
    private val loadSummaryClick: (LoadDetailEntry) -> Unit,
    private val startLoadClick: (LoadDetailEntry) -> Unit,
    private val addProNumberSubmit: (LoadDetailEntry, String) -> Unit,
    private val addOrUpdateCountSheetPhotos: (LoadDetailEntry, List<UploadPhotoBean>) -> Unit,
    private val deleteCountSheetPhotos: (LoadDetailEntry, List<UploadPhotoBean>?, Boolean) -> Unit,
    private val addSealClick: (List<String>?, String?) -> Unit,
    private val addOrUpdateSealPhotos: (List<UploadPhotoBean>, String?) -> Unit,
    private val deleteSealPhotos: (List<UploadPhotoBean>?, String?, Boolean) -> Unit,
    private val addOrUpdateTruckPhotos: (List<UploadPhotoBean>) -> Unit,
    private val deleteTruckPhotos: (List<UploadPhotoBean>?, Boolean) -> Unit,
    private val addTrailerNoClick: () -> Unit,
    private val completeTaskClick: () -> Unit,
    private val bolListClick: () -> Unit
) : BaseBindingDifferQuickAdapter<LoadTaskDetailWrapper, ItemLoadTaskDetailV1Binding>() {

    @SuppressLint("SetTextI18n")
    override fun convert(
        helper: BaseBindingViewHolder<ItemLoadTaskDetailV1Binding>?,
        item: LoadTaskDetailWrapper
    ) {
        helper?.binding?.apply {
            when (item.itemType) {
                LoadTaskDetailItemType.AddTrailerNo -> {
                    tvOrderCount.setGone()
                    flLoadInfoOutline.setGone()
                    llLoadDetail.setGone()
                    llLoadAddPhotos.setGone()
                    llLoadAddTrailerNo.setVisible()
                    loadTitleLl.setOnClickListener(null)
                    loadTitleLl.setBackgroundResource(R.drawable.rect_393939_top_4)
                    tvAddTrailerNoLoadNumber.text =
                        mData?.filter { it.loadDetail != null }?.map { it.loadDetail!!.id }?.joinToString { s -> s }
                    tvLoadId.text = ResUtil.getString(R.string.title_add_ctnr_trailer_no)
                    addTrailerNoBtn.setOnClickListener {
                        addTrailerNoClick.invoke()
                    }
                }
                LoadTaskDetailItemType.AddPhotos -> {
                    tvOrderCount.setGone()
                    flLoadInfoOutline.setGone()
                    llLoadDetail.setGone()
                    llLoadAddTrailerNo.setGone()
                    llLoadAddPhotos.setVisible()
                    loadTitleLl.setOnClickListener(null)
                    loadTitleLl.setBackgroundResource(R.drawable.rect_393939_top_4)
                    tvLoadId.text = ResUtil.getString(R.string.add_photos_required)
                    tvLabelTruckPhoto.text =
                        ResUtil.getString(if (item.allowTakeVideo) R.string.upload_photo_or_video_of_truck else R.string.upload_photo_of_truck)
                            .toSpanned()
                    uploadPhotoTruck.apply {
                        setVideoParam(
                            item.dn,
                            item.maxTaskVideoLengthInSeconds,
                            item.allowTakeVideo
                        )
                        bindTags(TakeCameraFileTag.Truck.name, TakeCameraFileTag.loadTaskTags)
                        initLoadParamInfo()
                        initPhotoListByAdapter(
                            item.uploadTruckPhotos,
                            LoadTaskDetailItemType.AddPhotos
                        )
                        setUploadPhotoByAdapterCallBack(object : UploadPhotoByAdapterCallBack {
                            override fun addOrUpdatePhotos(
                                photoBeanList: List<UploadPhotoBean>,
                                parentItemKey: Any?
                            ) {
                                addOrUpdateTruckPhotos.invoke(photoBeanList)
                            }

                            override fun deletePhotos(
                                photos: List<UploadPhotoBean>?,
                                isRemoveAll: Boolean,
                                parentItemKey: Any?
                            ) {
                                deleteTruckPhotos.invoke(photos, isRemoveAll)
                            }
                        })
                    }
                    tvLabelSealPhoto.text =
                        (if (item.allowTakeVideo) {
                            ResUtil.getString(if (item.isRequireSealNo) R.string.upload_photo_or_video_of_seal_require else R.string.upload_photo_or_video_of_seal)
                        } else {
                            ResUtil.getString(if (item.isRequireSealNo) R.string.upload_photo_of_seal_require else R.string.upload_photo_of_seal)
                        }).toSpanned()
                    uploadPhotoSeal.apply {
                        setVideoParam(
                            item.dn,
                            item.maxTaskVideoLengthInSeconds,
                            item.allowTakeVideo
                        )
                        bindTags(TakeCameraFileTag.Seal.name, TakeCameraFileTag.loadTaskTags)
                        initLoadParamInfo()
                        initPhotoListByAdapter(item.uploadSealPhotos, LoadTaskDetailItemType.AddPhotos)
                        setUploadPhotoByAdapterCallBack(object : UploadPhotoByAdapterCallBack {
                            override fun addOrUpdatePhotos(photoBeanList: List<UploadPhotoBean>, parentItemKey: Any?) {
                                addOrUpdateSealPhotos.invoke(photoBeanList, addSealEt.text.toString().trim())
                            }

                            override fun deletePhotos(photos: List<UploadPhotoBean>?, isRemoveAll: Boolean, parentItemKey: Any?) {
                                deleteSealPhotos.invoke(photos, addSealEt.text.toString().trim(), isRemoveAll)
                            }
                        })
                    }
                    tvLabelSealNumber.text = ResUtil.getString(if (item.isRequireSealNo) R.string.text_seal_number_require
                    else R.string.text_seal_number).toSpanned()
                    addSealEt.setText(item.sealNo)
                    addSealEt.setOnEditorActionListener { _, _, _ ->
                        addSealEt.text.toString().trim().let {
                            if (it.isNotEmpty()) {
                                addSealClick.invoke(uploadPhotoSeal.getAvailablePhotoIds(), it)
                            }
                        }
                        false
                    }
                    completeTaskBtn.setOnClickListener {
                        completeTaskClick.invoke()
                    }
                    bolListBtn.setOnClickListener {
                        bolListClick.invoke()
                    }
                }
                else -> {
                    item.loadDetail?.apply {
                        llLoadAddPhotos.setGone()
                        llLoadAddTrailerNo.setGone()
                        flLoadInfoOutline.setVisible()
                        tvLoadId.text = id

                        if (isLoadCompleted) {
                            llLoadDetail.setGone()
                            tvOrderCount.setGone()
                            tvStateComplete.setVisible()
                            loadSummaryIv.setGone()
                            tvOrderCount.setGone()
                            loadTitleLl.setBackgroundResource(R.drawable.rect_393939_r4)
                            loadTitleLl.setOnClickListener {
                                startLoadClick.invoke(this)
                            }
                        } else {
                            llLoadDetail.setVisible()
                            tvOrderCount.setVisible()
                            tvStateComplete.setGone()
                            loadSummaryIv.setVisible()
                            tvOrderCount.setVisible()
                            loadTitleLl.setBackgroundResource(R.drawable.rect_525252_top_4)
                            loadTitleLl.setOnClickListener(null)
                            //check show add pro number
                            val needProNo = isNeedProNo || item.isNeedProNo
                            llAddProNumber.apply {
                                if (needProNo) {
                                    tvLabelAddProNumber.text = ResUtil.getString(R.string.add_pro_number_require).toSpanned()
                                    setVisible()
                                } else setGone()
                            }
                            vLineAddProNo.apply { if (needProNo) setVisible() else setGone() }
                            scanProNoScanner.setScanReset(false)
                            scanProNoScanner.setInputRestriction(!item.isAllowManualProNumber)
                            scanProNoScanner.text = item.loadDetail.proNo
                            scanProNoScanner.setScanEvent { _, data ->
                                scanProNoScanner.text = data
                                addProNumberSubmit.invoke(this, data)
                            }
                            proNoTv.text = item.loadDetail.proNo
                            if (!item.isAllowUpdateProNo) {
                                tvLabelAddProNumber.text = ResUtil.getString(R.string.text_pro_no)
                                scanProNoScanner.setVisibleOrGone(false)
                                proNoTv.setVisibleOrGone(true)
                            } else {
                                tvLabelAddProNumber.text = ResUtil.getString(R.string.add_pro_number_require).toSpanned()
                                scanProNoScanner.setVisibleOrGone(true)
                                proNoTv.setVisibleOrGone(false)
                            }

                            //check show count sheet
                            tvLabelCountSheetPhoto.text =
                                (if (item.allowTakeVideo) {
                                    ResUtil.getString(if (item.needCountingSheet) R.string.upload_photo_or_video_of_count_sheet_require else R.string.upload_photo_or_video_of_count_sheet)
                                } else {
                                    ResUtil.getString(if (item.needCountingSheet) R.string.upload_photo_of_count_sheet_require else R.string.upload_photo_of_count_sheet)
                                })
                                    .toSpanned()
                            uploadPhotoCountSheet.apply {
                                setVideoParam(
                                    item.dn,
                                    item.maxTaskVideoLengthInSeconds,
                                    item.allowTakeVideo
                                )
                                initLoadParamInfo()
                                initPhotoListByAdapter(item.uploadCountSheetPhotos, item.loadDetail.id)
                                setUploadPhotoByAdapterCallBack(object : UploadPhotoByAdapterCallBack {
                                    override fun addOrUpdatePhotos(photoBeanList: List<UploadPhotoBean>, parentItemKey: Any?) {
                                        (parentItemKey as? String)?.let { loadId ->
                                            data.find { it.loadDetail?.id == loadId }?.let { loadWrapper ->
                                                addOrUpdateCountSheetPhotos.invoke(loadWrapper.loadDetail!!, photoBeanList)
                                            }
                                        }
                                    }

                                    override fun deletePhotos(
                                        photos: List<UploadPhotoBean>?, isRemoveAll: Boolean, parentItemKey: Any?) {
                                        (parentItemKey as? String)?.let { loadId ->
                                            data.find { it.loadDetail?.id == loadId }?.let { loadWrapper ->
                                                deleteCountSheetPhotos.invoke(loadWrapper.loadDetail!!, photos, isRemoveAll)
                                            }
                                        }
                                    }
                                })
                            }
                            loadSummaryIv.setOnClickListener {
                                loadSummaryClick.invoke(this)
                            }
                            startLoadBtn.setOnClickListener {
                                startLoadClick.invoke(this)
                            }
                            startLoadBtn.text =
                                ResUtil.getString(if (LoadStatusEntry.LOADING == status) R.string.btn_continue else R.string.btn_start)

                            tvLoadNumber.text = loadNo
                            tvLoadSequence.text = sequence?.toString()
                            tvLoadType.text = type
                            tvCreateBy.text = createdBy

                            val loadedPltCount = orderList?.sumOf { it.orderLps?.count { lpViewEntry -> lpViewEntry.isLoaded() } ?: 0 } ?: 0
                            val loadedStr = if (loadedPltCount > 0) "${loadedPltCount}/" else ""
                            val pltCount = orderList?.sumOf { it.orderLps?.count() ?: 0 } ?: 0
                            val orderCount = orderList?.count() ?: 0
                            tvOrderCount.text =
                                "$loadedStr$pltCount ${ResUtil.getString(R.string.label_plt)} | $orderCount ${ResUtil.getString(R.string.text_orders)}"
                        }
                    }
                }
            }
        }
    }

    override fun areItemsTheSame(oldItem: LoadTaskDetailWrapper, newItem: LoadTaskDetailWrapper) =
        oldItem.loadDetail == newItem.loadDetail && oldItem.itemType == newItem.itemType

    override fun areContentsTheSame(oldItem: LoadTaskDetailWrapper, newItem: LoadTaskDetailWrapper) =
        oldItem.needCountingSheet == newItem.needCountingSheet && oldItem.isNeedProNo == newItem.isNeedProNo && oldItem.loadDetail?.isNeedProNo == newItem.loadDetail?.isNeedProNo
}

sealed interface LoadTaskDetailItemType {
    object Default : LoadTaskDetailItemType
    object AddPhotos : LoadTaskDetailItemType
    object AddTrailerNo : LoadTaskDetailItemType
}

data class LoadTaskDetailWrapper(
    val itemType: LoadTaskDetailItemType = LoadTaskDetailItemType.Default,
    val loadDetail: LoadDetailEntry? = null,
    val needCountingSheet: Boolean = false,
    val uploadCountSheetPhotos: List<UploadPhotoBean>? = null,
    val isNeedProNo: Boolean = false,
    val isAllowManualProNumber: Boolean = false,
    val isAllowUpdateProNo: Boolean = false,
    val uploadTruckPhotos: List<UploadPhotoBean>? = null,
    val uploadSealPhotos: List<UploadPhotoBean>? = null,
    val sealNo: String? = null,
    val isRequireSealNo: Boolean = false,
    val dn: String? = null,
    val maxTaskVideoLengthInSeconds: Int? = null,
    val allowTakeVideo: Boolean = false,
)
