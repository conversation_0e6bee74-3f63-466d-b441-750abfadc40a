package com.lt.linc.load_v1.detail

import com.linc.platform.common.step.StepBaseEntry
import com.linc.platform.common.step.StepStatusEntry
import com.linc.platform.common.step.StepTypeEntry
import com.linc.platform.foundation.model.CustomerViewEntry
import com.linc.platform.foundation.model.organization.common.carrier.CarrierEntry
import com.linc.platform.foundation.model.questionnaire.CustomerQuestionnaireEntry
import com.linc.platform.generaltask.model.GeneralTaskViewEntry
import com.linc.platform.load.model.LoadDetailEntry
import com.linc.platform.load.model.LoadTaskViewEntry
import com.lt.linc.common.mvi.DeferredUiEvent
import com.lt.linc.common.mvi.ReactiveDataState
import com.lt.linc.common.mvi.ReactiveUiState
import com.lt.linc.common.mvi.UiEvent
import com.lt.linc.common.mvvm.kotlin.OneShot
import com.lt.linc.load_v1.config.LoadTaskUserLevelConfig
import com.lt.linc.util.v1widget.UploadPhotoBean

/**
 * <AUTHOR>
 * @Date 2022/6/22
 */
data class LoadTaskDetailState(
    val userId: String,
    val taskEntry: LoadTaskViewEntry,
    val stepEntry: StepBaseEntry,
    val userLevelConfig: LoadTaskUserLevelConfig? = null,
    val customerEntry: CustomerViewEntry? = null,
    val isNeedManualCcMsg: String? = null,
    val loadListCompose: LoadListCompose? = null,
    val completeAskStartStep: Boolean = true,
    val carrierList: List<CarrierEntry>? = null
) : ReactiveDataState {

    val taskId: String = taskEntry.id

    val stepName: String = stepEntry.name

    val stepType: StepTypeEntry = stepEntry.type

    val stepStatus: StepStatusEntry = stepEntry.status

    val isOwner: Boolean = stepEntry.isOwner(userId)

    data class LoadListCompose(
        val loadViewWrappers: List<LoadViewWrapper>? = null,
        val uploadTruckPhotos: List<UploadPhotoBean>? = null,
        val uploadSealPhotos: List<UploadPhotoBean>? = null,
        val sealNo: String? = null)

    data class LoadViewWrapper(val loadDetail: LoadDetailEntry, val uploadCountSheetPhotos: List<UploadPhotoBean>? = null)
}


data class LoadTaskDetailUiState(
    val taskId: String? = null,
    val refreshStatus: RefreshStatus = RefreshStatus.None,
    val loadListForShowing: List<LoadTaskDetailWrapper> = listOf(),
    val pageProcess: OneShot<PageProcess>? = null,
    val stepOperateButtonState: OneShot<StepOperateButtonState>? = null) : ReactiveUiState {

    sealed interface RefreshStatus {
        object None : RefreshStatus
        object Refreshing : RefreshStatus
        object RefreshComplete : RefreshStatus
    }

    sealed interface PageProcess {
        object Finish : PageProcess

        //show Force Close Confirm
        class ForceCloseConfirm(val error: String) : PageProcess

        object RefreshTask : PageProcess
    }

    data class StepOperateButtonState(val text: String, val isVisible: Boolean)
}


interface LoadTaskDetailUiEvent {

    object AskCloseTask : DeferredUiEvent<Boolean?>

    sealed interface ProcessEvent : UiEvent {

        object CloseTaskFail : ProcessEvent

        object StartToAddSeal : ProcessEvent

        class StartAnswerQuestionnaire(
            val taskViewEntry: GeneralTaskViewEntry,
            val customerQuestionnaire: CustomerQuestionnaireEntry
        ) : ProcessEvent

        object ShowAssetAssignedActivity : ProcessEvent

        data class ShowErrorMessageDialog(val error: String): ProcessEvent
    }

}