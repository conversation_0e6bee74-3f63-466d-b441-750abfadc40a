package com.lt.linc.load_v1.work

import com.linc.platform.common.step.StepBaseEntry
import com.linc.platform.foundation.model.CustomerViewEntry
import com.linc.platform.foundation.model.OrderEntry
import com.linc.platform.foundation.model.OrderStatusEntry
import com.linc.platform.foundation.model.organization.common.carrier.CarrierEntry
import com.linc.platform.load.model.LoadDetailEntry
import com.linc.platform.load.model.LoadStatusEntry
import com.linc.platform.load.model.LoadTaskViewEntry
import com.linc.platform.load.model.LoadViewEntry
import com.lt.linc.common.mvi.DeferredUiEvent
import com.lt.linc.common.mvi.ReactiveDataState
import com.lt.linc.common.mvi.ReactiveUiState
import com.lt.linc.common.mvvm.kotlin.OneShot
import com.lt.linc.load_v1.config.LoadTaskUserLevelConfig
import kotlin.time.Duration

/**
 * <AUTHOR>
 * @Date 2022/6/23
 */
data class LoadWorkState(
    val loadTaskEntry: LoadTaskViewEntry,
    val stepEntry: StepBaseEntry,
    val loadDetailEntry: LoadDetailEntry,
    val userLevelConfig: LoadTaskUserLevelConfig? = null,
    val customerEntry: CustomerViewEntry? = null,
    val loadViewEntry: LoadViewEntry? = null,
    //load order list
    val loadOrderList: List<OrderEntry>? = null,
    //load photos
    val loadPhotoList: List<FileEntryViewEntryWrapper>? = null,
    //loaded lp list
    val loadedPltList: List<LoadWorkPalletWrapper>? = null,
    val unLoadedPltList: List<LoadWorkPalletWrapper>? = null,
    //pageWorkStatus : load all orders  off or no
    val pageWorkStatus: LoadWorkUiState.PageWorkStatus = LoadWorkUiState.PageWorkStatus.OnOrderList,
    //workOnLoadLevelStatus : load or unload
    val workOnLoadLevelStatus: LoadWorkUiState.WorkOnLoadLevelStatus = LoadWorkUiState.WorkOnLoadLevelStatus.Load,
    //verified Ucc order id List
    val verifiedUccOrderIdList: List<String>? = null,
    //recycler view select position
    val lastHandlerLpId: String? = null,
    //carrier list
    val carrierList: List<CarrierEntry>? = null) : ReactiveDataState {

    val loadId: String = loadDetailEntry.id

    val taskId: String = loadTaskEntry.id

    val dockName: String = loadTaskEntry.dock?.name ?: ""

    //loaded plt amount by load
    val loadedPltByLoad: Int = loadedPltList?.count() ?: 0

    //total plt amount by load
    val totalPltByLoad: Int = loadOrderList?.sumOf { orderEntry -> orderEntry.requestQty } ?: 0

    //lp load status
    val isAllPltLoadByLoad = loadedPltByLoad != 0 && loadedPltByLoad == totalPltByLoad

    //complete order amount
    val loadedOrderAmount: Int =
        loadOrderList?.count { order -> order.status == OrderStatusEntry.SHIPPED || order.status == OrderStatusEntry.LOADED } ?: 0

    val totalOrderByLoad = loadOrderList?.count() ?: 0

    //load status
    private val loadStatus: LoadStatusEntry = loadViewEntry?.status ?: loadDetailEntry.status
    val isLoadCompleted: Boolean = LoadStatusEntry.LOADED == loadStatus || LoadStatusEntry.SHIPPED == loadStatus

    //loaded plt amount by task
    val loadedPltByOtherLoadsByTask: Int = loadTaskEntry.loadList?.filter { it.id != loadId }
        ?.sumOf { it.orderList?.sumOf { orderEntry -> orderEntry.orderLps?.count { lp -> lp.isLoaded() } ?: 0 } ?: 0 } ?: 0

    //total plt amount by task
    val totalPltByTask: Int =
        loadTaskEntry.loadList?.sumOf { it.orderList?.sumOf { orderEntry -> orderEntry.orderLps?.count() ?: 0 } ?: 0 } ?: 0

    //loaded load amount by task
    val loadedLoadByTask = loadTaskEntry.loadList?.count { it.isLoadCompleted } ?: 0

    //total load amount by task
    val totalLoadByTask = loadTaskEntry.loadList?.count() ?: 0

    fun updatePltByUnloadMode(inputPallet: String?, isUndo: Boolean): LoadWorkState {
        val slpIndex = loadedPltList?.indexOfFirst { entry -> entry.plt.id == inputPallet } ?: -1
        if (slpIndex >= 0) {
            return this.copy(loadedPltList = loadedPltList?.toMutableList()?.apply {
                set(slpIndex, this[slpIndex].copy(isScannedWaitToUnload = !isUndo))
            })
        }
        return this
    }
}

data class LoadWorkUiState(
    val loadId: String? = null, val dockName: String? = null, val palletInputScanAllowManualInput: Boolean = false,
    //loadAll Button Visible by user leve config
    val loadAllButtonVisible: Boolean = false,
    //update loaded plt progress by load
    val loadedPltProgressByLoadEvent: WorkProgress = WorkProgress(),
    //update loaded order progress by load
    val loadedOrderProgressByLoadEvent: WorkProgress = WorkProgress(),
    //update loaded plt progress by task
    val loadedPltProgressByTaskEvent: WorkProgress = WorkProgress(),
    //update loaded load progress by task
    val loadedLoadProgressByTaskEvent: WorkProgress = WorkProgress(), val takePhotoToLoadBtnVisible: Boolean = false,
    //load all orders switch visible or gone
    val pageWorkStatusSwitchVisible: Boolean = false,
    //work on load  or show order list
    val pageWorkStatusSwitch: OneShot<PageWorkStatus>? = null,
    //update page status switch
    val workOnLoadLevelStatusSwitch: WorkOnLoadLevelStatusSwitch = WorkOnLoadLevelStatusSwitch(isEnable = false),
    //order List for Showing
    val orderListForShowing: OneShot<List<OrderEntryWrapper>?>? = null,
    //plt List for Showing
    val pltListForShowing: OneShot<List<LoadWorkPalletWrapper>?>? = null,
    //plt List Select Position
    val pltListSelectPosition: OneShot<Int>? = null,
    //button complete or update enable
    val completeBtnEnable: Boolean = false,
    //on load level undo button visible or gone
    val loadLevelUndoButtonVisible: Boolean = false,
    //reset input scan focus
    val resetInputFocus: OneShot<Boolean>? = null,
    //show do autoCc fail dialog
    val showAutoCCErrorDialog: OneShot<String>? = null,
    //show bind sscc fail dialog
    val showBindSsccErrorDialog: OneShot<String>? = null,
    // Step process duration
    val processDuration: Duration? = null,
    //page process
    val pageProcess: OneShot<PageProcess>? = null) : ReactiveUiState {

    sealed interface PageWorkStatus {

        object OnLoadLevel : PageWorkStatus

        object OnOrderList : PageWorkStatus
    }

    sealed interface WorkOnLoadLevelStatus {
        object Load : WorkOnLoadLevelStatus
        object UnLoad : WorkOnLoadLevelStatus
    }

    sealed interface PageProcess {
        object Finish : PageProcess

        //show scan ucc dialog
        class ScanUccDialogSubmitLpState(val idList: List<String>, val slp: String) : PageProcess

        //show scan ucc dialog
        class ScanUccDialogEnterOrderState(val idList: List<String>, val orderEntry: OrderEntry) : PageProcess

        //show add pallet dialog
        class AddPalletDialogState(val palletTypeIds: List<String>?, val slp: String, val orderEntry: OrderEntry) : PageProcess

        object GoToAddSeal : PageProcess

        class GoToLoadOrderWork(
            val loadTaskViewEntry: LoadTaskViewEntry,
            val stepBaseEntry: StepBaseEntry,
            val loadDetailEntry: LoadDetailEntry,
            val customerEntry: CustomerViewEntry?,
            val userLevelConfig: LoadTaskUserLevelConfig?,
            val orderEntry: OrderEntry) : PageProcess

        class GoToAddMaterial(val orderEntry: OrderEntry) : PageProcess

        sealed interface CompleteErrorStateDialog : PageProcess

        class NeedAddMaterialDialog(val error: String) : CompleteErrorStateDialog

        class StartLoadTaskDetailActivity(val loadTaskViewEntry: LoadTaskViewEntry, val stepBaseEntry: StepBaseEntry, val intentFlag: Int) :
            PageProcess
    }

    data class WorkProgress(val loadedProgress: Int = 0, val totalProgress: Int = 0)

    data class WorkOnLoadLevelStatusSwitch(
        val isEnable: Boolean = true, val workOnLoadLevelStatus: WorkOnLoadLevelStatus = WorkOnLoadLevelStatus.Load)
}


interface LoadTaskWorkUiEvent {

    data class AskOverrideDNSequence(val askContent: String) : DeferredUiEvent<Boolean?>
}