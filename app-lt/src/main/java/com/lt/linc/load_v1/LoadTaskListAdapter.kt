package com.lt.linc.load_v1

import android.annotation.SuppressLint
import androidx.core.content.ContextCompat
import com.linc.platform.common.task.PriorityEntry
import com.linc.platform.load.model.LoadTaskViewEntry
import com.linc.platform.utils.ResUtil
import com.lt.linc.R
import com.lt.linc.common.extensions.setGone
import com.lt.linc.common.extensions.setVisible
import com.lt.linc.common.mvvm.kotlin.BaseBindingQuickAdapter
import com.lt.linc.common.mvvm.kotlin.BaseBindingViewHolder
import com.lt.linc.databinding.ItemLoadTaskListV1Binding

/**
 * <AUTHOR>
 * @Date 2022/6/17
 */
class LoadTaskListAdapter(private val onStartClick: (LoadTaskViewEntry) -> Unit) :
    BaseBindingQuickAdapter<LoadTaskViewEntry, ItemLoadTaskListV1Binding>() {

    @SuppressLint("SetTextI18n")
    override fun convert(
        helper: BaseBindingViewHolder<ItemLoadTaskListV1Binding>?, item: LoadTaskViewEntry) {
        helper?.binding?.apply {
            tvTaskId.text = item.id
            if (item.isTaskDone) {
                stateCompleteTv.setVisible()
                tvDock.setGone()
                llTaskDetail.setGone()
                taskTitleLayoutLl.setBackgroundResource(R.drawable.rect_393939_r4)
                taskTitleLayoutLl.setOnClickListener {
                    onStartClick(item)
                }
            } else {
                stateCompleteTv.setGone()
                tvDock.setVisible()
                llTaskDetail.setVisible()
                tvCreateBy.text = item.createdBy
                tvNote.text = item.description
                startLoadTaskBtn.text = ResUtil.getString(if (item.isPROGRESS) R.string.btn_continue else R.string.btn_start)
                tvLoadNo.text = item.loadList?.mapNotNull { it.loadNo }?.joinToString(",").orEmpty()
                item.dock?.apply {
                    val dockStatus = dockStatus?.name ?: helper.getString(R.string.text_available)
                    tvDock.text = "$name($dockStatus)"
                }
                tvPriority.apply {
                    text = when (item.priority) {
                        PriorityEntry.HIGH -> helper.getString(R.string.label_priority_high)
                        PriorityEntry.LOW -> helper.getString(R.string.label_priority_low)
                        PriorityEntry.TOP -> helper.getString(R.string.label_priority_top)
                        else -> helper.getString(R.string.label_priority_middle)
                    }.uppercase()

                    when (item.priority) {
                        PriorityEntry.HIGH, PriorityEntry.TOP -> {
                            setTextColor(ContextCompat.getColor(mContext, R.color.color_ef4134))
                            setBackgroundResource(R.drawable.rect_521b17_r2)
                        }
                        PriorityEntry.LOW -> {
                            setTextColor(ContextCompat.getColor(mContext, R.color.color_f0cc4d))
                            setBackgroundResource(R.drawable.rect_897915_r2)
                        }
                        else -> {
                            setTextColor(ContextCompat.getColor(mContext, R.color.accent_green_v1))
                            setBackgroundResource(R.drawable.rect_214d36_r2)
                        }
                    }
                }

                taskTitleLayoutLl.setBackgroundResource(R.drawable.rect_525252_top_4)
                taskTitleLayoutLl.setOnClickListener(null)
                startLoadTaskBtn.setOnClickListener {
                    onStartClick(item)
                }
            }
        }
    }
}