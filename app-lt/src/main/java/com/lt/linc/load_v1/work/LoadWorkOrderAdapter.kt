package com.lt.linc.load_v1.work

import android.annotation.SuppressLint
import com.customer.widget.QuickScanner
import com.linc.platform.foundation.model.OrderEntry
import com.linc.platform.foundation.model.OrderStatusEntry
import com.linc.platform.foundation.model.file.FileEntryViewEntry
import com.linc.platform.utils.ResUtil
import com.lt.linc.R
import com.lt.linc.common.extensions.setGone
import com.lt.linc.common.extensions.setVisible
import com.lt.linc.common.extensions.setVisibleOrGone
import com.lt.linc.common.mvvm.kotlin.BaseBindingDifferQuickAdapter
import com.lt.linc.common.mvvm.kotlin.BaseBindingViewHolder
import com.lt.linc.databinding.ItemLoadWokOrderV1Binding
import com.lt.linc.util.v1widget.*

/**
 * <AUTHOR>
 * @Date 2022/6/23
 */
class LoadWorkOrderAdapter(
    private val onStartClick: (OrderEntry) -> Unit,
    private val addProNumberSubmit: (OrderEntry, String) -> Unit,
    private val addOrUpdateOrderPhoto: (OrderEntry, List<UploadPhotoBean>) -> Unit,
    private val deleteOrderPhotos: (List<UploadPhotoBean>?) -> Unit,
    private val onShowOrderSummary: (OrderEntry) -> Unit,
    var loadId: String? = null) : BaseBindingDifferQuickAdapter<OrderEntryWrapper, ItemLoadWokOrderV1Binding>() {

    @SuppressLint("SetTextI18n")
    override fun convert(helper: BaseBindingViewHolder<ItemLoadWokOrderV1Binding>?, item: OrderEntryWrapper) {
        helper?.binding?.apply {
            tvOrderId.text = item.orderEntry.id

            val isLoaded = OrderStatusEntry.SHIPPED == item.orderEntry.status || OrderStatusEntry.LOADED == item.orderEntry.status
            if (isLoaded) {
                llOrderDetail.setGone()
                orderSummaryIv.setGone()
                tvLpCount.setGone()
                tvStateComplete.setVisible()
                orderTitleLl.setBackgroundResource(R.drawable.rect_393939_r4)
                orderTitleLl.setOnClickListener { onStartClick.invoke(item.orderEntry) }
            } else {
                llOrderDetail.setVisible()
                orderSummaryIv.setVisible()
                tvLpCount.setVisible()
                tvStateComplete.setGone()
                orderTitleLl.setBackgroundResource(R.drawable.rect_525252_r4)
                orderTitleLl.setOnClickListener(null)
                tvLpCount.text =
                    "${item.orderEntry.orderLps?.firstOrNull()?.location.orEmpty()} | ${item.orderEntry.orderLps?.count() ?: 0} ${
                        ResUtil.getString(R.string.label_plt)
                    }"
                tvLoadSequence.text = item.orderEntry.sequence
                tvClpCount.text = item.orderEntry.orderLps?.sumOf { it.getChildrenClpCount() }?.toString().orEmpty()
                orderSummaryIv.setOnClickListener { onShowOrderSummary.invoke(item.orderEntry) }
                startOrderBtn.text =
                    ResUtil.getString(if (item.orderEntry.orderLps?.any { it.isLoaded() } == true) R.string.btn_continue else R.string.btn_start)
                startOrderBtn.setOnClickListener { onStartClick.invoke(item.orderEntry) }

                scanProNoScanner.setScanReset(false)
                scanProNoScanner.setInputRestriction(!item.isAllowManualProNumber)
                proNoTv.text = item.orderEntry.proNo
                if (!item.isAllowUpdateProNo) {
                    llAddProNumber.setVisibleOrGone(false)
                    proNoTv.setVisibleOrGone(true)
                    tvLabelAddProNumber.text = ResUtil.getString(R.string.text_pro_no)
                } else {
                    llAddProNumber.setVisibleOrGone(true)
                    proNoTv.setVisibleOrGone(false)
                    tvLabelAddProNumber.text =
                        ResUtil.getString(if (item.isRequireProNumber) R.string.add_pro_number_require else R.string.add_pro_number)
                            .toSpanned()
                }
                scanProNoScanner.text = item.orderEntry.proNo
                scanProNoScanner.setScanEvent { scanner, data ->
                    enterProNoToSubmit(data, item, scanner as QuickScanner)
                }
                noProBtn.setOnClickListener {
                    enterProNoToSubmit(loadId?.filter { it.isDigit() }, item, scanProNoScanner)
                }
                tvLabelOrderPhoto.text = item.allowTakeVideo?.let {
                    ResUtil.getString(if (it) R.string.upload_photo_or_video_of_order else R.string.upload_photo_of_order)
                } ?: ResUtil.getString(R.string.upload_photo_of_order)
                uploadPhotoOrder.apply {
                    setVideoParam(
                        item.orderEntry.id,
                        item.maxTaskVideoLengthInSeconds,
                        item.allowTakeVideo
                    )
                    initLoadParamInfo()
                    initPhotoListByAdapter(
                        item.orderPhotos?.map { it.uploadPhotoBean },
                        item.orderEntry.id
                    )
                    setUploadPhotoByAdapterCallBack(object : UploadPhotoByAdapterCallBack {
                        override fun addOrUpdatePhotos(
                            photoBeanList: List<UploadPhotoBean>,
                            parentItemKey: Any?
                        ) {
                            (parentItemKey as? String)?.let { orderId ->
                                data.find { it.orderEntry.id == orderId }?.apply {
                                    addOrUpdateOrderPhoto.invoke(orderEntry, photoBeanList)
                                }
                            }
                        }

                        override fun deletePhotos(
                            photos: List<UploadPhotoBean>?,
                            isRemoveAll: Boolean,
                            parentItemKey: Any?
                        ) {
                            deleteOrderPhotos.invoke(photos)
                        }
                    })
                }
            }
        }
    }

    private fun enterProNoToSubmit(proNo: String?, item: OrderEntryWrapper, proNoEditScanner: QuickScanner) {
        proNo ?: return
        proNoEditScanner.text = proNo
        addProNumberSubmit.invoke(item.orderEntry, proNo)
    }

    override fun areItemsTheSame(oldItem: OrderEntryWrapper, newItem: OrderEntryWrapper): Boolean =
        oldItem.orderEntry.id == newItem.orderEntry.id

    override fun areContentsTheSame(oldItem: OrderEntryWrapper, newItem: OrderEntryWrapper): Boolean =
        oldItem == newItem && oldItem.isRequireProNumber == newItem.isRequireProNumber && oldItem.orderPhotos == newItem.orderPhotos &&
                oldItem.orderEntry.proNo == newItem.orderEntry.proNo && oldItem.isAllowUpdateProNo == newItem.isAllowUpdateProNo
}

data class OrderEntryWrapper(
    val orderEntry: OrderEntry,
    val isRequireProNumber: Boolean = false,
    val isAllowManualProNumber: Boolean = false,
    val isAllowUpdateProNo: Boolean = false,
    val orderPhotos: List<FileEntryViewEntryWrapper>? = null,
    val maxTaskVideoLengthInSeconds: Int? = null,
    val allowTakeVideo: Boolean? = null,
)

data class FileEntryViewEntryWrapper(val fileEntryViewEntry: FileEntryViewEntry, val uploadPhotoBean: UploadPhotoBean)