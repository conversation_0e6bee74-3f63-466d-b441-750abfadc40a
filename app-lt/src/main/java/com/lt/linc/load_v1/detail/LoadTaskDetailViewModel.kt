package com.lt.linc.load_v1.detail

import android.app.Activity
import android.content.Context
import android.content.Intent
import com.linc.platform.baseapp.api.EntryTicketApi
import com.linc.platform.baseapp.model.DockOperateEntry
import com.linc.platform.baseapp.model.LocationEntry
import com.linc.platform.common.help.FunctionHelpPresenterImpl
import com.linc.platform.common.step.StepStatusEntry
import com.linc.platform.common.step.StepStatusUpdateEntry
import com.linc.platform.common.task.TaskForceCloseEntry
import com.linc.platform.common.task.TaskStatusEntry
import com.linc.platform.common.task.worktime.TaskWorkTimeDal
import com.linc.platform.foundation.api.CarrierApi
import com.linc.platform.foundation.api.CustomerApi
import com.linc.platform.foundation.api.CustomerQuestionnaireApi
import com.linc.platform.foundation.api.OrderApi
import com.linc.platform.foundation.model.*
import com.linc.platform.foundation.model.organization.common.carrier.CarrierSearchEntry
import com.linc.platform.foundation.model.questionnaire.QuestionnaireResultSearch
import com.linc.platform.http.ErrorCode
import com.linc.platform.http.ErrorResponse
import com.linc.platform.idm.api.IdmApi
import com.linc.platform.idm.model.mapToUserLevel
import com.linc.platform.load.api.LoadTaskApi
import com.linc.platform.load.api.LoadWorkApi
import com.linc.platform.load.model.*
import com.linc.platform.load.v1.model.UpdatePhotosRequestEntry
import com.linc.platform.pack.api.PackTaskApi
import com.linc.platform.pack.model.OrderIdsRequest
import com.linc.platform.utils.CollectionUtil
import com.linc.platform.utils.ResUtil
import com.linc.platform.utils.ToastUtil
import com.lt.linc.R
import com.lt.linc.common.Constant
import com.lt.linc.common.extensions.addToNewList
import com.lt.linc.common.extensions.deepCopy
import com.lt.linc.common.mvi.StepProcessReactiveViewModel
import com.lt.linc.common.mvi.subscribe
import com.lt.linc.common.mvvm.kotlin.BaseRepository
import com.lt.linc.common.mvvm.kotlin.extensions.*
import com.lt.linc.common.mvvm.kotlin.shoot
import com.lt.linc.load.detail.TaskDetailActivity
import com.lt.linc.load.operate.AddCtnrTrailerNoActivity
import com.lt.linc.load.operate.AddSealOrCountingSheetActivity
import com.lt.linc.load_v1.LoadProcess
import com.lt.linc.load_v1.LoadViewModel
import com.lt.linc.load_v1.collection.addseal.AddSealV1Activity
import com.lt.linc.load_v1.config.LoadTaskUserLevelConfig
import com.lt.linc.load_v1.detail.LoadTaskDetailState.LoadListCompose
import com.lt.linc.load_v1.detail.LoadTaskDetailState.LoadViewWrapper
import com.lt.linc.load_v1.detail.LoadTaskDetailUiEvent.AskCloseTask
import com.lt.linc.load_v1.detail.LoadTaskDetailUiEvent.ProcessEvent
import com.lt.linc.load_v1.detail.LoadTaskDetailUiState.PageProcess
import com.lt.linc.load_v1.detail.LoadTaskDetailUiState.RefreshStatus
import com.lt.linc.load_v1.work.mbol.MbolListV1Activity
import com.lt.linc.util.SnackType
import com.lt.linc.util.Snacker
import com.lt.linc.util.v1widget.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.firstOrNull

/**
 * <AUTHOR>
 * @Date 2022/6/22
 */
class LoadTaskDetailViewModel(
    private val repository: LoadTaskDetailRepo = LoadTaskDetailRepo(),
    private val facilityName: String,
    private val intentFlag: Int = 0,
    private val activityViewModel: LoadViewModel,
    initDataState: LoadTaskDetailState,
    initUiState: LoadTaskDetailUiState = LoadTaskDetailUiState(),
) : StepProcessReactiveViewModel<LoadTaskDetailState, LoadTaskDetailUiState>(initDataState, initUiState, initDataState.stepEntry) {

    private val functionHelpPresenterImpl by lazy { FunctionHelpPresenterImpl() }
    private val taskWorkTimeDal by lazy { TaskWorkTimeDal() }


    init {

        //loadListCompose
        subscribe(LoadTaskDetailState::taskEntry) { taskEntry ->
            //sort by sequence
            taskEntry.loadList?.sortedBy { it.sequence }?.apply {
                //filter truck photos
                val unUploadTruckPhotoList = dataState.loadListCompose?.uploadTruckPhotos?.filter {
                    !it.filePath.isNullOrEmpty() && it.uploadStatus != UploadStatus.UPLOADED
                }
                val newTruckPhotoList =
                    taskEntry.truckPhotoIds?.mapNotNull { photoId ->
                        taskEntry?.photoMap?.get(photoId)?.let { photo ->
                            UploadPhotoBean(
                                photoServerId = photo.id,
                                itemType = if (photo.fileType == FileTypeEntry.MP4) ItemType.VIDEO_DATA else ItemType.PHOTO_DATA,
                                videoName = photo.name,
                            )
                        }
                    }.addToNewList(unUploadTruckPhotoList)
                //filter seal photos
                val unUploadSealPhotoList = dataState.loadListCompose?.uploadSealPhotos?.filter {
                    !it.filePath.isNullOrEmpty() && it.uploadStatus != UploadStatus.UPLOADED
                }
                val newSealPhotoList =
                    taskEntry.sealEntry?.photos?.mapNotNull { photoId ->
                        taskEntry?.photoMap?.get(photoId)?.let { photo ->
                            UploadPhotoBean(
                                photoServerId = photo.id,
                                itemType = if (photo.fileType == FileTypeEntry.MP4) ItemType.VIDEO_DATA else ItemType.PHOTO_DATA,
                                videoName = photo.name,
                            )
                        }
                    }.addToNewList(unUploadSealPhotoList)
                val sealNo = taskEntry.sealEntry?.sealNo
                //load list and merge counting sheet photos
                val newLoadViewWrappers = this.map { entry ->
                    val unUploadPhotoList = dataState.loadListCompose?.loadViewWrappers?.find {
                        it.loadDetail.id == entry.id
                    }?.uploadCountSheetPhotos?.filter {
                        !it.filePath.isNullOrEmpty() && it.uploadStatus != UploadStatus.UPLOADED
                    }
                    val newUploadPhotoList =
                        entry.countingSheetPhotos?.mapNotNull { photoId ->
                            taskEntry?.photoMap?.get(photoId)?.let { photo ->
                                UploadPhotoBean(
                                    photoServerId = photo.id,
                                    itemType = if (photo.fileType == FileTypeEntry.MP4) ItemType.VIDEO_DATA else ItemType.PHOTO_DATA,
                                    videoName = photo.name,
                                )
                            }
                        }.addToNewList(unUploadPhotoList)
                    LoadViewWrapper(loadDetail = entry, uploadCountSheetPhotos = newUploadPhotoList)
                }
                setDataState { copy(loadListCompose = LoadListCompose(newLoadViewWrappers, newTruckPhotoList, newSealPhotoList, sealNo)) }
            }
        }

        //load list
        subscribe(
            LoadTaskDetailState::customerEntry,
            LoadTaskDetailState::userLevelConfig,
            LoadTaskDetailState::loadListCompose
        ) { customerEntry, userLevelConfig, loadListCompose ->
            loadListCompose?.apply {
                customerEntry ?: return@apply
                loadViewWrappers ?: return@apply
                //check is need counting sheet
                val isNeedCountingSheetByFacility =
                    customerEntry.requiredCountingSheetFacilities?.any { name -> name.lowercase() == facilityName.lowercase() } ?: false
                val isNeedCountingSheet =
                    customerEntry.loadCollectFields?.contains(CustomerViewEntry.LOAD_COLLECT_FIELD_COUNTING_SHEET) ?: false || isNeedCountingSheetByFacility
                //check is need pro no
                val isNeedProNo = customerEntry.loadCollectFields?.contains(CustomerViewEntry.LOAD_COLLECT_FIELD_PRO_NO) ?: false
                //check is need seal no
                val isNeedSealNo = customerEntry.loadCollectFields?.contains(CustomerViewEntry.LOAD_COLLECT_FIELD_SEAL_NO) ?: false

                val detailWrapperList = arrayListOf<LoadTaskDetailWrapper>()
                detailWrapperList.addAll(loadViewWrappers.map {
                    LoadTaskDetailWrapper(
                        loadDetail = it.loadDetail,
                        needCountingSheet = isNeedCountingSheet,
                        uploadCountSheetPhotos = it.uploadCountSheetPhotos,
                        isNeedProNo = isNeedProNo,
                        isAllowUpdateProNo = dataState.carrierList?.find { carrier-> carrier.id == it.loadDetail.carrierId }?.isAutoCreateProNo != true,
                        isAllowManualProNumber = userLevelConfig?.allowManualProNumber ?: false,
                        dn = loadViewWrappers?.firstOrNull()?.loadDetail?.orderIds?.firstOrNull(),
                        maxTaskVideoLengthInSeconds = customerEntry.maxTaskVideoLengthInSeconds,
                        allowTakeVideo = customerEntry.allowTakeVideo
                    )
                })
                //add trailer no item
                if (isNeedAddTrailerNo()) {
                    detailWrapperList.add(LoadTaskDetailWrapper(itemType = LoadTaskDetailItemType.AddTrailerNo))
                }
                //add photos item
                detailWrapperList.add(
                    LoadTaskDetailWrapper(
                        itemType = LoadTaskDetailItemType.AddPhotos,
                        uploadTruckPhotos = uploadTruckPhotos,
                        uploadSealPhotos = uploadSealPhotos,
                        sealNo = sealNo,
                        isRequireSealNo = isNeedSealNo,
                        dn = loadViewWrappers?.firstOrNull()?.loadDetail?.orderIds?.firstOrNull(),
                        maxTaskVideoLengthInSeconds = customerEntry.maxTaskVideoLengthInSeconds,
                        allowTakeVideo = customerEntry.allowTakeVideo
                    )
                )
                setUiState { copy(loadListForShowing = detailWrapperList) }
            }
        }
    }

    fun loadTaskDetail(onAskStartStep: (String?) -> Flow<Boolean?>) {
        launch {
            setUiState { copy(refreshStatus = RefreshStatus.Refreshing) }
            //request task detail
            requestAwait(repository.loadTaskData(dataState.taskId), showLoading = false).getOrNull()?.also {
                val customerId = it.loadList?.first()?.customerId
                //user level config is null to request
                val levelConfig = dataState.userLevelConfig ?: requestAwait(
                    repository.getUserLevelControl(dataState.stepEntry.id, customerId),
                    showLoading = false
                ).getOrNull()

                // 过滤出所有订单的 carrierId，并查询 carrier 信息
                val carrierSearchEntry = CarrierSearchEntry().apply {
                    ids = it.loadList.map {load-> load.carrierId }
                }

                val carrierList = requestAwait(repository.searchCarrier(carrierSearchEntry)).getOrNull()

                setUiState { copy(refreshStatus = RefreshStatus.RefreshComplete) }
                setDataStateAwait {
                    copy(
                        taskEntry = it.assembleLoadOrders(),
                        stepEntry = it.getStepInfo(stepType),
                        userLevelConfig = levelConfig,
                        customerEntry = it.customerEntry,
                        carrierList = carrierList,
                    )
                }

                //check autoCc
                if (it.customerEntry?.allowAutoCc == true) {
                    onAllOrderCheckCompletedToStartStep(onAskStartStep)
                } else {
                    checkLpTemplateMatchWithOrderRequire(onAskStartStep)
                }

                //auto close step
                if (isStepCanAutoClose()) {
                    updateStepStatus(StepStatusEntry.DONE)
                }
            } ?: setUiState { copy(refreshStatus = RefreshStatus.RefreshComplete) }
        }
    }

    private fun checkLpTemplateMatchWithOrderRequire(onAskStartStep: (String?) -> Flow<Boolean?>) {
        dataState.taskEntry.orderIds ?: return
        request(repository.doOrderLpTemplateCheck(dataState.taskEntry.orderIds), error = {
            if (ErrorCode.LP_CONFIGURATION_NOT_MATCH_WITH_ORDER_ITEMLINE == it.code) {
                setDataState { copy(isNeedManualCcMsg = it.errorMessage) }
            } else {
                showToast(it.errorMessage)
            }
        }, success = {
            onAllOrderCheckCompletedToStartStep(onAskStartStep)
        })
    }

    private fun onAllOrderCheckCompletedToStartStep(onAskStartStep: (String?) -> Flow<Boolean?>) {
        launch {
            //completeAskStartStep： Prevent pop-ups multiple times
            if (StepStatusEntry.NEW == dataState.stepStatus && dataState.completeAskStartStep) {
                setDataState { copy(completeAskStartStep = false) }
                val isStartStep = onAskStartStep(dataState.stepName).firstOrNull() ?: false
                setDataState { copy(completeAskStartStep = true) }
                if (isStartStep) {
                    startStep()
                } else {
                    setUiState { copy(pageProcess = PageProcess.Finish.shoot()) }
                }
            }
        }
    }

    private fun startStep() {
        if (!dataState.isOwner) {
            showToast(R.string.text_please_take_over_step)
            setUiState { copy(pageProcess = PageProcess.Finish.shoot()) }
            return
        }
        launch {
            val response = requestAwait(repository.startTask(dataState.taskId))
            if (response.isSuccess) {
                updateStepStatus(StepStatusEntry.IN_PROGRESS)
                return@launch
            }
            setUiState { copy(pageProcess = PageProcess.Finish.shoot()) }
        }

    }

    fun closeStep(onCloseStepFail: (String) -> Unit) {
        if (!dataState.isOwner) {
            showToast(R.string.text_please_take_over_step)
            return
        }
        if (isAllLoadCompleted()) {
            updateStepStatus(StepStatusEntry.DONE)
        } else {
            onCloseStepFail.invoke(ResUtil.getString(R.string.text_please_complete_all_load))
        }
    }

    fun forceCloseStep(reason: String?) {
        request(repository.forceCloseStep(dataState.taskId, dataState.stepEntry.id, reason)) {
            dockCheckOut()
        }
    }

    fun reopenStepAndTask() {
        request(repository.reopenStep(dataState.taskId, dataState.stepEntry.id)) {
            showToast(R.string.text_reopen_success)
            updateDateStateStepStatus(StepStatusEntry.IN_PROGRESS)
            setUiState { copy(pageProcess = PageProcess.RefreshTask.shoot()) }
        }
    }

    private fun updateStepStatus(statusEntry: StepStatusEntry) {
        if (StepStatusEntry.DONE == statusEntry && isNeedAddTrailerNo() && dataState.taskEntry.trailerNo.isNullOrEmpty()) {
            showToast(R.string.msg_please_collect_ctnr_trailer_no_first)
            return
        }
        request(repository.updateStepStatus(dataState.taskId, dataState.stepEntry.id, statusEntry), error = {
            closeTaskError(statusEntry, it)
        }, success = {
            //Update stepStatus
            updateDateStateStepStatus(statusEntry)
            taskWorkTimeDal.endWorkTime()
            when {
                StepStatusEntry.IN_PROGRESS == statusEntry -> {
                    if (dataState.customerEntry?.allowAutoCc == true) {
                        dataState.taskEntry.orderIds?.forEach { request(repository.doAutoCc(it)) }
                    }
                    //update step status
                    dataState.stepEntry.deepCopy()?.let {
                        it.status = StepStatusEntry.IN_PROGRESS
                        setDataState { copy(stepEntry = it) }
                    }
                    showToast(R.string.text_step_started)
                }
                isNeedConfirmCloseTask() -> confirmToCloseTask()
                else -> dockCheckOut()
            }
        })
    }

    //Update stepStatus
    private fun updateDateStateStepStatus(statusEntry: StepStatusEntry) {
        val newStepEntry = dataState.stepEntry.deepCopy()?.apply { status = statusEntry }
        setDataState { copy(stepEntry = newStepEntry!!) }
    }

    private fun confirmToCloseTask() {
        launch {
            if (awaitEvent { AskCloseTask } == true) {
//                if (repository.facilityEntry?.enableScanAssetInTask == true) {
//                    AssetReleaseTaskIdHandler().releaseTaskId(dataState.taskId, dataState.taskEntry.taskType, repository.facilityId)
//                }
                request(repository.closeTask(dataState.taskId), error = {
                    if (ResUtil.getString(R.string.error_load_seal) == it.errorMessage) {
                        ToastUtil.showToast(it.errorMessage)
                        fireEvent { ProcessEvent.StartToAddSeal }
                    } else {
                        fireEvent { ProcessEvent.CloseTaskFail }
                    }
                }, success = {
                    dataState.taskEntry.status = TaskStatusEntry.CLOSED
                    dockCheckOut()
                })
            }
        }
    }

    fun dockCheckOut(fromCompleteTask: Boolean = true) {
        launch {
            dataState.taskEntry.dock ?: return@launch
            if (dataState.taskEntry.dock.isRelease) {
                completeDockCheckOut(fromCompleteTask)
                return@launch
            }
            val operateEntry = DockOperateEntry().also {
                it.locationId = dataState.taskEntry.dockId
                it.taskType = dataState.taskEntry.taskType
                it.taskIds = listOf(dataState.taskEntry.id)
                it.entryId = dataState.taskEntry.entryId
            }
            requestAwait(repository.dockCheckout(operateEntry))
            requestAwait(repository.triggerSendCheckoutQRCode(dataState.taskEntry.entryId))
            completeDockCheckOut(fromCompleteTask)
        }
    }

    private suspend fun completeDockCheckOut(fromCompleteTask: Boolean = false) {
        if (fromCompleteTask && !dataState.taskEntry.getCustomerId().isNullOrEmpty()) {
            //check task done
            if (!dataState.taskEntry.isTaskDone) {
                val taskResult = requestAwait(repository.loadTaskData(taskId = dataState.taskId))
                if (taskResult.isFailure || taskResult.getOrNull()?.isTaskDone != true) {
                    showToast(R.string.text_step_closed)
                    setUiState { copy(pageProcess = PageProcess.Finish.shoot()) }
                    return
                }
            }
            val search = QuestionnaireResultSearch(
                taskId = dataState.taskEntry.id,
                customerId = dataState.taskEntry.getCustomerId(),
                taskType = dataState.taskEntry.taskType
            )
            val searchResult = requestAwait(repository.searchQuestionnaireResult(search), error = null)
            if (searchResult.getOrNull().isNullOrEmpty()) {
                requestAwait(repository.getCustomerQuestionnaire(dataState.taskEntry.getCustomerId())).onSuccess {
                    it ?: return@onSuccess
                    if (!it.getTaskQuestionnaireDetails(dataState.taskEntry.taskType).isNullOrEmpty()) {
                        fireEvent { ProcessEvent.StartAnswerQuestionnaire(dataState.taskEntry, it) }
                    }
                }
            }
//            fireEvent { ProcessEvent.ShowAssetAssignedActivity }
        }
        showToast(R.string.text_step_closed)
        setUiState { copy(pageProcess = PageProcess.Finish.shoot()) }
    }

    fun checkToWorkProgress(activity: Activity, loadDetailEntry: LoadDetailEntry, onAskStartLoad: (String) -> Flow<Boolean?>) {
        launch {
            if (!dataState.isNeedManualCcMsg.isNullOrEmpty()) {
                fireEvent { ProcessEvent.ShowErrorMessageDialog(dataState.isNeedManualCcMsg?: "") }
                return@launch
            }
            //check has unCompleted load before
            loadDetailEntry.sequence?.let { sequence ->
                val unCompleteOrderIds = dataState.taskEntry.loadList?.filter {
                    it.sequence != null && it.sequence < sequence && !it.isLoadCompleted
                }?.map { it.id }

                if (!unCompleteOrderIds.isNullOrEmpty()) {
                    val message = "${unCompleteOrderIds.joinToString { s -> s }} ${ResUtil.getString(R.string.hint_is_not_complete)},${
                        ResUtil.getString(R.string.hint_do_you_want_to_start)
                    } ${loadDetailEntry.id}?"
                    val isStartLoad = onAskStartLoad(message).firstOrNull() ?: false

                    //start to load
                    if (isStartLoad) {
                        goToLoadWorkActivity(activity, loadDetailEntry)
                    }
                    return@launch
                }
            }
            //Check Is Allow Open Load Task
            dataState.customerEntry?.apply {
                if (validatePalletCountAtLoadTask) {

                    //get order pallet count
                    val palletCount = loadDetailEntry.orderList?.sumOf { it.palletLabels?.count() ?: 0 } ?: 0
                    if (palletCount != loadDetailEntry.requireLoadLps.count()) {
                        showToast(
                            ResUtil.format(R.string.msg_order_pallet_count_validate,
                                loadDetailEntry.orderIds?.joinToString { s -> s })
                        )
                        return@launch
                    }
                }
            }
            goToLoadWorkActivity(activity, loadDetailEntry)
        }
    }

    fun updateLoadProNo(loadDetailEntry: LoadDetailEntry, proNumber: String) {
        launch {
            val result = requestAwait(repository.updateLoadProNo(loadDetailEntry.id, proNumber))
            if (result.isSuccess) {
                showToast(R.string.msg_update_success)
                //update all order pro Number
                loadDetailEntry.orderList?.filter { order ->
                    OrderTypeEntry.DS != order.orderType && order.proNo.isNullOrEmpty()
                }?.map { orderEntry ->
                    OrderBatchUpdateEntry().apply {
                        orderId = orderEntry.id
                        update = OrderUpdateEntry().apply { proNo = proNumber }
                    }
                }?.let { orderRequests ->
                    requestAwait(repository.batchUpdateOrderProNumber(orderRequests))
                }
            }
            val newLoadViewWrappers = dataState.loadListCompose?.loadViewWrappers?.toMutableList()?.apply {
                val index = indexOfFirst { it.loadDetail.id == loadDetailEntry.id }
                if (index > -1) {
                    set(index, this[index].let {
                        it.copy(loadDetail = it.loadDetail.run { deepCopy() ?: this })
                    }.apply {
                        if (result.isSuccess) {
                            loadDetail.proNo = proNumber
                        }
                    })
                }
            }
            setDataState { copy(loadListCompose = dataState.loadListCompose?.copy(loadViewWrappers = newLoadViewWrappers)) }
        }
    }

    fun addOrUpdateCountSheetPhotos(loadDetailEntry: LoadDetailEntry, uploadPhotoBeanList: List<UploadPhotoBean>) {
        launch {
            awaitDataState()
            dataState.loadListCompose?.loadViewWrappers?.find { it.loadDetail.id == loadDetailEntry.id }?.let { loadViewWrapper ->
                uploadPhotoBeanList.filter { uploadPhotoBean ->
                    //photoBean is deleted so not updated to count sheet
                    val photoBeanExist = loadViewWrapper.uploadCountSheetPhotos?.any { uploadPhotoBean.filePath == it.filePath } ?: false
                    uploadPhotoBean.uploadStatus != UploadStatus.UPLOADED || (uploadPhotoBean.uploadStatus == UploadStatus.UPLOADED && photoBeanExist)
                }.apply {
                    val newUploadPhotoIds = loadViewWrapper.uploadCountSheetPhotos.getAvailablePhotoIds()
                        .addToNewList(filter { it.uploadStatus == UploadStatus.UPLOADED }.mapNotNull { it.photoServerId })?.distinct()
                    //do upload count sheet
                    if (!newUploadPhotoIds.isNullOrEmpty() && !uploadCountingSheet(loadDetailEntry.id, newUploadPhotoIds)) {
                        forEach { it.uploadStatus = UploadStatus.PARENT_UPDATE_FAIL }
                    }
                }
            }?.let { newUploadPhotoBeanList ->
                val newLoadViewWrappers = dataState.loadListCompose?.loadViewWrappers?.toMutableList()?.apply {
                    indexOfFirst { it.loadDetail.id == loadDetailEntry.id }.let {
                        if (it < 0) return@apply
                        val oldLoadViewWrapper = this[it]
                        val newPalletUploadPhotoBean = oldLoadViewWrapper.uploadCountSheetPhotos.mergeToNewList(newUploadPhotoBeanList)
                        set(it, oldLoadViewWrapper.copy(uploadCountSheetPhotos = newPalletUploadPhotoBean))
                    }
                }
                setDataState { copy(loadListCompose = loadListCompose?.copy(loadViewWrappers = newLoadViewWrappers)) }
            }
        }
    }

    fun deleteCountSheetPhotos(loadDetailEntry: LoadDetailEntry, delPhotoList: List<UploadPhotoBean>?, isRemoveAll: Boolean) {
        launch {
            awaitDataState()
            dataState.loadListCompose?.loadViewWrappers?.find { it.loadDetail.id == loadDetailEntry.id }?.let {
                val lastCountSheetPhotos = it.uploadCountSheetPhotos?.filteredDeleteItems(delPhotoList, isRemoveAll)
                //do update counting sheet
                if (uploadCountingSheet(loadDetailEntry.id, lastCountSheetPhotos.getAvailablePhotoIds())) {
                    return@let lastCountSheetPhotos
                }
                it.uploadCountSheetPhotos
            }.let {
                val newLoadViewWrappers = dataState.loadListCompose?.loadViewWrappers?.toMutableList()?.apply {
                    val loadIndex = indexOfFirst { it.loadDetail.id == loadDetailEntry.id }
                    if (loadIndex < 0) return@launch
                    set(loadIndex, this[loadIndex].copy(uploadCountSheetPhotos = it))
                }
                setDataState { copy(loadListCompose = loadListCompose?.copy(loadViewWrappers = newLoadViewWrappers)) }
            }
        }
    }

    private suspend fun uploadCountingSheet(loadId: String, photoIds: List<String>?): Boolean =
        requestAwait(repository.uploadCountingSheet(loadId, photoIds)) {
            showToast(R.string.msg_update_success)
        }.isSuccess

    fun addOrUpdateSealPhotos(uploadPhotoBeanList: List<UploadPhotoBean>, sealNo: String?) {
        launch {
            awaitDataState()
            uploadPhotoBeanList.filter { uploadPhotoBean ->
                //photoBean is deleted so not updated to SealPhotos
                val photoBeanExist = dataState.loadListCompose?.uploadSealPhotos?.any { uploadPhotoBean.filePath == it.filePath } ?: false
                uploadPhotoBean.uploadStatus != UploadStatus.UPLOADED || (uploadPhotoBean.uploadStatus == UploadStatus.UPLOADED && photoBeanExist)
            }.apply {
                val sealPhotoIds = dataState.loadListCompose?.uploadSealPhotos.getAvailablePhotoIds()
                    .addToNewList(filter { it.uploadStatus == UploadStatus.UPLOADED }.mapNotNull { it.photoServerId })?.distinct()
                //do update seal
                if (!sealPhotoIds.isNullOrEmpty() && !uploadSeal(sealNo, sealPhotoIds)) {
                    forEach { it.uploadStatus = UploadStatus.PARENT_UPDATE_FAIL }
                }
            }.let {
                val newUploadSealPhotos = dataState.loadListCompose?.uploadSealPhotos.mergeToNewList(it)
                setDataState { copy(loadListCompose = loadListCompose?.copy(uploadSealPhotos = newUploadSealPhotos, sealNo = sealNo)) }
            }
        }
    }

    fun deleteSealPhotos(delPhotoList: List<UploadPhotoBean>?, sealNo: String?, isRemoveAll: Boolean) {
        launch {
            awaitDataState()
            dataState.loadListCompose?.uploadSealPhotos?.let {
                val lastUploadPhotos = it.filteredDeleteItems(delPhotoList, isRemoveAll)
                //do update seal
                if (uploadSeal(sealNo, lastUploadPhotos.getAvailablePhotoIds())) {
                    return@let lastUploadPhotos
                }
                it
            }.let {
                setDataState { copy(loadListCompose = loadListCompose?.copy(uploadSealPhotos = it, sealNo = sealNo)) }
            }
        }
    }

    private suspend fun uploadSeal(sealNoStr: String?, photoIds: List<String>?): Boolean =
        requestAwait(repository.uploadSealNo(dataState.taskId, LoadWorkEntry().apply {
            photos = photoIds
            sealNo = sealNoStr
        })).onSuccess {
            showToast(R.string.msg_update_success)
        }.isSuccess

    fun updateSealNo(sealNoStr: String?, photoList: List<String>?) {
        launch {
            if (uploadSeal(sealNoStr, photoList)) {
                setDataState { copy(loadListCompose = loadListCompose?.copy(sealNo = sealNoStr)) }
            }
        }
    }

    fun addOrUpdateTruckPhotos(uploadPhotoBeanList: List<UploadPhotoBean>) {
        launch {
            awaitDataState()
            uploadPhotoBeanList.filter { uploadPhotoBean ->
                //photoBean is deleted so not updated to TruckPhotos
                val photoBeanExist = dataState.loadListCompose?.uploadTruckPhotos?.any { uploadPhotoBean.filePath == it.filePath } ?: false
                uploadPhotoBean.uploadStatus != UploadStatus.UPLOADED || (uploadPhotoBean.uploadStatus == UploadStatus.UPLOADED && photoBeanExist)
            }.apply {
                val truckPhotoIds = dataState.loadListCompose?.uploadTruckPhotos.getAvailablePhotoIds()
                    .addToNewList(filter { it.uploadStatus == UploadStatus.UPLOADED }.mapNotNull { it.photoServerId })?.distinct()
                if (!truckPhotoIds.isNullOrEmpty() && !uploadTruckPhotos(truckPhotoIds)) {
                    forEach { it.uploadStatus = UploadStatus.PARENT_UPDATE_FAIL }
                }
            }.let {
                val newUploadTruckPhotos = dataState.loadListCompose?.uploadTruckPhotos.mergeToNewList(it)
                setDataState { copy(loadListCompose = loadListCompose?.copy(uploadTruckPhotos = newUploadTruckPhotos)) }
            }
        }
    }

    fun deleteTruckPhotos(delPhotoList: List<UploadPhotoBean>?, isRemoveAll: Boolean) {
        launch {
            awaitDataState()
            dataState.loadListCompose?.uploadTruckPhotos?.let {
                val lastUploadPhotos = it.filteredDeleteItems(delPhotoList, isRemoveAll)
                //do update truck
                if (uploadTruckPhotos(lastUploadPhotos.getAvailablePhotoIds())) {
                    return@let lastUploadPhotos
                }
                it
            }.let {
                setDataState { copy(loadListCompose = loadListCompose?.copy(uploadTruckPhotos = it)) }
            }
        }
    }

    private suspend fun uploadTruckPhotos(photoIds: List<String>?): Boolean =
        requestAwait(repository.updateTruckPhotos(UpdatePhotosRequestEntry(dataState.taskId, photoIds))).onSuccess {
            showToast(R.string.msg_update_success)
            dataState.taskEntry.truckPhotoIds = photoIds
        }.isSuccess

    private fun goToLoadWorkActivity(activity: Activity, loadDetailEntry: LoadDetailEntry) {
        dataState.apply {
            activityViewModel.startProcess(LoadProcess.LoadWork(loadDetailEntry,userLevelConfig))
        }
    }

    fun goToAddCtnrTrailerNoActivity(activity: Activity) {
        activity.startActivity(Intent(activity, AddCtnrTrailerNoActivity::class.java).putExtra(Constant.INTENT_TASK_ID, dataState.taskId))
    }

    fun goToAddSealActivity(activity: Activity) {
        activity.startActivity(Intent(activity, AddSealV1Activity::class.java).putExtra(LoadTaskViewEntry.TAG, dataState.taskEntry))
    }

    fun goToAddSealOrCountingSheetActivity(activity: Activity) {
        activity.startActivity(
            Intent(activity, AddSealOrCountingSheetActivity::class.java).putExtra("id", dataState.taskId)
                .putExtra(AddSealOrCountingSheetActivity.MODE, AddSealOrCountingSheetActivity.SEAL_MODE)
                .putExtra(AddSealOrCountingSheetActivity.ENTRY_ID, dataState.taskEntry.entryId)
                .putExtra(TaskDetailActivity.COMPANY_ID, dataState.taskEntry.companyId)
        )
    }

    fun getAndOpenHelpPage(context: Context?, helpPageKey: String?, facilityId: String?) {
        functionHelpPresenterImpl.getAndOpenHelpPage(context, helpPageKey, "", facilityId)
    }

    fun getDock(): LocationEntry? = dataState.taskEntry.dock


    private fun allowShortShipOrPartialShip(shortShipOrders: List<ShortShipOrderEntry>): Boolean {
        val isAllowShortShip = shortShipOrders.none { it.customer?.isAllowShortShip == false }
        val allowPartialShip = shortShipOrders.all {
            (OrderTypeEntry.DS == it.orderType && it.customer?.allowPartialShipForDropShip == true)
                    || (OrderTypeEntry.RG == it.orderType && it.customer?.allowPartialShipForRegular == true)
        }
        return isAllowShortShip || allowPartialShip
    }

    private fun allowShortShipOrPartialShipOrders(shortShipOrders: List<ShortShipOrderEntry>): String {
        val messages: MutableList<String> = ArrayList()
        shortShipOrders.forEach {
            messages.add(getFormattedString(R.string.msg_short_ship_error_tips, it.orderId?:""))
        }
        return messages.joinToString(",")
    }

    private fun notAllowShortShipOrPartialShipOrders(shortShipOrders: List<ShortShipOrderEntry>): String {
        val messages: MutableList<String> = ArrayList()
        shortShipOrders.filter { order-> order.customer?.isAllowShortShip == false
                || order.customer?.allowPartialShipForDropShip == false
                || order.customer?.allowPartialShipForRegular == false
                || OrderTypeEntry.RG != order.orderType
        }.forEach {
            messages.add(getFormattedString(R.string.msg_not_allow_short_ship_error_tips, it.orderId?:"",
                it.customer?.orgName?: ""))
        }
        return messages.joinToString(",")
    }

    private fun isAllLoadCompleted(): Boolean = dataState.taskEntry.loadList?.all { it.isLoadCompleted } ?: false

    private fun isStepCanAutoClose(): Boolean {
        return dataState.run { isAllLoadCompleted() && isAllowAutoCloseLoadTask() && !taskEntry.isReopen && !stepEntry.isDone }
    }

    //check is pre-load
    private fun isDropLoadByTask(): Boolean =
        dataState.taskEntry.loadList?.any { TrailerPickUpModeEntry.DROP_LOAD == it.trailerPickUpMode }
            ?: false

    private fun isNeedAddTrailerNo(): Boolean =
        isDropLoadByTask() || dataState.customerEntry?.loadCollectFields?.contains(CustomerViewEntry.LOAD_COLLECT_FIELD_CTNR_TRAILER_NO) == true

    private fun isAllowAutoCloseLoadTask(): Boolean =
        dataState.customerEntry?.allowAutoCloseLoadTask ?: false

    private fun isNeedConfirmCloseTask(): Boolean =
        Constant.FLAG_FROM_DIRECT_LOAD == intentFlag && !dataState.taskEntry.isTaskDone && !isAllowAutoCloseLoadTask()

    fun goToMbolListActivity(activity: Activity) {
        if (!isAllLoadCompleted()) {
            showToast(ResUtil.getString(R.string.text_please_complete_all_load))
            return;
        }
        val intent = Intent()
        intent.putExtra(MbolListV1Activity.KEY_TASK_ID, dataState.taskId)
        intent.setClass(activity, MbolListV1Activity::class.java)
        activity.startActivity(intent)
    }

    private fun closeTaskError(statusEntry: StepStatusEntry, error: ErrorResponse) {
        launch {
            val shortShipOrders = requestAwait(repository.getShortShipOrder(dataState.taskId)).getOrNull()
            if (statusEntry == StepStatusEntry.DONE
                && ErrorCode.INVALID_CARRIER_NAME != error.code
                && ErrorCode.LOAD_TASK_CLOSE_REQUIRE_PRINT_LABEL != error.code) {
                if (shortShipOrders.isNullOrEmpty()) {
                    setUiState { copy(pageProcess = PageProcess.ForceCloseConfirm(error.errorMessage).shoot()) }
                    return@launch
                }
                if (allowShortShipOrPartialShip(shortShipOrders)) {
                    setUiState { copy(pageProcess = PageProcess.ForceCloseConfirm(allowShortShipOrPartialShipOrders(shortShipOrders)).shoot()) }
                } else {
                    showSnack(SnackType.ErrorV1(), notAllowShortShipOrPartialShipOrders(shortShipOrders))
                }
            } else {
                showToast(error.errorMessage)
            }
        }
    }
}

class LoadTaskDetailRepo : BaseRepository() {

    private val idmApi by apiServiceLazy<IdmApi>()
    private val loadTaskApi by apiServiceLazy<LoadTaskApi>()
    private val customerApi by apiServiceLazy<CustomerApi>()
    private val packTaskApi by apiServiceLazy<PackTaskApi>()
    private val entryTicketApi by apiServiceLazy<EntryTicketApi>()
    private val loadWorkApi by apiServiceLazy<LoadWorkApi>()
    private val orderApi by apiServiceLazy<OrderApi>()
    private val customerQuestionnaireApi by apiServiceLazy<CustomerQuestionnaireApi>()
    private val carrierApi by apiServiceLazy<CarrierApi>()

    fun getUserLevelControl(stepId: String, customerId: String?) =
        rxRequest(idmApi.getUserLevelControl(stepId, customerId)).mapToUserLevel<LoadTaskUserLevelConfig>()

    fun loadTaskData(taskId: String?) = rxRequest(loadTaskApi.getLoadTask(taskId))

    fun startTask(taskId: String?) = rxRequest(loadTaskApi.startTask(taskId))

    fun updateStepStatus(taskId: String?, stepId: String?, statusEntry: StepStatusEntry) =
        rxRequest(loadTaskApi.updateStepStatus(taskId, stepId, StepStatusUpdateEntry().apply { status = statusEntry }))

    fun forceCloseStep(taskId: String?, stepId: String?, reason: String?) =
        rxRequest(loadTaskApi.forceCloseStep(taskId, stepId, TaskForceCloseEntry().also { it.reason = reason }))

    fun reopenStep(taskId: String?, stepId: String?) = rxRequest(loadTaskApi.reopenStep(taskId, stepId))

    fun closeTask(taskId: String?) = rxRequest(loadTaskApi.closeTask(taskId))

    fun getCustomer(customerId: String?) = rxRequest(customerApi.getCustomer(customerId))

    fun doOrderLpTemplateCheck(list: List<String>) =
        rxRequest(packTaskApi.orderLpTemplateCheck(OrderIdsRequest().apply { orderIds = list }))

    fun doAutoCc(orderId: String?) = rxRequest(packTaskApi.autoCc(orderId))

    fun dockCheckout(operateEntry: DockOperateEntry) = rxRequest(entryTicketApi.dockCheckout(operateEntry.entryId, operateEntry))

    fun triggerSendCheckoutQRCode(entryId: String) = rxRequest(entryTicketApi.triggerSendCheckoutQRCode(entryId))

    fun updateLoadProNo(loadId: String, proNumber: String) =
        rxRequest(loadWorkApi.updateLoad(loadId, LoadUpdateEntry().apply { proNo = proNumber }))

    fun uploadCountingSheet(loadId: String, list: List<String>?) =
        rxRequest(loadWorkApi.uploadCountingSheet(loadId, LoadWorkEntry().apply { photos = list }))

    fun uploadSealNo(taskId: String, workEntry: LoadWorkEntry) = rxRequest(loadWorkApi.uploadSeal(taskId, workEntry))

    fun updateTruckPhotos(requestEntry: UpdatePhotosRequestEntry) = rxRequest(loadWorkApi.updatePhotos(requestEntry))

    fun batchUpdateOrderProNumber(list: List<OrderBatchUpdateEntry>) = rxRequest(orderApi.batchUpdate(list))

    fun searchQuestionnaireResult(search: QuestionnaireResultSearch) = rxRequest(customerQuestionnaireApi.searchQuestionnaireResult(search))

    fun getCustomerQuestionnaire(customerId: String?) = rxRequest(customerQuestionnaireApi.getQuestionnaireByCustomerId(customerId))

    fun getShortShipOrder(taskId: String) = rxRequest(loadTaskApi.getShortShipOrder(taskId))

    fun searchCarrier(search: CarrierSearchEntry) = rxRequest(carrierApi.search(search))
}