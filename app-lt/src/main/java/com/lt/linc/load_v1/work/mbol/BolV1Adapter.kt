package com.lt.linc.load_v1.work.mbol

import android.view.View
import com.linc.platform.foundation.model.OrderEntry
import com.lt.linc.common.mvvm.kotlin.BaseBindingQuickAdapter
import com.lt.linc.common.mvvm.kotlin.BaseBindingViewHolder
import com.lt.linc.databinding.ItemListLoadBolViewV1Binding

/**
 * <AUTHOR>
 * @Date 2022/11/24
 */
class BolV1Adapter(
    private val onPreviewClick: (OrderEntry) -> Unit, private val onPrintClick: (OrderEntry) -> Unit) :
    BaseBindingQuickAdapter<OrderEntry, ItemListLoadBolViewV1Binding>() {

    override fun convert(helper: BaseBindingViewHolder<ItemListLoadBolViewV1Binding>?, item: OrderEntry) {
        helper?.binding?.apply {
            orderIdTv.text = item.id
            shippingAccountAddressTv.text = item.shipTo?.address1.orEmpty()
            itemTv.text = item.items?.joinToString(" | ") { it.name.orEmpty() }
            printBtn.visibility =
                if (item.carrierSignature.isNullOrEmpty() || item.shipperSignature.isNullOrEmpty()) View.INVISIBLE else View.VISIBLE
            previewBtn.setOnClickListener { onPreviewClick(item) }
            printBtn.setOnClickListener { onPrintClick(item) }
        }
    }
}