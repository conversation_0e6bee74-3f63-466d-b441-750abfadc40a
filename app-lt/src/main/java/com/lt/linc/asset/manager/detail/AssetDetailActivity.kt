package com.lt.linc.asset.manager.detail

import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.view.Menu
import android.view.MenuItem
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintLayout
import com.customer.widget.photo.PhotoBean
import com.customer.widget.photo.PhotoWidgetParam
import com.customer.widget.util.CommUtil
import com.linc.platform.asset.model.AccountingStatusEntry
import com.linc.platform.asset.model.AssetCategoryViewEntry
import com.linc.platform.asset.model.AssetConditionEntry
import com.linc.platform.asset.model.AssetItemViewEntry
import com.linc.platform.asset.model.AssetViewEntry
import com.linc.platform.asset.model.OwnershipEntry
import com.linc.platform.asset.model.OwnershipTypeEntry
import com.linc.platform.asset.model.UsingCompanyEntry
import com.linc.platform.asset.model.getSerializedName
import com.linc.platform.core.PermissionManager
import com.linc.platform.foundation.model.FieldTypeEntry
import com.linc.platform.idm.model.PermissionEntry
import com.linc.platform.print.model.LabelSizeEntry
import com.linc.platform.utils.CollectionUtil
import com.linc.platform.utils.StringUtil
import com.lt.linc.R
import com.lt.linc.common.DataReceiver
import com.lt.linc.common.LabelTextInputView
import com.lt.linc.common.extensions.setGone
import com.lt.linc.common.extensions.setVisible
import com.lt.linc.common.mvi.ReactiveActivity
import com.lt.linc.common.mvi.ReactiveViewScope
import com.lt.linc.common.mvi.onEvent
import com.lt.linc.common.mvvm.kotlin.extensions.UniversalActivityParam
import com.lt.linc.common.mvvm.kotlin.extensions.universalParam
import com.lt.linc.databinding.ActivityAssetDetailBinding
import com.lt.linc.toolset.print.setting.PrintSettingActivity
import com.lt.linc.util.UiUtil
import com.lt.linc.util.v1styledialog.CallbackDialogV1
import com.lt.linc.util.v1styledialog.CenterDialog
import com.lt.linc.util.v1widget.UploadPhotoCallBack
import kotlinx.coroutines.flow.Flow
import java.text.SimpleDateFormat
import java.util.Date

class AssetDetailActivity : ReactiveActivity<AssetDetailViewModel, AssetDetailUiState, ActivityAssetDetailBinding>() {

    private var isEdit:Boolean = false
    data class Param(val assetViewEntry: AssetViewEntry): UniversalActivityParam

    override fun createViewModel(): AssetDetailViewModel {
        val param = universalParam as Param
        return AssetDetailViewModel(AssetDetailDataState(assetViewEntry = param.assetViewEntry))
    }

    override fun onCreateOptionsMenu(menu: Menu?): Boolean {
        menu?.add(Menu.NONE, 1, 0, "")?.setIcon(R.drawable.ic_print_white)?.setShowAsAction(MenuItem.SHOW_AS_ACTION_ALWAYS)
        return super.onCreateOptionsMenu(menu)
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        if (item.itemId == 1) {
            selectLabelSize()
            return true
        }
        return super.onOptionsItemSelected(item)
    }

    override fun initView(savedInstanceState: Bundle?) {
        binding.apply {
            initToolBar(toolbar, R.string.title_asset_detail)
            editSaveBtn.setOnClickListener {
                if (!isEdit) {
                    setEditModel()
                } else {
                    onSave()
                }
            }
            cancelBtn.setOnClickListener {
                setOnlyReadModel()
            }
            if (PermissionManager.getInstance().hasPermission(PermissionEntry.ASSET_ASSET_DETAIL_WRITE)
                    || PermissionManager.getInstance().hasPermission(PermissionEntry.ASSET_ACCOUNTING_INFORMATION_WRITE)) {
                bottomOptionLl.setVisible()
            }
        }
        onSetupPrinterEvent()
        onGotFacilitiesEvent()
        onGotCustomersEvent()
        onGotAddressesEvent()
        onGetCategoryAndAssetItemFailureEvent()
        onAssetUpdatedEvent()
    }

    private fun setOnlyReadModel() {
        binding.apply {
            isEdit = false
            cancelBtn.setGone()
            showEditView(false)
            editSaveBtn.text = getString(R.string.text_edit)
        }
    }

    private fun setEditModel() {
        binding.apply {
            isEdit = true
            cancelBtn.setVisible()
            showEditView(true)
            editSaveBtn.text = getString(R.string.btn_save)
        }
    }

    private fun onSave() {
        viewModel.updateAsset(
                getAssetDetailDataReceiver(),
                getAccountInformationDataReceiver(),
                viewModel.getAssetPhotoIds(),
                viewModel.getAdditionalInventoryDetailDataReceivers()
        )
    }

    private fun getAssetDetailDataReceiver(): Map<String, DataReceiver> {
        val dataReceiverMap = mutableMapOf<String, DataReceiver>()
        binding.apply {
            dataReceiverMap[AssetPropertyKey.FACILITY] = facilityLtiv.getDataReceiver()
            dataReceiverMap[AssetPropertyKey.USING_COMPANY] = usingCompanyLtiv.getDataReceiver()
            dataReceiverMap[AssetPropertyKey.OWNERSHIP_TYPE] = ownershipTypeLtiv.getDataReceiver()
            dataReceiverMap[AssetPropertyKey.USING_CUSTOMER] = usingCustomerLtiv.getDataReceiver()
            dataReceiverMap[AssetPropertyKey.CONDITION] = conditionLtiv.getDataReceiver()
            dataReceiverMap[AssetPropertyKey.LOCATION] = locationLtiv.getDataReceiver()
            dataReceiverMap[AssetPropertyKey.VENDOR] = vendorLtiv.getDataReceiver()
            dataReceiverMap[AssetPropertyKey.PURCHASE_DATE] = purchaseDateLtiv.getDataReceiver()
            dataReceiverMap[AssetPropertyKey.ASSET_DESCRIPTION] = assetDescriptionLtarv.getDataReceiver()
            dataReceiverMap[AssetPropertyKey.NOTES] = notesLtarv.getDataReceiver()
        }
        return dataReceiverMap
    }

    private fun getAccountInformationDataReceiver(): Map<String, DataReceiver> {
        val dataReceiverMap = mutableMapOf<String, DataReceiver>()
        binding.apply {
            dataReceiverMap[AssetPropertyKey.OWNERSHIP] = ownershipLtiv.getDataReceiver()
            dataReceiverMap[AssetPropertyKey.FIXED_ASSET] = fixedAssetLtiv.getDataReceiver()
            dataReceiverMap[AssetPropertyKey.ACCOUNTING_STATUS] = accountingStatusLtiv.getDataReceiver()
            dataReceiverMap[AssetPropertyKey.ACCOUNTING_ID] = accountingIdLtiv.getDataReceiver()
            dataReceiverMap[AssetPropertyKey.COST] = costLtiv.getDataReceiver()
            dataReceiverMap[AssetPropertyKey.PURCHASE_ORDER_NUMBER] = purchaseOrderNumberLtiv.getDataReceiver()
        }
        return dataReceiverMap
    }

    private fun selectLabelSize() {
        val labels = listOf(LabelSizeEntry.FOUR_SIX, LabelSizeEntry.FOUR_TWO)
        val labelTexts = labels.map { StringUtil.getValueOfLpSize(it) }
        CommUtil.showListDialog(this, -1, labelTexts) { which: Int ->
            val selectedLabelSize = labels.getOrNull(which) ?: return@showListDialog
            viewModel.printBarcode(selectedLabelSize, ::showReprintConfirmDialog)
        }
    }

    private fun showReprintConfirmDialog(message: String): Flow<Boolean?> {
        return CallbackDialogV1.showConfirm(
                this, title = getString(R.string.text_print_fail),
                message = getString(R.string.text_please_reprint) + "\r\n" + message
        )
    }

    override fun ReactiveViewScope.subscribeToUiState() {
        showAssetInfo()
        showCategoryInfo()
        showAssetItemInfo()
        showUsingCustomer()
        showCurrentLocation()
    }

    private fun ReactiveViewScope.showAssetInfo() {
        subscribe(AssetDetailUiState::assetViewEntry) {
            it?.let {
                fillAssetInfo(it)
            }
        }
    }

    private fun ReactiveViewScope.showCategoryInfo() {
        subscribe(AssetDetailUiState::categoryViewEntry) {
            it?.let {
                fillCategoryInfo(it)
            }
        }
    }

    private fun ReactiveViewScope.showAssetItemInfo() {
        subscribe(AssetDetailUiState::assetItemViewEntry) {
            it?.let {
                fillAssetItemInfo(it)
            }
        }
    }

    private fun ReactiveViewScope.showUsingCustomer() {
        subscribe(AssetDetailUiState::usingCustomer) {
            binding.apply {
                it?.let { usingCustomer ->
                    val usingCustomerDataReceiver = DataReceiver().apply {
                        this.id = usingCustomer.basic.id
                        this.data = usingCustomer.basic.name
                        this.label = getString(R.string.text_using_customer)
                    }
                    usingCustomerLtiv.setDataReceiver(usingCustomerDataReceiver)
                    usingCustomerLtiv.setText(usingCustomer.basic.name)
                }
                usingCustomerLtiv.setOnSelectListener(object : LabelTextInputView.OnClickListener {
                    override fun onClick() {
                        val customers = viewModel.getCustomers()
                        if (customers.isNullOrEmpty()) {
                            viewModel.searchCustomer()
                            return
                        }
                        val customerOptions = customers.map { v -> v.getName() }
                        usingCustomerLtiv.setOptions(customerOptions)
                        usingCustomerLtiv.showOptionDialog()
                    }
                })
            }
        }
    }

    private fun ReactiveViewScope.showCurrentLocation() {
        subscribe(AssetDetailUiState::selectedAddress) {
            binding.apply {
                it?.let { selectedAddress ->
                    val locationDataReceiver = DataReceiver().apply {
                        this.id = selectedAddress.id
                        this.data = selectedAddress.showName()
                        this.label = getString(R.string.label_location)
                    }
                    locationLtiv.setDataReceiver(locationDataReceiver)
                    locationLtiv.setText(selectedAddress.showName())
                }
                locationLtiv.setOnSelectListener(object : LabelTextInputView.OnClickListener {
                    override fun onClick() {
                        val addresses = viewModel.getAddresses()
                        if (addresses.isNullOrEmpty()) {
                            viewModel.loadAddress()
                            return
                        }
                        val addressOptions = addresses.map { v -> v.showName() }.filter { !it.isNullOrEmpty() }
                        locationLtiv.setOptions(addressOptions)
                        locationLtiv.showOptionDialog()
                    }
                })
            }
        }
    }

    private fun fillAssetInfo(assetViewEntry: AssetViewEntry) {
        binding.apply {
            assetIdTv.text = assetViewEntry.assetId
            fillAssetDetailInfo(assetViewEntry)
            if (PermissionManager.getInstance().hasPermission(PermissionEntry.ASSET_ACCOUNTING_INFORMATION_READ)
                    || PermissionManager.getInstance().hasPermission(PermissionEntry.ASSET_ACCOUNTING_INFORMATION_WRITE)) {
                fillAccountingInfo(assetViewEntry)
            }
            fillAssetPhotoInfo(assetViewEntry)
            fillAdditionalInventoryDetailInfo(assetViewEntry)
        }
    }

    private fun fillAssetDetailInfo(assetViewEntry: AssetViewEntry) {
        binding.apply {
            val facilityDataReceiver = DataReceiver().apply {
                this.id = assetViewEntry.facilityId
                this.data = assetViewEntry.facilityName
                this.label = getString(R.string.label_facility)
            }
            facilityLtiv.setDataReceiver(facilityDataReceiver)
            facilityLtiv.setText(assetViewEntry.facilityName)
            facilityLtiv.setOnSelectListener(object : LabelTextInputView.OnClickListener {
                override fun onClick() {
                    val facilities = viewModel.getFacilities()
                    if (facilities.isNullOrEmpty()) {
                        viewModel.searchFacility()
                        return
                    }
                    val facilityOptions = facilities.map { v -> v.getName() }
                    facilityLtiv.setOptions(facilityOptions)
                    facilityLtiv.showOptionDialog()
                }
            })

            val usingCompanies = UsingCompanyEntry.values().map { it.getSerializedName() }
            usingCompanyLtiv.setText(assetViewEntry.usingCompany.getSerializedName())
            usingCompanyLtiv.setOptions(usingCompanies)

            val ownershipTypes = OwnershipTypeEntry.values().filter { v -> v != OwnershipTypeEntry.UNKNOWN }.map { it.getSerializedName() }
            ownershipTypeLtiv.setText(assetViewEntry.ownershipType.getSerializedName())
            ownershipTypeLtiv.setOptions(ownershipTypes)

            if (TextUtils.isEmpty(assetViewEntry.usingCustomerId)) {
                usingCustomerLtiv.setText("-")
            }

            val conditions = AssetConditionEntry.values().map { it.getSerializedName() }
            conditionLtiv.setText(assetViewEntry.condition.getSerializedName())
            conditionLtiv.setOptions(conditions)

            purchaseDateLtiv.setText(assetViewEntry.purchaseDate.format())
            purchaseDateLtiv.setTypeView(LabelTextInputView.TYPE_DATE)

            if (viewModel.getSelectedAddress() == null) {
                locationLtiv.setText(assetViewEntry.location.formatString())
            }
            
            vendorLtiv.setText(assetViewEntry.vendor.formatString())
            assetDescriptionLtarv.setText(assetViewEntry.description.formatString())
            notesLtarv.setText(assetViewEntry.notes.formatString())
        }
    }

    private fun fillAccountingInfo(assetViewEntry: AssetViewEntry) {
        binding.apply {
            accountingInformationCollapsiblePanel.setVisible()
            val ownerships = OwnershipEntry.values().map { it.getSerializedName() }
            ownershipLtiv.setText(assetViewEntry.ownership.getSerializedName())
            ownershipLtiv.setOptions(ownerships)

            val fixedAssets = listOf("true", "false")
            fixedAssetLtiv.setText(assetViewEntry.fixedAsset.toString())
            fixedAssetLtiv.setOptions(fixedAssets)

            val accountingStatuses = AccountingStatusEntry.values().map { it.getSerializedName() }
            accountingStatusLtiv.setText(assetViewEntry.accountingStatus.getSerializedName())
            accountingStatusLtiv.setOptions(accountingStatuses)

            accountingIdLtiv.setText(assetViewEntry.accountingId.formatString())
            costLtiv.setText(assetViewEntry.cost.formatString())
            purchaseOrderNumberLtiv.setText(assetViewEntry.po.formatString())
        }
    }

    private fun fillAssetPhotoInfo(assetViewEntry: AssetViewEntry) {
        binding.apply {
            assetUploadPhotoWidget.let {
                val imageFileIds = assetViewEntry.imageFileIds
                viewModel.addAssetPhoto(imageFileIds)
                showAssetPhotoEditView(false)
                it.initParamInfo("wms", "asset", "takephoto")
                it.setUploadPhotoCallBack(object : UploadPhotoCallBack {
                    override fun addPhotos(photoId: List<String>) {
                        if (photoId.isEmpty()) return
                        viewModel.addAssetPhoto(photoId)
                    }

                    override fun deletePhotos(list: List<String>?, isRemoveAll: Boolean) {
                        if (list.isNullOrEmpty()) return
                        viewModel.deleteAssetPhotos(list)
                    }
                })
                it.initServerPhotoList(imageFileIds)
            }
        }
    }

    private fun fillAdditionalInventoryDetailInfo(assetViewEntry: AssetViewEntry) {
        binding.apply {
            if (CollectionUtil.isNotNullOrEmpty(assetViewEntry.assetFields)) {
                additionalInventoryDetailCollapsiblePanel.setVisible()
                val additionalInventoryDetailDataReceivers = listOf<DataReceiver>().toMutableList()
                assetViewEntry.assetFields.forEach {
                    if (!it.name.isNullOrEmpty()) {
                        val dataReceiver = DataReceiver().apply {
                            this.id = it.propertyId
                            this.data = it.value
                            this.label = it.name
                            this.type = it.type
                            this.isRequired = it.isRequired
                        }
                        additionalInventoryDetailDataReceivers.add(dataReceiver)
                        val propertyView = generalAdditionalInventoryPropertyView()
                        propertyView.setDataReceiver(dataReceiver)
                        propertyView.setLabelText(it.name!!)
                        propertyView.setText(it.value ?: "-")
                        propertyView.setDisableBackgroundRes(R.drawable.rect_525252_r4)
                        propertyView.setEnable(false)
                        when(it.type) {
                            FieldTypeEntry.TEXT -> {
                                propertyView.setTypeView(LabelTextInputView.TYPE_INPUT)
                            }
                            FieldTypeEntry.SELECT -> {
                                propertyView.setTypeView(LabelTextInputView.TYPE_SELECT)
                                propertyView.setOptions(it.options!!)
                            }
                            FieldTypeEntry.DATE -> {
                                propertyView.setTypeView(LabelTextInputView.TYPE_DATE)
                            }
                            else -> {
                                propertyView.setTypeView(LabelTextInputView.TYPE_INPUT)
                            }
                        }
                        additionalInventoryDetailLayout.addView(propertyView)
                    }
                }
                viewModel.setAdditionalInventoryDetailDataReceivers(additionalInventoryDetailDataReceivers)
            }

        }
    }

    private fun generalAdditionalInventoryPropertyView(): LabelTextInputView {
        val labelTextInputView = LabelTextInputView(this)
        val layoutParams = ConstraintLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
        labelTextInputView.layoutParams = layoutParams
        labelTextInputView.setPadding(UiUtil.dip2px(this, 16f), UiUtil.dip2px(this, 10f), UiUtil.dip2px(this, 16f), 0)
        return labelTextInputView
    }

    private fun fillCategoryInfo(categoryViewEntry: AssetCategoryViewEntry) {
        binding.apply {
            categoryLtiv.setText(categoryViewEntry.name)
        }
    }

    private fun fillAssetItemInfo(assetItemViewEntry: AssetItemViewEntry) {
        binding.apply {
            itemLtiv.setText(assetItemViewEntry.id)
            assetItemLayout.itemNameLtv.setText(assetItemViewEntry.name)
            assetItemLayout.brandLtv.setText(assetItemViewEntry.assetItemStaticProperty.brand)
            assetItemLayout.itemDescriptionLtv.setText(assetItemViewEntry.desc)
            assetItemLayout.lengthLtv.setText(assetItemViewEntry.assetItemStaticProperty.length.formatDouble())
            assetItemLayout.widthLtv.setText(assetItemViewEntry.assetItemStaticProperty.width.formatDouble())
            assetItemLayout.heightLtv.setText(assetItemViewEntry.assetItemStaticProperty.height.formatDouble())
            assetItemLayout.weightLtv.setText(assetItemViewEntry.assetItemStaticProperty.weight.formatDouble())
            val imageFileIds = assetItemViewEntry.assetItemStaticProperty.imageFileIds
            if (!imageFileIds.isNullOrEmpty()) {
                assetItemLayout.assetItemIv.setImageURI(PhotoBean(imageFileIds[0]).getWebFullUrl(PhotoWidgetParam().downloadUrl))
            }
        }
    }

    private fun getAdditionalInventoryDetailViews(): List<LabelTextInputView> {
        binding.apply {
            val childCount = additionalInventoryDetailLayout.childCount
            val viewList = mutableListOf<LabelTextInputView>()
            for (index in 0..childCount) {
                val childView = additionalInventoryDetailLayout.getChildAt(index)
                if (childView is LabelTextInputView) {
                    viewList.add(childView)
                }
            }
            return viewList
        }
    }

    private fun String?.formatDouble(): String {
        if (this.isNullOrEmpty()) {
            return "-"
        }
        return (this.toDoubleOrNull()?: this).toString()
    }

    private fun String?.formatString(): String {
        if (this.isNullOrEmpty()) {
            return "-"
        }
        return this
    }

    private fun Date?.format(): String {
        this?: return "-"
        val sdf = SimpleDateFormat("yyyy-MM-dd")
        return sdf.format(this)
    }

    private fun Double?.formatString(): String {
        this?: return "-"
        return this.toString()
    }

    private fun showEditView(isEdit: Boolean) {
        if (PermissionManager.getInstance().hasPermission(PermissionEntry.ASSET_ASSET_DETAIL_WRITE)) {
            showAssetDetailEditView(isEdit)
            showAssetPhotoEditView(isEdit)
            showAdditionalInventoryDetailEditView(isEdit)
        }
        if (PermissionManager.getInstance().hasPermission(PermissionEntry.ASSET_ACCOUNTING_INFORMATION_WRITE)) {
            showAccountingInformationEditView(isEdit)
        }
    }

    private fun showAssetDetailEditView(isEdit: Boolean) {
        binding.apply {
            facilityLtiv.setEnable(isEdit)
            usingCompanyLtiv.setEnable(isEdit)
            ownershipTypeLtiv.setEnable(isEdit)
            usingCustomerLtiv.setEnable(isEdit)
            conditionLtiv.setEnable(isEdit)
            locationLtiv.setEnable(isEdit)
            vendorLtiv.setEnable(isEdit)
            purchaseDateLtiv.setEnable(isEdit)
            assetDescriptionLtarv.setEnable(isEdit)
            notesLtarv.setEnable(isEdit)
        }
    }

    private fun showAccountingInformationEditView(isEdit: Boolean) {
        binding.apply {
            ownershipLtiv.setEnable(isEdit)
            fixedAssetLtiv.setEnable(isEdit)
            accountingStatusLtiv.setEnable(isEdit)
            accountingIdLtiv.setEnable(isEdit)
            costLtiv.setEnable(isEdit)
            purchaseOrderNumberLtiv.setEnable(isEdit)
        }
    }

    private fun showAssetPhotoEditView(isEdit: Boolean) {
        binding.apply {
            assetUploadPhotoWidget.setOnlyPreview(!isEdit)
        }
    }

    private fun showAdditionalInventoryDetailEditView(isEdit: Boolean) {
        getAdditionalInventoryDetailViews()?.forEach { v -> v.setEnable(isEdit) }
    }

    private fun onGetCategoryAndAssetItemFailureEvent() = onEvent<AssetDetailEvent.GetCategoryAndAssetItemFailure> {
        CenterDialog.alert(
                context = this@AssetDetailActivity,
                message = msg,
                canceledOnTouchOutside = false,
                cancelable = false,
                okClick = {
                    finish()
                }
        ).show()
    }

    private fun onGotFacilitiesEvent() = onEvent<AssetDetailEvent.GotFacilities> {
        binding.apply {
            val facilitiesOptions = facilities.map { v -> v.getName() }
            facilityLtiv.setOptions(facilitiesOptions)
            facilityLtiv.showOptionDialog()
        }
    }

    private fun onGotCustomersEvent() = onEvent<AssetDetailEvent.GotCustomers> {
        binding.apply {
            val customerOptions = customers.map { v -> v.basic.name }
            usingCustomerLtiv.setOptions(customerOptions)
            usingCustomerLtiv.showOptionDialog()
        }
    }

    private fun onGotAddressesEvent() = onEvent<AssetDetailEvent.GotAddresses> {
        binding.apply {
            val addressOptions = addresses.map { v -> v.showName() }
            locationLtiv.setOptions(addressOptions)
            locationLtiv.showOptionDialog()
        }
    }

    private fun onSetupPrinterEvent() = onEvent<AssetDetailEvent.SetupPrinter> {
        startActivity(Intent(this@AssetDetailActivity, PrintSettingActivity::class.java))
    }

    private fun onAssetUpdatedEvent() = onEvent<AssetDetailEvent.AssetUpdated> {
        setOnlyReadModel()
    }
}