package com.lt.linc.asset.task.audit

import android.text.TextUtils
import com.linc.platform.asset.api.AssetAuditTaskApi
import com.linc.platform.asset.model.*
import com.linc.platform.http.ErrorResponse
import com.lt.linc.R
import com.lt.linc.common.AuditDataReceiver
import com.lt.linc.common.mvi.ReactiveViewModel
import com.lt.linc.common.mvi.mapDataToUi
import com.lt.linc.common.mvvm.kotlin.BaseRepository
import com.lt.linc.common.mvvm.kotlin.extensions.*
import com.lt.linc.common.mvvm.kotlin.shoot
import com.lt.linc.util.SnackType

/**
 * @Description:
 * @Author: Dennis
 * @CreateDate: 2024/5/21
 */
class AssetDetailViewModel(
        initialDataState: AssetAuditDataState,
        initialUiState: AssetAuditUiState = AssetAuditUiState()
) : ReactiveViewModel<AssetAuditDataState, AssetAuditUiState>(initialDataState, initialUiState){

    private val repository: Repository = Repository()

    init {
        autoUpdateDataToUi()
    }

    private fun autoUpdateDataToUi() {
        mapDataToUi(AssetAuditDataState::assetForAuditViewEntry, AssetAuditUiState::assetForAuditViewEntry){
            it.shoot()
        }
    }

    fun updateAsset(isInUse: Boolean, note: String) {
        val errMsg = validate(isInUse, note)
        if (!TextUtils.isEmpty(errMsg)) {
            showToast(errMsg!!)
            return
        }

        val assetAuditUpdateEntry = AssetAuditUpdateEntry().apply {
            this.note = note
            this.imageFileIds =  getPhotoIds()
            this.issueType = getIssueType(isInUse)
            this.assetStatus = getAssetStatus(isInUse)
            this.commonPropDetails = getAuditCommonPropDetails(isInUse)
            this.propertyDetails = getAuditPropertyDetails()
        }

        val assetId = getAssetForAuditViewEntry().assetId
        launch {
            requestAwait(repository.auditAsset(getAssetForAuditViewEntry().subTaskId, assetId, assetAuditUpdateEntry)).onSuccess {
                fireEvent { AssetAuditEvent.AssetAudited(assetId, AuditDetailStatusEntry.COMPLETED) }
            }.onFailure {
                if (it is ErrorResponse ) {
                    showSnack(SnackType.ErrorV2(), it.errorMessage)
                }
            }
        }
    }

    fun reportIssue(reportIssueType: AssetReportIssueType, photoIds: List<String>, note: String) {
        val commonPropDetails = dataState.assetForAuditViewEntry.commonPropDetails
        val usingCompany = commonPropDetails?.find { v -> v.propertyId == AssetAuditActivity.USING_COMPANY }
        usingCompany?.toValue = AssetAuditActivity.NOT_IN_USE
        val assetAuditUpdateEntry = AssetAuditUpdateEntry().apply {
            this.note = note
            this.imageFileIds =  photoIds
            this.issueType = AuditIssueTypeEntry.NOT_FOUND
            this.assetStatus = reportIssueType.getSerializedName()
            this.commonPropDetails = if (usingCompany != null) listOf(usingCompany) else listOf()
        }

        val assetId = getAssetForAuditViewEntry().assetId
        launch {
            requestAwait(repository.auditAsset(getAssetForAuditViewEntry().subTaskId, assetId, assetAuditUpdateEntry)).onSuccess {
                fireEvent { AssetAuditEvent.AssetAudited(assetId, AuditDetailStatusEntry.ISSUE_REPORTED) }
            }.onFailure {
                if (it is ErrorResponse ) {
                    showSnack(SnackType.ErrorV2(), it.errorMessage)
                }
            }
        }
    }

    private fun getAuditCommonPropDetails(isInUse: Boolean): List<AuditPropDetailEntry>? {
        return getAssetForAuditViewEntry().commonPropDetails?.let {
            val auditPropertyDataReceivers = getAuditPropertyDataReceivers()
            return it.map { prop ->
                run {
                    val auditDataReceiver = auditPropertyDataReceivers?.find { auditData -> auditData.id == prop.propertyId }
                    if (!isInUse && auditDataReceiver?.id == AssetAuditActivity.USING_COMPANY) {
                        prop.toValue = AssetAuditActivity.NOT_IN_USE
                    } else {
                        prop.toValue = if (auditDataReceiver?.isChecked == true) prop.fromValue else auditDataReceiver?.data ?: ""
                    }
                    prop
                }
            }
        }
    }

    private fun getAuditPropertyDetails(): List<AuditPropDetailEntry>? {
        return getAssetForAuditViewEntry().propertyDetails?.let {
            val auditPropertyDataReceivers = getAuditPropertyDataReceivers()
            return it.map { prop ->
                run {
                    val auditDataReceiver = auditPropertyDataReceivers?.find { auditData -> auditData.id == prop.propertyId }
                    prop.toValue = if (auditDataReceiver?.isChecked == true) prop.fromValue else auditDataReceiver?.data ?: ""
                    prop
                }
            }
        }
    }

    private fun getAssetStatus(isInUse: Boolean): String? {
        val isNewFound = getAssetForAuditViewEntry().issueType == AuditIssueTypeEntry.NEW_FOUND
        if (isNewFound) {
            return if (isInUse) AuditAssetStatusEntry.NEW_FOUND.getSerializedName() else getNoInUseReason()?.getSerializedName()
        }
        return if (isInUse) AuditAssetStatusEntry.ACTIVE.getSerializedName() else getNoInUseReason()?.getSerializedName()
    }

    private fun getIssueType(isInUse: Boolean): AuditIssueTypeEntry {
        val isNewFound = getAssetForAuditViewEntry().issueType == AuditIssueTypeEntry.NEW_FOUND
        if (isNewFound) {
            return if (isInUse) AuditIssueTypeEntry.NEW_FOUND else AuditIssueTypeEntry.NEW_FOUND_NIU
        }
        return if (isInUse) AuditIssueTypeEntry.NO_ISSUE else AuditIssueTypeEntry.NOT_IN_USE
    }

    private fun validate(isInUse: Boolean, note: String): String? {
        val auditPropertyDataReceivers = getAuditPropertyDataReceivers()
        auditPropertyDataReceivers?.let {
            for (dataReceiver in it) {
                if (!isInUse && dataReceiver.isChecked == false && dataReceiver.id == AssetAuditActivity.USING_COMPANY) {
                    continue
                }
                if ((dataReceiver.isChecked == true && dataReceiver.oriData.isNullOrEmpty()) || 
                    (dataReceiver.isChecked == false && dataReceiver.data.isNullOrEmpty())) {
                    return String.format(getString(R.string.msg_please_input_select_current_value), dataReceiver.label ?: "")
                }
            }
        }
        if (!isInUse) {
            if (getNoInUseReason() == null) {
                return getString(R.string.msg_please_select_an_reason)
            }
            if (TextUtils.isEmpty(note)) {
                return getString(R.string.msg_please_input_note)
            }
        }
        if (getPhotoIds().isNullOrEmpty()) {
            return getString(R.string.hint_please_take_photo)
        }
        return null
    }

    fun addAssetPhoto(photoId: String) {
        val toMutableList = getPhotoIds().toMutableList()
        toMutableList.add(photoId)
        setDataState { copy(photoIds = toMutableList) }
    }

    fun deleteAssetPhotos(photoIds: List<String>) {
        val toMutableList = getPhotoIds().toMutableList()
        toMutableList.removeAll(photoIds)
        setDataState { copy(photoIds = toMutableList) }
    }

    fun saveNoInUseReason(noInUseReason: AssetNoInUseReasonEntry) {
        setDataState { copy(noInUseReason = noInUseReason) }
    }

    fun setAuditPropertyDataReceivers(auditPropertyDataReceivers: List<AuditDataReceiver>) {
        setDataState { copy(auditPropertyDataReceivers = auditPropertyDataReceivers) }
    }

    fun getNoInUseReason() = dataState.noInUseReason

    fun getAssetForAuditViewEntry() = dataState.assetForAuditViewEntry

    fun getAuditPropertyDataReceivers() = dataState.auditPropertyDataReceivers

    fun getPhotoIds() = dataState.photoIds

}

private class Repository : BaseRepository() {
    private val assetAuditTaskApi by apiServiceLazy<AssetAuditTaskApi>()

    fun auditAsset(taskId: String, assetId: String, entry: AssetAuditUpdateEntry) = rxRequest(assetAuditTaskApi.auditAsset(taskId, assetId, entry))

}