package com.lt.linc.asset.manager.detail

import android.text.TextUtils
import com.linc.platform.asset.api.AssetApi
import com.linc.platform.asset.api.AssetReceiveApi
import com.linc.platform.asset.model.AddressSearchEntry
import com.linc.platform.asset.model.AddressType
import com.linc.platform.asset.model.AssetFieldEntry
import com.linc.platform.asset.model.AssetUpdateEntry
import com.linc.platform.asset.model.getAccountingStatusEntry
import com.linc.platform.asset.model.getAssetConditionEntry
import com.linc.platform.asset.model.getOwnershipEntry
import com.linc.platform.asset.model.getOwnershipTypeEntry
import com.linc.platform.asset.model.getUsingCompanyEntry
import com.linc.platform.common.lp.ScenarioEntry
import com.linc.platform.core.PermissionManager
import com.linc.platform.foundation.api.FacilityApi
import com.linc.platform.foundation.api.OrganizationApi
import com.linc.platform.foundation.model.FieldTypeEntry
import com.linc.platform.foundation.model.OrganizationStatusEntry
import com.linc.platform.foundation.model.organization.common.base.OrganizationSearchEntry
import com.linc.platform.foundation.model.organization.common.facility.FacilitySearchEntry
import com.linc.platform.foundation.model.organization.common.facility.FacilityTypeEntry
import com.linc.platform.http.IdsRequest
import com.linc.platform.idm.model.PermissionEntry
import com.linc.platform.print.commonprintlp.PrintData
import com.linc.platform.print.commonprintlp.PrintMsg
import com.linc.platform.print.commonprintlp.PrintResult
import com.linc.platform.print.model.LabelSizeEntry
import com.linc.platform.utils.PrintUtil
import com.lt.linc.R
import com.lt.linc.common.DataReceiver
import com.lt.linc.common.mvi.ReactiveViewModel
import com.lt.linc.common.mvi.mapDataToUi
import com.lt.linc.common.mvvm.kotlin.BaseRepository
import com.lt.linc.common.mvvm.kotlin.extensions.apiServiceLazy
import com.lt.linc.common.mvvm.kotlin.extensions.facilityEntry
import com.lt.linc.common.mvvm.kotlin.extensions.getString
import com.lt.linc.common.mvvm.kotlin.extensions.idmUserId
import com.lt.linc.common.mvvm.kotlin.extensions.launch
import com.lt.linc.common.mvvm.kotlin.extensions.requestAllAwait
import com.lt.linc.common.mvvm.kotlin.extensions.showLoading
import com.lt.linc.common.mvvm.kotlin.extensions.showSnack
import com.lt.linc.common.mvvm.kotlin.extensions.showToast
import com.lt.linc.util.SnackType
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.firstOrNull
import java.text.ParseException
import java.text.SimpleDateFormat
import java.util.Date

class AssetDetailViewModel(
        initialDataState: AssetDetailDataState,
        initialUiState: AssetDetailUiState = AssetDetailUiState()
) : ReactiveViewModel<AssetDetailDataState, AssetDetailUiState>(initialDataState, initialUiState) {

    private val repository: Repository = Repository()
    private val printUtil = PrintUtil.newInstance()

    init {
        autoUpdateDataToUi()
        getCategoryAndAssetItem()
        loadUsingCustomer()
        loadAddress(true)
    }

    private fun autoUpdateDataToUi() {
        mapDataToUi(AssetDetailDataState::assetViewEntry, AssetDetailUiState::assetViewEntry) {
            it
        }
        mapDataToUi(AssetDetailDataState::assetItemViewEntry, AssetDetailUiState::assetItemViewEntry) {
            it
        }
        mapDataToUi(AssetDetailDataState::categoryViewEntry, AssetDetailUiState::categoryViewEntry) {
            it
        }
        mapDataToUi(AssetDetailDataState::usingCustomer, AssetDetailUiState::usingCustomer) {
            it
        }
        mapDataToUi(AssetDetailDataState::selectedAddress, AssetDetailUiState::selectedAddress) {
            it
        }
    }

    private fun getCategoryAndAssetItem() {
        val assetView = getAssetView()
        val categoryId = assetView.categoryId
        val itemSpecId = assetView.itemSpecId
        launch {
            val (categoryResponse, assetItemResponse) = requestAllAwait(
                    repository.getCategory(categoryId),
                    repository.getAssetItem(itemSpecId)
            )
            val categoryViewEntry = categoryResponse.getOrNull()
            val assetItemViewEntry = assetItemResponse.getOrNull()
            if (categoryViewEntry == null) {
                fireEvent { AssetDetailEvent.GetCategoryAndAssetItemFailure(getString(R.string.msg_the_asset_category_or_item_no_found_please_search_other_asset)) }
                return@launch
            }
            if (assetItemViewEntry == null) {
                fireEvent { AssetDetailEvent.GetCategoryAndAssetItemFailure(getString(R.string.msg_the_asset_category_or_item_no_found_please_search_other_asset)) }
                return@launch
            }
            setDataState { copy(categoryViewEntry = categoryViewEntry, assetItemViewEntry = assetItemViewEntry) }
        }
    }

    private fun loadUsingCustomer() {
        val usingCustomerId = getAssetView().usingCustomerId
        if (!TextUtils.isEmpty(usingCustomerId)) {
            launch {
                requestAwait(repository.getUsingCustomer(usingCustomerId)).onSuccess {
                    it?.let {
                        setDataState { copy(usingCustomer = it) }
                    }
                }
            }
        }
    }

    fun searchFacility() {
        launch {
            requestAwait(repository.searchFacility()).onSuccess {
                it?.let {
                   it.sortBy { v -> v.getName().uppercase() }
                    setDataState { copy(facilities = it) }
                    fireEvent { AssetDetailEvent.GotFacilities(it) }
                }
            }
        }
    }

    fun searchCustomer() {
        launch {
            requestAwait(repository.searchCustomer()).onSuccess {
                it?.let {
                    it.sortBy { v -> v.getName().uppercase() }
                    setDataState { copy(customers = it) }
                    fireEvent { AssetDetailEvent.GotCustomers(it) }
                }
            }
        }
    }

    fun loadAddress(isInit: Boolean = false) {
        launch {
            val searchEntry = AddressSearchEntry(excludeTypes = listOf(AddressType.STORE))
            requestAwait(repository.searchAddresses(searchEntry)).onSuccess {
                it?.let { addresses ->
                    if (isInit) {
                        val currentLocation = getAssetView().location
                        val selectedAddress = addresses.find { address -> address.name == currentLocation }
                        setDataState { copy(addresses = addresses, selectedAddress = selectedAddress) }
                    } else {
                        setDataState { copy(addresses = addresses) }
                        fireEvent { AssetDetailEvent.GotAddresses(addresses) }
                    }
                }
            }
        }
    }

    fun updateAsset(
            assetDetailDataReceiver: Map<String, DataReceiver>,
            accountInformationDataReceiver: Map<String, DataReceiver>,
            assetPhotoIds: List<String>,
            additionalInventoryDetailDataReceivers: List<DataReceiver>?
    ) {
        val isPass = validate(assetDetailDataReceiver.values.toList(), accountInformationDataReceiver.values.toList(), assetPhotoIds, additionalInventoryDetailDataReceivers)
        if (!isPass) {
            return
        }
        val assetView = getAssetView()
        val assetUpdateEntry = AssetUpdateEntry().apply {
            // Asset detail
            if (PermissionManager.getInstance().hasPermission(PermissionEntry.ASSET_ASSET_DETAIL_WRITE)) {
                this.itemSpecId = getAssetItemView()?.id
                this.categoryId = getCategoryView()?.id
                this.facilityId = getFacilityIdByName(assetDetailDataReceiver[AssetPropertyKey.FACILITY]?.data)
                this.usingCompany = assetDetailDataReceiver[AssetPropertyKey.USING_COMPANY]?.data.getUsingCompanyEntry()
                this.ownershipType = assetDetailDataReceiver[AssetPropertyKey.OWNERSHIP_TYPE]?.data.getOwnershipTypeEntry()
                this.usingCustomerId = getUsingCustomerIdByName(assetDetailDataReceiver[AssetPropertyKey.USING_CUSTOMER]?.data)
                this.condition = assetDetailDataReceiver[AssetPropertyKey.CONDITION]?.data.getAssetConditionEntry()
                this.location = getAddressNameByShowName(assetDetailDataReceiver[AssetPropertyKey.LOCATION]?.data)
                this.vendor = assetDetailDataReceiver[AssetPropertyKey.VENDOR]?.data.trimDash()
                this.purchaseDate = assetDetailDataReceiver[AssetPropertyKey.PURCHASE_DATE]?.data.toDate()
                this.description = assetDetailDataReceiver[AssetPropertyKey.ASSET_DESCRIPTION]?.data.trimDash()
                this.notes = assetDetailDataReceiver[AssetPropertyKey.NOTES]?.data.trimDash()
            }

            // accounting information
            if (PermissionManager.getInstance().hasPermission(PermissionEntry.ASSET_ACCOUNTING_INFORMATION_WRITE)) {
                this.ownership = accountInformationDataReceiver[AssetPropertyKey.OWNERSHIP]?.data.getOwnershipEntry()
                this.fixedAsset = accountInformationDataReceiver[AssetPropertyKey.FIXED_ASSET]?.data.toBoolean()
                this.accountingStatus = accountInformationDataReceiver[AssetPropertyKey.ACCOUNTING_STATUS]?.data.getAccountingStatusEntry()
                this.accountingId = accountInformationDataReceiver[AssetPropertyKey.ACCOUNTING_ID]?.data.trimDash()
                this.cost = accountInformationDataReceiver[AssetPropertyKey.COST]?.data.trimDash()?.toDoubleOrNull()
                this.po = accountInformationDataReceiver[AssetPropertyKey.PURCHASE_ORDER_NUMBER]?.data.trimDash()
            }

            if (PermissionManager.getInstance().hasPermission(PermissionEntry.ASSET_ASSET_DETAIL_WRITE)) {
                // Asset photo
                this.imageFileIds = assetPhotoIds
                // additional inventory detail
                this.assetFields = generateAssetFields(additionalInventoryDetailDataReceivers)
            }
        }
        launch {
            requestAwait(repository.updateAsset(assetView.assetId, assetUpdateEntry)).onSuccess {
                fireEvent { AssetDetailEvent.AssetUpdated }
                showSnack(SnackType.SuccessV1(), String.format(getString(R.string.msg_asset_has_been_update), it?.id))
            }
        }
    }

    private fun getUsingCustomerIdByName(usingCustomerName: String?): String? {
        usingCustomerName?: return null
        if (usingCustomerName == getUsingCustomer()?.basic?.name) {// usingCustomer no change
            return getUsingCustomer()?.basic?.id
        }
        // usingCustomer change
        return getCustomers()?.find {
            it.basic.name == usingCustomerName
        }?.basic?.id
    }

    private fun getFacilityIdByName(facilityName: String?): String? {
        facilityName?: return null
        if (facilityName == getAssetView().facilityName) {// facility no change
            return getAssetView().facilityId
        }
        // facility change
        return getFacilities()?.find {
            it.name == facilityName
        }?.id
    }

    private fun getAddressNameByShowName(showName: String?): String? {
        if (showName.isNullOrEmpty()) {
            return null
        }
        return getAddresses()?.find {
            it.showName() == showName
        }?.name
    }

    private fun String?.toDate(): Date? {
        this?: return null
        val sdf = SimpleDateFormat("yyyy-MM-dd")
        return try {
            sdf.parse(this)
        } catch (e: ParseException) {
            e.printStackTrace()
            return null
        }
    }

    private fun generateAssetFields(additionalInventoryDetailDataReceivers: List<DataReceiver>?): List<AssetFieldEntry>? {
        additionalInventoryDetailDataReceivers?.let {
            return it.map { v ->
                AssetFieldEntry().apply {
                    this.propertyId = v.id
                    this.name = v.label
                    this.value = v.data.trimDash()
                }
            }
        }
        return null
    }

    private fun validate(
            assetDetailDataReceiver: List<DataReceiver>,
            accountInformationDataReceiver: List<DataReceiver>,
            assetPhotoIds: List<String>,
            additionalInventoryDetailDataReceivers: List<DataReceiver>?
    ): Boolean {
        if (PermissionManager.getInstance().hasPermission(PermissionEntry.ASSET_ASSET_DETAIL_WRITE)) {
            // Asset detail
            assetDetailDataReceiver.let {
                for (dataReceiver in it.iterator()) {
                    if (dataReceiver.data.trimDash().isNullOrEmpty()) {
                        val inputMsg = String.format(getString(R.string.msg_please_input_xxx), dataReceiver.label)
                        val selectMsg = String.format(getString(R.string.msg_please_select_xxx), dataReceiver.label)
                        showToast( if (dataReceiver.type == FieldTypeEntry.TEXT || dataReceiver.type == FieldTypeEntry.NUMBER) inputMsg else selectMsg)
                        return false
                    }
                }
            }
        }
        if (PermissionManager.getInstance().hasPermission(PermissionEntry.ASSET_ACCOUNTING_INFORMATION_WRITE)) {
            // accounting information
            accountInformationDataReceiver.let {
                for (dataReceiver in it.iterator()) {
                    if (dataReceiver.data.trimDash().isNullOrEmpty()) {
                        val inputMsg = String.format(getString(R.string.msg_please_input_xxx), dataReceiver.label)
                        val selectMsg = String.format(getString(R.string.msg_please_select_xxx), dataReceiver.label)
                        showToast( if (dataReceiver.type == FieldTypeEntry.TEXT || dataReceiver.type == FieldTypeEntry.NUMBER) inputMsg else selectMsg)
                        return false
                    }
                }
            }
        }
        if (PermissionManager.getInstance().hasPermission(PermissionEntry.ASSET_ASSET_DETAIL_WRITE)) {
            // Asset photo
            if (assetPhotoIds.isNullOrEmpty()) {
                showToast(R.string.hint_please_take_photo)
                return false
            }
            if (!getAssetView().assetFields.isNullOrEmpty()) {
                // additional inventory detail
                additionalInventoryDetailDataReceivers?.let {
                    for (dataReceiver in it.iterator()) {
                        if (dataReceiver.isRequired == true && dataReceiver.data.trimDash().isNullOrEmpty()) {
                            val inputMsg = String.format(getString(R.string.msg_please_input_xxx), dataReceiver.label)
                            val selectMsg = String.format(getString(R.string.msg_please_select_xxx), dataReceiver.label)
                            showToast( if (dataReceiver.type == FieldTypeEntry.TEXT || dataReceiver.type == FieldTypeEntry.NUMBER) inputMsg else selectMsg)
                            return false
                        }
                    }
                }
            }
        }
        return true
    }

    private fun String?.trimDash(): String? {
        this?: return null
        var str = this.trim()
        if (str.startsWith("-")) {
            str = str.substring(1, str.length)
        }
        if (str.endsWith("-")) {
            str = str.substring(0, str.length-1)
        }
        return str
    }

    fun printBarcode(labelSize: LabelSizeEntry, onReprintConfirm: (message: String) -> Flow<Boolean?>) {
        val assetId = getAssetView().assetId
        val printData = repository.getPrintData(labelSize)
        if (!PrintUtil.hasPrinter(printData)) {
            fireEvent { AssetDetailEvent.SetupPrinter }
            return
        }
        launch {
            val request = if (labelSize == LabelSizeEntry.FOUR_TWO) {
                repository.get4x2PrintZplCode(assetId)
            } else {
                repository.get4x6PrintZplCode(assetId)
            }
            requestAwait(request).onSuccess {
                it?.let {
                    printBarcode(it[0].zplCode, printData, onReprintConfirm)
                }
            }
        }
    }

    private suspend fun printBarcode(
            zplCode: String,
            printData: PrintData,
            onReprintConfirm: (message: String) -> Flow<Boolean?>,
    ): Result<Boolean> {
        printData.jobData = PrintData.JobData.ZPL(printCommands = zplCode)

        val result = printUtil.printWithFlow(printData, onShowProgress = { showLoading(it) }).firstOrNull()
        return when (result) {
            null -> Result.success(false)
            PrintResult.NoPrinter -> {
                fireEvent { AssetDetailEvent.SetupPrinter }
                Result.success(false)
            }
            is PrintResult.Success -> Result.success(true)
            is PrintResult.Failed -> {
                val reprint = onReprintConfirm(PrintMsg.formatError(result.printerEntry, result.response.errorMessage)).firstOrNull()
                if (reprint == true) {
                    printBarcode(zplCode, printData, onReprintConfirm)
                } else {
                    Result.success(false)
                }
            }
        }
    }

    fun getAssetView() = dataState.assetViewEntry

    fun getCategoryView() = dataState.categoryViewEntry

    fun getAssetItemView() = dataState.assetItemViewEntry

    fun setAdditionalInventoryDetailDataReceivers(additionalInventoryDetailDataReceivers: List<DataReceiver>) {
        setDataState { copy(additionalInventoryDetailDataReceivers = additionalInventoryDetailDataReceivers) }
    }

    fun getAdditionalInventoryDetailDataReceivers() = dataState.additionalInventoryDetailDataReceivers

    fun addAssetPhoto(photoIds: List<String>) {
        val toMutableList = getAssetPhotoIds().toMutableList()
        toMutableList.addAll(photoIds)
        setDataState { copy(assetPhotoIds = toMutableList) }
    }

    fun deleteAssetPhotos(photoIds: List<String>) {
        val toMutableList = getAssetPhotoIds().toMutableList()
        toMutableList.removeAll(photoIds)
        setDataState { copy(assetPhotoIds = toMutableList) }
    }

    fun getAssetPhotoIds() = dataState.assetPhotoIds

    fun getFacilities() = dataState.facilities

    fun getCustomers() = dataState.customers

    fun getAddresses() = dataState.addresses

    fun getUsingCustomer() = dataState.usingCustomer

    fun getSelectedAddress() = dataState.selectedAddress
}

private class Repository : BaseRepository() {
    private val assetReceiveApi by apiServiceLazy<AssetReceiveApi>()
    private val assetApi by apiServiceLazy<AssetApi>()
    private val facilityApi by apiServiceLazy<FacilityApi>()
    private val organizationApi by apiServiceLazy<OrganizationApi>()

    fun getPrintData(labelSize: LabelSizeEntry): PrintData {
        val facility = facilityEntry
        return PrintData.basic(idmUserId, facility!!.getName(), labelSize)
    }

    fun get4x2PrintZplCode(assetId: String) = rxRequest(assetReceiveApi.get4x2PrintZplCode(IdsRequest().apply {
        ids = listOf(assetId)
    }))

    fun get4x6PrintZplCode(assetId: String) = rxRequest(assetReceiveApi.get4x6PrintZplCode(IdsRequest().apply {
        ids = listOf(assetId)
    }))

    fun getCategory(categoryId: String) = rxRequest(assetApi.getCategory(categoryId))

    fun getAssetItem(itemId: String) = rxRequest(assetApi.getAssetItem(itemId))

    fun searchFacility() = rxRequest(facilityApi.search(FacilitySearchEntry().apply {
        this.excludeTypes = listOf(FacilityTypeEntry.VIRTUAL)
    }))

    fun getUsingCustomer(usingCustomerId: String) = rxRequest(organizationApi.get(usingCustomerId))

    fun searchCustomer() = rxRequest(organizationApi.search(OrganizationSearchEntry().apply {
        this.searchScenario = ScenarioEntry.AUTO_COMPLETE
        this.scenario = ScenarioEntry.AUTO_COMPLETE
        this.status = OrganizationStatusEntry.ACTIVE
    }))

    fun searchAddresses(searchEntry: AddressSearchEntry) = rxRequest(assetApi.searchAddresses(searchEntry))

    fun updateAsset(assetId: String, assetUpdateEntry: AssetUpdateEntry) = rxRequest(assetReceiveApi.updateAsset(assetId, assetUpdateEntry))
}