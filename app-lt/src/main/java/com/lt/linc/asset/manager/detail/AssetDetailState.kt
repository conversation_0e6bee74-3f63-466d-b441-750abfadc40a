package com.lt.linc.asset.manager.detail

import com.linc.platform.asset.model.AddressEntry
import com.linc.platform.asset.model.AssetCategoryViewEntry
import com.linc.platform.asset.model.AssetItemViewEntry
import com.linc.platform.asset.model.AssetViewEntry
import com.linc.platform.foundation.model.organization.common.base.OrganizationViewEntry
import com.linc.platform.foundation.model.organization.common.facility.FacilityEntry
import com.lt.linc.common.DataReceiver
import com.lt.linc.common.mvi.ReactiveDataState
import com.lt.linc.common.mvi.ReactiveUiState
import com.lt.linc.common.mvi.UiEvent

data class AssetDetailDataState(
        val assetViewEntry: AssetViewEntry,
        val categoryViewEntry: AssetCategoryViewEntry? = null,
        val assetItemViewEntry: AssetItemViewEntry? = null,
        val additionalInventoryDetailDataReceivers: List<DataReceiver>? = null,
        val assetPhotoIds: List<String> = listOf(),
        val usingCustomer: OrganizationViewEntry? = null,
        val facilities: List<FacilityEntry>? = null,
        val customers: List<OrganizationViewEntry>? = null,
        val addresses: List<AddressEntry>? = null,
        val selectedAddress: AddressEntry? = null,
) : ReactiveDataState {

}

data class AssetDetailUiState(
        val assetViewEntry: AssetViewEntry? = null,
        val categoryViewEntry: AssetCategoryViewEntry? = null,
        val assetItemViewEntry: AssetItemViewEntry? = null,
        val usingCustomer: OrganizationViewEntry? = null,
        val selectedAddress: AddressEntry? = null,
) : ReactiveUiState {

}

interface AssetDetailEvent {

    object SetupPrinter : UiEvent
    object AssetUpdated : UiEvent
    data class GetCategoryAndAssetItemFailure(val msg: String) : UiEvent
    data class GotFacilities(val facilities: List<FacilityEntry>) : UiEvent
    data class GotCustomers(val customers: List<OrganizationViewEntry>) : UiEvent
    data class GotAddresses(val addresses: List<AddressEntry>) : UiEvent
}