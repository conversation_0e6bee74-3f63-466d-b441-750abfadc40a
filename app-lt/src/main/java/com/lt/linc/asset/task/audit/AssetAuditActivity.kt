package com.lt.linc.asset.task.audit

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintLayout
import com.linc.platform.asset.model.AssetForAuditViewEntry
import com.linc.platform.asset.model.AssetNoInUseReasonEntry
import com.linc.platform.asset.model.AuditDetailStatusEntry
import com.linc.platform.asset.model.AuditIssueTypeEntry
import com.linc.platform.asset.model.getSerializedName
import com.linc.platform.foundation.model.FieldTypeEntry
import com.lt.linc.R
import com.lt.linc.asset.task.tasklist.AssetAuditReportIssueDialog
import com.lt.linc.common.AuditDataReceiver
import com.lt.linc.common.AuditView
import com.lt.linc.common.Constant
import com.lt.linc.common.extensions.addToNewList
import com.lt.linc.common.extensions.setGone
import com.lt.linc.common.extensions.setVisible
import com.lt.linc.common.extensions.setVisibleOrGone
import com.lt.linc.common.mvi.ReactiveActivity
import com.lt.linc.common.mvi.ReactiveViewScope
import com.lt.linc.common.mvi.onEvent
import com.lt.linc.common.mvvm.kotlin.extensions.UniversalActivityParam
import com.lt.linc.common.mvvm.kotlin.extensions.universalParam
import com.lt.linc.databinding.ActivityAssetAuditBinding
import com.lt.linc.util.SelectDialog
import com.lt.linc.util.UiUtil
import com.lt.linc.util.v1widget.UploadPhotoCallBack

class AssetAuditActivity : ReactiveActivity<AssetDetailViewModel, AssetAuditUiState, ActivityAssetAuditBinding>() {

    data class Param(val assetForAuditViewEntry: AssetForAuditViewEntry): UniversalActivityParam

    companion object {
        const val USING_COMPANY =  "USING_COMPANY"
        const val NOT_IN_USE =  "Not in use"
    }

    override fun createViewModel(): AssetDetailViewModel {
        val param = universalParam as Param
        return AssetDetailViewModel(AssetAuditDataState(assetForAuditViewEntry = param.assetForAuditViewEntry))
    }

    override fun initView(savedInstanceState: Bundle?) {
        binding.apply {
            initToolBar(toolbar, getString(R.string.title_asset_detail))
            uploadPhotoWidget.let {
                it.initParamInfo("wms", "asset", "takephoto")
                it.setUploadPhotoCallBack(object : UploadPhotoCallBack {
                    override fun addPhotos(photoId: List<String>) {
                        if (photoId.isEmpty()) return
                        viewModel.addAssetPhoto(photoId[0])
                    }

                    override fun deletePhotos(list: List<String>?, isRemoveAll: Boolean) {
                        if (list.isNullOrEmpty()) return
                        viewModel.deleteAssetPhotos(list)
                    }
                })
                it.initServerPhotoList(viewModel.getPhotoIds())
            }
            yesRbtn.setOnCheckedChangeListener { _, isChecked ->
                run {
                    reasonNoteLl.setVisibleOrGone(!isChecked)
                    setUsingCompany(isChecked)
                }
            }
            selectReasonTv.setOnClickListener { showNoInUseReasonDialog() }
            updateBtn.setOnClickListener { onUpdateAsset() }
            reportIssueIv.setOnClickListener { showReportIssueDialog() }
        }
        onAssetAuditedEvent()
    }

    private fun setUsingCompany(isInUse: Boolean) {
        if (!isInUse) {
            getUsingCompanyAuditView()?.let {
                val auditDataReceiver = it.getAuditDataReceiver()
                if (!TextUtils.isEmpty(auditDataReceiver.id)) {
                    it.setAuditText(auditDataReceiver.id!!, "")
                }
            }
        }
    }

    private fun getUsingCompanyAuditView(): AuditView? {
        binding.apply {
            val childCount = auditCheckLl.childCount
            for (index in 0..childCount) {
                if (auditCheckLl.getChildAt(index) is AuditView) {
                    val auditView = auditCheckLl.getChildAt(index) as AuditView
                    val auditDataReceiver = auditView.getAuditDataReceiver()
                    if (auditDataReceiver.id == USING_COMPANY) {
                        return auditView
                    }
                }
            }
        }
        return null
    }

    private fun onUpdateAsset() {
        binding.apply {
            val isInUse = yesRbtn.isChecked
            val note = noteEdit.text.toString().trim()
            viewModel.updateAsset(isInUse, note)
        }
    }

    private fun showReportIssueDialog() {
        AssetAuditReportIssueDialog(this) { reportIssueType, photoIds, note ->
            viewModel.reportIssue(reportIssueType, photoIds, note)
        }.show()
    }

    private fun showNoInUseReasonDialog() {
        binding.apply {
            val selectedOption = selectReasonTv.text.toString().trim()
            val noInUseReasons = AssetNoInUseReasonEntry.values()
            SelectDialog(
                    title = getString(R.string.hint_select_an_reason),
                    selectedOption = selectedOption,
                    options = noInUseReasons.map { it.getSerializedName() }
            ) { position, selectedReason ->
                selectReasonTv.text = selectedReason
                selectReasonTv.setTextColor(getColor(R.color.white))
                viewModel.saveNoInUseReason(noInUseReasons[position])
            }.show(supportFragmentManager, "SelectDialog")
        }
    }

    override fun ReactiveViewScope.subscribeToUiState() {
        showAssetAuditInfo()
    }

    private fun ReactiveViewScope.showAssetAuditInfo() {
        subscribe(AssetAuditUiState::assetForAuditViewEntry) {
            it?.tryGet { assetForAuditViewEntry ->
                showAssetInfo(assetForAuditViewEntry)
            }
        }
    }

    private fun onAssetAuditedEvent() = onEvent<AssetAuditEvent.AssetAudited> {
        val data = Intent()
        data.putExtra(Constant.INTENT_ASSET_ID, assetId)
        data.putExtra(AuditDetailStatusEntry.TAG, auditDetailStatusEntry)
        setResult(Activity.RESULT_OK, data)
        finish()
    }

    private fun showAssetInfo(assetForAuditViewEntry: AssetForAuditViewEntry) {
        binding.apply {
            assetIdTv.text = assetForAuditViewEntry.assetId
            if (assetForAuditViewEntry.issueType == AuditIssueTypeEntry.NEW_FOUND) {
                notRecordLl.setVisible()
                facilityAud.setVisible()
                facilityAud.setOriText(assetForAuditViewEntry.assetFacilityName)
                facilityAud.setAuditText(assetForAuditViewEntry.facilityId, assetForAuditViewEntry.facilityName)
                facilityAud.setEnable(false)
                reportIssueIv.setGone()
            }
            categoryLtv.setText(assetForAuditViewEntry.categoryName)
            itemLtv.setText(assetForAuditViewEntry.itemName?:"")
            if (assetForAuditViewEntry.imageFileIds.isNotEmpty()) {
                uploadPhotoWidget.initServerPhotoList(assetForAuditViewEntry.imageFileIds)
            }
            auditCheckLl.removeAllViews()
            val combinePropertyDetails = assetForAuditViewEntry.commonPropDetails.addToNewList(assetForAuditViewEntry.propertyDetails)
            val auditPropertyDataReceivers = listOf<AuditDataReceiver>().toMutableList()
            combinePropertyDetails?.forEach {
                val auditDataReceiver = AuditDataReceiver().apply {
                    this.id = it.propertyId
                    this.label = it.name
                    this.oriData = it.fromValue
                }
                auditPropertyDataReceivers.add(auditDataReceiver)
                val propertyView = generalPropertyView()
                propertyView.setLabelText(it.name)
                propertyView.setOriText(it.fromValue)
                propertyView.setAuditDataReceiver(auditDataReceiver)
                if (it.type == FieldTypeEntry.SELECT) {
                    propertyView.setAuditType(AuditView.AUDIT_TYPE_SELECT)
                    propertyView.setSpinnerOptions(it.options)
                }
                propertyView.setHintText("Current ${it.name}")
                auditCheckLl.addView(propertyView)
            }
            viewModel.setAuditPropertyDataReceivers(auditPropertyDataReceivers)
        }
    }

    private fun generalPropertyView(): AuditView {
        val auditView = AuditView(this)
        val layoutParams = ConstraintLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
        auditView.layoutParams = layoutParams
        auditView.setPadding(0, UiUtil.dip2px(this, 8f), 0, 0)
        return auditView
    }

}