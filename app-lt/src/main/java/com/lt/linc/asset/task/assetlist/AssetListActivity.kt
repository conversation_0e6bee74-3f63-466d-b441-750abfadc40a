package com.lt.linc.asset.task.assetlist

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import androidx.recyclerview.widget.LinearLayoutManager
import com.customer.widget.RecyclerViewItemSpace
import com.customer.widget.core.ActivityBundleHolder
import com.linc.platform.asset.model.AssetAuditTaskViewEntry
import com.linc.platform.asset.model.AssetForAuditViewEntry
import com.linc.platform.asset.model.AuditDetailStatusEntry
import com.lt.linc.R
import com.lt.linc.asset.task.audit.AssetAuditActivity
import com.lt.linc.common.Constant
import com.lt.linc.common.mvi.ReactiveActivity
import com.lt.linc.common.mvi.ReactiveViewScope
import com.lt.linc.common.mvi.onEvent
import com.lt.linc.common.mvvm.kotlin.Message
import com.lt.linc.common.mvvm.kotlin.extensions.UniversalActivityParam
import com.lt.linc.common.mvvm.kotlin.extensions.universalParam
import com.lt.linc.databinding.ActivityAssetListBinding
import com.lt.linc.util.SnackType
import com.lt.linc.util.v1styledialog.CenterDialog
import java.io.Serializable

class AssetListActivity : ReactiveActivity<AssetListViewModel, AssetListUiState, ActivityAssetListBinding>() {

    private val REQUEST_CODE_ASSET_AUDIT = 1

    private val adapter: AssetListAdapter by lazy { AssetListAdapter() }

    data class Param(val taskViewEntry: AssetAuditTaskViewEntry): UniversalActivityParam

    override fun createViewModel(): AssetListViewModel {
        val param = universalParam as Param
        return AssetListViewModel(AssetListDataState(taskViewEntry = param.taskViewEntry))
    }

    override fun initView(savedInstanceState: Bundle?) {
        binding.apply {
            initToolBar(toolbar, String.format(getString(R.string.title_asset_audit_task_id), viewModel.getTaskViewEntry().id))
            assetIdOrVinScanner.setScanEvent { _, data ->
                getAssetForAudit(data)
            }

            assetRecycleView.layoutManager = LinearLayoutManager(this@AssetListActivity)
            assetRecycleView.addItemDecoration(RecyclerViewItemSpace(1, 1, false))
            adapter.setOnItemClickListener { _, _, position -> getAssetForAudit(viewModel.getAuditTaskAssetList()!![position].assetId) }
            assetRecycleView.adapter = adapter
            doneBtn.setOnClickListener {  viewModel.closeTask() }
        }
        onStartAuditEvent()
        onClosedTaskEvent()
        onAssetNotReceiveEvent()
    }

    private fun getAssetForAudit(assetId: String) {
        viewModel.getAssetForAudit(viewModel.getTaskViewEntry().id, assetId)
    }

    private fun startAssetAuditActivity(assetForAuditViewEntry: AssetForAuditViewEntry) {
        val intent = Intent()
        val clazz = AssetAuditActivity::class.java
        intent.setClass(this, clazz)
        val param = AssetAuditActivity.Param(assetForAuditViewEntry = assetForAuditViewEntry)
        ActivityBundleHolder.pushSerializable(clazz, Pair<String, Serializable>(UniversalActivityParam.TAG, param))
        startActivityForResult(intent, REQUEST_CODE_ASSET_AUDIT)
    }

    private fun setDoneButtonActive(active: Boolean) {
        binding.apply {
            doneBtn.isEnabled = active
        }
    }

    override fun ReactiveViewScope.subscribeToUiState() {
        showAssetList()
    }

    private fun ReactiveViewScope.showAssetList() =
            subscribe(AssetListUiState::auditTaskAssetList) { assetList ->
                assetList ?: return@subscribe
                adapter.setNewData(assetList)
                val isAllCompleted = assetList.all { it.status == AuditDetailStatusEntry.COMPLETED || it.status == AuditDetailStatusEntry.ISSUE_REPORTED }
                setDoneButtonActive(isAllCompleted)
            }

    private fun onStartAuditEvent() = onEvent<AssetListEvent.StartAudit> {
        startAssetAuditActivity(assetForAuditViewEntry)
    }

    private fun onAssetNotReceiveEvent() = onEvent<AssetListEvent.AssetNotReceive> {
        CenterDialog.alert(
                context = this@AssetListActivity,
                message = String.format(getString(R.string.msg_asset_not_received), assetId)
        ).show()
    }

    private fun onClosedTaskEvent() = onEvent<AssetListEvent.ClosedTask> {
        finish()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == REQUEST_CODE_ASSET_AUDIT && resultCode == Activity.RESULT_OK && data != null) {
            val assetAuditTaskDetailEntry = viewModel.getAuditTaskAssetList()
            val assetId = data.getStringExtra(Constant.INTENT_ASSET_ID)
            val auditDetailStatusEntry = data.getSerializableExtra(AuditDetailStatusEntry.TAG) as AuditDetailStatusEntry
            assetAuditTaskDetailEntry?.find { value -> value.assetId == assetId }?.status = auditDetailStatusEntry
            viewModel.setAssetAuditTaskDetailEntry(assetAuditTaskDetailEntry)
            showSnack(Message.StringMessage(String.format(getString(R.string.msg_asset_issue_reported_successfully), assetId)), SnackType.SuccessV1())
        }
    }
}