package com.lt.linc.util.v1styledialog

import android.annotation.SuppressLint
import android.app.Dialog
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.*
import androidx.annotation.LayoutRes
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.widget.AppCompatEditText
import androidx.appcompat.widget.AppCompatTextView
import com.customer.widget.StateButton
import com.customer.widget.StateColorDrawable
import com.customer.widget.util.CommUtil
import com.lt.linc.R
import com.lt.linc.common.ValuedCallBack
import com.lt.linc.common.VoidCallBack
import com.lt.linc.common.extensions.dp
import rx.Observable
import java.util.concurrent.TimeUnit

/**
 * <AUTHOR>
 * @date 2022/08
 * Alert dialog showing in center of screen.
 */

object CenterDialog {

    /**
     * Build a fully customized center dialog.
     * [title] Title text. center alignment, textSize=20sp, textColor=white, textStyle=bold. Will hide titleView if this is null.
     * [message] Message text. center alignment, textSize=14sp, textColor=white, textStyle=normal. Will hide messageView if this is null.
     * [contentView] Optional customized View between title/message and button.
     * [buttonsView] Customized Buttons View. Default to [R.layout.dialog_center_v1_button_layout].
     * [positiveClick] Callback when click positive button.
     * [showNegativeButton] Whether show negative button or not, default to true.
     * [negativeClick] Callback when click negative button.
     * [layoutId] Customize layoutId. Default to [R.layout.dialog_center_v1].
     * [titleModifier] Modify title TextView.
     * [messageModifier] Modify message TextView.
     * [positiveModifier] Modify positive Button. By default positive button is with a green background and text "Confirm", you can customize by this.
     * [negativeModifier] Modify negative Button. By default negative button is with a outline border and text "Cancel", you can customize by this.
     * [cancelable] Whether this dialog is cancelable with the BACK key.
     */
    @SuppressLint("InflateParams")
    fun customized(
            context: Context,
            title: CharSequence? = null,
            message: CharSequence? = null,
            contentView: View? = null,
            buttonsView: View? = null,
            positiveClick: VoidCallBack? = null,
            showNegativeButton: Boolean = true,
            negativeClick: VoidCallBack? = null,
            @LayoutRes layoutId: Int? = null,
            titleModifier: (AppCompatTextView.() -> Unit)? = null,
            messageModifier: (AppCompatTextView.() -> Unit)? = null,
            positiveModifier: (StateButton.() -> Unit)? = null,
            negativeModifier: (StateButton.() -> Unit)? = null,
            cancelable: Boolean = true,
            canceledOnTouchOutside: Boolean = true,
    ): Dialog {
        val dialog = AlertDialog.Builder(context).create()
        dialog.apply {
            window?.setBackgroundDrawableResource(android.R.color.transparent)
            val inflater = LayoutInflater.from(context)
            val view = inflater.inflate(layoutId ?: R.layout.dialog_center_v1, null)
            setView(view)
            setCancelable(cancelable)
            setCanceledOnTouchOutside(canceledOnTouchOutside)
            view.findViewById<AppCompatTextView>(R.id.title_tv)?.let {
                if (title == null) {
                    it.visibility = View.GONE
                } else {
                    it.text = title
                }
                if (titleModifier != null) {
                    it.run { titleModifier() }
                }
            }
            view.findViewById<ScrollView>(R.id.message_scroll_view)?.let {
                if (message == null) {
                    it.visibility = View.GONE
                } else {
                    view.findViewById<AppCompatTextView>(R.id.message_tv)?.let { messageView ->
                        messageView.text = message
                        if (messageModifier != null) messageView.run { messageModifier() }
                    }
                }
            }
            view.findViewById<FrameLayout>(R.id.content_layout)?.let {
                if (contentView == null) {
                    it.visibility = View.GONE
                } else {
                    it.addView(contentView)
                }
            }
            val buttonsLayout = buttonsView ?: inflater.inflate(R.layout.dialog_center_v1_button_layout, null)
            view.findViewById<FrameLayout>(R.id.button_layout).addView(buttonsLayout)

            view.findViewById<StateButton>(R.id.positive_button)?.let {
                it.setOnClickListener {
                    positiveClick?.call()
                    dismiss()
                }
                if (positiveModifier != null) {
                    it.run { positiveModifier() }
                }
            }
            view.findViewById<StateButton>(R.id.negative_button)?.let {
                if (!showNegativeButton) {
                    it.visibility = View.GONE
                } else {
                    it.setOnClickListener {
                        negativeClick?.call()
                        dismiss()
                    }
                    if (negativeModifier != null) {
                        it.run { negativeModifier() }
                    }
                }
            }

        }
        return dialog
    }

    data class ButtonTextColor(val normal: Int, val pressed: Int = 0, val unable: Int = 0)

    private const val INPUT_EDIT_ID = 100001

    /**
     * Show a single input dialog.
     * [defaultValue] Default value of EditText
     * [inputType] InputType of EditText
     * [hint] Hint text of EditText
     * [autoShowKeyboard] If true, will automatically show the keyboard.
     * @see customized for all params description.
     */
    fun singleInput(
        context: Context,
        positiveClick: ValuedCallBack<String>,
        negativeClick: VoidCallBack? = null,
        defaultValue: String? = null,
        inputType: Int? = null,
        hint: String? = null,
        title: String? = null,
        message: String? = null,
        cancelable: Boolean = true,
        positiveText: String? = context.getString(R.string.btn_confirm),
        negativeText: String? = context.getString(R.string.btn_cancel),
        positiveTextColor: ButtonTextColor? = null,
        negativeTextColor: ButtonTextColor? = null,
        autoShowKeyboard: Boolean = false,
        titleTextSize: Float? = null,
    ): Dialog {
        val inputView = AppCompatEditText(context).apply {
            id = INPUT_EDIT_ID
            hint?.let { value ->
                this.hint = value
                this.setHintTextColor(context.getColor(R.color.text_hint_v1))
            }
            this.setTextColor(context.getColor(R.color.white))
            inputType?.let { value -> this.inputType = value }
            defaultValue?.let { value ->
                this.setText(value)
                setSelection(this.text?.length ?: 0)
            }
            val paddingHorizontal = 16.dp
            val paddingVertical = 12.dp
            setPadding(paddingHorizontal, paddingVertical, paddingHorizontal, paddingVertical)
            val marginLayoutParams = FrameLayout.LayoutParams(
                ViewGroup.MarginLayoutParams.MATCH_PARENT,
                ViewGroup.MarginLayoutParams.WRAP_CONTENT
            )
            layoutParams = marginLayoutParams
            background = context.getDrawable(R.drawable.rectangle_2e2e2e)
        }
        val dialog = customized(
            context,
            title = title,
            titleModifier = {
                titleTextSize?.let { textSize = titleTextSize }
            },
            message = message,
            cancelable = cancelable,
            positiveModifier = {
                text = positiveText
                positiveTextColor?.let { setStateTextColor(it.normal, it.pressed, it.unable) }
            },
            negativeModifier = {
                text = negativeText
                negativeTextColor?.let { setStateTextColor(it.normal, it.pressed, it.unable) }
            },
            contentView = inputView,
            positiveClick = { positiveClick.call(inputView.text.toString()) },
            negativeClick = { negativeClick?.call() }
        )
        if (autoShowKeyboard) {
            dialog.setOnShowListener {
                // delay to wait for dialog to show on screen
                Observable.timer(200, TimeUnit.MILLISECONDS).subscribe { CommUtil.showKeyBoard(inputView) }
            }
        }
        return dialog
    }

    /**
     * A confirm dialog to show title/message and requires user to choose either positive or negative.
     * [positiveBackground] Background drawable of positive button.
     * @see customized for all params description.
     */
    @Suppress("DEPRECATION")
    @JvmStatic
    fun confirm(
        context: Context,
        positiveClick: VoidCallBack,
        negativeClick: VoidCallBack? = null,
        title: CharSequence? = null,
        message: CharSequence? = null,
        cancelable: Boolean = true,
        positiveText: CharSequence? = context.getString(R.string.btn_confirm),
        negativeText: CharSequence? = context.getString(R.string.btn_cancel),
        positiveTextColor: ButtonTextColor? = null,
        negativeTextColor: ButtonTextColor? = null,
        positiveBackground: StateColorDrawable? = null,
        showNegativeButton: Boolean = true
    ): Dialog {
        return customized(
            context,
            title = title,
            message = message,
            cancelable = cancelable,
            canceledOnTouchOutside = cancelable,
            positiveClick = positiveClick,
            negativeClick = negativeClick,
            positiveModifier = {
                text = positiveText
                if (positiveBackground != null) {
                    setBackgroundDrawable(positiveBackground)
                }
                positiveTextColor?.let { setStateTextColor(it.normal, it.pressed, it.unable) }
            },
            negativeModifier = {
                text = negativeText
                negativeTextColor?.let { setStateTextColor(it.normal, it.pressed, it.unable) }
            },
            showNegativeButton = showNegativeButton
        )
    }

    /**
     * A alert dialog to show title/message and user can only click [okClick] to dismiss this dialog.
     * [okBackground] Background drawable of positive button.
     * @see customized for all params description.
     */
    @Suppress("DEPRECATION")
    @JvmStatic
    fun alert(
        context: Context,
        okClick: VoidCallBack? = null,
        title: String? = null,
        message: String? = null,
        cancelable: Boolean = true,
        canceledOnTouchOutside: Boolean = true,
        okText: String? = context.getString(R.string.text_ok),
        okTextColor: ButtonTextColor? = null,
        okBackground: StateColorDrawable? = null,
    ): Dialog {
        return customized(
            context,
            title = title,
            message = message,
            cancelable = cancelable,
            canceledOnTouchOutside = canceledOnTouchOutside,
            positiveClick = okClick,
            positiveModifier = {
                text = okText
                if (okBackground != null) {
                    setBackgroundDrawable(okBackground)
                }
                okTextColor?.let { setStateTextColor(it.normal, it.pressed, it.unable) }
            },
            showNegativeButton = false
        )
    }

    private class ChoiceListView(context: Context, val isSingleChoice: Boolean = true) : ListView(context) {
        init {
            layoutParams = FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
            choiceMode = if (isSingleChoice) CHOICE_MODE_SINGLE else CHOICE_MODE_MULTIPLE
            divider = null
            dividerHeight = context.resources.getDimensionPixelOffset(R.dimen.panel_vertical_margin_v1)
            setOnItemClickListener { _, _, _, _ -> (adapter as? ChoiceAdapter)?.notifyDataSetChanged() }
        }
    }

    private const val itemBackGroundNormal = (0xFF37383B).toInt()

    private class ChoiceAdapter(
        context: Context,
        items: List<CharSequence>,
    ) :
        ArrayAdapter<CharSequence>(context, R.layout.dialog_center_v1_list_item, R.id.title_tv, items) {
        override fun getView(position: Int, convertView: View?, parent: ViewGroup): View {
            val isItemChecked = (parent as? ChoiceListView)?.isItemChecked(position) ?: false
            val isFirstCreate = convertView == null
            val view = super.getView(position, convertView, parent)
            if (isFirstCreate) {
                view.background = StateColorDrawable(
                    background = itemBackGroundNormal,
                    backgroundSelected = context.getColor(R.color.accent_blue_v1_o40),
                    borderSelected = context.getColor(R.color.accent_blue_v1),
                    backgroundActivated = context.getColor(R.color.accent_blue_v1_o40),
                    borderActivated = context.getColor(R.color.accent_blue_v1),
                    borderWidth = 1.dp,
                    corner = 4f.dp
                )
            }
            view.findViewById<CheckBox>(R.id.check_box).apply {
                this.isChecked = isItemChecked
            }
            return view
        }
    }

    private class ChoiceGridView(context: Context, val isSingleChoice: Boolean = true) : GridView(context) {
        init {
            layoutParams = FrameLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.WRAP_CONTENT)
            choiceMode = if (isSingleChoice) CHOICE_MODE_SINGLE else CHOICE_MODE_MULTIPLE
            numColumns = 2
            verticalSpacing = 20
            horizontalSpacing = 20
            setOnItemClickListener { _, _, _, _ -> (adapter as? ChoiceGridAdapter)?.notifyDataSetChanged() }
        }
    }

    private class ChoiceGridAdapter(
        context: Context,
        items: List<CharSequence>,
    ) :
        ArrayAdapter<CharSequence>(context, R.layout.dialog_center_v1_grid_item, R.id.title_tv, items) {
        override fun getView(position: Int, convertView: View?, parent: ViewGroup): View {
            val isItemChecked = (parent as? ChoiceGridView)?.isItemChecked(position) ?: false
            val isFirstCreate = convertView == null
            val view = super.getView(position, convertView, parent)
            if (isFirstCreate) {
                view.background = StateColorDrawable(
                    background = itemBackGroundNormal,
                    backgroundSelected = context.getColor(R.color.accent_blue_v1_o40),
                    borderSelected = context.getColor(R.color.accent_blue_v1),
                    backgroundActivated = context.getColor(R.color.accent_blue_v1_o40),
                    borderActivated = context.getColor(R.color.accent_blue_v1),
                    borderWidth = 1.dp,
                    corner = 4f.dp
                )
            }
            view.findViewById<CheckBox>(R.id.check_box).apply {
                this.isChecked = isItemChecked
            }
            return view
        }
    }

    /**
     * A dialog to choose single item.
     * [items] Required. List of [T], must not be empty.
     * [itemTitleMapper] Required. Map T to showing title.
     * [defaultChose] Optional. If exists, will choose this item by default, otherwise will choose the first item.
     * @see customized for all params description.
     */
    @JvmStatic
    fun <T> singleChoiceList(
        context: Context,
        showNegativeButton: Boolean = true,
        title: String? = null,
        message: String? = null,
        cancelable: Boolean = true,
        items: List<T>,
        itemTitleMapper: (T) -> CharSequence,
        defaultChose: T? = null,
        positiveText: String? = context.getString(R.string.btn_confirm),
        negativeText: String? = context.getString(R.string.btn_cancel),
        positiveTextColor: ButtonTextColor? = null,
        negativeTextColor: ButtonTextColor? = null,
        negativeClick: VoidCallBack? = null,
        positiveClick: (T?) -> Unit,
    ): Dialog {
        assert(items.isNotEmpty())

        val titles = items.map(itemTitleMapper)
        val adapter = ChoiceAdapter(context, titles)

        val listView = ChoiceListView(context, true)
        listView.adapter = adapter
        val selectedIndex = items.indexOf(defaultChose).let { if (it != -1) it else 0 }
        listView.setItemChecked(selectedIndex, true)

        return customized(
            context,
            title = title,
            message = message,
            cancelable = cancelable,
            contentView = listView,
            positiveClick = {
                val index = listView.checkedItemPosition
                positiveClick(items.getOrNull(index))
            },
            positiveModifier = {
                text = positiveText
                positiveTextColor?.let { setStateTextColor(it.normal, it.pressed, it.unable) }
            },
            negativeClick = negativeClick,
            negativeModifier = {
                text = negativeText
                negativeTextColor?.let { setStateTextColor(it.normal, it.pressed, it.unable) }
            },
            showNegativeButton = showNegativeButton,
            canceledOnTouchOutside = cancelable
        )
    }

    /**
     * A dialog to choose single item for grid layout.
     * [items] Required. List of [T], must not be empty.
     * [itemTitleMapper] Required. Map T to showing title.
     * [defaultChose] Optional. If exists, will choose this item by default, otherwise will choose the first item.
     * @see customized for all params description.
     */
    fun <T> singleChoiceGrid(
        context: Context,
        title: String? = null,
        message: String? = null,
        cancelable: Boolean = true,
        items: List<T>,
        itemTitleMapper: (T) -> CharSequence,
        defaultChose: T? = null,
        positiveText: String? = context.getString(R.string.btn_confirm),
        negativeText: String? = context.getString(R.string.btn_cancel),
        negativeClick: VoidCallBack? = null,
        positiveClick: (T?) -> Unit,
    ): Dialog {
        assert(items.isNotEmpty())

        val titles = items.map { itemTitleMapper(it) }
        val adapter = ChoiceGridAdapter(context, titles)
        val gridView = ChoiceGridView(context, true)
        gridView.adapter = adapter
        val selectedIndex = items.indexOf(defaultChose).let { if (it != -1) it else 0 }
        gridView.setItemChecked(selectedIndex, true)

        return customized(
            context,
            title = title,
            message = message,
            cancelable = cancelable,
            contentView = gridView,
            positiveClick = {
                val index = gridView.checkedItemPosition
                positiveClick(items.getOrNull(index))
            },
            positiveModifier = {
                text = positiveText
            },
            negativeClick = negativeClick,
            negativeModifier = {
                text = negativeText
            }
        )
    }
}

