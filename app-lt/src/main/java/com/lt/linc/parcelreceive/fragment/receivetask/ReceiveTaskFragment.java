package com.lt.linc.parcelreceive.fragment.receivetask;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;

import androidx.appcompat.widget.AppCompatButton;
import androidx.appcompat.widget.AppCompatEditText;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.fragment.app.FragmentActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.customer.widget.ItemDividerDecoration;
import com.customer.widget.core.LincBaseFragment;
import com.customer.widget.photo.PhotoWidget;
import com.customer.widget.photo.PhotoWidgetParam;
import com.linc.platform.baseapp.model.LocationEntry;
import com.linc.platform.common.step.StepBaseEntry;
import com.linc.platform.common.step.StepStatusEntry;
import com.linc.platform.foundation.model.CustomerViewEntry;
import com.linc.platform.foundation.model.ItemUnitsEntry;
import com.linc.platform.foundation.model.UnitEntry;
import com.linc.platform.inventory.model.LpDetail;
import com.linc.platform.parcelreceive.model.EventBusData;
import com.linc.platform.parcelreceive.model.ReceiveTaskInfo;
import com.linc.platform.parcelreceive.presenter.impl.ReceiveWorkPresenterImpl;
import com.linc.platform.parcelreceive.view.ReceiveWorkView;
import com.linc.platform.receive.ReceiveCommonHelper;
import com.linc.platform.receive.model.ReceiptItemLineEntry;
import com.linc.platform.receive.model.ReceiveTaskEntry;
import com.linc.platform.utils.AsyncPostTask;
import com.linc.platform.utils.CollectionUtil;
import com.linc.platform.utils.Constant;
import com.linc.platform.utils.Lists;
import com.linc.platform.utils.ToastUtil;
import com.lt.linc.R;
import com.lt.linc.parcelreceive.activity.SmallParcelReceiveTaskActivity;
import com.lt.linc.parcelreceive.adapter.ReceiveTaskAdapter;
import com.lt.linc.pick.newpick.work.ScannerLayout;
import com.lt.linc.receive.setup.work.receivetoputaway.ScannerTool;
import com.lt.linc.toolset.print.lp.PrintLpActivity;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description:
 * @Author: Dennis
 * @CreateDate: 2021/1/20 9:00
 */
public class ReceiveTaskFragment extends LincBaseFragment implements ReceiveWorkView,
        ItemConditionDialogFragment.OnItemConditionSelectedListener, LocationDialogFragment.OnLocationSelectedListener,
        UnitDialogFragment.OnUnitSelectedListener, ItemLineDialogFragment.OnItemLineSelectedListener, BaseQuickAdapter.OnItemChildClickListener {

    private final static int RESULT_VALUE = 0;

    private ScannerLayout mScannerLocation;
    private AppCompatImageView mIvLocationChecked;
    private ScannerLayout mScannerItem;
    private AppCompatButton mBtnSelect;
    private AppCompatEditText mEdtQty;
    private AppCompatTextView mTvUnit;
    private ScannerLayout mScannerSn;
    private AppCompatTextView mTvItemCondition;
    private RelativeLayout mRlItemCondition;
    private ScannerLayout mScannerPrintLp;
    private AppCompatButton mBtnPrintLp;
    private RecyclerView mRecyclerView;
    private AppCompatButton mBtnSubmit;
    private LinearLayout mLlQty;
    private AppCompatTextView mTvSubTitle;
    private AppCompatTextView mTvTrackingNo;
    private LinearLayout mLlReceiveTask;
    private LinearLayout mLlDone;
    private LinearLayout mLlStartStep;
    private LinearLayout mLlTakeOver;
    private PhotoWidget mPhotoWidget;

    private ItemConditionDialogFragment mItemConditionDialogFragment;
    private ReceiveWorkPresenterImpl mReceiveWorkPresenter;
    private ReceiveTaskAdapter mAdapter;

    public static ReceiveTaskFragment getInstance(String trackingNo, ReceiveTaskEntry receiveTaskEntry, ArrayList<String> receiptIds) {
        ReceiveTaskFragment fragment = new ReceiveTaskFragment();
        Bundle bundle = new Bundle();
        bundle.putString(Constant.INTENT_TRACKING_NO, trackingNo);
        bundle.putSerializable(Constant.INTENT_RECEIPT_IDS, receiptIds);
        bundle.putSerializable(Constant.INTENT_RECEIVE_TASK, receiveTaskEntry);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int getLayoutId() {
        return R.layout.fragment_receive_task;
    }

    @Override
    protected void initView() {
        bindView();
        Bundle bundle = getArguments();
        String trackingNo = bundle.getString(Constant.INTENT_TRACKING_NO);
        ArrayList<String> receiptIds = bundle.getStringArrayList(Constant.INTENT_RECEIPT_IDS);
        ReceiveTaskEntry receiveTaskEntry = (ReceiveTaskEntry) bundle.getSerializable(Constant.INTENT_RECEIVE_TASK);
        mReceiveWorkPresenter = new ReceiveWorkPresenterImpl(trackingNo, receiptIds, receiveTaskEntry, this);
        mReceiveWorkPresenter.getItemLines(receiptIds, null);
        bindDataToView(trackingNo, receiveTaskEntry);
        showViewByTaskStep(mReceiveWorkPresenter.getReceiveTaskStep());
        initRecyclerView();
        initPhotoWidget();
        initEvent();
    }

    private void bindDataToView(String trackingNo, ReceiveTaskEntry receiveTaskEntry) {
        mTvTrackingNo.setText(String.format(getString(R.string.tv_tracking_label), trackingNo));
        mTvItemCondition.setText(Constant.GOODS_TYPE_GOOD);
        mReceiveWorkPresenter.saveItemCondition(Constant.GOODS_TYPE_GOOD);
        if (receiveTaskEntry != null) {
            mTvSubTitle.setText(receiveTaskEntry.id);
            mTvSubTitle.setVisibility(View.VISIBLE);
        } else {
            mTvSubTitle.setVisibility(View.GONE);
        }
    }

    private void initRecyclerView() {
        LinearLayoutManager layoutManager = new LinearLayoutManager(getActivity());
        mRecyclerView.setLayoutManager(layoutManager);
        mRecyclerView.addItemDecoration(new ItemDividerDecoration(getActivity()));
        mAdapter = new ReceiveTaskAdapter(mReceiveWorkPresenter.getReceiveTaskInfoList());
        mAdapter.setOnItemChildClickListener(this);
        mRecyclerView.setAdapter(mAdapter);
    }

    private void initPhotoWidget() {
        PhotoWidgetParam param = new PhotoWidgetParam();
        param.setApiInfo("wms", "receive", "takephoto");
        mPhotoWidget.setTabParam(param);
    }

    private void initEvent() {
        ScannerTool.onScanned(mScannerLocation, this::doSearchLocation);
        ScannerTool.onScanned(mScannerPrintLp, this::doSearchLp);
        ScannerTool.onScanned(mScannerItem, this::doSearchItem);
        ScannerTool.onScanned(mScannerSn, this::doSearchItemSn);
        mEdtQty.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
            }

            @Override
            public void afterTextChanged(Editable s) {
                AsyncPostTask.postDelayDoMain(() -> {
                    String qtyStr = s.toString().trim();
                    try {
                        mReceiveWorkPresenter.saveQty(Integer.parseInt(qtyStr));
                    } catch (NumberFormatException e) {
                        mReceiveWorkPresenter.saveQty(0d);
                    }
                });
            }
        });
    }

    private void doSearchItemSn(String barcode) {
        if (!TextUtils.isEmpty(barcode)) {
            mScannerSn.setText(barcode);
            mReceiveWorkPresenter.getSnForItemSpec(barcode);
        }
    }

    private void doSearchItem(String barcode) {
        if (!TextUtils.isEmpty(barcode)) {
            mScannerItem.setText(barcode);
            if (mReceiveWorkPresenter.isHasLoadReceiveItemLine()) {
                mReceiveWorkPresenter.searchItem(barcode);
            } else {
                mReceiveWorkPresenter.getItemLines(mReceiveWorkPresenter.getReceiptIds(), barcode);
            }
        }
    }

    private void doSearchLp(String barcode) {
        if (!TextUtils.isEmpty(barcode)) {
            if (!mReceiveWorkPresenter.isSameLp(barcode)) {
                ToastUtil.showToast(getString(R.string.msg_submit_first));
                return;
            }
            mScannerPrintLp.setText(barcode);
            mReceiveWorkPresenter.searchLP(barcode);
        }
    }

    private void doSearchLocation(String barcode) {
        if (!TextUtils.isEmpty(barcode)) {
            if (!mReceiveWorkPresenter.isSameLocation(barcode)) {
                ToastUtil.showToast(getString(R.string.msg_submit_first));
                return;
            }
            mScannerLocation.setText(barcode);
            mReceiveWorkPresenter.searchLocation(barcode);
        }
    }

    private void onClick(View view) {
        switch (view.getId()) {
            case R.id.btn_select:
                selectItem();
                break;
            case R.id.rl_item_condition:
                selectItemCondition();
                break;
            case R.id.btn_print_lp:
                printLp();
                break;
            case R.id.btn_submit:
                submitReceiveTask();
                break;
            case R.id.tv_unit:
                toggleUnit();
                break;
            case R.id.step_start_btn:
                startStep();
                break;
            case R.id.take_over_btn:
                takeOver();
                break;
        }
    }

    private void startStep() {
        mReceiveWorkPresenter.startStep(false);
    }

    private void takeOver() {
        mReceiveWorkPresenter.tackOverTask();
    }

    private void submitReceiveTask() {
        if (CollectionUtil.isNotNullOrEmpty(mReceiveWorkPresenter.getReceiveTaskInfoList())) {
            if (mReceiveWorkPresenter.isNeedCreateReceiveTask()) {
                mReceiveWorkPresenter.createReceiveTask(getIdmUserId(), mPhotoWidget.getPhotoNames(true));
            } else {
                mReceiveWorkPresenter.submitReceiveTask(mPhotoWidget.getPhotoNames(true));
            }
        } else {
            ToastUtil.showToast(getString(R.string.msg_add_item_first));
        }
    }

    @Override
    public void showViewByTaskStep(StepBaseEntry receiveTaskStep) {
        if (receiveTaskStep != null) {
            if (!Lists.ensureNotNull(receiveTaskStep.assigneeUserIds).contains(getIdmUserId())) {
                mLlReceiveTask.setVisibility(View.GONE);
                mLlStartStep.setVisibility(View.GONE);
                mLlTakeOver.setVisibility(View.VISIBLE);
                mLlDone.setVisibility(View.GONE);
            } else if (receiveTaskStep.status == StepStatusEntry.DONE) {
                mLlReceiveTask.setVisibility(View.GONE);
                mLlStartStep.setVisibility(View.GONE);
                mLlTakeOver.setVisibility(View.GONE);
                mLlDone.setVisibility(View.VISIBLE);
            } else if (receiveTaskStep.status == StepStatusEntry.NEW) {
                mLlReceiveTask.setVisibility(View.GONE);
                mLlStartStep.setVisibility(View.VISIBLE);
                mLlDone.setVisibility(View.GONE);
                mLlTakeOver.setVisibility(View.GONE);
            } else {
                mLlReceiveTask.setVisibility(View.VISIBLE);
                mLlStartStep.setVisibility(View.GONE);
                mLlDone.setVisibility(View.GONE);
                mLlTakeOver.setVisibility(View.GONE);
            }
        }
    }

    @Override
    public void noFoundItemLines() {
        ToastUtil.showToast(getString(com.linc.platform.R.string.msg_no_receipt_item_line));
        getActivity().finish();
    }

    @Override
    public void getItemLinesSuccessful(List<ReceiptItemLineEntry> receiptItemLineEntries) {
        mBtnSelect.setVisibility(receiptItemLineEntries.size() == 1? View.GONE : View.VISIBLE);
    }

    @Override
    public void getUnitItemSpecSuccessful(List<UnitEntry> unitEntries) {
        if (CollectionUtil.isNotNullOrEmpty(unitEntries)) {
            UnitDialogFragment unitDialogFragment = UnitDialogFragment.getInstance();
            unitDialogFragment.setUnitList(unitEntries);
            unitDialogFragment.setUnitSelectedListener(this);
            unitDialogFragment.show(getFragmentManager(), UnitDialogFragment.TAG);
        }
    }

    @Override
    public void getCustomerSuccessful(CustomerViewEntry customerViewEntry) {
        if (customerViewEntry != null) {
            String defaultGoodsType = ReceiveCommonHelper.getDefaultGoodsType(mReceiveWorkPresenter.getReceiptItemLineEntries().get(0), customerViewEntry);
            mTvItemCondition.setText(defaultGoodsType);
            if (ReceiveCommonHelper.enableShowGoodsTypeView(customerViewEntry)) {
                mRlItemCondition.setVisibility(View.VISIBLE);
            } else {
                mRlItemCondition.setVisibility(View.GONE);
            }
        }
    }

    @Override
    public void autoAddItem(ReceiveTaskInfo receiveTaskInfo) {
        String locationName = mScannerLocation.getText().trim();
        String itemName = mScannerItem.getText().trim();
        String qty = mEdtQty.getText().toString().trim();
        String sn = mScannerSn.getText().trim();
        String lp = mScannerPrintLp.getText().trim();

        if (mReceiveWorkPresenter.checkParamsAvailable(locationName, itemName, qty, sn, lp)) {
            mReceiveWorkPresenter.addReceiveTaskInfoToList(receiveTaskInfo);
            mAdapter.notifyDataSetChanged();
//            clearView();
            ToastUtil.showToast(getString(R.string.msg_auto_add_item));
        }
    }

    private void clearView() {
        mScannerItem.setText("");
        mEdtQty.setText("");
        mLlQty.setVisibility(View.GONE);
        mScannerSn.setText("");
        mScannerSn.setVisibility(View.GONE);
        mReceiveWorkPresenter.clearItemSpec();
    }

    private void selectItem() {
        ItemLineDialogFragment itemLineDialogFragment = ItemLineDialogFragment.getInstance();
        itemLineDialogFragment.setItemLineEntryList(mReceiveWorkPresenter.getReceiptItemLineEntries());
        itemLineDialogFragment.setItemLineSelectedListener(this);
        itemLineDialogFragment.show(getFragmentManager(), ItemLineDialogFragment.TAG);
    }

    private void toggleUnit() {
        mReceiveWorkPresenter.getUnitItemSpec();
    }

    private void selectItemCondition() {
        if (mItemConditionDialogFragment == null) {
            mItemConditionDialogFragment = new ItemConditionDialogFragment();
            CustomerViewEntry customer = mReceiveWorkPresenter.getCustomer();
            if (customer != null) {
                String defaultGoodsType = ReceiveCommonHelper.getDefaultGoodsType(mReceiveWorkPresenter.getReceiptItemLineEntries().get(0), customer);
                mItemConditionDialogFragment.setDefaultGoodsType(defaultGoodsType);
                mItemConditionDialogFragment.setGoodsTypeList(customer.getAllowedReceivingGoodsTypes());
            }
            mItemConditionDialogFragment.setItemConditionSelectedListener(this);
        }
        mItemConditionDialogFragment.show(getFragmentManager(), ItemConditionDialogFragment.TAG);
    }

    private void printLp() {
        Intent intent = new Intent(getActivity(), PrintLpActivity.class);
        intent.putExtra(PrintLpActivity.REPRINT_LP, false);
        startActivityForResult(intent, RESULT_VALUE);
    }

    @Override
    public void onItemConditionSelectedListener(String value) {
        String itemConditionInList = mReceiveWorkPresenter.getItemConditionInList();
        if (itemConditionInList == null || itemConditionInList.equalsIgnoreCase(value)) {
            mTvItemCondition.setText(value);
            mReceiveWorkPresenter.saveItemCondition(value);
            setPhotoWidgetVisibility(value);
        } else {
            ToastUtil.showToast(getString(R.string.msg_inconsistent_item_condition));
        }
    }

    private void setPhotoWidgetVisibility(String value) {
        if (Constant.GOODS_TYPE_DAMAGE.equals(value) || Constant.GOODS_TYPE_CONTAIN_DAMAGE.equals(value)) {
            mPhotoWidget.setVisibility(View.VISIBLE);
        } else {
            mPhotoWidget.setVisibility(View.GONE);
        }
    }

    @Override
    public void searchLpSuccessful(LpDetail lpDetail) {
        mScannerPrintLp.setText(lpDetail.id);
    }

    @Override
    public void searchLpFailure(String errMsg) {
        mScannerPrintLp.setText("");
        ToastUtil.showToast(errMsg);
    }

    @Override
    public void searchLocationSuccessful(List<LocationEntry> locationEntries) {
        if (locationEntries.size() == 1) {
            mIvLocationChecked.setVisibility(View.VISIBLE);
            mScannerLocation.setText(locationEntries.get(0).getName());
        } else {
            LocationDialogFragment locationDialogFragment = LocationDialogFragment.getInstance();
            locationDialogFragment.setLocationSelectedListener(this);
            locationDialogFragment.setLocationList(locationEntries);
            locationDialogFragment.show(getFragmentManager(), LocationDialogFragment.TAG);
        }
    }

    @Override
    public void onLocationSelectedListener(LocationEntry locationEntry) {
        mIvLocationChecked.setVisibility(View.VISIBLE);
        mScannerLocation.setText(locationEntry.getName());
        mReceiveWorkPresenter.saveLocationEntry(locationEntry);
    }

    @Override
    public void searchLocationFailure(String errMsg) {
        mScannerLocation.setText("");
        mIvLocationChecked.setVisibility(View.GONE);
        ToastUtil.showToast(errMsg);
        mScannerLocation.requestEdtFocus();
    }

    @Override
    public void searchItemSuccessful(List<ReceiptItemLineEntry> receiptItemLineEntries) {
        if (receiptItemLineEntries.size() == 1) {
            clearItemSpec();// clear item for sync data
            mLlQty.setVisibility(View.VISIBLE);
            mReceiveWorkPresenter.saveReceiptItemLineEntry(receiptItemLineEntries.get(0));
            setQtyAndUnit(receiptItemLineEntries.get(0));
        } else {
            ItemLineDialogFragment itemLineDialogFragment = ItemLineDialogFragment.getInstance();
            itemLineDialogFragment.setItemLineEntryList(receiptItemLineEntries);
            itemLineDialogFragment.setItemLineSelectedListener(this);
            itemLineDialogFragment.show(getFragmentManager(), ItemLineDialogFragment.TAG);
        }
    }

    @Override
    public void searchItemFailure(String errMsg) {
        mScannerItem.setText("");
        mBtnSelect.setVisibility(View.GONE);
        mLlQty.setVisibility(View.GONE);
        mScannerSn.setVisibility(View.GONE);
        ToastUtil.showToast(errMsg);
        mScannerItem.requestEdtFocus();
    }

    @Override
    public void getSnItemSpecSuccessful(String sn) {
        mScannerSn.setText(sn);
    }

    @Override
    public void getSnItemSpecFailure(String errMsg) {
        mScannerSn.setText("");
        ToastUtil.showToast(errMsg);
        mScannerSn.requestEdtFocus();
    }

    @Override
    public void createReceiveTaskSuccessful(String taskId) {
        mTvSubTitle.setVisibility(View.VISIBLE);
        mTvSubTitle.setText(taskId);
    }

    @Override
    public void receiveTaskSubmitSuccessful() {
        EventBus.getDefault().post(new EventBusData());
        FragmentActivity activity = getActivity();
        if (activity instanceof SmallParcelReceiveTaskActivity) {
            SmallParcelReceiveTaskActivity attachActivity = (SmallParcelReceiveTaskActivity) activity;
            attachActivity.loadOrShowReceiveTaskIndicatorFragment(mReceiveWorkPresenter.getReceiveTaskEntry());
        }
    }

    @Override
    public void onUnitSelectedListener(UnitEntry unitEntry) {
        mTvUnit.setText(unitEntry.name);
        mReceiveWorkPresenter.saveUnitEntry(unitEntry);
    }

    @Override
    public void onItemLineSelectedListener(ReceiptItemLineEntry itemLineEntry) {
        if (mReceiveWorkPresenter.isNotSameItemLine(itemLineEntry)) {
            ToastUtil.showToast(getString(R.string.msg_not_the_same_item));
            return;
        }
        clearItemSpec();// clear item for sync data
        mScannerItem.setText(itemLineEntry.name);
        mLlQty.setVisibility(View.VISIBLE);
        mReceiveWorkPresenter.saveReceiptItemLineEntry(itemLineEntry);
        setQtyAndUnit(itemLineEntry);
    }

    private void clearItemSpec() {
        mScannerSn.setText("");
        mReceiveWorkPresenter.clearItemSpec();
    }

    private void setQtyAndUnit(ReceiptItemLineEntry itemLineEntry) {
        mScannerSn.setVisibility(itemLineEntry.hasSerialNumber && CollectionUtil.isNotNullOrEmpty(itemLineEntry.snList)? View.VISIBLE : View.GONE);
        int qtyInt = (int) (itemLineEntry.qty / 1);
        mEdtQty.setText(String.valueOf(qtyInt));
        mReceiveWorkPresenter.saveQty(qtyInt);

        ItemUnitsEntry unit = itemLineEntry.itemUnit;
        if (unit != null) {
            UnitEntry unitEntry = unit.translateUnit();
            mTvUnit.setText(unitEntry.name);
            mReceiveWorkPresenter.saveUnitEntry(unitEntry);
        }
    }


    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == RESULT_VALUE && resultCode == Activity.RESULT_OK) {
            ArrayList<String> lpIds = data.getStringArrayListExtra(PrintLpActivity.PRINTED_LP_LIST);
            if (lpIds != null && lpIds.size() == 1) {
                doSearchLp(lpIds.get(0));
            }
        }
    }

    @Override
    public void onItemChildClick(BaseQuickAdapter adapter, View view, int position) {
        mReceiveWorkPresenter.removeReceiveTaskInfo(position);
        mAdapter.notifyItemRemoved(position);
    }

    private void bindView() {
        mScannerLocation = findViewById(R.id.scanner_location);
        mIvLocationChecked = findViewById(R.id.iv_location_checked);
        mScannerItem = findViewById(R.id.scanner_item);
        mBtnSelect = findViewById(R.id.btn_select);
        mEdtQty = findViewById(R.id.edt_qty);
        mTvUnit = findViewById(R.id.tv_unit);
        mScannerSn = findViewById(R.id.scanner_sn);
        mTvItemCondition = findViewById(R.id.tv_item_condition);
        mRlItemCondition = findViewById(R.id.rl_item_condition);
        mScannerPrintLp = findViewById(R.id.scanner_print_lp);
        mBtnPrintLp = findViewById(R.id.btn_print_lp);
        mRecyclerView = findViewById(R.id.recycler_view);
        mBtnSubmit = findViewById(R.id.btn_submit);
        mLlQty = findViewById(R.id.ll_qty);
        mTvSubTitle = findViewById(R.id.tv_sub_title);
        mTvTrackingNo = findViewById(R.id.tv_tracking_no);
        mLlReceiveTask = findViewById(R.id.layout_receive_task);
        mLlDone = findViewById(R.id.done_layout);
        mLlStartStep = findViewById(R.id.start_step_layout);
        mLlTakeOver = findViewById(R.id.take_over_layout);
        mPhotoWidget = findViewById(R.id.photo_widget);
        mBtnSelect.setOnClickListener(this::onClick);
        mRlItemCondition.setOnClickListener(this::onClick);
        mBtnPrintLp.setOnClickListener(this::onClick);
        mBtnSubmit.setOnClickListener(this::onClick);
        mTvUnit.setOnClickListener(this::onClick);
        findViewById(R.id.step_start_btn).setOnClickListener(this::onClick);
        findViewById(R.id.take_over_btn).setOnClickListener(this::onClick);
    }
}
