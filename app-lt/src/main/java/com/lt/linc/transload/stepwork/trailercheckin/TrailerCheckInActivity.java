package com.lt.linc.transload.stepwork.trailercheckin;

import android.content.Intent;
import android.os.Bundle;
import androidx.appcompat.widget.AppCompatButton;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.appcompat.widget.Toolbar;
import android.text.TextUtils;
import android.view.Menu;
import android.view.MenuItem;
import android.widget.LinearLayout;

import com.annimon.stream.Collectors;
import com.annimon.stream.Stream;
import com.customer.widget.core.LincBaseActivity;
import com.linc.platform.baseapp.model.LocationEntry;
import com.linc.platform.common.CheckInDataEntry;
import com.linc.platform.common.EntryTicketEntry;
import com.linc.platform.common.step.StepBaseEntry;
import com.linc.platform.common.step.StepTypeEntry;
import com.linc.platform.idm.model.UserViewEntry;
import com.linc.platform.transload.model.TransloadTaskViewEntry;
import com.linc.platform.transload.presenter.TrailerCheckInPresenter;
import com.linc.platform.transload.presenter.impl.TrailerCheckInPresenterImpl;
import com.linc.platform.transload.view.TrailerCheckInView;
import com.linc.platform.utils.CollectionUtil;
import com.linc.platform.utils.ConfigurationMapUtil;
import com.linc.platform.utils.ToastUtil;
import com.linc.platform.windowcheckin.CheckinStepEntry;
import com.linc.platform.windowcheckin.TransloadCheckinEntry;
import com.lt.linc.R;
import com.lt.linc.toolset.dockselect.DockSelectorActivity;
import com.lt.linc.toolset.workerselect.WorkerSelectorActivity;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */

public class TrailerCheckInActivity extends LincBaseActivity implements TrailerCheckInView {
    private static final int SELECT_ENTRY = 1;
    private static final int SELECT_ASSIGNEE = 2;
    private static final int SELECT_DOCK = 3;
    private Toolbar toolbar;
    private AppCompatTextView rnNoTxt;
    private AppCompatTextView loadNoTxt;
    private LinearLayout trailerNoLayout;
    private AppCompatTextView trailerNoTxt;
    private AppCompatTextView tractorNoTxt;
    private LinearLayout assigneeLayout;
    private AppCompatTextView assigneeTxt;
    private AppCompatTextView mcDotTxt;
    private AppCompatTextView carrierTxt;
    private AppCompatTextView driverTxt;
    private LinearLayout selectDockLayout;
    private AppCompatTextView dockNameTxt;
    private AppCompatButton checkInBtn;

    private TrailerCheckInPresenter presenter;

    private String newLoadId;
    private StepBaseEntry stepBaseEntry;
    private EntryTicketEntry selectedEntryTicket;
    private LocationEntry selectedDock;
    private List<UserViewEntry> assigneeEntries;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_trailer_check_in);
        initViewLayout();

        newLoadId = getIntent().getStringExtra(SelectEntryActivity.NEW_LOAD_ID);
        stepBaseEntry = (StepBaseEntry) getIntent().getSerializableExtra(StepBaseEntry.TAG);
        selectedEntryTicket = (EntryTicketEntry) getIntent().getSerializableExtra(EntryTicketEntry.TAG);
        presenter = new TrailerCheckInPresenterImpl(this, stepBaseEntry);

        if (!TextUtils.isEmpty(selectedEntryTicket.dockId)) {
            selectedDock = new LocationEntry();
            selectedDock.id = selectedEntryTicket.dockId;
            selectedDock.name = selectedEntryTicket.dockName;
            dockNameTxt.setText(selectedDock.name);
        }

        initToolBar(toolbar, stepBaseEntry.taskId);
        toolbar.setSubtitle(R.string.text_transload_trailer_check_in);

        trailerNoLayout.setOnClickListener(view -> startSelectEntryActivity());
        selectDockLayout.setOnClickListener(view -> startSelectDockActivity());
        assigneeLayout.setOnClickListener(view -> startSelectAssigneeActivity());
        checkInBtn.setOnClickListener(view -> doCheckIn());

        initEntryView();
    }


    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        menu.add(R.string.action_help);
        return super.onCreateOptionsMenu(menu);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (TextUtils.equals(item.getTitle(), getString(R.string.action_help))) {
            presenter.getAndOpenHelpPage(this, ConfigurationMapUtil.PAGE_KEY_TRANSLOAD_TASK_TRAILER_CHECKIN, getFacilityId());
        }
        return super.onOptionsItemSelected(item);
    }

    private void startSelectDockActivity() {
        Intent intent = new Intent(this, DockSelectorActivity.class);
        if (selectedDock != null) {
            intent.putExtra(LocationEntry.TAG, selectedDock);
        }
        startActivityForResult(intent, SELECT_DOCK);
    }

    private void startSelectEntryActivity() {
        Intent intent = new Intent(this, SelectEntryActivity.class);
        intent.putExtra(SelectEntryActivity.NEW_LOAD_ID, newLoadId);
        intent.putExtra(StepBaseEntry.TAG, stepBaseEntry);
        intent.putExtra(SelectEntryActivity.UPDATE_MODEL, true);
        startActivityForResult(intent, SELECT_ENTRY);
    }

    private void startSelectAssigneeActivity() {
        Intent intent = new Intent(this, WorkerSelectorActivity.class);
        if (CollectionUtil.isNotNullOrEmpty(assigneeEntries)) {
            intent.putExtra(WorkerSelectorActivity.SELECTED_IDM, (Serializable) assigneeEntries);
        }
        startActivityForResult(intent, SELECT_ASSIGNEE);
    }

    private void doCheckIn() {
        if (selectedDock == null) {
            ToastUtil.showToast(this, R.string.msg_please_select_dock);
            return;
        }

        if (selectedEntryTicket == null) {
            ToastUtil.showToast(this, R.string.msg_please_select_entry);
            return;
        }

        if (CollectionUtil.isNullOrEmpty(assigneeEntries)) {
            ToastUtil.showToast(this, R.string.msg_please_select_assignee);
            return;
        }

        presenter.createNewShipping(newLoadId, selectedEntryTicket.id);
    }

    @Override
    protected void onResume() {
        super.onResume();
        rnNoTxt.setText(stepBaseEntry.receiptId);
        loadNoTxt.setText(newLoadId);
    }

    @Override
    public void onNewShippingCreateSuccess(StepBaseEntry newShippingStep) {
        if (newShippingStep == null) {
            ToastUtil.showToast(this, R.string.msg_new_shipping_step_ont_found);
            return;
        }

        presenter.checkInEntry(selectedEntryTicket.id, buildCheckinEntry(newShippingStep));
    }

    private TransloadCheckinEntry buildCheckinEntry(StepBaseEntry newShippingStep) {
        TransloadTaskViewEntry taskViewEntry = presenter.getTransloadTask();

        TransloadCheckinEntry transloadCheckinEntry = new TransloadCheckinEntry();
        transloadCheckinEntry.taskId = taskViewEntry.id;
        transloadCheckinEntry.taskAssigneeUserId = taskViewEntry.assigneeUserId;
        transloadCheckinEntry.taskNote = taskViewEntry.description;
        transloadCheckinEntry.dockId = selectedDock.id;

        CheckinStepEntry checkinStepEntry = new CheckinStepEntry();
        checkinStepEntry.id = newShippingStep.id;
        checkinStepEntry.stepType = StepTypeEntry.SHIPPING;
        checkinStepEntry.receiptId = stepBaseEntry.receiptId;
        checkinStepEntry.loadId = newLoadId;
        checkinStepEntry.dockId = selectedDock.id;
        checkinStepEntry.assigneeUserIds = Stream.of(assigneeEntries)
                .map(assignee -> assignee.idmUserId)
                .collect(Collectors.toList());

        transloadCheckinEntry.checkInSteps = Collections.singletonList(checkinStepEntry);

        return transloadCheckinEntry;
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (entrySelectCompleted(requestCode, resultCode)) {
            selectedEntryTicket = (EntryTicketEntry) data.getSerializableExtra(EntryTicketEntry.TAG);
            initEntryView();
        }

        if (assigneeSelectCompleted(requestCode, resultCode)) {
            assigneeEntries = (List<UserViewEntry>) data.getSerializableExtra(WorkerSelectorActivity.RESULT);
            initAssigneeView();
        }

        if (dockSelectCompleted(requestCode, resultCode)) {
            selectedDock = (LocationEntry) data.getSerializableExtra(LocationEntry.TAG);
            dockNameTxt.setText(selectedDock.name);
        }
    }

    private boolean dockSelectCompleted(int requestCode, int resultCode) {
        return resultCode == RESULT_OK && requestCode == SELECT_DOCK;
    }

    private boolean entrySelectCompleted(int requestCode, int resultCode) {
        return resultCode == RESULT_OK && requestCode == SELECT_ENTRY;
    }

    private boolean assigneeSelectCompleted(int requestCode, int resultCode) {
        return resultCode == RESULT_OK && requestCode == SELECT_ASSIGNEE;
    }

    private void initEntryView() {
        if (selectedEntryTicket.checkInData != null) {
            CheckInDataEntry checkInDataEntry = selectedEntryTicket.checkInData;
            String trailer = CollectionUtil.isNotNullOrEmpty(checkInDataEntry.trailers)
                    ? Stream.of(checkInDataEntry.trailers).collect(Collectors.joining(" | "))
                    : "";

            trailerNoTxt.setText(trailer);
            tractorNoTxt.setText(checkInDataEntry.tractor);
            mcDotTxt.setText(checkInDataEntry.mcDot);
            carrierTxt.setText(checkInDataEntry.carrierName);
            driverTxt.setText(checkInDataEntry.driverName);
        }
    }

    private void initAssigneeView() {
        String assignee = CollectionUtil.isNotNullOrEmpty(assigneeEntries)
                ? Stream.of(assigneeEntries).map(e -> e.getName()).collect(Collectors.joining("\n"))
                : "";

        assigneeTxt.setText(assignee);
    }

    @Override
    public void onEntryCheckInResult(boolean success) {
        if (success) {
            ToastUtil.showToast(this, R.string.msg_checkin_success);
            finish();
        }
    }

    private void initViewLayout() {
        toolbar = findView(R.id.toolbar);
        rnNoTxt = findView(R.id.rn_no_txt);
        loadNoTxt = findView(R.id.load_no_txt);
        trailerNoLayout = findView(R.id.trailer_no_layout);
        trailerNoTxt = findView(R.id.trailer_no_txt);
        tractorNoTxt = findView(R.id.tractor_no_txt);
        assigneeLayout = findView(R.id.assignee_layout);
        assigneeTxt = findView(R.id.assignee_txt);
        mcDotTxt = findView(R.id.mc_dot_txt);
        carrierTxt = findView(R.id.carrier_txt);
        driverTxt = findView(R.id.driver_txt);
        selectDockLayout = findView(R.id.select_dock_layout);
        dockNameTxt = findView(R.id.dock_name_txt);
        checkInBtn = findView(R.id.check_in_btn);
    }
}
