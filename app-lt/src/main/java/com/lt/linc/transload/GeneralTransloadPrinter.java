package com.lt.linc.transload;

import com.linc.platform.foundation.model.organization.common.facility.FacilityEntry;
import com.linc.platform.http.ErrorCodeSubscriber;
import com.linc.platform.http.ErrorResponse;
import com.linc.platform.http.HttpService;
import com.linc.platform.http.IdResponse;
import com.linc.platform.localconfig.FacilityConfigPresenterImpl;
import com.linc.platform.print.api.LabelApi;
import com.linc.platform.print.api.PrintApi;
import com.linc.platform.print.api.PrinterWhApi;
import com.linc.platform.print.commonprintlp.PrintData;
import com.linc.platform.print.commonprintlp.PrintMsg;
import com.linc.platform.print.commonprintlp.PrintView;
import com.linc.platform.print.model.LabelSizeEntry;
import com.linc.platform.print.model.PrinterEntry;
import com.linc.platform.transload.view.GeneralTransloadPrinterView;
import com.linc.platform.utils.PrintUtil;
import com.linc.platform.utils.RxUtil;
import com.linc.platform.utils.ToastUtil;
import com.lt.linc.R;

import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;


import retrofit2.Response;

/**
 * <AUTHOR>
 */

public final class GeneralTransloadPrinter {
    private GeneralTransloadPrinterView labelPrinterView;
    private PrintApi printApi;
    private LabelApi labelApi;
    private PrinterWhApi printerWhApi;

    private String fileId;
    private String idmUserId;
    private String companyId;
    private PrinterEntry printerEntry;
    private PrintData printData;

    public GeneralTransloadPrinter(GeneralTransloadPrinterView labelPrinterView, String companyId, String idmUserId) {
        this.idmUserId = idmUserId;
        this.companyId = companyId;
        this.labelPrinterView = labelPrinterView;
        printApi = HttpService.createService(PrintApi.class);
        labelApi = HttpService.createService(LabelApi.class);
        initPrinter();
    }

    public static GeneralTransloadPrinter getInstance(GeneralTransloadPrinterView labelPrinterView,
                                                      String companyId, String idmUserId) {
        return new GeneralTransloadPrinter(labelPrinterView, companyId, idmUserId);
    }

    private void initPrinter() {
        printData = PrintData.basic(idmUserId, getFacilityName(), LabelSizeEntry.A4);
        printerEntry = PrintUtil.getPrinter(printData);
    }

    public void createPrintTallySheetFileId(String receiptId) {
        if (printerEntry == null) {
            labelPrinterView.selectPrinter();
            return;
        }

        labelPrinterView.showProgress(true);
        printApi.createPrintTallySheetFileId(receiptId)
                .compose(RxUtil.asyncSchedulers())
                .subscribe(new ErrorCodeSubscriber<Response<IdResponse>>() {
                    @Override
                    public void onSuccess(Response<IdResponse> idResponseResponse) {
                        fileId = idResponseResponse.body().fileId;
                        printLabel();
                    }

                    @Override
                    public void onFailed(ErrorResponse errorResponse) {
                        ToastUtil.showErrorToast(errorResponse.getErrorMessage());
                    }

                    @Override
                    public void onDone() {
                        labelPrinterView.showProgress(false);
                    }
                });
    }

    public void createTransloadLabelFileId(String receiptId) {
        if (printerEntry == null) {
            labelPrinterView.selectPrinter();
            return;
        }

        labelPrinterView.showProgress(true);
        labelApi.createFileId(receiptId)
                .compose(RxUtil.asyncSchedulers())
                .subscribe(new ErrorCodeSubscriber<Response<IdResponse>>() {
                    @Override
                    public void onSuccess(Response<IdResponse> idResponseResponse) {
                        fileId = idResponseResponse.body().fileId;
                        printLabel();
                    }

                    @Override
                    public void onFailed(ErrorResponse errorResponse) {
                        ToastUtil.showErrorToast(errorResponse.getErrorMessage());
                    }

                    @Override
                    public void onDone() {
                        labelPrinterView.showProgress(false);
                    }
                });

    }

    private void printLabel() {
        PrintView viewDelegate = new PrintView() {
            @Override
            public void onPrinterNotSelect() {
                ToastUtil.showToast(R.string.msg_please_select_printer);
            }

            @Override
            public void onPrintSuccess(@NotNull PrintData data, @NotNull PrinterEntry printerEntry) {
                labelPrinterView.onPrintResult(true);
            }

            @Override
            public void onPrintFailed(@NotNull ErrorResponse response, @Nullable PrinterEntry printerEntry) {
                if (printerEntry == null) {
                    ToastUtil.showToast(response.getErrorMessage());
                    return;
                }
                if (printerEntry.isWifi()) {
                    ToastUtil.showErrorToast(PrintMsg.formatError(printerEntry, response.error));
                } else {
                    labelPrinterView.onBlueToothPrintFailed(Integer.parseInt(response.code));
                }
            }

            @Override
            public void showProgress(boolean show) {
                labelPrinterView.showProgress(show);
            }
        };

        printData.jobData = new PrintData.JobData.PDF(fileId);
        PrintUtil.newInstance(viewDelegate).print(printData);
    }

    public String getFacilityName() {
        FacilityEntry facilityEntry = FacilityConfigPresenterImpl.getInstance().
                getFacility(idmUserId);

        return facilityEntry == null ? "" : facilityEntry.getName();
    }

    public PrinterEntry getPrinter() {
        return printerEntry;
    }
}
