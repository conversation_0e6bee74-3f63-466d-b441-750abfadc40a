package com.lt.linc.transload.stepwork;

import android.os.Bundle;
import androidx.appcompat.widget.AppCompatButton;
import androidx.appcompat.widget.Toolbar;
import android.text.TextUtils;
import android.view.Menu;
import android.view.MenuItem;

import com.customer.widget.CircleProgress;
import com.customer.widget.ScanEditText;
import com.customer.widget.core.LincBaseActivity;
import com.linc.platform.common.step.StepBaseEntry;
import com.linc.platform.transload.presenter.AddProNoPresenter;
import com.linc.platform.transload.presenter.impl.AddProNoPresenterImpl;
import com.linc.platform.transload.view.AddProNoView;
import com.linc.platform.utils.ConfigurationMapUtil;
import com.linc.platform.utils.ToastUtil;
import com.lt.linc.R;

/**
 * <AUTHOR>
 */

public class AddProNoActivity extends LincBaseActivity implements AddProNoView {
    private Toolbar toolbar;
    private ScanEditText proNoEdt;
    private AppCompatButton submitBtn;
    private StepBaseEntry stepBaseEntry;
    private AddProNoPresenter presenter;
    private CircleProgress circleProgress;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_transload_add_pro_no);
        toolbar = (Toolbar) findViewById(R.id.toolbar);
        proNoEdt = (ScanEditText) findViewById(R.id.pro_no_edt);
        submitBtn = (AppCompatButton) findViewById(R.id.submit_btn);

        initToolBar(toolbar, getString(R.string.text_add_pro_no));
        circleProgress = CircleProgress.create(this);

        stepBaseEntry = (StepBaseEntry) getIntent().getSerializableExtra(StepBaseEntry.TAG);
        presenter = new AddProNoPresenterImpl(this, stepBaseEntry);

        submitBtn.setOnClickListener(v -> toSubmit());
    }


    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        menu.add(R.string.action_help);
        return super.onCreateOptionsMenu(menu);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (TextUtils.equals(item.getTitle(), getString(R.string.action_help))) {
            presenter.getAndOpenHelpPage(this, ConfigurationMapUtil.PAGE_KEY_TRANSLOAD_TASK_SHIPPING_ADD_PRO_NO, getFacilityId());
        }
        return super.onOptionsItemSelected(item);
    }


    private void toSubmit() {
        if (TextUtils.isEmpty(proNoEdt.getText())) {
            ToastUtil.showToast(this, R.string.text_please_input_pro_no);
            return;
        }

        presenter.addProNo(proNoEdt.getText());
    }

    @Override
    public void showProgress(boolean show) {
        if (show) {
            circleProgress.show();
        } else {
            circleProgress.dismiss();
        }
    }

    @Override
    public void onAddSuccess(boolean success) {
        if (success) {
            ToastUtil.showToast(this, R.string.submit_success);
            finish();
        }
    }
}
