package com.lt.linc.transload.stepwork.lpnsetup;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.linc.platform.transload.model.CartonItemViewEntry;
import com.linc.platform.transload.model.CartonViewEntry;
import com.linc.platform.utils.CollectionUtil;
import com.linc.platform.utils.StringUtil;
import com.lt.linc.R;

/**
 * <AUTHOR>
 */

public class LpnViewAdapter extends BaseQuickAdapter<CartonViewEntry, BaseViewHolder> {
    private double receivedQtyCount;
    private double expectedItemQty;

    public LpnViewAdapter() {
        super(R.layout.item_lpn_view);
    }

    @Override
    protected void convert(BaseViewHolder helper, CartonViewEntry item) {
        double itemQty = 0D;
        if (CollectionUtil.isNotNullOrEmpty(item.items)) {
            for (CartonItemViewEntry cartonItemViewEntry : item.items) {
                itemQty += cartonItemViewEntry.qty;
            }
        }

        receivedQtyCount += itemQty;
        int textColor = receivedQtyCount > expectedItemQty
                ? helper.itemView.getResources().getColor(R.color.exception_red)
                : helper.itemView.getResources().getColor(R.color.text_grey);

        helper.setText(R.id.carton_no_txt, item.cartonNo)
                .setTextColor(R.id.item_qty_txt, textColor)
                .setTextColor(R.id.carton_no_txt, textColor)
                .setText(R.id.item_qty_txt, StringUtil.ignorePointZero(itemQty))
                .addOnClickListener(R.id.delete_img);
    }

    public void setExpectedItemQty(double expectedItemQty) {
        receivedQtyCount = 0;
        this.expectedItemQty = expectedItemQty;
    }
}
