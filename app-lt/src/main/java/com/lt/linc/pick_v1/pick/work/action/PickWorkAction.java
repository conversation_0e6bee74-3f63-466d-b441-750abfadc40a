package com.lt.linc.pick_v1.pick.work.action;

import android.text.TextUtils;

import com.annimon.stream.Collectors;
import com.annimon.stream.Stream;
import com.linc.platform.baseapp.model.LocationEntry;
import com.linc.platform.baseapp.model.LocationTypeEntry;
import com.linc.platform.common.SNType;
import com.linc.platform.foundation.model.ItemAkaEntry;
import com.linc.platform.foundation.model.ItemSpecEntry;
import com.linc.platform.pick.model.InventoryIssueParameter;
import com.linc.platform.pick.model.PickLocationViewEntry;
import com.linc.platform.pick.model.PickResultUpdateEntry;
import com.linc.platform.pick.model.PickStageLocationSuggestEntry;
import com.linc.platform.pick.model.PickTaskViewEntry;
import com.linc.platform.pick.model.PickWayEntry;
import com.linc.platform.pick.model.batchprint.SmallParcelShipmentLabel;
import com.linc.platform.pick.model.picktote.PickToteCartWrap;
import com.linc.platform.pick.model.picktote.ToteEntry;
import com.linc.platform.utils.CollectionUtil;
import com.linc.platform.utils.LocationUtil;
import com.lt.linc.pick_v1.pick.model.PickV1TypeMode;
import com.lt.linc.pick_v1.pick.start.model.PickV1TypeEntry;
import com.lt.linc.pick_v1.pick.work.model.ItemInfoViewEntry;
import com.lt.linc.pick_v1.pick.work.model.ItemLotNoEntry;
import com.lt.linc.pick_v1.pick.work.model.ItemSnViewEntry;
import com.lt.linc.pick_v1.pick.work.model.LocationItemSuggestionViewEntry;
import com.lt.linc.pick_v1.pick.work.model.LpItemOrderSuggestionViewEntry;
import com.lt.linc.pick_v1.pick.work.model.PickItemLinesViewEntry;
import com.lt.linc.pick_v1.pick.work.model.PickSuggestUomEntry;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class PickWorkAction {

    private String idmUserId;
    private String facilityName;
    private PickTaskViewEntry pickTask;

    private final PickV1TypeEntry pickTypeEntry;

    private PickLocationViewEntry currentLocation;

    private PickResultUpdateEntry pickResult;

    private List<PickItemLinesViewEntry> pickTaskProgressItemLines;

    private String currentPickOrderId;

    private PickSuggestUomEntry suggestUomEntry;

    private List<String> usedLpList = new ArrayList<>();

    private int currentItemSuggestQty;

    private int currentItemPickQty;

    private boolean isSingleItemOnLP;

    private boolean isPickByPallet;

    private List<String> suggestLp;

    private List<ItemLotNoEntry> enterLotNoList;

    private final PickWorkItemAction pickItemAction;

    private final PickToteCartWrap pickToteCartWrap;

    private String itemValidateRegex;

    private List<PickStageLocationSuggestEntry> stageSuggestLocations;
    private List<SmallParcelShipmentLabel> smallParcelShipmentLabels;
    private List<SmallParcelShipmentLabel> printedSpShipmentLabels = new ArrayList<>();
    private List<SmallParcelShipmentLabel> printFailedSpShipmentLabels = new ArrayList<>();

    private int printShippingLabelIntervalTime = 3000;

    public PickWorkAction(PickTaskViewEntry pickTask, PickV1TypeEntry pickTypeEntry, PickToteCartWrap pickToteCartWrap, String idmUserId, String facilityName) {
        this.pickTypeEntry = pickTypeEntry;
        this.pickToteCartWrap = pickToteCartWrap;
        this.pickItemAction = new PickWorkItemAction(pickTask);
        this.idmUserId = idmUserId;
        this.facilityName = facilityName;
        updatePickTask(pickTask);
    }

    private void updatePickTask(PickTaskViewEntry pickTask) {
        this.pickTask = pickTask;
        this.pickResult = new PickResultUpdateEntry();
    }

    public PickTaskViewEntry getPickTask() {
        return pickTask;
    }

    public void setPickSuggestData(LocationItemSuggestionViewEntry pickSuggestData) {
        if (null == pickSuggestData) {
            pickSuggestData = new LocationItemSuggestionViewEntry();
        }
        setSuggestLocation(pickSuggestData.suggestLocation);
        updateOrderId(pickSuggestData.orderItemSuggestion == null ? "" : pickSuggestData.orderItemSuggestion.orderId);
        updatePickUnitId(pickSuggestData.orderItemSuggestion == null ? "" : pickSuggestData.orderItemSuggestion.inventoryUnitId);
        updateItemSuggestQty(pickSuggestData.orderItemSuggestion == null ? 0 : pickSuggestData.orderItemSuggestion.suggestQty.intValue());
        String suggestUom = pickSuggestData.orderItemSuggestion == null ? "" : pickSuggestData.orderItemSuggestion.suggestUom;
        String inventoryUom = pickSuggestData.orderItemSuggestion == null ? "" : pickSuggestData.orderItemSuggestion.inventoryUom;
        double rate = pickSuggestData.orderItemSuggestion == null ? 1 : pickSuggestData.orderItemSuggestion.rate;
        updateSuggestUom(suggestUom, inventoryUom, rate);
        updatePickItem(pickSuggestData.orderItemSuggestion == null ? null : pickSuggestData.orderItemSuggestion.itemSpec,
                pickSuggestData.orderItemSuggestion == null ? null : pickSuggestData.orderItemSuggestion.akas,
                pickSuggestData.orderItemSuggestion == null ? null : pickSuggestData.orderItemSuggestion.imageFileIds);
        updateBaseUnit(pickSuggestData.orderItemSuggestion != null && pickSuggestData.orderItemSuggestion.isBaseUnit);
        updateLotNo(pickSuggestData.inventoryLotNos, pickSuggestData.orderItemSuggestion == null ? null : pickSuggestData.orderItemSuggestion.lotNo);
        updateItemSnList(pickSuggestData.itemSnList);
        updateUsedLpList(pickSuggestData.usedLpList);
        setSingleItemOnLP(pickSuggestData.isSingleItemOnLP != null && pickSuggestData.isSingleItemOnLP);
        setStageSuggestLocations(pickSuggestData.stageSuggestLocations);
        if (pickSuggestData.orderItemSuggestion != null) {
            suggestLp = pickSuggestData.orderItemSuggestion.suggestLp;
        }
    }

    private void setSuggestLocation(PickLocationViewEntry suggestLocation) {
        if (suggestLocation == null) {
            return;
        }
        this.currentLocation = suggestLocation;
        if (isPickSuggestLocation()) {
            updateFromLp(suggestLocation.hlpId);
        }
    }

    public void updateLocation(LocationEntry scanLocation) {
        if (currentLocation == null) {
            currentLocation = new PickLocationViewEntry();
        }
        setSuggestLocation(currentLocation.location(scanLocation));
    }

    public void updatePickUnitId(String unitId) {
        pickResult.unitId = unitId;
    }

    public void setPickTaskProgress(List<PickItemLinesViewEntry> pickItemLinesViewEntryList) {
        this.pickTaskProgressItemLines = pickItemLinesViewEntryList;
    }

    public String getSuggestLocationName() {
        if (currentLocation != null) {
            return LocationUtil.getLocationNameWithSection(currentLocation.location, currentLocation.section, currentLocation.zone);
        }
        return "";
    }

    public boolean isPickSuggestLocation() {
        if (currentLocation == null) {
            return false;
        }
        return currentLocation.type == LocationTypeEntry.PICK;
    }

    public String getStageSuggestLocationNames() {
        if(CollectionUtil.isNotNullOrEmpty(stageSuggestLocations)) {
            return Stream.of(stageSuggestLocations).map(PickStageLocationSuggestEntry::getLocationName).collect(Collectors.joining(","));
        }
        return "";
    }

    public void setStageSuggestLocations(List<PickStageLocationSuggestEntry> stageSuggestLocations) {
        this.stageSuggestLocations = stageSuggestLocations;
    }

    public boolean hasStageSuggestLocations() {
        return CollectionUtil.isNotNullOrEmpty(stageSuggestLocations);
    }

    private void setSingleItemOnLP(boolean singleItemOnLP) {
        isSingleItemOnLP = singleItemOnLP;
    }

    public boolean isSingleItemOnLP() {
        return isSingleItemOnLP;
    }

    public void updateOrderItemSuggestion(LpItemOrderSuggestionViewEntry orderItem) {
        updateFromLp(orderItem.orderLpSuggestion.lpId);
        updateOrderId(orderItem.orderLpSuggestion.orderId);
        updatePickUnitId(orderItem.orderLpSuggestion.inventoryUnitId);
        updateItemSuggestQty(orderItem.orderLpSuggestion.suggestQty.intValue());
        updateSuggestUom(orderItem.orderLpSuggestion.suggestUom, orderItem.orderLpSuggestion.inventoryUom, orderItem.orderLpSuggestion.rate);
        updatePickItem(orderItem.orderLpSuggestion.itemSpec, orderItem.orderLpSuggestion.akas, orderItem.orderLpSuggestion.imageFileIds);
        updateItemSnList(orderItem.itemSnList);
        updateUsedLpList(orderItem.usedLpList);
        updateBaseUnit(orderItem.orderLpSuggestion.isBaseUnit);
        updateLotNo(orderItem.inventoryLotNos, orderItem.orderLpSuggestion.lotNo);
        setSingleItemOnLP(orderItem.orderLpSuggestion.isSingleItemOnLP != null && orderItem.orderLpSuggestion.isSingleItemOnLP);
        adjustAutoPickByPallet(orderItem.orderLpSuggestion.lpTotalQty.intValue(), orderItem.orderLpSuggestion.rate);
    }

    private void updateBaseUnit(boolean baseUnit) {
        pickItemAction.updateBaseUnit(baseUnit);
    }

    private void updateLotNo(List<ItemLotNoEntry> inventoryLotNos, List<ItemLotNoEntry> itemLotNoList) {
        pickItemAction.updateLotNo(inventoryLotNos, itemLotNoList);
    }

    public int getSuggestItemQty() {
        return currentItemSuggestQty;
    }

    public List<String> getPickItemPhotos() {
        return pickItemAction.getPickItemPhotos();
    }

    public String getPickItemSpecName() {
        return pickItemAction.getLastItemSpecName();
    }

    public ItemInfoViewEntry getPickItemInfo() {
        return pickItemAction.getPickItemInfo();
    }

    public PickSuggestUomEntry getSuggestUom() {
        return suggestUomEntry;
    }

    public void updateSuggestUom(String suggestUom, String inventoryUom, Double inventoryUomUnitProportion) {
        this.suggestUomEntry = new PickSuggestUomEntry(suggestUom, inventoryUom, inventoryUomUnitProportion);
    }

    public String getPickOrderId() {
        if (isLocationMatch(currentLocation.locationId)) {
            return currentPickOrderId;
        }
        return "";
    }

    public boolean isItemCodeMatch(String scanItem) {
        return pickItemAction.isItemCodeMatch(scanItem);
    }

    public boolean isNeedCollectSnOrRFID() {
        return pickItemAction.isNeedCollectSn(currentPickOrderId) || isNeedCollectRFID();
    }

    public boolean isNeedCollectRFID() {
        return pickItemAction.isNeedCollectRFID(currentPickOrderId);
    }

    private void updateItemSuggestQty(int qty) {
        this.currentItemSuggestQty = qty;
    }

    public void updateItemPickQty(int qty) {
        this.currentItemPickQty = qty;
    }

    public boolean isCurrentItemPickComplete() {
        return currentItemPickQty == currentItemSuggestQty;
    }

    public boolean requireScanLotNoOnPicking() {
        return pickItemAction.requireScanLotNoOnPicking();
    }

    public boolean hasInventoryLotNo() {
        return pickItemAction.hasInventoryLotNo();
    }

    public void setPickItemLotNo(List<ItemLotNoEntry> lotNoEntryList) {
        this.enterLotNoList = lotNoEntryList;
    }

    public boolean isLocationMatch(String scanLocationId) {
        return currentLocation != null && scanLocationId.equals(currentLocation.locationId);
    }

    private void updateFromLp(String fromLp) {
        pickResult.fromLPId = fromLp;
        pickItemAction.updateFromLp(fromLp);
    }

    public String getPickFromLp() {
        return pickResult.fromLPId;
    }

    private void updateOrderId(String orderId) {
        this.currentPickOrderId = orderId;
    }

    private void updatePickItem(ItemSpecEntry itemSpec, List<ItemAkaEntry> itemAkaList, List<String> imageFileIds) {
        pickItemAction.updatePickItem(itemSpec, itemAkaList, imageFileIds);
    }

    private void updateItemSnList(List<String> snList) {
        pickItemAction.updateItemSnList(snList);
    }

    public ItemSnViewEntry getItemSnViewEntry() {
        return pickItemAction.getItemSnViewEntry(currentPickOrderId);
    }

    private void updateUsedLpList(List<String> usedLpList) {
        this.usedLpList = usedLpList;
    }

    public void setPickedItemSnList(List<String> snList) {
        pickResult.snList = snList;
    }

    public List<String> previousLpList() {
        if (CollectionUtil.isNotNullOrEmpty(usedLpList)) {
            return usedLpList;
        }
        PickV1TypeMode pickType = pickTypeModel();
        switch (pickType) {
            case PICK_TO_TOTE:
                ToteEntry toteEntry = pickToteCartWrap.getPickToToteEquipment(currentPickOrderId);
                if (null == toteEntry) {
                    return null;
                }
                return Collections.singletonList(toteEntry.barcode);
            case PICK_TO_PRE_PRINT_LP:
                if (TextUtils.isEmpty(pickTypeEntry.lp)) {
                    return null;
                }
                return Collections.singletonList(pickTypeEntry.lp);
        }
        return null;
    }

    public PickV1TypeMode pickTypeModel() {
        return pickTypeEntry.pickTypeMode;
    }

    public void savePrintLp(String printLp) {
        if (pickTypeModel() == PickV1TypeMode.PICK_TO_LIVE_PRINT_LP) {
            pickTypeEntry.lp = printLp;
        }
    }

    public void updateToLP(String container) {
        pickResult.toLPId = container;
    }

    public boolean isNeedScanPreviousLP(String container, boolean newContainer) {
        if (CollectionUtil.isNotNullOrEmpty(usedLpList) && !newContainer) {
            return !usedLpList.contains(container);
        }
        return false;
    }

    public boolean isScanLpMatchPrintedLp(String container, boolean forceScanPrintedLp) {
        String printLp = pickTypeEntry.lp;
        if (forceScanPrintedLp && !TextUtils.isEmpty(printLp) && !container.equals(printLp)) {
            return false;
        }
        return true;
    }

    public void setPickByPallet(boolean isPickByPallet) {
        this.isPickByPallet = isPickByPallet;
        if (isPickByPallet) {
            pickResult.toLPId = pickResult.fromLPId;
        } else {
            pickResult.toLPId = "";
        }
    }

    public boolean isPickByPallet() {
        return isPickByPallet;
    }

    public PickResultUpdateEntry getPickResult() {
        pickResult.isEntireLPPick = isPickByPallet;
        pickResult.locationId = getLastLocationId();
        pickResult.orderId = currentPickOrderId;
        pickResult.itemSpecName = pickItemAction.getLastItemSpecName();
        pickResult.itemSpecId = pickItemAction.getLastItemSpecId();
        pickResult.pickedQtyByLPUnit = suggestUomEntry.inventoryUomUnitProportion * currentItemPickQty;
        pickResult.snType = isNeedCollectRFID() ? SNType.RFID : SNType.SN;
        return pickResult;
    }

    public List<PickResultUpdateEntry> getPickResults() {
        pickResult = getPickResult();
        if (CollectionUtil.isNullOrEmpty(enterLotNoList)) {
            return Collections.singletonList(pickResult);
        }
        List<PickResultUpdateEntry> pickResults = Stream.of(enterLotNoList).map(lotNo -> {
            PickResultUpdateEntry result = pickResult.withLotNo(lotNo.lotNo, suggestUomEntry.inventoryUomUnitProportion * lotNo.qty);
            return result;
        }).toList();
        return pickResults;
    }

    public String getLastLocationId() {
        if (currentLocation != null) {
            return currentLocation.locationId;
        }
        return "";
    }

    public String getLastItemSpecId() {
        return pickItemAction.getLastItemSpecId();
    }

    public boolean isCurrentTaskNeedPick() {
        return !Stream.of(pickTaskProgressItemLines).filter(item -> !item.isFinish).toList().isEmpty();
    }

    public InventoryIssueParameter getPickIssueParameter() {
        InventoryIssueParameter pickIssueParameter = new InventoryIssueParameter();
        pickIssueParameter.taskId = pickTask.id;
        pickIssueParameter.taskType = pickTask.taskType;
        if (pickTask.customerEntry != null) {
            pickIssueParameter.customerId = pickTask.customerEntry.orgId;
        }
        pickIssueParameter.locationId = getLastLocationId();
        pickIssueParameter.inputtedItemSpecId = getLastItemSpecId();
        return pickIssueParameter;
    }

    private void adjustAutoPickByPallet(double lpTotalQty, double rate) {
        //如果需要采集sn就不需要自动pick by pallet
        if (!needPickByPallet() || isNeedCollectSnOrRFID()) {
            return;
        }
        setPickByPallet(lpTotalQty != 0 && lpTotalQty * rate == currentItemSuggestQty && isSingleItemOnLP);
    }

    public boolean needPickByPallet() {
        return !isPickSuggestLocation();
    }

    public String getPickToteCart() {
        if (pickTypeModel() == PickV1TypeMode.PICK_TO_TOTE) {
            return pickToteCartWrap.getToteCartBarcode();
        }
        return "";
    }

    public List<String> getSuggestLp() {
        return suggestLp;
    }

    public boolean isLpCodeMatch(String lpId) {
        if (CollectionUtil.isNullOrEmpty(suggestLp)) {
            return true;
        }
        return suggestLp.contains(lpId);
    }

    public void clearSuggestLpIds(){
        suggestLp.clear();
    }

    public String getItemValidateRegex() {
        return itemValidateRegex;
    }

    public void setItemValidateRegex(String itemValidateRegex) {
        this.itemValidateRegex = itemValidateRegex;
    }

    public boolean needPrintShippingLabelOnSubmit(){
       return pickTask.customerEntry != null && PickWayEntry.WAVE_PICK_BY_ITEM == pickTask.pickWay
               && pickTask.customerEntry.printShippingLabelOnSubmit;
    }

    public String getIdmUserId(){
        return idmUserId;
    }

    public String getFacilityName(){
        return facilityName;
    }

    public void clearShipmentLabels() {
        smallParcelShipmentLabels = null;
        printedSpShipmentLabels.clear();
        printFailedSpShipmentLabels.clear();
    }

    public boolean hasPrintFailedShipmentLabels() {
        return !printFailedSpShipmentLabels.isEmpty();
    }

    public void retryPrintFailedShippingLabels() {
        smallParcelShipmentLabels = new ArrayList<>(printFailedSpShipmentLabels);
        printFailedSpShipmentLabels.clear();
        printedSpShipmentLabels.clear();
    }

    public void addPrintedSpShipmentLabels(List<SmallParcelShipmentLabel> printedSpShipmentLabels) {
        this.printedSpShipmentLabels.addAll(printedSpShipmentLabels);
    }

    public void addPrintFailedSpShipmentLabels(List<SmallParcelShipmentLabel> printFailedSpShipmentLabels) {
        this.printFailedSpShipmentLabels.addAll(printFailedSpShipmentLabels);
    }

    public void setSmallParcelShipmentLabels(List<SmallParcelShipmentLabel> smallParcelShipmentLabels) {
        this.smallParcelShipmentLabels = smallParcelShipmentLabels;
    }

    public List<SmallParcelShipmentLabel> getSmallParcelShipmentLabels() {
        return smallParcelShipmentLabels;
    }

    public List<SmallParcelShipmentLabel> getPrintedSpShipmentLabels() {
        return printedSpShipmentLabels;
    }

    public List<SmallParcelShipmentLabel> getPrintFailedSpShipmentLabels() {
        return printFailedSpShipmentLabels;
    }

    public void setPrintShippingLabelIntervalTime(int printShippingLabelIntervalTime) {
        this.printShippingLabelIntervalTime = printShippingLabelIntervalTime;
    }

    public int getPrintShippingLabelIntervalTime() {
        return printShippingLabelIntervalTime;
    }
}
