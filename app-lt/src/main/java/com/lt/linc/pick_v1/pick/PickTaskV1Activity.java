package com.lt.linc.pick_v1.pick;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.KeyEvent;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AlertDialog;

import com.annimon.stream.Stream;
import com.customer.widget.EditTextDialog;
import com.customer.widget.TimeElapsedCallBack;
import com.customer.widget.TimeElapsedView;
import com.linc.platform.common.lp.LpTypeEntry;
import com.linc.platform.common.step.StepBaseEntry;
import com.linc.platform.common.step.StepStatusEntry;
import com.linc.platform.common.step.StepTypeEntry;
import com.linc.platform.core.PermissionManager;
import com.linc.platform.generaltask.model.GeneralTaskViewEntry;
import com.linc.platform.idm.model.PermissionEntry;
import com.linc.platform.pick.model.PickTaskStartEvent;
import com.linc.platform.pick.model.PickTaskViewEntry;
import com.linc.platform.utils.ConfigurationMapUtil;
import com.linc.platform.utils.ResUtil;
import com.lt.linc.R;
import com.lt.linc.common.mvvm.BaseVMFragmentActivity;
import com.lt.linc.common.taskworktime.TaskWorkTimeAlarm;
import com.lt.linc.common.taskworktime.TaskWorkTimeUiEvent;
import com.lt.linc.home.more.hospital.dialog.HospitalInfoDialog;
import com.lt.linc.home.more.material.MaterialCenterActivity;
import com.lt.linc.home.more.material.work.materialselect.MaterialItemSelectActivity;
import com.lt.linc.home.more.wifilocating.WifiLocatingUtil;
import com.lt.linc.pick.batchprint.PickTaskPrintLabelActivity;
import com.lt.linc.pick.newpick.returnpicked.ReturnPickedActivity;
import com.lt.linc.pick.newpick.returnpicked.ReturnPickedEvenBusData;
import com.lt.linc.pick_v1.pick.model.PickStepMode;
import com.lt.linc.pick_v1.pick.reprintshippinglabel.ReprintShippingLabelActivity;
import com.lt.linc.pick_v1.pick.start.PickTaskStartFragment;
import com.lt.linc.pick_v1.pick.step_progress.PickStepProgressFragment;
import com.lt.linc.pick_v1.pick.view_progress.PickTaskViewProgressFragment;
import com.lt.linc.pick_v1.pick.work.PickTaskWorkFragment;
import com.lt.linc.pick_v1.pick.work.dialog.PickForceCloseDialog;
import com.lt.linc.step.stepstatus.StepStatusFragment;
import com.lt.linc.step.stepstatus.StepStatusPermissionConfig;
import com.lt.linc.toolset.barcodedetail.ItemDetailActivity;
import com.lt.linc.toolset.print.item.PrintItemActivity;
import com.lt.linc.toolset.print.lp.PrintLpActivity;
import com.lt.linc.util.rfid.RFIDDeviceManager;
import com.unis.autotrackdispatcher.annotation.Trace;
import com.unis.fragmentlauncher.SupportFragment;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;


@Trace(value = "PICK V1")
public class PickTaskV1Activity extends BaseVMFragmentActivity<PickTaskVM> {

    public static final int REQUEST_CODE_RETURN_PICKED = 1002;

    private TimeElapsedView timeElapsedView;

    private StepBaseEntry stepBaseEntry;
    private PickTaskViewEntry pickTaskViewEntry;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        stepBaseEntry = getIntentObject(StepBaseEntry.TAG);
        super.onCreate(savedInstanceState);
        timeElapsedView = findViewById(R.id.time_elapsed_view);
        observeTaskTimeEvent();
        EventBus.getDefault().register(this);
        initToolBar(getToolbar(), stepBaseEntry.taskId);
        WifiLocatingUtil.requestPermission(this);
    }

    @Override
    protected int getLayoutId() {
        return R.layout.activity_fragment_container_v1;
    }


    @Override
    protected PickTaskVM createViewModel() {
        return new PickTaskVM();
    }

    @Override
    protected SupportFragment getRootFragment(Intent intent) {
        if (stepBaseEntry != null && (stepBaseEntry.status == StepStatusEntry.DONE || stepBaseEntry.status == StepStatusEntry.FORCE_CLOSED || !stepBaseEntry.assigneeUserIds.contains(getIdmUserId()))) {
            StepStatusPermissionConfig config = new StepStatusPermissionConfig(true, false);
            StepStatusFragment fragment = StepStatusFragment.newInstance(stepBaseEntry, config);
            fragment.setOnOperateSuccessListener((result -> {
                viewModel.updateStepStatusOperateResult(result);
            }));
            return fragment;
        }
        return PickTaskStartFragment.newInstance();
    }

    @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
    public void onEvent(PickTaskStartEvent event) {
        stepBaseEntry = event.stepBaseEntry;
        pickTaskViewEntry = event.taskViewEntry;
        viewModel.setStepBaseEntry(stepBaseEntry);
        viewModel.setPickTask(pickTaskViewEntry);
        if (!TextUtils.isEmpty(pickTaskViewEntry.equipmentBarcode)) {
            viewModel.setToteCartDetail(pickTaskViewEntry.equipmentBarcode);
        }
    }


    @Override
    protected boolean isShowToolbar() {
        return true;
    }

    @Override
    public boolean onPrepareOptionsMenu(Menu menu) {
        super.onPrepareOptionsMenu(menu);
        menu.clear();
        getMenuInflater().inflate(R.menu.activity_pick_v1_menu, menu);
        menu.add(getString(R.string.menu_view_progress));
        if (viewModel.pickWorkFlow().pickUserLevelController().allowReturnPickedInventory) {
            menu.add(getString(R.string.menu_item_return_inventory));
        }
        if (viewModel.pickWorkFlow().pickUserLevelController().allowPrintOrCopyLP) {
            menu.add(getString(R.string.menu_item_print_copy_lp));
        }
        if (viewModel.pickWorkFlow().pickUserLevelController().allowLabelBatchPrint) {
            menu.add(getString(R.string.title_label_batch_print));
        }
        if (viewModel.pickWorkFlow().pickUserLevelController().allowInventorySearch && isPickWorkPage()) {
            menu.add(getString(R.string.inventory_search));
        }
        if (viewModel.pickWorkFlow().pickUserLevelController().allowReportPartialPelletIssue) {
            menu.add(getString(R.string.text_report_partial_pallet_issue));
        }
        if (viewModel.pickWorkFlow().pickUserLevelController().allowPrintItemLabel) {
            menu.add(R.string.title_print_item_label);
        }
        if (enableCollectMaterial()) {
            menu.add(R.string.menu_collect_material);
        }
        if (viewModel.pickWorkFlow().pickUserLevelController().allowForceCloseStep) {
            menu.add(R.string.force_close_step);
        }
        if (viewModel.pickWorkFlow().pickUserLevelController().allowShowHelp) {
            menu.add(R.string.action_help);
        }
        if (isPickWorkPage() && getFacility().locationSegmentedScanning) {
            menu.add(R.string.menu_switch_1_or_2_segmented_location);
        }
        if (getFacility().enableScanAssetInTask) {
//            menu.add(R.string.menu_scan_asset);
        }
        if (viewModel.needPrintShippingLabelOnSubmit()) {
            menu.add(R.string.text_shipping_label);
        }
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        SupportFragment currentVisibilityFragment = getFragmentDelegate().getCurrentVisibilityFragment();
        int id = item.getItemId();
        if (id == R.id.pick_step && currentVisibilityFragment != null) {
            currentVisibilityFragment.startFragment(PickStepProgressFragment.newInstance(viewModel.getCurrentPickStep()));
        }
        String title = item.getTitle().toString();
        if (TextUtils.equals(title, getString(R.string.menu_view_progress)) && currentVisibilityFragment != null) {
            currentVisibilityFragment.startFragment(PickTaskViewProgressFragment.newInstance(pickTaskViewEntry));
        } else if (TextUtils.equals(title, getString(R.string.menu_item_return_inventory))) {
            ReturnPickedEvenBusData eventData = new ReturnPickedEvenBusData(
                pickTaskViewEntry.getStep(StepTypeEntry.PICK),
                pickTaskViewEntry,
                null, // LpDetail
                null  // InventoryEntry
            );
            EventBus.getDefault().postSticky(eventData);
            Intent intent = new Intent(new Intent(this, ReturnPickedActivity.class));
            startActivityForResult(intent, REQUEST_CODE_RETURN_PICKED);
        } else if (title.equals(getString(R.string.menu_item_print_copy_lp))) {
            Intent intent = new Intent();
            intent.setClass(this, PrintLpActivity.class);
            intent.putExtra(PrintLpActivity.DEFAULT_LP_TYPE, LpTypeEntry.CLP.name());
            startActivity(intent);
        } else if (title.equals(getString(R.string.title_label_batch_print))) {
            Intent intent = new Intent(new Intent(this,
                    PickTaskPrintLabelActivity.class));
            intent.putExtra(PickTaskViewEntry.TAG, pickTaskViewEntry);
            startActivity(intent);
        } else if (TextUtils.equals(title, getString(R.string.inventory_search))) {
            PickTaskWorkFragment fragment = (PickTaskWorkFragment) getFragmentDelegate().getCurrentVisibilityFragment();
            String itemSpecId = fragment.getViewModel().getCurrentItemSpecId();
            Intent intent = new Intent(this, ItemDetailActivity.class);
            intent.putExtra(ItemDetailActivity.SCAN_EDIT_ID, itemSpecId);
            startActivity(intent);
        } else if (title.equals(getString(R.string.text_report_partial_pallet_issue))) {
            showReportPartialPalletIssueDialog();
        } else if (TextUtils.equals(title, getString(R.string.title_print_item_label))) {
            Intent intent = new Intent(appContext, PrintItemActivity.class);
            startActivity(intent);
        } else if (TextUtils.equals(title, getString(R.string.action_help))) {
            viewModel.getAndOpenHelpPage(this, getHelpPageKey(), getFacilityId());
        } else if (title.equals(getString(R.string.force_close_step))) {
            forceCloseStep();
        } else if (TextUtils.equals(item.getTitle(), getString(R.string.menu_switch_1_or_2_segmented_location))) {
            ((PickTaskWorkFragment) currentVisibilityFragment).getViewModel().switchSegmentedLocationModel();
        } else if (TextUtils.equals(title, getString(R.string.menu_collect_material))) {
            ArrayList<String> customerIds = new ArrayList<>();
            customerIds.add(viewModel.getPickTaskViewEntry().customerId);
            Intent intent = new Intent(this, MaterialCenterActivity.class);
            intent.putExtra(GeneralTaskViewEntry.TAG, pickTaskViewEntry);
            intent.putExtra(MaterialCenterActivity.CUSTOMER_IDS, customerIds);
            boolean isPickToSlp = Stream.ofNullable(pickTaskViewEntry.orders).allMatch(order -> order.pickToSLP);
            intent.putExtra(MaterialItemSelectActivity.IS_PICK_TO_SLP, isPickToSlp);
            startActivity(intent);
        } else if (TextUtils.equals(title, getString(R.string.menu_scan_asset))) {
//            Class<?> clazz = AssetAssignedTaskActivity.class;
//            Intent intent = new Intent(this, clazz);
//            AssetAssignedTaskActivity.Param param = new AssetAssignedTaskActivity.Param(pickTaskViewEntry, null, ACTIVITY_START_FROM_MENU);
//            ActivityBundleHolder.pushSerializable(clazz, new Pair<String, Serializable>(UniversalActivityParam.TAG, param));
//            startActivity(intent);
        } else if (title.equals(getString(R.string.text_shipping_label))) {
            Intent intent = new Intent(new Intent(this,
                    ReprintShippingLabelActivity.class));
            intent.putExtra(PickTaskViewEntry.TAG, pickTaskViewEntry);
            startActivity(intent);
        }

        return super.onOptionsItemSelected(item);
    }

    private void showReportPartialPalletIssueDialog() {
        EditTextDialog editTextDialog = new EditTextDialog();
        editTextDialog.setArgument(getString(R.string.text_report_partial_pallet_issue),
                null,
                EditTextDialog.TYPE_EDIT_ONE,
                R.string.msg_report_partial_pallet_issue_input_lp, 0, new EditTextDialog.EditTextDialogCallback() {
                    @Override
                    public void onCancelClick() {

                    }

                    @Override
                    public void onOkClick(String firstEdt, String secondEdt) {
                        String lp = TextUtils.isEmpty(firstEdt) ? "" : firstEdt.equals(ResUtil.getString(R.string.msg_report_partial_pallet_issue_input_lp)) ? "" : firstEdt;
                        viewModel.onGetLocationIdByLpThenReportPartialPalletIssue(lp);
                    }
                });
        editTextDialog.show(getSupportFragmentManager(), null);
    }

    public void forceCloseStep() {
        tryForceCloseStep();
    }

    private void tryForceCloseStep() {
        if (pickTaskViewEntry != null && pickTaskViewEntry.isForbidForceCloseStepsFromPickTask()) {
            showNotAllowForceCloseStepDialog();
            return;
        }

        showForceCloseStepDialog();
    }

    private void showNotAllowForceCloseStepDialog() {
        new AlertDialog.Builder(this)
                .setTitle(R.string.title_customer_not_allow_force_close_step)
                .setMessage(R.string.msg_should_turn_off_allow_force_close_step_flag)
                .setPositiveButton(R.string.button_ok, null)
                .create()
                .show();
    }

    private void showForceCloseStepDialog() {
        PickForceCloseDialog dialog = new PickForceCloseDialog();
        dialog.setShowCreateHospital(isShowCreateHospitalItem())
                .setCallBackAction(() -> {
                    viewModel.forceCloseStep(stepBaseEntry.type, getIdmUserId());
                    dialog.dismiss();
                    return null;
                }, () -> {
                    HospitalInfoDialog.Companion.newInstance(stepBaseEntry.taskId, pickTaskViewEntry.customerName, pickTaskViewEntry.customerId).show(getSupportFragmentManager(), PickForceCloseDialog.class.getSimpleName());
                    dialog.dismiss();
                    return null;
                }).show(getSupportFragmentManager(), PickForceCloseDialog.class.getSimpleName());
    }

    private boolean isShowCreateHospitalItem() {
        return PermissionManager.getInstance().hasPermission(PermissionEntry.HospitalTask);
    }

    private String getHelpPageKey() {
        String helpPageKey = "";
        PickStepMode pickStep = viewModel.getCurrentPickStep();
        switch (pickStep) {
            case GO_TO_LOCATION:
                helpPageKey = ConfigurationMapUtil.PAGE_KEY_PICK_TASK_SCAN_LOCATION_TO_PICK;
                break;

            case SCAN_ILP:
                helpPageKey = ConfigurationMapUtil.PAGE_KEY_PICK_TASK_SCAN_LP_TO_PICK;
                break;

            case SCAN_ITEM:
                helpPageKey = ConfigurationMapUtil.PAGE_KEY_PICK_TASK_PICK_PARTIA_LP_VIEW;
                break;
        }
        return helpPageKey;
    }

    private boolean enableCollectMaterial() {
        return pickTaskViewEntry != null
                && pickTaskViewEntry.customerEntry != null
                && pickTaskViewEntry.customerEntry.enableCollectMaterialAtPickingSetting != null
                && pickTaskViewEntry.customerEntry.enableCollectMaterialAtPickingSetting.enableCollectMaterialAtPicking
                && Stream.ofNullable(pickTaskViewEntry.orders)
                .anyMatch(order -> Stream.ofNullable(pickTaskViewEntry.customerEntry.enableCollectMaterialAtPickingSetting.enableCollectMaterialAtPickingOrderTypes)
                        .anyMatch(orderType -> order.orderType == orderType));
    }

    @Override
    protected void onDestroy() {
        EventBus.getDefault().unregister(this);
        super.onDestroy();
        viewModel.pauseTaskWorkTime();
    }

    private boolean isPickWorkPage() {
        return getFragmentDelegate().getCurrentVisibilityFragment() instanceof PickTaskWorkFragment;
    }

    @Override
    public void onBackPressed() {
        if (stepBaseEntry != null && stepBaseEntry.status == StepStatusEntry.DONE) {
            finish();
            return;
        }
        super.onBackPressed();
    }

    @Override
    public boolean supportVoice() {
        return true;
    }

    @Override
    protected boolean enableManualTimer() {
        return true;
    }

    private void observeTaskTimeEvent(){
        viewModel.taskWorkTimeUiEvent.observe(this, event-> {
           if(event instanceof  TaskWorkTimeUiEvent.InitWorkTime){
               boolean  showStepStatus  = stepBaseEntry != null && (stepBaseEntry.status == StepStatusEntry.DONE || stepBaseEntry.status == StepStatusEntry.FORCE_CLOSED || !stepBaseEntry.assigneeUserIds.contains(getIdmUserId()));
               if (enableManualTimer() && !showStepStatus ) viewModel.startTaskWorkTime();
           }
           else if(event instanceof  TaskWorkTimeUiEvent.StartWorkTime){
               long duration = ((TaskWorkTimeUiEvent.StartWorkTime) event).getWorkingDuration();
               timeElapsedView.setVisibility(View.VISIBLE);
               timeElapsedView.startTime(duration, true, new TimeElapsedCallBack(){
                           @Override
                           public void onTimePause() {
                               viewModel.pauseTaskWorkTime();
                           }
                           @Override
                           public void onTimeResume() {
                               viewModel.resumeTaskWorkTime();
                           }
                       }
               );
           }
           else if(event instanceof  TaskWorkTimeUiEvent.PauseWorkTime){
               timeElapsedView.pauseTime();
           }
           else if(event instanceof  TaskWorkTimeUiEvent.ResumeWorkTime){
               timeElapsedView.resumeTime();
           }
        });
    }

    @Override
    protected void onWorkIdle() {
        new TaskWorkTimeAlarm().alarmReminder(this, viewModel.getPickTaskViewEntry(), () -> {
            timeElapsedView.pauseTime();
            return null;
        });
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if (keyCode == 139 || keyCode == 280 || keyCode == 293 || keyCode == 294) {
            if (event.getRepeatCount() == 0) {
                RFIDDeviceManager.getInstance().startInventoryByMingHao();
            }
            return true;
        }
        return super.onKeyDown(keyCode, event);
    }
}
