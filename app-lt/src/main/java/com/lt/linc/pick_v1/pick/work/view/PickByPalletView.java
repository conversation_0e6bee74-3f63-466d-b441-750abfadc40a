package com.lt.linc.pick_v1.pick.work.view;

import android.view.View;
import android.widget.Switch;

import com.lt.linc.R;
import com.lt.linc.pick_v1.pick.work.viewmodel.PickByPalletVM;
import com.lt.linc.pick_v1.pick.work.viewmodel.PickTaskWorkVM;

public class PickByPalletView extends PickBaseViewState<PickByPalletVM> {

    private Switch pickByPalletSwitch;
    @Override
    protected PickByPalletVM createViewModel(PickTaskWorkVM pickTaskVM) {
        return new PickByPalletVM(pickTaskVM);
    }

    @Override
    protected void onViewCreated(View stateView) {
        super.onViewCreated(stateView);
        bindView(stateView);
        pickByPalletSwitch.setOnCheckedChangeListener((compoundButton, checked) -> viewModel.setPickByPallet(checked));
        viewModel.isPickByPalletLiveData.observe(getFragment(), (isPickByPallet) -> {
            pickByPalletSwitch.setChecked(isPickByPallet);
        });
    }

    @Override
    protected int getLayoutId() {
        return R.layout.layout_pick_v1_work_pick_by_pallet;
    }

    @Override
    public String getState() {
        return STATE;
    }

    private void bindView(View view) {
        pickByPalletSwitch = view.findViewById(R.id.switch_pick_by_pallet_switch);
    }
}
