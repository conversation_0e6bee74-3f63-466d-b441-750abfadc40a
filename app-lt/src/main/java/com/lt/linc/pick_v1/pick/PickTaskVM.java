package com.lt.linc.pick_v1.pick;

import static com.linc.platform.utils.ResUtil.getString;

import androidx.lifecycle.MutableLiveData;
import android.content.Context;
import android.text.TextUtils;
import android.view.Gravity;

import com.annimon.stream.Stream;
import com.linc.platform.common.help.FunctionHelpPresenterImpl;
import com.linc.platform.common.lp.LpApi;
import com.linc.platform.common.step.StepBaseEntry;
import com.linc.platform.common.step.StepStatusEntry;
import com.linc.platform.common.step.StepTypeEntry;
import com.linc.platform.common.task.TaskTypeEntry;
import com.linc.platform.common.task.worktime.TaskWorkTimeDal;
import com.linc.platform.http.HttpService;
import com.linc.platform.idm.api.IdmApi;
import com.linc.platform.idm.model.UserLevelConfigKt;
import com.linc.platform.inventory.model.InventoryIssueReasonCodeEntry;
import com.linc.platform.inventoryissue.InventoryIssueApi;
import com.linc.platform.pick.api.PickStepApi;
import com.linc.platform.pick.model.InventoryIssueBatchCreateEntry;
import com.linc.platform.pick.model.InventoryPickIssueCreateEntry;
import com.linc.platform.pick.model.PickTaskUpdateEntry;
import com.linc.platform.pick.model.PickTaskViewEntry;
import com.linc.platform.pick.model.PickWayEntry;
import com.linc.platform.pick.model.picktote.PickToteCartDetailEntry;
import com.linc.platform.pick.model.picktote.PickToteCartWrap;
import com.linc.platform.utils.CollectionUtil;
import com.linc.platform.utils.ToastUtil;
import com.lt.linc.R;
import com.lt.linc.common.mvi.UiEvent;
import com.lt.linc.common.mvvm.BaseVM;
import com.lt.linc.common.taskworktime.TaskWorkTimeUiEvent;
import com.lt.linc.pick_v1.pick.base.PickTaskBaseVM;
import com.lt.linc.pick_v1.pick.model.PickStepMode;
import com.lt.linc.pick_v1.pick.model.PickUserLevelConfig;
import com.lt.linc.pick_v1.pick.model.PickWorkFlow;
import com.lt.linc.pick_v1.pick.model.PickWorkFlowEvent;
import com.lt.linc.pick_v1.pick.work.viewmodel.repository.PickTaskRepository;
import com.lt.linc.step.processtime.StepProcessTimeHandler;
import com.lt.linc.step.stepstatus.StepStatusOperateResult;
import com.lt.linc.util.SnackType;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class PickTaskVM extends BaseVM implements PickTaskEvent {

    public MutableLiveData<PickStepMode> pickStepLiveData = new MutableLiveData<>();
    public MutableLiveData<Boolean> showToteCartCheckOutDialog = new MutableLiveData<>();
    public MutableLiveData<Boolean> showToteCartUsedDialog = new MutableLiveData<>();
    public MutableLiveData<PickToteCartDetailEntry> toteCartDetail = new MutableLiveData<>();
    public MutableLiveData<UiEvent> taskWorkTimeUiEvent = new MutableLiveData<>();

    private StepBaseEntry stepBaseEntry;
    private PickTaskViewEntry pickTaskViewEntry;
    private PickWorkFlow pickWorkFlow;
    private LpApi mLpApi;
    private IdmApi idmApi;
    private InventoryIssueApi inventoryIssueApi;
    private PickStepApi pickStepApi;
    private final FunctionHelpPresenterImpl mFunctionHelpPresenterImpl;
    private PickTaskRepository pickTaskRepository;
    private PickToteCartWrap pickToteCartWrap;

    private List<PickTaskBaseVM> childPickTaskVM = new ArrayList<>();

    private StepProcessTimeHandler stepProcessTimeHandler = new StepProcessTimeHandler();
    private TaskWorkTimeDal taskWorkTimeDal = new TaskWorkTimeDal();

    PickTaskVM() {
        pickWorkFlow = new PickWorkFlow();
        mLpApi = HttpService.createService(LpApi.class);
        idmApi= HttpService.createService(IdmApi.class);
        inventoryIssueApi = HttpService.createService(InventoryIssueApi.class);
        pickStepApi = HttpService.createService(PickStepApi.class);
        mFunctionHelpPresenterImpl = new FunctionHelpPresenterImpl();
        pickTaskRepository = new PickTaskRepository();
        pickToteCartWrap = new PickToteCartWrap();
        notifyPickStepChanged(PickStepMode.SELECT_PICK_TO_LP_MODE);
    }

    public <T extends PickTaskBaseVM> void bindChildVM(T child) {
        if (childPickTaskVM.contains(child)) {
            return;
        }
        childPickTaskVM.add(child);
    }

    @Override
    public StepBaseEntry getStepBaseEntry() {
        return stepBaseEntry;
    }


    @Override
    public PickWorkFlow pickWorkFlow() {
        return pickWorkFlow;
    }

    @Override
    public void initWorkFlow(PickWorkFlow workFlow) {
        Stream.of(childPickTaskVM).forEach(vm -> {
            if (vm != null) {
                vm.initWorkFlow(workFlow);
            }
        });
    }

    @Override
    public void handlePickWorkFlowMessage(PickWorkFlowEvent message) {

    }

    @Override
    public void sendPickWorkFlowMessage(PickWorkFlowEvent msg) {

    }

    public void setStepBaseEntry(StepBaseEntry stepBaseEntry) {
        this.stepBaseEntry = stepBaseEntry;
        stepProcessTimeHandler.setStepAndReport(stepBaseEntry);
    }

    public void updateStepBaseEntry(StepBaseEntry stepBaseEntry) {
        this.stepBaseEntry = stepBaseEntry;
    }

    @Override
    public PickTaskViewEntry getPickTaskViewEntry() {
        return pickTaskViewEntry;
    }

    @Override
    public void setPickTask(PickTaskViewEntry pickTaskViewEntry) {
        this.pickTaskViewEntry = pickTaskViewEntry;
        pickToteCartWrap.setOrderIds(pickTaskViewEntry.orderIds);
        StepBaseEntry stepBaseEntry = Stream.of(pickTaskViewEntry.stepEntries).filter(stepEntry -> stepEntry.type == StepTypeEntry.PICK).findSingle().orElse(null);
        if (stepBaseEntry != null) {
            setStepBaseEntry(stepBaseEntry);
        }
        initTaskWorkTime();
        getUserLevelControl();
    }

    @Override
    public void notifyPickStepChanged(PickStepMode pickStepMode) {
        pickStepLiveData.setValue(pickStepMode);
    }

    @Override
    public PickStepMode getCurrentPickStep() {
        return pickStepLiveData.getValue();
    }

    private void getUserLevelControl(){
        executeWithLoading(idmApi.getUserLevelControl(stepBaseEntry.id, pickTaskViewEntry.customerId), userLevelKeyList -> {
            pickWorkFlow.setPickUserLevelConfig(UserLevelConfigKt.convertToConfig(userLevelKeyList, PickUserLevelConfig.class));
            initWorkFlow(pickWorkFlow);
        }, error -> {
            String errorMsg = error.getErrorMessage();
            ToastUtil.showErrorToast(errorMsg);
        });
    }

    public void updateStepStatusOperateResult(StepStatusOperateResult result){
        if (result instanceof StepStatusOperateResult.TakeOver) {
            finishPage();
        }
        if (result instanceof StepStatusOperateResult.Start || result instanceof StepStatusOperateResult.Reopen) {
            initTaskWorkTime();
        }
    }



    public boolean isPickStepInNew() {
        return stepBaseEntry.status == StepStatusEntry.NEW;
    }

    public void onGetLocationIdByLpThenReportPartialPalletIssue(String lp) {
        if (TextUtils.isEmpty(lp)) {
            ToastUtil.showToast(R.string.msg_report_partial_pallet_issue_input_lp);
            return;
        }
        executeWithLoading(mLpApi.get(lp), lpViewEntry -> onReportPartialPalletIssue(lpViewEntry.locationId, lp));
    }

    private void onReportPartialPalletIssue(String locationId, String lp) {
        InventoryPickIssueCreateEntry createEntry = new InventoryPickIssueCreateEntry();
        createEntry.taskId = pickTaskViewEntry.id;
        createEntry.locationId = locationId;
        createEntry.lpId = lp;
        createEntry.taskType = TaskTypeEntry.PICK;
        createEntry.reasonCode = InventoryIssueReasonCodeEntry.INVENTORY_ISSUE.getReasonCode();
        InventoryIssueBatchCreateEntry batchCreateEntry = new InventoryIssueBatchCreateEntry();
        batchCreateEntry.issues = Collections.singletonList(createEntry);
        executeWithLoading(inventoryIssueApi.addInventoryIssue(batchCreateEntry), aVoid -> ToastUtil.showToast(R.string.msg_report_partial_pallet_succeed));
    }

    public void getAndOpenHelpPage(Context context, String helpPageKey, String facilityId) {
        mFunctionHelpPresenterImpl.getAndOpenHelpPage(context, helpPageKey, pickTaskViewEntry.customerId, facilityId);
    }

    public void forceCloseStep(StepTypeEntry stepTypeEntry, String userId) {
        executeWithLoading(pickStepApi.forceClose(pickTaskViewEntry.id, pickTaskViewEntry.getStep(stepTypeEntry).id), idResponse -> {
            ToastUtil.showToast(R.string.msg_step_is_done);
            finishPage();
        });
    }

    public PickToteCartWrap pickToteCartWrap() {
        return pickToteCartWrap;
    }

    public void setToteCartDetail(String toteCart) {
        getToteCartDetail(toteCart, false);
    }

    public void getToteCartDetail(String toteCart, boolean checkInToteCart) {
        executeWithLoading(pickTaskRepository.getToteCartDetail(toteCart), toteCartDetailEntry -> {
            if (toteCartDetailEntry == null || toteCartDetailEntry.toteCart == null || CollectionUtil.isNullOrEmpty(toteCartDetailEntry.toteDetails)) {
                toteCartDetail.setValue(null);
                return;
            }
            pickToteCartWrap.setPickToteCartDetailEntry(toteCartDetailEntry);
            if (pickToteCartWrap.isToteCartUsedByOtherTask(getPickTaskViewEntry().id)) {
                showToteCartUsedDialog.setValue(true);
                return;
            }
            boolean isToteCartFull = pickToteCartWrap.isToteCartFull();
            showToteCartCheckOutDialog.setValue(isToteCartFull);
            toteCartDetail.setValue(toteCartDetailEntry);
            if (checkInToteCart) {
                updatePickTaskByToteCart(toteCartDetailEntry.toteCart.barcode);
            }
        });
    }

    public void checkToteCartDetail() {
        PickToteCartDetailEntry toteCartDetailEntry = toteCartDetail.getValue();
        if (null == toteCartDetailEntry) {
            return;
        }
        getToteCartDetail(toteCartDetailEntry.toteCart.barcode, false);
    }

    public void updatePickTaskByToteCart(String barcode) {
        PickTaskUpdateEntry updatePickTask = new PickTaskUpdateEntry();
        updatePickTask.equipmentBarcode = barcode;
        executeWithLoading(pickTaskRepository.updatePickTask(getPickTaskViewEntry().id, updatePickTask), Void -> {

        });
    }

    @Override
    protected void onCleared() {
        super.onCleared();
        stepProcessTimeHandler.tryReportEndProcess();
    }

    private void initTaskWorkTime() {
        taskWorkTimeUiEvent.postValue(new TaskWorkTimeUiEvent.InitWorkTime());
    }

    public void startTaskWorkTime() {
        taskWorkTimeDal.initStartTime(pickTaskViewEntry, false, (duration) -> {
            if (duration > 0) {
                showSnack(new SnackType.ErrorV2(
                        Gravity.TOP,
                        R.string.timer_paused
                ), getString(R.string.resume_timer_to_continue_task));
            }
            taskWorkTimeUiEvent.postValue(new TaskWorkTimeUiEvent.StartWorkTime(duration,false));
        });
    }

    public void resumeTaskWorkTime() {
        taskWorkTimeDal.resumeWorkTime(() -> {
            taskWorkTimeUiEvent.postValue(new TaskWorkTimeUiEvent.ResumeWorkTime());
        });
    }

    public void pauseTaskWorkTime() {
        taskWorkTimeDal.pauseWorkTime(() -> {
            taskWorkTimeUiEvent.postValue(new TaskWorkTimeUiEvent.PauseWorkTime());
            showSnack(new SnackType.ErrorV2(
                    Gravity.TOP,
                    R.string.timer_paused
            ), getString(R.string.resume_timer_to_continue_task));
        });
    }

    public boolean needPrintShippingLabelOnSubmit(){
        return pickTaskViewEntry.customerEntry != null && PickWayEntry.WAVE_PICK_BY_ITEM == pickTaskViewEntry.pickWay
                && pickTaskViewEntry.customerEntry.printShippingLabelOnSubmit;
    }

}


