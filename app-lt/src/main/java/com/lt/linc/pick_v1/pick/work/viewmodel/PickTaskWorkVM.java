package com.lt.linc.pick_v1.pick.work.viewmodel;

import static com.lt.linc.pick_v1.pick.model.PickEventConstant.MESSAGE_AFTER_CHECK_RFID_SUBMIT;
import static com.lt.linc.pick_v1.pick.model.PickEventConstant.MESSAGE_AFTER_ON_SCREEN_OP_COUNT;
import static com.lt.linc.pick_v1.pick.model.PickEventConstant.MESSAGE_ENTER_ITEM_QTY;
import static com.lt.linc.pick_v1.pick.model.PickEventConstant.MESSAGE_ENTER_LOT_NO;
import static com.lt.linc.pick_v1.pick.model.PickEventConstant.MESSAGE_NEXT_LOCATION;
import static com.lt.linc.pick_v1.pick.model.PickEventConstant.MESSAGE_ON_SCREEN_COUNT_MATCH;
import static com.lt.linc.pick_v1.pick.model.PickEventConstant.MESSAGE_ON_SCREEN_COUNT_NOT_MATCH;
import static com.lt.linc.pick_v1.pick.model.PickEventConstant.MESSAGE_PRINT_NEW_CONTAINER;
import static com.lt.linc.pick_v1.pick.model.PickEventConstant.MESSAGE_SCAN_ITEM_CODE;
import static com.lt.linc.pick_v1.pick.model.PickEventConstant.MESSAGE_SCAN_ITEM_CODE_NO_MATCH;
import static com.lt.linc.pick_v1.pick.model.PickEventConstant.MESSAGE_SCAN_ITEM_SN;
import static com.lt.linc.pick_v1.pick.model.PickEventConstant.MESSAGE_SCAN_LOCATION;
import static com.lt.linc.pick_v1.pick.model.PickEventConstant.MESSAGE_SCAN_LP;
import static com.lt.linc.pick_v1.pick.model.PickEventConstant.MESSAGE_SCAN_TO_CONTAINER;
import static com.lt.linc.pick_v1.pick.model.PickEventConstant.MESSAGE_SHOW_INVENTORY_ISSUE_DIALOG;
import static com.lt.linc.pick_v1.pick.model.PickEventConstant.MESSAGE_SHOW_ON_SCREEN_OP_COUNT_DIALOG;
import static com.lt.linc.pick_v1.pick.model.PickEventConstant.MESSAGE_SHOW_SUGGEST_NEXT_TASK;
import static com.lt.linc.pick_v1.pick.model.PickEventConstant.MESSAGE_SUBMIT;
import static com.lt.linc.pick_v1.pick.model.PickEventConstant.MESSAGE_SUGGEST_NEXT_TASK;
import static com.lt.linc.pick_v1.pick.model.PickEventConstant.MESSAGE_SWITCH_PICK_BY_PALLET;
import static com.lt.linc.pick_v1.pick.model.PickEventConstant.MESSAGE_UPDATE_PICK_ITEM_INFO;
import static com.lt.linc.pick_v1.pick.model.PickEventConstant.MESSAGE_UPDATE_PICK_ITEM_PHOTO;
import static com.lt.linc.pick_v1.pick.model.PickEventConstant.MESSAGE_UPDATE_PICK_ITEM_QTY;
import static com.lt.linc.pick_v1.pick.model.PickEventConstant.MESSAGE_UPDATE_PICK_TASK_TOTAL_ITEM;
import static com.lt.linc.pick_v1.pick.model.PickEventConstant.MESSAGE_UPDATE_PICK_TO_TABLE_TASK_TOTAL_ITEM;
import static com.lt.linc.pick_v1.pick.model.PickEventConstant.MESSAGE_UPDATE_SCAN_LOCATION_TYPE;
import static com.lt.linc.pick_v1.pick.model.PickEventConstant.MESSAGE_UPDATE_SCAN_SN_RESULT;
import static com.lt.linc.pick_v1.pick.model.PickEventConstant.MESSAGE_UPDATE_STAGE_SUGGEST_LOCATION;
import static com.lt.linc.pick_v1.pick.model.PickEventConstant.MESSAGE_UPDATE_SUGGEST_ITEM_QTY;
import static com.lt.linc.pick_v1.pick.model.PickEventConstant.MESSAGE_UPDATE_SUGGEST_LOCATION;
import static com.lt.linc.pick_v1.pick.model.PickEventConstant.MESSAGE_UPDATE_SUGGEST_UOM;
import static com.lt.linc.pick_v1.pick.model.PickEventConstant.MESSAGE_WORK_FLOW_SUGGEST_LP;

import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.MutableLiveData;

import android.text.TextUtils;
import android.util.Base64;
import android.util.Log;

import com.annimon.stream.Stream;
import com.linc.platform.baseapp.model.LocationEntry;
import com.linc.platform.baseapp.model.LocationTypeEntry;
import com.linc.platform.common.BarcodeType;
import com.linc.platform.common.lp.LPDal;
import com.linc.platform.common.step.StepBaseEntry;
import com.linc.platform.common.step.StepStatusEntry;
import com.linc.platform.core.LocalPersistence;
import com.linc.platform.cyclecount.model.OnScreenCountEntry;
import com.linc.platform.cyclecount.model.OnScreenCountResponseEntry;
import com.linc.platform.foundation.model.ConfigurationMapSearchEntry;
import com.linc.platform.foundation.model.ItemSNValidateRuleEntry;
import com.linc.platform.foundation.model.ItemSpecSearchEntry;
import com.linc.platform.http.ErrorResponse;
import com.linc.platform.http.IdResponse;
import com.linc.platform.pick.model.AutoAssignPickTaskRequestEntry;
import com.linc.platform.pick.model.PickResultUpdateEntry;
import com.linc.platform.pick.model.PickTaskViewEntry;
import com.linc.platform.pick.model.batchprint.SmallParcelShipmentLabel;
import com.linc.platform.print.commonprintlp.PrintData;
import com.linc.platform.print.commonprintlp.PrintView;
import com.linc.platform.print.commonprintlp.PrinterConfig;
import com.linc.platform.print.model.LabelSizeEntry;
import com.linc.platform.print.model.PrinterEntry;
import com.linc.platform.utils.ArrayUtil;
import com.linc.platform.utils.CollectionUtil;
import com.linc.platform.utils.ConfigurationMapUtil;
import com.linc.platform.utils.LocationUtil;
import com.linc.platform.utils.PrintUtil;
import com.linc.platform.utils.ResUtil;
import com.linc.platform.utils.RxUtil;
import com.linc.platform.utils.StringUtil;
import com.linc.platform.utils.ToastUtil;
import com.lt.linc.R;
import com.lt.linc.home.task.taskstepmenu.TaskStepMenuActivity;
import com.lt.linc.pick_v1.pick.PickTaskVM;
import com.lt.linc.pick_v1.pick.base.PickTaskWorkBaseVM;
import com.lt.linc.pick_v1.pick.model.PickEventConstant;
import com.lt.linc.pick_v1.pick.model.PickStepMode;
import com.lt.linc.pick_v1.pick.model.PickV1TypeMode;
import com.lt.linc.pick_v1.pick.model.PickWorkFlowEvent;
import com.lt.linc.pick_v1.pick.start.model.PickV1TypeEntry;
import com.lt.linc.pick_v1.pick.work.action.PickWorkAction;
import com.lt.linc.pick_v1.pick.work.model.ItemLotNoEntry;
import com.lt.linc.pick_v1.pick.work.model.ItemSnViewEntry;
import com.lt.linc.pick_v1.pick.work.model.LastPickEntry;
import com.lt.linc.pick_v1.pick.work.model.LocationItemSuggestReqEntry;
import com.lt.linc.pick_v1.pick.work.model.LpItemOrderSuggestReqEntry;
import com.lt.linc.pick_v1.pick.work.model.PickByLotNoViewEntry;
import com.lt.linc.pick_v1.pick.work.model.PickWorkMessage;
import com.lt.linc.pick_v1.pick.work.viewmodel.repository.PickTaskRepository;
import com.lt.linc.step.stepstatus.StepStatusFragment;
import com.lt.linc.step.stepstatus.StepStatusPermissionConfig;
import com.lt.linc.util.SnackType;

import org.greenrobot.eventbus.EventBus;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

import retrofit2.Response;
import rx.Observable;

public class PickTaskWorkVM extends PickTaskWorkBaseVM {

    public MutableLiveData<Boolean> startToPrinterSettingPage = new MutableLiveData<>();
    public MutableLiveData<String> showPrintShippingLabelFinishedDialog = new MutableLiveData<>();
    protected PickWorkAction pickAction;
    private PickTaskRepository pickTaskRepository;

    public PrinterEntry zplPrinterEntry;
    private final PrintUtil printUtil;

    public PickTaskWorkVM(@NonNull PickTaskVM viewModel, PickV1TypeEntry pickTypeEntry, String idmUserId, String facilityName) {
        super(viewModel, pickTypeEntry.pickTypeMode);
        this.pickAction = new PickWorkAction(getPickTaskViewEntry(), pickTypeEntry, pickTaskVM().pickToteCartWrap(), idmUserId, facilityName);
        pickWorkFlow().setPickWorkAction(pickAction);
        pickTaskRepository = new PickTaskRepository();
        printUtil = PrintUtil.newInstance();
        refreshZplPrinter();
    }

    public void refreshZplPrinter() {
        zplPrinterEntry = PrinterConfig.getPrinter(pickAction.getIdmUserId(), pickAction.getFacilityName(), LabelSizeEntry.FOUR_SIX);
    }

    @Override
    public void handlePickWorkMessage(PickWorkMessage message) {
        int what = message.what;
        Object data = message.obj;
        switch (what) {
            case MESSAGE_SCAN_LOCATION:
                onScanLocationEvent((LocationEntry) data);
                break;
            case MESSAGE_SCAN_LP:
                onScanFromLpEvent((String) data);
                break;
            case MESSAGE_SCAN_ITEM_CODE:
                boolean isMatchItem = pickAction.isItemCodeMatch((String) data);
                if (!isMatchItem) {
                    if (pickWorkFlow().allowMultiItemPickInOneLocation()) {
                        sendPickWorkMessage(PickWorkMessage.obtain(MESSAGE_SCAN_ITEM_CODE_NO_MATCH, data));
                    } else {
                        ToastUtil.showToast(R.string.case_upc_error_please_scan_again);
                    }
                    return;
                }
                if (pickWorkFlow().isPickByLotNo()) {
                    PickByLotNoViewEntry pickByLotNoViewEntry = new PickByLotNoViewEntry(pickAction.getPickItemInfo(), pickAction.getPickFromLp(), pickAction.getSuggestUom().suggestUom);
                    sendPickWorkMessage(PickWorkMessage.obtain(MESSAGE_ENTER_LOT_NO, pickByLotNoViewEntry));
                    return;
                }
                if (!pickWorkFlow().checkScanSkuToAddQty()) {
                    return;
                }
                sendPickWorkMessage(PickWorkMessage.obtain(MESSAGE_SCAN_ITEM_CODE, data));
                break;
            case MESSAGE_SCAN_ITEM_CODE_NO_MATCH:
                String itemSpecId = (String) data;
                if (pickAction.isPickSuggestLocation()) {
                    getLpItemOrderSuggest(pickAction.getPickFromLp(), itemSpecId);
                } else {
                    ToastUtil.showToast(R.string.msg_current_location_is_not_an_pick_location_please_scan_lp_to_pick);
                }
                break;
            case MESSAGE_ENTER_ITEM_QTY:
                int qty = (int) data;
                sendPickWorkMessage(PickWorkMessage.obtain(MESSAGE_ENTER_ITEM_QTY, pickAction.getPickItemSpecName()));
                if (pickAction.getSuggestItemQty() == 0 || qty <= pickAction.getSuggestItemQty()) {
                    sendPickWorkMessage(PickWorkMessage.obtain(MESSAGE_UPDATE_PICK_ITEM_QTY, data));
                    pickAction.updateItemPickQty(qty);
                    onScanItemComplete();
                }
                break;
            case MESSAGE_SCAN_ITEM_SN:
                String sn = (String) data;
                ItemSnViewEntry itemSnViewEntry = pickAction.getItemSnViewEntry();
                itemSnViewEntry.sn = sn;
                sendPickWorkMessage(PickWorkMessage.obtain(MESSAGE_SCAN_ITEM_SN, itemSnViewEntry));
                break;
            case MESSAGE_UPDATE_SCAN_SN_RESULT:
                if (pickWorkFlow().isScanSnWorkFlow) {
                    List<String> scanSnList = (List<String>) data;
                    pickAction.setPickedItemSnList(scanSnList);
                    int pickQty = scanSnList.size();
                    sendPickWorkMessage(PickWorkMessage.obtain(MESSAGE_UPDATE_PICK_ITEM_QTY, pickQty));
                    pickAction.updateItemPickQty(pickQty);
                    if (pickAction.isCurrentItemPickComplete()) {
                        if (pickWorkFlow().isPickByLotNo()) {
                            PickByLotNoViewEntry pickByLotNoViewEntry = new PickByLotNoViewEntry(pickAction.getPickItemInfo(), pickAction.getPickFromLp(), pickAction.getSuggestUom().suggestUom);
                            sendPickWorkMessage(PickWorkMessage.obtain(MESSAGE_ENTER_LOT_NO, pickByLotNoViewEntry));
                            return;
                        }
                        onScanItemComplete();
                    }
                }
                break;
            case MESSAGE_UPDATE_PICK_ITEM_QTY:
                int pickQty = (int) data;
                sendPickWorkMessage(PickWorkMessage.obtain(MESSAGE_UPDATE_PICK_ITEM_QTY, data));
                pickAction.updateItemPickQty(pickQty);
                if (pickAction.isCurrentItemPickComplete()) {
                    onScanItemComplete();
                }
                break;
            case MESSAGE_ENTER_LOT_NO:
                List<ItemLotNoEntry> lotNoEntryList = (List<ItemLotNoEntry>) data;
                pickAction.setPickItemLotNo(lotNoEntryList);
                onScanItemComplete();
                break;
            case MESSAGE_SHOW_INVENTORY_ISSUE_DIALOG:
                sendPickWorkMessage(PickWorkMessage.obtain(MESSAGE_SHOW_INVENTORY_ISSUE_DIALOG));
                break;
            case MESSAGE_SCAN_TO_CONTAINER:
                String toLp = (String) data;
                onScanToLpEvent(toLp);
                break;
            case MESSAGE_PRINT_NEW_CONTAINER:
                String printLp = (String) data;
                pickAction.savePrintLp(printLp);
                PickV1TypeMode pickType = pickAction.pickTypeModel();
                if (pickType == PickV1TypeMode.PICK_TO_LIVE_PRINT_LP && !pickWorkFlow().pickUserLevelController().forceScanPrintedLp) {
                    onScanToLpEvent(printLp);
                }
                break;
            case MESSAGE_SUBMIT:
                submitPickItem();
                break;
            case MESSAGE_SUGGEST_NEXT_TASK:
                suggestNextTask();
                break;
            case MESSAGE_NEXT_LOCATION:
                notifyPickStepChanged(PickStepMode.GO_TO_LOCATION);
                getPickLocationItemSuggestView((boolean) data);
                break;
            case MESSAGE_SWITCH_PICK_BY_PALLET:
                pickAction.setPickByPallet((boolean) data);
                if ((boolean) data) {
                    onScanItemComplete();
                } else {
                    notifyPickStepChanged(PickStepMode.SCAN_ITEM);
                }
                sendPickWorkMessage(PickWorkMessage.obtain(MESSAGE_SWITCH_PICK_BY_PALLET, data));
                break;
            case MESSAGE_AFTER_ON_SCREEN_OP_COUNT:
                actionAfterPickSubmitSuccess();
                break;

            case MESSAGE_ON_SCREEN_COUNT_MATCH:
                onScreenCountMatch((OnScreenCountResponseEntry) data);
                break;

            case MESSAGE_ON_SCREEN_COUNT_NOT_MATCH:
                onScreenCountNotMatch((OnScreenCountResponseEntry) data);
                break;
            case MESSAGE_AFTER_CHECK_RFID_SUBMIT:
                onSubmitPickItem();
        }
    }

    @Override
    public void handlePickWorkFlowMessage(PickWorkFlowEvent message) {
        int event = message.workFlowEvent;
        Object value = message.workFlowValue;
        switch (event) {
            case PickEventConstant.MESSAGE_WORK_FLOW_CHECK_VERIFY_LOCATION:
                boolean isVerify = (boolean) value;
                if (!isVerify) {
                    notifyPickStepChanged(pickAction.isPickSuggestLocation() ? PickStepMode.SCAN_ITEM : PickStepMode.SCAN_ILP);
                }
                break;
            case PickEventConstant.MESSAGE_WORK_FLOW_SCAN_ITEM_SN:
                BarcodeType barcodeType = (BarcodeType) value;
                if (barcodeType != BarcodeType.SN) {
                    pickAction.setPickedItemSnList(new ArrayList<String>());
                } else {
                    loadItemValidateRegex(pickAction.getLastItemSpecId());
                }
                break;
        }
        sendPickWorkFlowMessage(message);
    }

    @Override
    public void onCreate() {
        super.onCreate();
        init();
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        notifyPickStepChanged(PickStepMode.SELECT_PICK_TO_LP_MODE);
    }

    private void init() {
        if (parentViewModel.isPickStepInNew()) {
            startPickStep();
        } else {
            onInitLocationSuggest();
        }
        getBluetoothPrintShippingLabelIntervalTime();
    }

    public void onInitLocationSuggest() {
        notifyPickStepChanged(PickStepMode.GO_TO_LOCATION);
        updatePickTaskProgress();
    }

    /**
     * click back step button
     */
    public void backPickStep() {
        PickStepMode pickStepMode = getCurrentPickStep();
        switch (pickStepMode) {
            case SCAN_ILP:
                notifyPickStepChanged(PickStepMode.GO_TO_LOCATION);
                break;
            case SCAN_ITEM:
                if (pickAction.isPickSuggestLocation()) {
                    notifyPickStepChanged(PickStepMode.GO_TO_LOCATION);
                } else {
                    notifyPickStepChanged(PickStepMode.SCAN_ILP);
                }
                pickWorkFlow().checkNeedScanLp();
                break;
            case SCAN_CONTAINER:
                notifyPickStepChanged(PickStepMode.SCAN_ITEM);
                break;
            case SUBMIT:
                if (pickAction.isPickByPallet()) {
                    notifyPickStepChanged(PickStepMode.SCAN_ILP);
                } else {
                    notifyPickStepChanged(PickStepMode.SCAN_CONTAINER);
                }
                break;
        }
    }

    private void getBluetoothPrintShippingLabelIntervalTime() {
        ConfigurationMapSearchEntry searchEntry = new ConfigurationMapSearchEntry();
        searchEntry.tableName = ConfigurationMapUtil.TABLE_BLUETOOTH_PRINT_SHIPPING_LABEL_ON_SUBMIT_INTERVAL_TIME;

        executeWithoutLoading(pickTaskRepository.searchConfigurationMap(searchEntry), configurationMapViewEntries -> {
            if (CollectionUtil.isNotNullOrEmpty(configurationMapViewEntries)) {
                int intervalTime = 3000;
                try {
                    intervalTime = Integer.parseInt(configurationMapViewEntries.get(0).valueMapping.get(ConfigurationMapUtil.KEY_BLUETOOTH_PRINT_INTERVAL_TIME));
                } catch (Exception e) {
                }
                pickAction.setPrintShippingLabelIntervalTime(intervalTime);
            }
        });
    }

    private void onScanLocationEvent(LocationEntry scanLocationEntry) {
        boolean allowOverridePickingSuggestedLocation = pickWorkFlow().pickUserLevelController().allowOverridePickingSuggestedLocation;
        boolean isMatch = pickAction.isLocationMatch(scanLocationEntry.id);
        if (!isMatch) {
            if (!allowOverridePickingSuggestedLocation) {
                ToastUtil.showToast(R.string.msg_not_allow_override_picking_suggested_location);
                return;
            }
            pickAction.updateLocation(scanLocationEntry);
            pickWorkFlow().checkNeedPickByPallet();
            sendPickWorkMessage(PickWorkMessage.obtain(MESSAGE_UPDATE_SUGGEST_LOCATION, LocationUtil.getLocationNameWithSection(scanLocationEntry.name, scanLocationEntry.section, scanLocationEntry.zone)));
            showSnackSuccess(ResUtil.getContext().getResources().getString(R.string.location_added));
            if (scanLocationEntry.type == LocationTypeEntry.PICK) {
                getLpItemOrderSuggest(scanLocationEntry.hlpId);
                return;
            }
        }
        if (pickAction.isPickSuggestLocation()) {
            notifyPickStepChanged(PickStepMode.SCAN_ITEM);
        } else {
            notifyPickStepChanged(PickStepMode.SCAN_ILP);
            pickWorkFlow().checkNeedSuggestLp();
        }
        showSnackSuccess(ResUtil.getContext().getResources().getString(R.string.location_added));
    }

    private void onScanFromLpEvent(String lp) {
        boolean facilityAllowOverridePickingSuggestedLocation = parentViewModel.getPickTaskViewEntry().facilityEntry.notAllowOverridePickingSuggestedLocation;
        if (facilityAllowOverridePickingSuggestedLocation) {
            boolean userAllowOverridePickingSuggestedLocation = pickWorkFlow().pickUserLevelController().allowOverridePickingSuggestedLocation;
            executeWithoutLoading(pickTaskRepository.getLp(lp), lpEntry -> {
                if (lpEntry == null)  return;
                if (!TextUtils.isEmpty(lpEntry.locationId) && lpEntry.locationId.equals(pickAction.getLastLocationId())) {
                    getLpItemOrderSuggest(lp);
                } else {
                    if (!userAllowOverridePickingSuggestedLocation || TextUtils.isEmpty(lpEntry.locationId)) {
                        ToastUtil.showToast(R.string.msg_lp_not_belong_this_location);
                    } else {
                        String locationId = lpEntry.locationId;
                        executeWithoutLoading(pickTaskRepository.getLocation(locationId), locationEntry -> {
                            pickAction.updateLocation(locationEntry);
                            pickWorkFlow().checkNeedPickByPallet();
                            sendPickWorkMessage(PickWorkMessage.obtain(MESSAGE_UPDATE_SUGGEST_LOCATION, LocationUtil.getLocationNameWithSection(locationEntry.name, locationEntry.section, locationEntry.zone)));
                            if (locationEntry.type == LocationTypeEntry.PICK) {
                                getLpItemOrderSuggest(locationEntry.hlpId);
                            } else {
                                getLpItemOrderSuggest(lp);
                            }
                            pickAction.clearSuggestLpIds();
                            EventBus.getDefault().post(PickWorkFlowEvent.obtain(PickEventConstant.MESSAGE_WORK_FLOW_SUGGEST_LP, new ArrayList<>()));
                        });
                    }
                }
            });
        } else {
            getLpItemOrderSuggest(lp);
        }
    }

    private void onScanToLpEvent(String container) {
        if (!checkContainer(container)) {
            return;
        }
        pickAction.updateToLP(container);
        notifyPickStepChanged(PickStepMode.SUBMIT);
        sendPickWorkMessage(PickWorkMessage.obtain(MESSAGE_SCAN_TO_CONTAINER, container));
        pickWorkFlow().checkPickByLotNo();
        if (pickWorkFlow().isAutoSubmit()) {
            submitPickItem();
        }
    }

    private boolean checkContainer(String container) {
        PickV1TypeMode pickType = pickAction.pickTypeModel();
        switch (pickType) {
            case PICK_TO_TOTE:
            case PICK_TO_PRE_PRINT_LP:
                if (pickAction.isNeedScanPreviousLP(container, pickWorkFlow().pickUserLevelController().newContainer)) {
                    ToastUtil.showToast(R.string.msg_scan_to_previous_LP);
                    return false;
                }
                break;
            case PICK_TO_LIVE_PRINT_LP:
                if (!pickAction.isScanLpMatchPrintedLp(container, pickWorkFlow().pickUserLevelController().forceScanPrintedLp)) {
                    ToastUtil.showToast(R.string.msg_not_match_printed_LP);
                    return false;
                }
                break;
        }
        return true;
    }

    private void onScanItemComplete() {
        speak(ResUtil.getString(R.string.complete));
        notifyPickStepChanged(PickStepMode.SCAN_CONTAINER);
        if (pickWorkFlow().isSkipClpAtWavePick()) {
            skipClpProcess();
        } else {
            pickWorkFlow().checkSkipScanningPreviousContainer();
        }
    }

    private void skipClpProcess() {
       pickWorkFlow().checkSkipClpAtWavePick();
    }

    public String getCurrentItemSpecId() {
        return pickAction.getLastItemSpecId();
    }


    /**
     * scan location request
     *
     * @param isNeedSkip
     */
    private void getPickLocationItemSuggestView(boolean isNeedSkip) {
        String taskId = pickAction.getPickTask().id;
        String locationId = pickAction.getLastLocationId();
        String itemSpecId = getCurrentItemSpecId();
        LastPickEntry lastPickEntry = new LastPickEntry(locationId, itemSpecId, isNeedSkip);
        LocationItemSuggestReqEntry locationItemSuggestEntry = new LocationItemSuggestReqEntry();
        locationItemSuggestEntry.lastPick = lastPickEntry;
        locationItemSuggestEntry.toteCart = pickAction.getPickToteCart();
        executeWithLoading(pickTaskRepository.getLocationItemSuggest(taskId, locationItemSuggestEntry), locationItemSuggestView -> {
            if (locationItemSuggestView == null || locationItemSuggestView.suggestLocation == null) {
                ToastUtil.showToast(R.string.label_no_suggestion_location);
                speak(ResUtil.getString(R.string.label_no_suggestion_location));
            } else {
                sendPickWorkMessage(PickWorkMessage.obtain(MESSAGE_UPDATE_SCAN_LOCATION_TYPE, locationItemSuggestView.suggestLocation));
                if (locationItemSuggestView.suggestLocation.noMoreSuggestions) {
                    ToastUtil.showToast(R.string.message_no_more_suggest);
                    speak(ResUtil.getString(R.string.message_no_more_suggest));
                }
            }
            pickAction.setPickSuggestData(locationItemSuggestView);
            notifyPickViewChanged();
            sendPickWorkMessage(PickWorkMessage.obtain(MESSAGE_UPDATE_SUGGEST_LOCATION, pickAction.getSuggestLocationName()));
            sendPickWorkMessage(PickWorkMessage.obtain(MESSAGE_UPDATE_STAGE_SUGGEST_LOCATION, pickAction.getStageSuggestLocationNames()));
            notifyWorkFlowOnInit(locationId);
        });
    }

    /**
     * scan formLp request
     *
     * @param lp
     */
    private void getLpItemOrderSuggest(String lp) {
        String itemSpecId = getCurrentItemSpecId();
        getLpItemOrderSuggest(lp, itemSpecId);
    }

    private void getLpItemOrderSuggest(String lp, String itemSpecId) {
        String taskId = pickAction.getPickTask().id;
        LpItemOrderSuggestReqEntry lpItemOrderSuggestReqEntry = new LpItemOrderSuggestReqEntry();
        lpItemOrderSuggestReqEntry.lpId = lp;
        lpItemOrderSuggestReqEntry.itemSpecId = itemSpecId;
        lpItemOrderSuggestReqEntry.orderId = pickAction.getPickOrderId();
        lpItemOrderSuggestReqEntry.isEntireLPPick = pickAction.isPickByPallet();
        lpItemOrderSuggestReqEntry.allowExceedMaximumPartialPallet = pickWorkFlow().pickUserLevelController().allowExceedMaximumPartialPallet;
        executeWithLoading(pickTaskRepository.getLpItemOrderSuggest(taskId, lpItemOrderSuggestReqEntry), lpItemOrderSuggestionViewEntry -> {
            if (lpItemOrderSuggestionViewEntry == null) {
                ToastUtil.showToast(ResUtil.getString(R.string.msg_inventory_not_found));
                speak(ResUtil.getString(R.string.msg_inventory_not_found));
                return;
            }
            showSnackSuccess(ResUtil.getContext().getResources().getString(R.string.lp_added));
            pickAction.updateOrderItemSuggestion(lpItemOrderSuggestionViewEntry);
            notifyWorkFlowOnLpSuggest();
            notifyPickStepChanged(PickStepMode.SCAN_ITEM);
            notifyPickViewChanged();
            sendPickWorkMessage(PickWorkMessage.obtain(MESSAGE_SWITCH_PICK_BY_PALLET, pickAction.isPickByPallet()));
        });
    }

    private void notifyPickViewChanged() {
        sendPickWorkMessage(PickWorkMessage.obtain(MESSAGE_UPDATE_SUGGEST_ITEM_QTY, pickAction.getSuggestItemQty()));
        sendPickWorkMessage(PickWorkMessage.obtain(MESSAGE_UPDATE_SUGGEST_UOM, pickAction.getSuggestUom()));
        sendPickWorkMessage(PickWorkMessage.obtain(MESSAGE_UPDATE_PICK_ITEM_INFO, pickAction.getPickItemInfo()));
        sendPickWorkMessage(PickWorkMessage.obtain(MESSAGE_UPDATE_PICK_ITEM_PHOTO, pickAction.getPickItemPhotos()));
        sendPickWorkMessage(PickWorkMessage.obtain(MESSAGE_NEXT_LOCATION, pickAction.getPickIssueParameter()));
    }

    private void submitPickItem() {
        if (pickWorkFlow().checkRFID()) {
            return;
        }
        onSubmitPickItem();
    }


    private void onSubmitPickItem() {
        String taskId = pickAction.getPickTask().id;
        if (pickWorkFlow().isPickByLotNo()) {
            onSubmitByLotNo(taskId);
            return;
        }

        if (pickAction.needPrintShippingLabelOnSubmit() && zplPrinterEntry == null) {
            startToPrinterSettingPage.postValue(true);
            return;
        }
        PickResultUpdateEntry pickResultUpdateEntry = pickAction.getPickResult();
        pickResultUpdateEntry.allowExceedMaximumPartialPallet = pickWorkFlow().pickUserLevelController().allowExceedMaximumPartialPallet;
        pickResultUpdateEntry.allowSkipForcePickingByPickType = pickWorkFlow().pickUserLevelController().allowSkipForcePickingByPickType;
        executeWithLoading(pickTaskRepository.submitPick(taskId, pickResultUpdateEntry), pickResultResponse -> {
            ToastUtil.showToast(R.string.msg_submit_success);
            if (!CollectionUtil.isNullOrEmpty(pickResultResponse.shipmentLabels)) {
                pickAction.setSmallParcelShipmentLabels(pickResultResponse.shipmentLabels);
                startPrintShippingLabel(100);
                return;
            }
            checkOnScreenOpCount(pickAction.getLastLocationId());
        }, error -> {
            String errorMsg = error.getErrorMessage();
            ToastUtil.showErrorToast(errorMsg);
            checkToteCartDetail();
        });
    }

    private void onSubmitByLotNo(String taskId){
        List<PickResultUpdateEntry> pickResults = pickAction.getPickResults();
        Stream.of(pickResults).forEach(pickResult -> {
            pickResult.allowExceedMaximumPartialPallet = pickWorkFlow().pickUserLevelController().allowExceedMaximumPartialPallet;
            pickResult.allowSkipForcePickingByPickType = pickWorkFlow().pickUserLevelController().allowSkipForcePickingByPickType;
        });
        executeWithLoading(pickTaskRepository.batchSubmitPick(taskId, pickResults), idResponse -> {
            ToastUtil.showToast(R.string.msg_submit_success);
            checkOnScreenOpCount(pickAction.getLastLocationId());
        }, error -> {
            String errorMsg = error.getErrorMessage();
            ToastUtil.showErrorToast(errorMsg);
            checkToteCartDetail();
        });
    }

    private void startPrintShippingLabel(int delayTime) {
        if (CollectionUtil.isNullOrEmpty(pickAction.getSmallParcelShipmentLabels())) {
            showLoading(false);
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append(ResUtil.getString(R.string.text_print_success))
                    .append("(")
                    .append(pickAction.getPrintedSpShipmentLabels().size())
                    .append("):");
            if (CollectionUtil.isNotNullOrEmpty(pickAction.getPrintedSpShipmentLabels())) {
                pickAction.getPrintedSpShipmentLabels().forEach(shipmentLabel ->
                        stringBuilder.append("\n").append(shipmentLabel.trackingNo));
            }
            stringBuilder.append("\n\n");
            stringBuilder.append(ResUtil.getString(R.string.text_print_fail))
                    .append("(")
                    .append(pickAction.getPrintFailedSpShipmentLabels().size())
                    .append("):");
            if (CollectionUtil.isNotNullOrEmpty(pickAction.getPrintFailedSpShipmentLabels())) {
                pickAction.getPrintFailedSpShipmentLabels().forEach(shipmentLabel ->
                        stringBuilder.append("\n").append(shipmentLabel.trackingNo));
            }
            showPrintShippingLabelFinishedDialog.postValue(stringBuilder.toString());
            return;
        }

        Observable.timer(delayTime, TimeUnit.MILLISECONDS)
                .compose(RxUtil.asyncSchedulers())
                .subscribe(aLong -> {
                    if (delayTime == 100) {
                        showLoading(true);
                    }
                    SmallParcelShipmentLabel shipmentLabel = pickAction.getSmallParcelShipmentLabels().get(0);
                    String printInfo = new String(Base64.decode(shipmentLabel.shipmentLabel, Base64.DEFAULT), StandardCharsets.UTF_8);
                    pickAction.getSmallParcelShipmentLabels().remove(shipmentLabel);
                    PrintData printData = PrintData.basic(pickAction.getIdmUserId(), pickAction.getFacilityName(), zplPrinterEntry.paperSize);
                    printData.jobData = new PrintData.JobData.ZPL(null, null, null, printInfo);
                    printUtil.print(printData, new PrintView() {
                        @Override
                        public void onPrinterNotSelect() {
                        }

                        @Override
                        public void onPrintSuccess(@NonNull PrintData data, @NonNull PrinterEntry printerEntry) {
                            pickAction.addPrintedSpShipmentLabels(List.of(shipmentLabel));
                            markTrackingNoAsPrinted(shipmentLabel.trackingNo);
                            startPrintShippingLabel(pickAction.getPrintShippingLabelIntervalTime());
                        }

                        @Override
                        public void onPrintFailed(@NonNull ErrorResponse response, @Nullable PrinterEntry printerEntry) {
                            pickAction.addPrintFailedSpShipmentLabels(List.of(shipmentLabel));
                            startPrintShippingLabel(pickAction.getPrintShippingLabelIntervalTime());
                        }

                        @Override
                        public void showProgress(boolean show) {

                        }
                    }, zplPrinterEntry);
                });
    }

    private void markTrackingNoAsPrinted(String trackingNo) {
        if (StringUtil.isEmpty(trackingNo)) return;
        executeWithoutLoading(pickTaskRepository.markTrackingNoAsPrinted(trackingNo), aVoid -> {});
    }

    public void confirmPrintShippingLabelFinish() {
        pickAction.clearShipmentLabels();
        checkOnScreenOpCount(pickAction.getLastLocationId());
    }

    public boolean hasPrintFailedShipmentLabels() {
        return pickAction.hasPrintFailedShipmentLabels();
    }

    public void retryPrintFailedShippingLabels() {
        pickAction.retryPrintFailedShippingLabels();
        startPrintShippingLabel(100);
    }

    /**
     * pick task progress
     */
    private void updatePickTaskProgress() {
        String taskId = pickAction.getPickTask().id;
        executeAndShowProgressByParam(pickTaskRepository.getPickProgress(taskId), pickProgress -> {
            if (CollectionUtil.isNotNullOrEmpty(pickProgress)) {
                pickAction.setPickTaskProgress(pickProgress);
                if (pickAction.isCurrentTaskNeedPick()) {
                    getPickLocationItemSuggestView(false);
                } else {
                    closePickStep();
                }
                if (pickAction.pickTypeModel() == PickV1TypeMode.OPPORTUNITY_PICK) {
                    executeWithoutLoading(pickTaskRepository.getPickTableProgress(taskId), pickTableProgress -> {
                        if (CollectionUtil.isNotNullOrEmpty(pickTableProgress.needCollectItemSpecIds)) {
                            sendPickWorkMessage(PickWorkMessage.obtain(MESSAGE_UPDATE_PICK_TO_TABLE_TASK_TOTAL_ITEM, pickTableProgress));
                        }
                    });
                } else {
                    sendPickWorkMessage(PickWorkMessage.obtain(MESSAGE_UPDATE_PICK_TASK_TOTAL_ITEM, pickProgress));
                }
            }
        }, () -> showLoading(true), true);
    }

    private void startPickStep() {
        executeWithLoading(pickTaskRepository.start(parentViewModel.getStepBaseEntry().id), idResponse -> {
            onInitLocationSuggest();
            StepBaseEntry stepBaseEntry = parentViewModel.getStepBaseEntry();
            stepBaseEntry.status = StepStatusEntry.IN_PROGRESS;
            parentViewModel.updateStepBaseEntry(stepBaseEntry);
        }, error -> {
            String errorMsg = error.getErrorMessage();
            ToastUtil.showErrorToast(errorMsg);
        });
    }

    private void closePickStep() {
        executeWithLoading(pickTaskRepository.close(pickAction.getPickTask().id, parentViewModel.getStepBaseEntry().id), idResponse -> {
            ToastUtil.showToast(R.string.msg_step_is_done);
            getStepBaseEntry().status = StepStatusEntry.DONE;
            if (pickWorkFlow().pickUserLevelController().autoStartNextNearestTask) {
                sendPickWorkMessage(PickWorkMessage.obtain(MESSAGE_SHOW_SUGGEST_NEXT_TASK));
            } else {
                showStepCloseStatus();
            }
        }, error -> {
            String errorMsg = error.getErrorMessage();
            ToastUtil.showErrorToast(errorMsg);
        });
    }

    /**
     * suggest next task
     */
    private void suggestNextTask() {
        String userId = LocalPersistence.getUserId(ResUtil.getContext());
        String lastPickLocationId = pickAction.getLastLocationId();
        AutoAssignPickTaskRequestEntry requestEntry = new AutoAssignPickTaskRequestEntry(lastPickLocationId, userId);
        executeWithLoading(pickTaskRepository.getAutoAssignNextTask(requestEntry), pickTaskViewEntry -> {
            if (pickTaskViewEntry == null) {
                ToastUtil.showToast(com.linc.platform.R.string.msg_pick_task_not_found);
                showStepCloseStatus();
                return;
            }
            startPickTask(pickTaskViewEntry);
        }, error -> {
            String errorMsg = error.getErrorMessage();
            ToastUtil.showErrorToast(errorMsg);
        });
    }

    private void showStepCloseStatus() {
        StepStatusPermissionConfig config = new StepStatusPermissionConfig(false, false);
        startFragment(StepStatusFragment.newInstance(getStepBaseEntry(), config));
    }

    private void startPickTask(PickTaskViewEntry pickTaskViewEntry) {
        PickTaskViewEntry entry = new PickTaskViewEntry();
        entry.id = pickTaskViewEntry.id;
        entry.taskType = pickTaskViewEntry.taskType;
        Bundle bundle = new Bundle();
        bundle.putSerializable(TaskStepMenuActivity.GENERAL_TASK_ENTRY, entry);
        startActivity(TaskStepMenuActivity.class, bundle);
        finishPage();
    }

    private void checkToteCartDetail() {
        if (pickAction.pickTypeModel() == PickV1TypeMode.PICK_TO_TOTE) {
            pickTaskVM().checkToteCartDetail();
        }
    }

    private void checkOnScreenOpCount(String locationId) {
        String customerId = pickAction.getPickTask().customerId;
        executeWithLoading(pickTaskRepository.onScreenCountSearch(customerId, locationId),
                onScreenCountResponseEntry -> {
                    if (onScreenCountResponseEntry.getOnScreenCount()) {
                        sendPickWorkMessage(PickWorkMessage.obtain(MESSAGE_SHOW_ON_SCREEN_OP_COUNT_DIALOG, onScreenCountResponseEntry));
                    } else {
                        actionAfterPickSubmitSuccess();
                    }
                },
                errorResponse -> {
                    ToastUtil.showErrorToast(errorResponse.getErrorMessage());
                    actionAfterPickSubmitSuccess();
                });
    }

    private void actionAfterPickSubmitSuccess() {
        checkToteCartDetail();
        onInitLocationSuggest();
    }

    public void onScreenCountMatch(OnScreenCountResponseEntry onScreenCountResponseEntry) {
        OnScreenCountEntry entry = new OnScreenCountEntry(
                getCustomerIdForOnScreenCount(onScreenCountResponseEntry),
                onScreenCountResponseEntry.getLocationId(), getItemSpecIdForOnScreenCount(onScreenCountResponseEntry),
                pickAction.getPickTask().id, pickAction.getPickTask().taskType);
        executeWithLoading(pickTaskRepository.onScreenCountMatch(entry), Void -> ToastUtil.showToast(ResUtil.getString(com.linc.platform.R.string.on_screen_count_complete)));
    }

    public void onScreenCountNotMatch(OnScreenCountResponseEntry onScreenCountResponseEntry) {
        OnScreenCountEntry entry = new OnScreenCountEntry(
                getCustomerIdForOnScreenCount(onScreenCountResponseEntry),
                onScreenCountResponseEntry.getLocationId(), getItemSpecIdForOnScreenCount(onScreenCountResponseEntry),
                pickAction.getPickTask().id, pickAction.getPickTask().taskType);
        executeWithLoading(pickTaskRepository.onScreenCountNotMatch(entry), Void -> ToastUtil.showToast(ResUtil.getString(com.linc.platform.R.string.on_screen_count_complete)));
    }

    private String getCustomerIdForOnScreenCount(OnScreenCountResponseEntry onScreenCountResponseEntry) {
        if (CollectionUtil.isNotNullOrEmpty(onScreenCountResponseEntry.getInventories())
                && onScreenCountResponseEntry.getInventories().size() == 1) {
            String customerId = onScreenCountResponseEntry.getInventories().get(0).getCustomerId();
            if (!TextUtils.isEmpty(customerId)) {
                return customerId;
            }
        }
        return pickAction.getPickTask().customerId;
    }

    private String getItemSpecIdForOnScreenCount(OnScreenCountResponseEntry onScreenCountResponseEntry) {
        if (CollectionUtil.isNotNullOrEmpty(onScreenCountResponseEntry.getInventories())
                && onScreenCountResponseEntry.getInventories().size() == 1) {
            String itemSpecId = onScreenCountResponseEntry.getInventories().get(0).getItemSpecId();
            if (!TextUtils.isEmpty(itemSpecId)) {
                return itemSpecId;
            }
        }
        return pickAction.getLastItemSpecId();
    }

    public void switchSegmentedLocationModel() {
        sendPickWorkMessage(PickWorkMessage.obtain(MESSAGE_UPDATE_SCAN_LOCATION_TYPE));
    }


    private void loadItemValidateRegex(String itemSpecId) {
        if (null == itemSpecId) return;
        pickAction.setItemValidateRegex(null);
        ItemSpecSearchEntry searchEntry = new ItemSpecSearchEntry();
        searchEntry.itemSpecId = itemSpecId;
        executeWithLoading(pickTaskRepository.getItemRule(searchEntry),
                rules -> {
                    ItemSNValidateRuleEntry itemSNValidateRuleEntry = Stream.of(rules).filter(rule -> TextUtils.equals(rule.itemSpecId, itemSpecId)).findSingle().orElse(null);
                    if (null != itemSNValidateRuleEntry) {
                        pickAction.setItemValidateRegex(itemSNValidateRuleEntry.validateRegex);
                    }
                },
                errorResponse -> {
                    ToastUtil.showErrorToast(errorResponse.getErrorMessage());
                    actionAfterPickSubmitSuccess();
                });
    }

    public String getItemValidateRegex(){
        return  pickAction.getItemValidateRegex();
    }
}
