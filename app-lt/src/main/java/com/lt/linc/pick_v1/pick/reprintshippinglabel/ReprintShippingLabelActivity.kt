package com.lt.linc.pick_v1.pick.reprintshippinglabel

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import com.customer.widget.RecyclerViewItemSpace
import com.linc.platform.pick.model.PickTaskViewEntry
import com.lt.linc.R
import com.lt.linc.common.mvi.ReactiveViewScope
import com.lt.linc.common.mvi.SimpleReactiveActivity
import com.lt.linc.databinding.ActivityTaskReprintShippingLabelBinding
import com.lt.linc.toolset.print.setting.PrintSettingActivity

class ReprintShippingLabelActivity :
    SimpleReactiveActivity<ReprintShippingLabelViewModel, ReprintShippingLabelState, ActivityTaskReprintShippingLabelBinding>() {

    private val adapter by lazy {
        ReprintShippingLabelAdapter(
            onClick = { item, isReprint ->
                if (viewModel.zplPrinterEntry == null) {
                    startActivity(Intent(this, PrintSettingActivity::class.java))
                    return@ReprintShippingLabelAdapter
                }
                viewModel.printShippingLabel(item, isReprint)

            })
    }

    @SuppressLint("SetTextI18n")
    override fun ReactiveViewScope.subscribeToUiState() {
        subscribe(ReprintShippingLabelState::selectTabIndex) {
            if (it == 0) {
                binding.notPrintedRb.isChecked = true
            } else {
                binding.printedRb.isChecked = true
            }
        }
        subscribe(ReprintShippingLabelState::showingShipmentDetails) {
            adapter.setNewData(it)
            binding.swipeRefreshLayout.isRefreshing = false
        }
        subscribe(ReprintShippingLabelState::printedCount) {
            binding.printedRb.text = "${getString(R.string.text_printed)}($it)"
        }
        subscribe(ReprintShippingLabelState::notPrintedCount) {
            binding.notPrintedRb.text = "${getString(R.string.text_not_printed)}($it)"
        }
    }

    override fun createViewModel(): ReprintShippingLabelViewModel = ReprintShippingLabelViewModel(
        initialState = ReprintShippingLabelState(
            taskEntry = intent.getSerializableExtra(PickTaskViewEntry.TAG) as PickTaskViewEntry
        )
    )

    override fun initView(savedInstanceState: Bundle?) {
        binding.apply {
            initToolBar(toolbar, R.string.text_shipping_label)
            shippingLabelRv.adapter = adapter
            shippingLabelRv.addItemDecoration(RecyclerViewItemSpace(this@ReprintShippingLabelActivity))
            swipeRefreshLayout.setOnRefreshListener {
                viewModel.loadShipmentDetails(isRefresh = true)
            }
            notPrintedRb.setOnClickListener { viewModel.updateSelectTabIndex(0) }
            printedRb.setOnClickListener { viewModel.updateSelectTabIndex(1) }
            viewModel.updateSelectTabIndex(0)
        }
    }

    override fun onResume() {
        super.onResume()
        viewModel.refreshZplPrinter()
    }
}