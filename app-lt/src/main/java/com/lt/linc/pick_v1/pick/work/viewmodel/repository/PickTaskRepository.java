package com.lt.linc.pick_v1.pick.work.viewmodel.repository;

import com.linc.platform.baseapp.api.LocationApi;
import com.linc.platform.baseapp.model.LocationEntry;
import com.linc.platform.common.lp.LPCreateEntry;
import com.linc.platform.common.lp.LpApi;
import com.linc.platform.common.lp.LpTypeEntry;
import com.linc.platform.common.task.TaskTypeEntry;
import com.linc.platform.cyclecount.api.CycleCountApi;
import com.linc.platform.cyclecount.model.OPCountActionEntry;
import com.linc.platform.cyclecount.model.OPCountOperatorEntry;
import com.linc.platform.cyclecount.model.OnScreenCountEntry;
import com.linc.platform.cyclecount.model.OnScreenCountResponseEntry;
import com.linc.platform.cyclecount.model.OnScreenCountSearchEntry;
import com.linc.platform.foundation.api.ItemSpecAPI;
import com.linc.platform.foundation.api.configurationmap.ConfigurationApi;
import com.linc.platform.foundation.model.ConfigurationMapSearchEntry;
import com.linc.platform.foundation.model.ConfigurationMapViewEntry;
import com.linc.platform.foundation.model.ItemSNValidateRuleEntry;
import com.linc.platform.foundation.model.ItemSpecSearchEntry;
import com.linc.platform.genericstep.api.GenericStepApi;
import com.linc.platform.http.IdResponse;
import com.linc.platform.pack.model.LPViewEntry;
import com.linc.platform.pick.api.PickStepApi;
import com.linc.platform.pick.api.PickTaskBatchPrintAPI;
import com.linc.platform.pick.model.AutoAssignPickTaskRequestEntry;
import com.linc.platform.pick.model.CheckIfRequireReplenishBeforePickResponse;
import com.linc.platform.pick.model.PickResultResponseEntity;
import com.linc.platform.pick.model.PickResultUpdateEntry;
import com.linc.platform.pick.model.PickTaskUpdateEntry;
import com.linc.platform.pick.model.PickTaskViewEntry;
import com.linc.platform.pick.model.picktote.PickToteCartDetailEntry;
import com.lt.linc.common.mvvm.BaseRepository;
import com.lt.linc.pick_v1.pick.work.model.LocationItemSuggestReqEntry;
import com.lt.linc.pick_v1.pick.work.model.LocationItemSuggestionViewEntry;
import com.lt.linc.pick_v1.pick.work.model.LpItemOrderSuggestReqEntry;
import com.lt.linc.pick_v1.pick.work.model.LpItemOrderSuggestionViewEntry;
import com.lt.linc.pick_v1.pick.work.model.PickItemLinesViewEntry;
import com.linc.platform.pick.model.picktablestage.PickTableProgressEntry;

import java.util.List;

import retrofit2.Response;
import rx.Observable;

public class PickTaskRepository extends BaseRepository {

    private PickTaskApi pickTaskApi;
    private PickStepApi pickStepApi;
    private GenericStepApi genericStepApi;
    private CycleCountApi cycleCountApi;
    private ItemSpecAPI itemSpecAPI;
    private LpApi lpApi;
    private LocationApi locationApi;
    private PickTaskBatchPrintAPI pickTaskBatchPrintAPI;
    private ConfigurationApi configurationApi;

    public PickTaskRepository() {
        pickTaskApi = getService(PickTaskApi.class);
        pickStepApi = getService(PickStepApi.class);
        genericStepApi = getService(GenericStepApi.class);
        cycleCountApi = getService(CycleCountApi.class);
        itemSpecAPI = getService(ItemSpecAPI.class);
        lpApi = getService(LpApi.class);
        locationApi = getService(LocationApi.class);
        pickTaskBatchPrintAPI = getService(PickTaskBatchPrintAPI.class);
        configurationApi = getService(ConfigurationApi.class);
    }

    public Observable<Response<LocationItemSuggestionViewEntry>> getLocationItemSuggest(String taskId, LocationItemSuggestReqEntry locationItemSuggestEntry) {
        return pickTaskApi.getLocationItemSuggest(taskId, locationItemSuggestEntry);
    }

    public Observable<Response<LpItemOrderSuggestionViewEntry>> getLpItemOrderSuggest(String taskId, LpItemOrderSuggestReqEntry lpItemOrderSuggestReqEntry) {
        return pickTaskApi.getLpItemOrderSuggest(taskId, lpItemOrderSuggestReqEntry);
    }

    public Observable<Response<PickResultResponseEntity>> submitPick(String taskId, PickResultUpdateEntry pickResult) {
        return pickTaskApi.updateTaskPickResult(taskId, pickResult);
    }

    public Observable<Response<IdResponse>> batchSubmitPick(String taskId, List<PickResultUpdateEntry> pickResults) {
        return pickTaskApi.batchUpdateTaskPickResult(taskId, pickResults);
    }

    public Observable<Response<List<PickItemLinesViewEntry>>> getPickProgress(String taskId) {
        return pickTaskApi.getPickProgress(taskId);
    }

    public Observable<Response<IdResponse>> close(String taskId, String stepId) {
        return pickStepApi.close(taskId, stepId);
    }

    public Observable<Response<IdResponse>> start(String stepId) {
        return genericStepApi.start(stepId);
    }

    public Observable<Response<PickTaskViewEntry>> getAutoAssignNextTask(AutoAssignPickTaskRequestEntry requestEntry) {
        return pickTaskApi.getAutoAssignNextTask(requestEntry);
    }

    public Observable<Response<PickToteCartDetailEntry>> getToteCartDetail(String toteCart) {
        return pickTaskApi.getToteCartDetail(toteCart);
    }

    public Observable<Response<IdResponse>> refreshChildToteHlpId(String taskId, String barcode) {
        return pickTaskApi.refreshChildToteHlpId(taskId, barcode);
    }

    public Observable<Response<Void>> updatePickTask(String taskId, PickTaskUpdateEntry pickTaskUpdateEntry) {
        return pickTaskApi.updatePickTask(taskId, pickTaskUpdateEntry);
    }

    public Observable<Response<CheckIfRequireReplenishBeforePickResponse>> checkIfRequireReplenishBeforePick(String taskId) {
        return pickTaskApi.checkIfRequireReplenishBeforePick(taskId);
    }

    public Observable<Response<OnScreenCountResponseEntry>> onScreenCountSearch(String customerId, String locationId) {
        OnScreenCountSearchEntry searchEntry = new OnScreenCountSearchEntry(TaskTypeEntry.PICK, OPCountOperatorEntry.IS_AFTER, OPCountActionEntry.SUBMITTING, customerId, locationId);
        return cycleCountApi.onScreenCountSearch(searchEntry);
    }

    public Observable<Response<Void>> onScreenCountMatch(OnScreenCountEntry onScreenCountEntry) {
        return cycleCountApi.onScreenCountMatch(onScreenCountEntry);
    }

    public Observable<Response<Void>> onScreenCountNotMatch(OnScreenCountEntry onScreenCountEntry) {
        return cycleCountApi.onScreenCountNotMatch(onScreenCountEntry);
    }

    public Observable<Response<List<ItemSNValidateRuleEntry>>> getItemRule(ItemSpecSearchEntry search) {
        return itemSpecAPI.getItemRule(search);
    }

    public Observable<Response<IdResponse>> createHlp() {
        LPCreateEntry lpCreateEntry = new LPCreateEntry();
        lpCreateEntry.type = LpTypeEntry.HLP;
        return lpApi.createLP(lpCreateEntry);
    }

    public Observable<Response<LPViewEntry>> getLp(String lpId) {
        return lpApi.get(lpId);
    }

    public Observable<Response<LocationEntry>> getLocation(String locationId) {
        return locationApi.get(locationId);
    }

    public Observable<Response<PickTableProgressEntry>> getPickTableProgress(String taskId) {
        return pickTaskApi.getPickTableProgress(taskId);
    }
    public Observable<Response<Void>> markTrackingNoAsPrinted(String trackingNo) {
        return pickTaskBatchPrintAPI.markTrackingNoAsPrinted(trackingNo);
    }

    public Observable<Response<List<ConfigurationMapViewEntry>>> searchConfigurationMap(ConfigurationMapSearchEntry search) {
        return configurationApi.search(search);
    }
}
