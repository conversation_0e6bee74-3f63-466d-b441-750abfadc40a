package com.lt.linc.pick_v1.pick.reprintshippinglabel

import com.linc.platform.pick.model.PickTaskViewEntry
import com.linc.platform.toolset.print.shippinglabel.model.SmallParcelShipmentDetailViewEntry
import com.lt.linc.common.mvi.ReactiveDataState

data class ReprintShippingLabelState(
    val selectTabIndex: Int = 0,
    val taskEntry: PickTaskViewEntry? = null,
    val shipmentDetails: List<SmallParcelShipmentDetailViewEntry>? = null,
    val showingShipmentDetails: List<SmallParcelShipmentDetailViewEntry>? = null
) : ReactiveDataState {
    val taskId: String = taskEntry?.id ?: ""
    val orderIds: List<String> = taskEntry?.orderIds ?: listOf()
    val printedCount: Int = shipmentDetails?.count { it.isShippingLabelPrinted == true } ?: 0
    val notPrintedCount: Int = shipmentDetails?.count { it.isShippingLabelPrinted != true } ?: 0
}