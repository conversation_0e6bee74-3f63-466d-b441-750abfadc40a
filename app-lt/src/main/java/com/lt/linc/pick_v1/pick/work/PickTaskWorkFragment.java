package com.lt.linc.pick_v1.pick.work;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import androidx.appcompat.widget.AppCompatImageButton;

import com.linc.platform.common.step.StepBaseEntry;
import com.linc.platform.pick.model.PickTaskStartEvent;
import com.linc.platform.pick.model.PickTaskViewEntry;
import com.lt.linc.R;
import com.lt.linc.common.mvvm.BaseVMFragment;
import com.lt.linc.pick_v1.pick.PickTaskVM;
import com.lt.linc.pick_v1.pick.base.state.StateLayout;
import com.lt.linc.pick_v1.pick.model.PickEventConstant;
import com.lt.linc.pick_v1.pick.model.PickStepMode;
import com.lt.linc.pick_v1.pick.model.PickWorkFlowEvent;
import com.lt.linc.pick_v1.pick.reprintshippinglabel.ReprintShippingLabelActivity;
import com.lt.linc.pick_v1.pick.start.PickTaskStartFragment;
import com.lt.linc.pick_v1.pick.start.model.PickV1TypeEntry;
import com.lt.linc.pick_v1.pick.work.view.PickAddContainerButtonView;
import com.lt.linc.pick_v1.pick.work.view.PickByPalletView;
import com.lt.linc.pick_v1.pick.work.view.PickItemQtyProgressView;
import com.lt.linc.pick_v1.pick.work.view.PickItemView;
import com.lt.linc.pick_v1.pick.work.view.PickNextLocationView;
import com.lt.linc.pick_v1.pick.work.view.PickScanLocationView;
import com.lt.linc.pick_v1.pick.work.view.PickScanLpDisableView;
import com.lt.linc.pick_v1.pick.work.view.PickScanLpView;
import com.lt.linc.pick_v1.pick.work.view.PickScanToContainerView;
import com.lt.linc.pick_v1.pick.work.view.PickStageSuggestLocationView;
import com.lt.linc.pick_v1.pick.work.view.PickSubmitView;
import com.lt.linc.pick_v1.pick.work.view.PickSuggestLocationView;
import com.lt.linc.pick_v1.pick.work.view.PickTaskProgressView;
import com.lt.linc.pick_v1.pick.work.viewmodel.PickTaskWorkVM;
import com.lt.linc.pick_v1.stage.PickStageActivity;
import com.lt.linc.toolset.print.setting.PrintSettingActivity;
import com.lt.linc.util.v1styledialog.BottomConfirmDialog;
import com.lt.linc.util.v1styledialog.CenterDialog;
import com.unis.fragmentlauncher.FragmentLaunchModel;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

public class PickTaskWorkFragment extends BaseVMFragment<PickTaskWorkVM> {

    public static final String INTENT_PICK_TYPE = "intent_pick_type";

    private StateLayout suggestLocationLayout;
    private StateLayout pickItemProgressLayout;
    private StateLayout pickByPalletLayout;
    private StateLayout pickScanLocationLayout;
    private StateLayout pickScanLpLayout;
    private StateLayout pickItemLayout;
    private StateLayout pickNextLocationButtonLayout;
    private StateLayout pickNewContainerButtonLayout;
    private StateLayout pickSubmitButtonLayout;
    private StateLayout pickProgressLayout;
    private StateLayout pickScanContainerLayout;
    private StateLayout stagesuggestLocationLayout;
    private AppCompatImageButton backStepBtn;

    private PickV1TypeEntry currentPickType;

    public static PickTaskWorkFragment newInstance(PickV1TypeEntry pickTypeMode) {
        PickTaskWorkFragment fragment = new PickTaskWorkFragment();
        Bundle bundle = new Bundle();
        bundle.putSerializable(PickTaskWorkFragment.INTENT_PICK_TYPE, pickTypeMode);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int getLayoutId() {
        return R.layout.fragment_pick_v1_work;
    }


    @Override
    protected PickTaskWorkVM createViewModel() {
        return new PickTaskWorkVM(getActivityViewModel(PickTaskVM.class),currentPickType, getIdmUserId(), getFacilityName());
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Bundle bundle = getArguments();
        currentPickType = (PickV1TypeEntry) bundle.getSerializable(PickTaskWorkFragment.INTENT_PICK_TYPE);
    }

    @Override
    protected void initView() {
        super.initView();
        bindView();
    }

    @Override
    public void onViewCreated(View view, Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        EventBus.getDefault().register(this);
        init();
        viewModel.pickTaskVM().pickStepLiveData.observe(this, this::onPickStepChanged);
        viewModel.pickTaskVM().showToteCartCheckOutDialog.observe(this, show -> {
                    if (show) {
                        showGoToStageDialog();
                    }
                }
        );
        viewModel.startToPrinterSettingPage.observe(this, show ->{
            if (show) startPrinterSettings();
        });
        viewModel.showPrintShippingLabelFinishedDialog.observe(this, message -> {
            if (TextUtils.isEmpty(message)) return;
            CenterDialog.confirm(getActivity(),
                    () -> viewModel.confirmPrintShippingLabelFinish(),
                    () -> {
                        viewModel.confirmPrintShippingLabelFinish();
                        toReprintShippingLabel();
                    },
                    getString(R.string.text_print_shipping_label) + " " + getString(R.string.btn_complete),
                    message,
                    false,
                    getString(R.string.btn_confirm),
                    getString(R.string.reprint),
                    null,
                    null,
                    null,
                    true
            ).show();
        });
    }

    private void init() {
        suggestLocationLayout.addState(new PickSuggestLocationView());
        pickItemProgressLayout.addState(new PickItemQtyProgressView());
        pickByPalletLayout.addState(new PickByPalletView());
        pickScanLocationLayout.addState(new PickScanLocationView());
        pickScanContainerLayout.addState(new PickScanToContainerView());
        pickScanLpLayout.addState(new PickScanLpDisableView());
        pickScanLpLayout.addState(new PickScanLpView());
        pickItemLayout.addState(new PickItemView());
        pickNextLocationButtonLayout.show(new PickNextLocationView());
        pickSubmitButtonLayout.show(new PickSubmitView());
        pickProgressLayout.addState(new PickTaskProgressView());
        pickNewContainerButtonLayout.show(new PickAddContainerButtonView());
        stagesuggestLocationLayout.show(new PickStageSuggestLocationView());
        pickNextLocationButtonLayout.setVisibility(viewModel.pickWorkFlow().pickUserLevelController().showNextLocation ? View.VISIBLE : View.GONE);
    }

    private void toReprintShippingLabel(){
        Intent intent = new Intent(new Intent(getActivity(), ReprintShippingLabelActivity.class));
        intent.putExtra(PickTaskViewEntry.TAG, viewModel.getPickTaskViewEntry());
        startActivity(intent);
    }

    private void backStepClick() {
        viewModel.backPickStep();
    }

    private void onPickStepChanged(PickStepMode pickStepMode) {
        switch (pickStepMode) {
            case GO_TO_LOCATION:
                backStepBtn.setVisibility(View.GONE);
                suggestLocationLayout.showState();
                pickItemProgressLayout.showState();
                pickByPalletLayout.hideState();
                pickScanLocationLayout.showState();
                pickScanLpLayout.showDisableState();
                pickItemLayout.showState();
                pickScanContainerLayout.hideState();
                pickProgressLayout.showState();
                stagesuggestLocationLayout.hideState();
                break;
            case SCAN_ILP:
                pickByPalletLayout.hideState();
                pickScanLocationLayout.hideState();
                pickScanLpLayout.showAbleState();
                pickScanContainerLayout.hideState();
                backStepBtn.setVisibility(View.VISIBLE);
                if (viewModel.pickWorkFlow().isNeedShowStageSuggestLocations()) {
                    stagesuggestLocationLayout.showState();
                } else {
                    stagesuggestLocationLayout.hideState();
                }
                break;
            case SCAN_ITEM:
                pickByPalletLayout.showState();
                pickScanLocationLayout.hideState();
                pickItemLayout.showState();
                pickScanLpLayout.hideState();
                pickScanContainerLayout.hideState();
                backStepBtn.setVisibility(View.VISIBLE);
                if (viewModel.pickWorkFlow().isNeedShowStageSuggestLocations()) {
                    stagesuggestLocationLayout.showState();
                } else {
                    stagesuggestLocationLayout.hideState();
                }
                break;
            case SCAN_CONTAINER:
                pickScanContainerLayout.showState();
                break;
            case SUBMIT:
                break;
        }
        onPickStepButtonChanged(pickStepMode);
    }

    private void onPickStepButtonChanged(PickStepMode pickStepMode) {
        switch (pickStepMode) {
            case SELECT_PICK_TO_LP_MODE:
            case GO_TO_LOCATION:
            case SCAN_ILP:
            case SCAN_ITEM:
                pickSubmitButtonLayout.setVisibility(View.GONE);
                pickNewContainerButtonLayout.setVisibility(View.GONE);
                break;
            case SCAN_CONTAINER:
                if (viewModel.pickWorkFlow().isPickByPallet()) {
                    pickNewContainerButtonLayout.setVisibility(View.GONE);
                    pickSubmitButtonLayout.setVisibility(View.VISIBLE);
                } else {
                    pickNewContainerButtonLayout.setVisibility(View.VISIBLE);
                    pickSubmitButtonLayout.setVisibility(View.GONE);
                }
                break;
            case SUBMIT:
                pickNewContainerButtonLayout.setVisibility(View.GONE);
                pickSubmitButtonLayout.setVisibility(View.VISIBLE);
                break;
        }
    }


    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEvent(PickWorkFlowEvent workFlowEvent) {
        int event = workFlowEvent.workFlowEvent;
        Object value = workFlowEvent.workFlowValue;
        switch (event) {
            case PickEventConstant.MESSAGE_WORK_FLOW_CHECK_VERIFY_LOCATION:
                boolean isVerify = (boolean)value;
                if(isVerify){
                    pickScanLocationLayout.showState();
                }else{
                    pickScanLocationLayout.hideState();
                }
                break;
            case PickEventConstant.MESSAGE_WORK_FLOW_HIDE_SCAN_LP:
                boolean isHideScanLp = (boolean) value;
                if (isHideScanLp) {
                    pickScanLpLayout.hideState();
                }
                break;
            case PickEventConstant.MESSAGE_WORK_FLOW_HIDE_PICK_BY_PALLET:
                boolean needPickByPallet = (boolean) value;
                if (needPickByPallet) {
                    pickByPalletLayout.addState(new PickByPalletView());
                } else {
                    pickByPalletLayout.removeAllState();
                }
                break;
        }
        viewModel.handlePickWorkFlowMessage(workFlowEvent);
    }

    @Override
    public void onDestroy() {
        EventBus.getDefault().unregister(this);
        super.onDestroy();
    }

    private void showGoToStageDialog() {
        BottomConfirmDialog dialog = new BottomConfirmDialog(getActivity(), getString(R.string.msg_tote_cart_to_stage), null,
                getString(R.string.no), getString(R.string.text_yes),
                null, () -> {
            Intent intent = new Intent(getActivity(), PickStageActivity.class);
            PickTaskStartEvent startEvent = new PickTaskStartEvent();
            startEvent.stepBaseEntry = viewModel.getStepBaseEntry();
            startEvent.taskViewEntry = viewModel.getPickTaskViewEntry();
            startEvent.startFrom = this.getClass().getName();
            EventBus.getDefault().postSticky(startEvent);
            intent.putExtra(StepBaseEntry.TAG, viewModel.getStepBaseEntry());
            startActivity(intent);
            finishPage();
        });
        dialog.show();
    }

    @Override
    public boolean supportVoice() {
        return true;
    }

    @Override
    public boolean onBackPressed() {
        startFragment(PickTaskStartFragment.newInstance(), FragmentLaunchModel.SINGLE_TASK);
        return true;
    }

    private void bindView() {
        suggestLocationLayout = findViewById(R.id.pick_suggest_location_layout);
        pickItemProgressLayout = findViewById(R.id.pick_item_progress_layout);
        pickByPalletLayout = findViewById(R.id.pick_by_pallet_layout);
        pickScanLocationLayout = findViewById(R.id.pick_scan_location_layout);
        pickScanLpLayout = findViewById(R.id.pick_scan_lp_layout);
        pickItemLayout = findViewById(R.id.pick_item_layout);
        pickNextLocationButtonLayout = findViewById(R.id.pick_next_location_layout);
        pickNewContainerButtonLayout = findViewById(R.id.pick_new_container_layout);
        pickSubmitButtonLayout = findViewById(R.id.pick_submit_layout);
        pickProgressLayout = findViewById(R.id.pick_progress_layout);
        pickScanContainerLayout = findViewById(R.id.pick_scan_container_layout);
        stagesuggestLocationLayout = findViewById(R.id.pick_stage_suggest_location_layout);
        backStepBtn = findViewById(R.id.pick_back_step_btn);
        backStepBtn.setOnClickListener(v -> backStepClick());
    }

    private void startPrinterSettings() {
        Intent intent = new Intent(getActivity(), PrintSettingActivity.class);
        startActivity(intent);
    }

    @Override
    public void onResume() {
        super.onResume();
        viewModel.refreshZplPrinter();
    }
}
