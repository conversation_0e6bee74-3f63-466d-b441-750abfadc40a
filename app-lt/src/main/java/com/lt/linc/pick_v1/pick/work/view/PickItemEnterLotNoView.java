package com.lt.linc.pick_v1.pick.work.view;

import static com.linc.platform.utils.ResUtil.getString;

import android.text.SpannableString;
import android.text.SpannableStringBuilder;
import android.text.Spanned;
import android.text.TextUtils;
import android.text.style.ForegroundColorSpan;
import android.view.View;
import android.widget.LinearLayout;

import androidx.appcompat.widget.AppCompatButton;
import androidx.appcompat.widget.AppCompatEditText;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.customer.widget.QuickScanner;
import com.linc.platform.utils.CollectionUtil;
import com.linc.platform.utils.ResUtil;
import com.linc.platform.utils.ToastUtil;
import com.lt.linc.R;
import com.lt.linc.pick_v1.pick.model.PickStepMode;
import com.lt.linc.pick_v1.pick.work.adapter.PickItemLotNoAdapter;
import com.lt.linc.pick_v1.pick.work.model.ItemLotNoEntry;
import com.lt.linc.pick_v1.pick.work.model.PickByLotNoViewEntry;
import com.lt.linc.pick_v1.pick.work.viewmodel.PickItemEnterLotNoVM;
import com.lt.linc.pick_v1.pick.work.viewmodel.PickTaskWorkVM;
import com.lt.linc.util.v1styledialog.CenterDialog;

import java.util.List;

public class PickItemEnterLotNoView extends PickBaseViewState<PickItemEnterLotNoVM> {

    private QuickScanner scanLotScanner;

    private AppCompatEditText enterLotNoQtyEt;

    private AppCompatButton nextBtn;

    private LinearLayout lotNoEnterLayout;

    private LinearLayout lotNoDetailLayout;

    private AppCompatTextView fromLpTv;

    private AppCompatTextView itemNameTv;

    private RecyclerView lotNoRlv;

    private LinearLayout llLotNoQty;

    private PickItemLotNoAdapter lotNoAdapter;

    @Override
    protected int getLayoutId() {
        return R.layout.layout_pick_v1_work_enter_lot_no;
    }

    protected void onViewCreated(View stateView) {
        super.onViewCreated(stateView);
        bindView(stateView);
        initLotNoRecyclerView();
        scanLotScanner.setScanEvent((view, data) -> viewModel.onScanLotNo(data));
        viewModel.pickByLotNoViewLiveData.observe(getFragment(), this::updatePicKLotNoDetail);
        viewModel.switchEnterMode.observe(getFragment(), this::updateEnterEditView);
        viewModel.enterLotNoList.observe(getFragment(), this::updateLotNoDetail);
        viewModel.scanLotNo.observe(getFragment(), this::setScanLotText);
    }

    private void initLotNoRecyclerView() {
        lotNoRlv.setLayoutManager(new LinearLayoutManager(context));
        lotNoRlv.setScrollbarFadingEnabled(false);
        lotNoAdapter = new PickItemLotNoAdapter();
        lotNoAdapter.setOnItemChildClickListener((adapter, view, position) -> {
            ItemLotNoEntry lotNoEntry = (ItemLotNoEntry) adapter.getItem(position);
            showRemoveLotNoDialog(lotNoEntry);
        });
        lotNoRlv.setAdapter(lotNoAdapter);
    }


    @Override
    public String getState() {
        return STATE_ABLE;
    }

    @Override
    protected PickItemEnterLotNoVM createViewModel(PickTaskWorkVM pickTaskVM) {
        return new PickItemEnterLotNoVM(pickTaskVM);
    }

    public void setPickByLotData(PickByLotNoViewEntry pickByLotNoViewEntry) {
        viewModel.setPickByLotData(pickByLotNoViewEntry);
    }

    private void updatePicKLotNoDetail(PickByLotNoViewEntry pickByLotNoViewEntry) {
        fromLpTv.setText(pickByLotNoViewEntry.fromLp);
        itemNameTv.setText(ResUtil.format(R.string.pick_progress_item, pickByLotNoViewEntry.itemInfoViewEntry.name));
        lotNoAdapter.setItemUom(viewModel.getSuggestUom());
    }

    private void setScanLotText(String scanLot) {
        scanLotScanner.setText(scanLot);
    }

    private void updateEnterEditView(boolean isEnterLotNo) {
        if (isEnterLotNo) {
            scanLotScanner.reset();
            enterLotNoQtyEt.setText("");
            scanLotScanner.postDelayed(new Runnable() {
                @Override
                public void run() {
                    scanLotScanner.setInputFocus();
                }
            }, 800);
            scanLotScanner.setVisibility(View.VISIBLE);
            llLotNoQty.setVisibility(View.GONE);
        } else {
            scanLotScanner.setVisibility(View.GONE);
            enterLotNoQtyEt.postDelayed(new Runnable() {
                @Override
                public void run() {
                    enterLotNoQtyEt.setFocusable(true);
                    enterLotNoQtyEt.requestFocus();
                }
            }, 800);
            llLotNoQty.setVisibility(View.VISIBLE);
        }
    }

    private void updateLotNoDetail(List<ItemLotNoEntry> lotNoEntryList) {
        lotNoAdapter.setNewData(lotNoEntryList);
        nextBtn.setVisibility(CollectionUtil.isNullOrEmpty(lotNoEntryList) ? View.GONE : View.VISIBLE);
    }

    private void enterClick(View view) {
        viewModel.enterLotNoQty(enterLotNoQtyEt.getText().toString(), scanLotScanner.getText());
    }


    private void nextLpClick(View view) {
        String editLotNo = scanLotScanner.getText();
        String editLotNoQty = enterLotNoQtyEt.getText().toString();
        if (!TextUtils.isEmpty(editLotNo) && TextUtils.isEmpty(editLotNoQty)) {
            ToastUtil.showToast(R.string.hint_enter_qty);
            return;
        }
        showNextLpConfirmDialog();
    }

    private void showNextLpConfirmDialog() {
        String message = ResUtil.getString(R.string.message_pick_by_lot_no_next_lp);
        String positiveText = ResUtil.getString(R.string.text_submit_uppercase);
        String negativeText = ResUtil.getString(R.string.text_report);
        SpannableString spannableString = new SpannableString(message);
        spannableString.setSpan(new ForegroundColorSpan(context.getResources().getColor(R.color.accent_blue_v1)),
                message.indexOf(negativeText), message.indexOf(negativeText) + negativeText.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        spannableString.setSpan(new ForegroundColorSpan(context.getResources().getColor(R.color.accent_blue_v1)),
                message.indexOf(positiveText), message.indexOf(positiveText) + positiveText.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        CenterDialog.confirm(context,
                () -> viewModel.submitEnterLotNos(),
                () -> viewModel.reportNextLpIssue(),
                ResUtil.getString(R.string.text_error),
                spannableString, true,
                positiveText, negativeText, null, null, null, true).show();
    }

    private void showRemoveLotNoDialog(ItemLotNoEntry lotNoEntry) {
        String lotNo = ResUtil.getString(R.string.lot_no) + ":" + lotNoEntry.lotNo;
        SpannableStringBuilder builder = new SpannableStringBuilder();
        SpannableString spannableString = new SpannableString(lotNo);
        spannableString.setSpan(new ForegroundColorSpan(context.getResources().getColor(R.color.green_56C288)),
                lotNo.indexOf(getString(R.string.lot_no)) + getString(R.string.lot_no).length() + 1, lotNo.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        builder.append(spannableString);
        builder.append(" | ");
        builder.append(lotNoEntry.qty.toString());
        builder.append(" " + viewModel.getSuggestUom());
        CenterDialog.confirm(context,
                () -> viewModel.removeLotNo(lotNoEntry), null,
                ResUtil.getString(R.string.text_confirm_removal), builder, true,
                ResUtil.getString(R.string.btn_confirm),
                ResUtil.getString(R.string.btn_cancel), null, null, null, true).show();
    }

    @Override
    protected void onPickStepChanged(PickStepMode pickStepMode) {
        viewModel.setEnterMode(true);
        switch (pickStepMode) {
            case SCAN_ITEM:
                nextBtn.setVisibility(View.VISIBLE);
                lotNoEnterLayout.setVisibility(View.VISIBLE);
                lotNoDetailLayout.setVisibility(View.GONE);
                break;
            case SCAN_CONTAINER:
                nextBtn.setVisibility(View.GONE);
                lotNoEnterLayout.setVisibility(View.GONE);
                lotNoDetailLayout.setVisibility(View.VISIBLE);
                break;
        }
    }

    @Override
    public void onStatePause() {
        super.onStatePause();
        viewModel.clear();
    }

    private void bindView(View view) {
        scanLotScanner = view.findViewById(R.id.scan_lot_no_scanner);
        enterLotNoQtyEt = view.findViewById(R.id.enter_lot_no_qty_edit);
        nextBtn = view.findViewById(R.id.next_lp_btn);
        lotNoEnterLayout = view.findViewById(R.id.lot_no_enter_layout);
        lotNoDetailLayout = view.findViewById(R.id.lot_no_detail_layout);
        fromLpTv = view.findViewById(R.id.pick_from_lp_tv);
        itemNameTv = view.findViewById(R.id.pick_item_name_tv);
        lotNoRlv = view.findViewById(R.id.lot_no_recycler_view);
        llLotNoQty = view.findViewById(R.id.ll_lot_no_qty);
        view.findViewById(R.id.enter_btn).setOnClickListener(this::enterClick);
        view.findViewById(R.id.next_lp_btn).setOnClickListener(this::nextLpClick);
    }
}
