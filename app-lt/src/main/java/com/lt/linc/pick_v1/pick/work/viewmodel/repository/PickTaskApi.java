package com.lt.linc.pick_v1.pick.work.viewmodel.repository;

import com.linc.platform.http.IdResponse;
import com.linc.platform.pick.model.AutoAssignPickTaskRequestEntry;
import com.linc.platform.pick.model.CheckIfRequireReplenishBeforePickResponse;
import com.linc.platform.pick.model.PickResultResponseEntity;
import com.linc.platform.pick.model.PickResultUpdateEntry;
import com.linc.platform.pick.model.PickTaskUpdateEntry;
import com.linc.platform.pick.model.PickTaskViewEntry;
import com.linc.platform.pick.model.picktablestage.PickTableProgressEntry;
import com.linc.platform.pick.model.picktablestage.UnStageItemEntry;
import com.linc.platform.pick.model.picktote.PickToteCartDetailEntry;
import com.lt.linc.pick_v1.pick.work.model.LocationItemSuggestReqEntry;
import com.lt.linc.pick_v1.pick.work.model.LocationItemSuggestionViewEntry;
import com.lt.linc.pick_v1.pick.work.model.LpItemOrderSuggestReqEntry;
import com.lt.linc.pick_v1.pick.work.model.LpItemOrderSuggestionViewEntry;
import com.lt.linc.pick_v1.pick.work.model.PickItemLinesViewEntry;

import java.util.List;

import retrofit2.Response;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.PUT;
import retrofit2.http.Path;
import rx.Observable;

public interface PickTaskApi {

    @POST("bam/pick/pick-task/{taskId}/location-item-suggest")
    Observable<Response<LocationItemSuggestionViewEntry>> getLocationItemSuggest(@Path("taskId") String taskId, @Body LocationItemSuggestReqEntry locationItemSuggestEntry);

    @POST("bam/pick/pick-task/{taskId}/lp-item-order-suggest")
    Observable<Response<LpItemOrderSuggestionViewEntry>> getLpItemOrderSuggest(@Path("taskId") String taskId, @Body LpItemOrderSuggestReqEntry lpItemOrderSuggestReqEntry);

    @POST("wms-app/outbound/pick/task/{taskId}/pick-result")
    Observable<Response<PickResultResponseEntity>> updateTaskPickResult(@Path("taskId") String taskId,
                                                                        @Body PickResultUpdateEntry pickResult);

    @POST("wms-app/outbound/pick/task/{taskId}/batch/pick-result")
    Observable<Response<IdResponse>> batchUpdateTaskPickResult(@Path("taskId") String taskId,
                                                               @Body List<PickResultUpdateEntry> pickResults);

    @GET("wms-app/outbound/pick/task/{taskId}/pick-progress")
    Observable<Response<List<PickItemLinesViewEntry>>> getPickProgress(@Path("taskId") String taskId);

    @GET("wms-app/outbound/pick/task/user/{userId}/location/{lastPickLocationId}/{taskId}/next-task")
    Observable<Response<PickTaskViewEntry>> getNextTask(@Path("userId") String userId, @Path("lastPickLocationId") String lastPickLocationId, @Path("taskId") String taskId);

    @POST("wms-app/control-panel/auto-assign-pick-task")
    Observable<Response<PickTaskViewEntry>> getAutoAssignNextTask(@Body AutoAssignPickTaskRequestEntry requestEntry);

    @POST("bam/pick/barcode/{toteCart}/getToteCartDetail")
    Observable<Response<PickToteCartDetailEntry>> getToteCartDetail(
            @Path("toteCart") String toteCart);

    @POST("bam/pick/pick-task/{taskId}/barcode/{barcode}/refreshChildToteHlpId")
    Observable<Response<IdResponse>> refreshChildToteHlpId(
            @Path("taskId") String taskId,
            @Path("barcode") String barcode);

    @PUT("wms-app/outbound/pick/task/{taskId}")
    Observable<Response<Void>> updatePickTask(
            @Path("taskId") String taskId,
            @Body PickTaskUpdateEntry pickTaskUpdateEntry);

    @POST("wms-app/outbound/pick/task/{taskId}/check-if-require-replenish-before-pick")
    Observable<Response<CheckIfRequireReplenishBeforePickResponse>> checkIfRequireReplenishBeforePick(@Path("taskId") String taskId);

    @POST("wms-app/outbound/pick/task/{taskId}/pick-stage-progress-bar")
    Observable<Response<PickTableProgressEntry>> getPickTableProgress(@Path("taskId") String taskId);
}
