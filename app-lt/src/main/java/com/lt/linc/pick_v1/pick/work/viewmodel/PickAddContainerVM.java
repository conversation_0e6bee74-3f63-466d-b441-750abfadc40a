package com.lt.linc.pick_v1.pick.work.viewmodel;

import static com.linc.platform.utils.ResUtil.getContext;
import static com.lt.linc.pick_v1.pick.model.PickEventConstant.MESSAGE_PRINT_NEW_CONTAINER;

import android.os.Bundle;
import androidx.annotation.NonNull;

import com.linc.platform.common.lp.LpTypeEntry;
import com.linc.platform.core.LocalPersistence;
import com.linc.platform.inventory.model.LpJobEntry;
import com.linc.platform.print.GeneralLPPrintCallback;
import com.linc.platform.print.GeneralLPPrinter;
import com.linc.platform.print.commonprintlp.PrintData;
import com.linc.platform.print.model.LabelSizeEntry;
import com.linc.platform.print.model.LpPrintHelper;
import com.linc.platform.utils.CollectionUtil;
import com.linc.platform.utils.PrintUtil;
import com.lt.linc.pick_v1.pick.work.model.PickWorkMessage;
import com.lt.linc.toolset.print.lp.PrintLpActivity;
import com.lt.linc.toolset.print.setting.PrintSettingActivity;

import java.util.List;


public class PickAddContainerVM extends PickTaskWorkChildBaseVM {

    private GeneralLPPrinter printer;

    public PickAddContainerVM(@NonNull PickTaskWorkVM viewModel) {
        super(viewModel);
        printer = GeneralLPPrinter.getInstance();
    }


    public void printLp(String userId, String facilityName) {
        PrintData printData = PrintData.basic(userId, facilityName, LabelSizeEntry.TWO_ONE);
        if (!PrintUtil.hasPrinter(printData)) {
            startActivity(PrintSettingActivity.class, null);
        } else {
            String orderId;
            if (CollectionUtil.isNotNullOrEmpty(getPickTaskViewEntry().orderIds)
                    && getPickTaskViewEntry().orderIds.size() == 1) {
                orderId = getPickTaskViewEntry().orderIds.get(0);
            }else{
                orderId = pickWorkFlow().getPickOrderId();
            }
            LpPrintHelper helper = new LpPrintHelper();
            helper.lpType = LpTypeEntry.getLPTypeByName(getPickTaskViewEntry().getPickToLPType());
            helper.printOrderId = orderId;
            helper.copyCount = 1;
            printer.createLpPrintJob(helper, printData, new GeneralLPPrintCallback() {
                        @Override
                        public void onSuccess(List<String> lpIds) {
                            if (CollectionUtil.isNotNullOrEmpty(lpIds)) {
                                sendPickWorkMessage(PickWorkMessage.obtain(MESSAGE_PRINT_NEW_CONTAINER, lpIds.get(0)));
                            }
                        }

                        @Override
                        public void onWifiFailed(LpJobEntry lpJobEntry, String message) {
                            printLpFailed(lpJobEntry);
                        }

                        @Override
                        public void showProgress(boolean show) {
                            showLoading(show);
                        }

                        @Override
                        public void onBluetoothPrintFail(LpJobEntry lpJobEntry, String errorMsg) {
                            printLpFailed(lpJobEntry);
                        }
                    });
        }
    }


    private void printLpFailed(LpJobEntry lpJobEntry) {
        Bundle bundle = new Bundle();
        bundle.putString(PrintLpActivity.DEFAULT_LP_TYPE, LocalPersistence.getPickToLPType(getContext()));
        bundle.putSerializable(PrintLpActivity.FAILED_LP_PRINT_JOB, lpJobEntry);
        List<String> orderIds = getPickTaskViewEntry().orderIds;
        if (CollectionUtil.isNotNullOrEmpty(orderIds)
                && orderIds.size() == 1) {
            bundle.putString(PrintLpActivity.PRINT_ORDER_ID, orderIds.get(0));
        }
        startActivity(PrintLpActivity.class, bundle);
    }

}
