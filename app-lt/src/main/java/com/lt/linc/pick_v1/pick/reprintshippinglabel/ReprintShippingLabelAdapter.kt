package com.lt.linc.pick_v1.pick.reprintshippinglabel

import android.annotation.SuppressLint
import com.linc.platform.toolset.print.shippinglabel.model.SmallParcelShipmentDetailViewEntry
import com.lt.linc.R
import com.lt.linc.common.extensions.setVisibleOrGone
import com.lt.linc.common.mvvm.kotlin.BaseBindingDifferQuickAdapter
import com.lt.linc.common.mvvm.kotlin.BaseBindingViewHolder
import com.lt.linc.databinding.ItemTaskReprintShippingLabelBinding

class ReprintShippingLabelAdapter(val onClick: (SmallParcelShipmentDetailViewEntry, Boolean) -> Unit) :
    BaseBindingDifferQuickAdapter<SmallParcelShipmentDetailViewEntry, ItemTaskReprintShippingLabelBinding>() {
    @SuppressLint("SetTextI18n")
    override fun convert(
        helper: BaseBindingViewHolder<ItemTaskReprintShippingLabelBinding>?, item: SmallParcelShipmentDetailViewEntry
    ) {
        helper?.binding?.apply {
            trackingNoTv.text = item.trackingNo
            orderIdTv.text = item.orderId
            itemNameTv.text = "${item.itemSpecName} (${item.qty}${item.uomName})"
            isPrintTv.text = "${mContext.getString(R.string.text_printed)}: ${
                if (item.isShippingLabelPrinted) mContext.getString(R.string.text_yes) else mContext.getString(R.string.text_no)
            }"
            printBtn.text = mContext.getString(if (item.isShippingLabelPrinted) R.string.reprint else R.string.print)
            printBtn.setOnClickListener {
                onClick.invoke(item, item.isShippingLabelPrinted)
            }
        }
    }

    override fun areItemsTheSame(
        oldItem: SmallParcelShipmentDetailViewEntry, newItem: SmallParcelShipmentDetailViewEntry
    ): Boolean {
        return oldItem.trackingNo == newItem.trackingNo
    }

    override fun areContentsTheSame(
        oldItem: SmallParcelShipmentDetailViewEntry, newItem: SmallParcelShipmentDetailViewEntry
    ): Boolean {
        return oldItem.trackingNo == newItem.trackingNo && oldItem.isShippingLabelPrinted == newItem.isShippingLabelPrinted
    }
}