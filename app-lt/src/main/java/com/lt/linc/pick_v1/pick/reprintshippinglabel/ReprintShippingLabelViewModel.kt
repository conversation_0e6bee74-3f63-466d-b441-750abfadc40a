package com.lt.linc.pick_v1.pick.reprintshippinglabel

import android.util.Base64
import com.linc.platform.http.ErrorResponse
import com.linc.platform.pick.api.PickTaskBatchPrintAPI
import com.linc.platform.print.commonprintlp.PrintData
import com.linc.platform.print.commonprintlp.PrintData.Companion.basic
import com.linc.platform.print.commonprintlp.PrintData.JobData.ZPL
import com.linc.platform.print.commonprintlp.PrintView
import com.linc.platform.print.commonprintlp.PrinterConfig
import com.linc.platform.print.model.LabelSizeEntry
import com.linc.platform.print.model.PrinterEntry
import com.linc.platform.toolset.print.shippinglabel.api.ReprintShippingLabelApi
import com.linc.platform.toolset.print.shippinglabel.model.SmallParcelShipmentDetailSearchEntry
import com.linc.platform.toolset.print.shippinglabel.model.SmallParcelShipmentDetailViewEntry
import com.linc.platform.utils.PrintUtil
import com.linc.platform.utils.StringUtil
import com.lt.linc.R
import com.lt.linc.common.extensions.deepCopy
import com.lt.linc.common.mvi.SimpleReactiveViewModel
import com.lt.linc.common.mvi.subscribe
import com.lt.linc.common.mvvm.kotlin.BaseRepository
import com.lt.linc.common.mvvm.kotlin.extensions.apiServiceLazy
import com.lt.linc.common.mvvm.kotlin.extensions.facilityName
import com.lt.linc.common.mvvm.kotlin.extensions.idmUserId
import com.lt.linc.common.mvvm.kotlin.extensions.launch
import com.lt.linc.common.mvvm.kotlin.extensions.showLoading
import com.lt.linc.common.mvvm.kotlin.extensions.showSnack
import com.lt.linc.util.SnackType
import java.nio.charset.StandardCharsets

class ReprintShippingLabelViewModel(
    val repository: ReprintShippingLabelRepository = ReprintShippingLabelRepository(),
    initialState: ReprintShippingLabelState = ReprintShippingLabelState(),
) : SimpleReactiveViewModel<ReprintShippingLabelState>(initialState) {

    private val printUtil = PrintUtil.newInstance()
    var zplPrinterEntry: PrinterEntry? = null

    init {
        initData()
        refreshZplPrinter()
        loadShipmentDetails()
    }

    fun initData() {

        subscribe(
            ReprintShippingLabelState::shipmentDetails,
            ReprintShippingLabelState::selectTabIndex
        ) { shipmentDetails, selectTabIndex ->
            val showingDetails = if (selectTabIndex == 0) {
                shipmentDetails?.filter { it.isShippingLabelPrinted != true }
            } else {
                shipmentDetails?.filter { it.isShippingLabelPrinted == true }
            }
            setDataState { copy(showingShipmentDetails = showingDetails) }
        }
    }

    fun updateSelectTabIndex(index: Int) {
        setDataState { copy(selectTabIndex = index) }
    }

    fun refreshZplPrinter() {
        zplPrinterEntry =
            PrinterConfig.getPrinter(repository.idmUserId, repository.facilityName, LabelSizeEntry.FOUR_SIX)
    }


    fun loadShipmentDetails(isRefresh: Boolean = false) {
        launch {
            val search = SmallParcelShipmentDetailSearchEntry().apply {
                pickTaskIds = listOf(dataState.taskId)
                orderIds = dataState.orderIds
                includeShippingLabel = true
            }
            val result = requestAwait(repository.searchShipmentDetails(search), showLoading = !isRefresh)
            if (result.isFailure) return@launch
            setDataState { copy(shipmentDetails = result.getOrNull()) }
        }
    }

    fun printShippingLabel(shipmentDetail: SmallParcelShipmentDetailViewEntry, isReprint: Boolean) {
        val printInfo = String(Base64.decode(shipmentDetail.shipmentLabel, Base64.DEFAULT), StandardCharsets.UTF_8)
        val printData = basic(repository.idmUserId, repository.facilityName, zplPrinterEntry!!.paperSize)
        printData.jobData = ZPL(null, null, null, printInfo)
        printUtil.print(printData, object : PrintView {
            override fun onPrinterNotSelect() {
            }

            override fun onPrintSuccess(data: PrintData, printerEntry: PrinterEntry) {
                showSnack(SnackType.SuccessV1(), R.string.text_print_success)
                if (!isReprint) {
                    markTrackingNoAsPrinted(shipmentDetail.trackingNo)
                }
            }

            override fun onPrintFailed(response: ErrorResponse, printerEntry: PrinterEntry?) {
                showSnack(SnackType.ErrorV2(), R.string.text_print_fail)
            }

            override fun showProgress(show: Boolean) {
                showLoading(show)
            }
        }, zplPrinterEntry)
    }

    private fun markTrackingNoAsPrinted(trackingNo: String) {
        if (StringUtil.isEmpty(trackingNo)) return
        launch {
            val result = requestAwait(repository.markTrackingNoAsPrinted(trackingNo))
            if (result.isFailure) return@launch
            val newShipmentDetails = dataState.shipmentDetails?.toMutableList()?.apply {
                val index = indexOfFirst { it.trackingNo == trackingNo }
                if (index >= 0) {
                    get(index).deepCopy()?.let {
                        it.isShippingLabelPrinted = true
                        set(index, it)
                    }
                }
            }
            setDataState { copy(shipmentDetails = newShipmentDetails) }
        }
    }
}


class ReprintShippingLabelRepository : BaseRepository() {

    private val reprintShippingLabelApi by apiServiceLazy<ReprintShippingLabelApi>()
    private val pickTaskBatchPrintAPI by apiServiceLazy<PickTaskBatchPrintAPI>()

    fun searchShipmentDetails(search: SmallParcelShipmentDetailSearchEntry) =
        rxRequest(reprintShippingLabelApi.searchDetails(search))

    fun markTrackingNoAsPrinted(trackingNo: String) =
        rxRequest(pickTaskBatchPrintAPI.markTrackingNoAsPrinted(trackingNo))
}