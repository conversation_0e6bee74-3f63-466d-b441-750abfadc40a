package com.lt.linc.common;

public class Constant {

    public static final String INTENT_REPLENISHMENT_TASK = "intent_replenishment_task";
    public static final String INTENT_REPLENISHMENT_COLLECT = "intent_replenishment_collect";
    public static final String INTENT_REPLENISHMENT_DROP = "intent_replenishment_drop";
    public static final String INTENT_INVENTORY_DETAIL = "intent_inventory_detail";
    public static final String INTENT_EVENT_FLAG = "intent_event_flag";

    public static final String INTENT_DATA_FLAG = "intent_data_flag";
    public static final String INTENT_DATA_NEED_MERGE = "intent_data_need_merge";
    public static final String INTENT_DATA_HAS_SUBMIT = "intent_data_has_submit";
    public static final String INTENT_TASK_TYPE = "intent_task_type";
    public static final String INTENT_PICK_TASK = "intent_pick_task";
    public static final String INTENT_STEP_ENTRY = "intent_step_entry";
    public static final String INTENT_FACILITY_ID = "intent_facility_id";
    public static final String INTENT_TASK_ID = "intent_task_id";
    public static final String INTENT_JOB_CODE = "intent_job_code";
    public static final String INTENT_DEFAULT_ESTIMATED_TIME = "intent_default_estimated_time";
    public static final String INTENT_CUSTOMER = "intent_customer";
    public static final String INTENT_CUSTOMER_NAME = "intent_customer_name";
    public static final String INTENT_NEED_ESTIMATED_TIME = "intent_need_estimated_time";
    public static final String INTENT_FLAG = "intent_flag";
    public static final String INTENT_TASK = "intent_task";
    public static final String INTENT_LOCATION = "intent_location";
    public static final String INTENT_ITEM_SPEC = "intent_item_spec";
    public static final String INTENT_ORDER_IDS = "intent_order_ids";
    public static final String INTENT_EQUIPMENT_NO = "INTENT_EQUIPMENT_NO";
    public static final String INTENT_EXTRA_DATA = "intent_extra_data";
    public static final String INTENT_START_ACTIVITY_FROM = "intent_start_activity_from";
    public static final String INTENT_LP_ID = "intent_lp_id";
    public static final String INTENT_ASSET_ID = "intent_asset_id";
    public static final int INTENT_DATA_FLAG_FINISH = 0;

    public static final int REQUEST_CODE_REPLENISHMENT = 0x001;
    public static final int RESULT_CODE_REPLENISHMENT = 0x101;
    public static final int FLAG_PICK_START_LOAD_ORDER_LIST_ACTIVITY = 0x002;
    public static final int FLAG_FROM_DIRECT_LOAD = 0x003;
}
