package com.lt.linc.common

import android.content.Context
import android.text.Editable
import android.text.TextWatcher
import android.util.AttributeSet
import android.view.View
import android.widget.CheckBox
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.widget.AppCompatEditText
import androidx.appcompat.widget.AppCompatTextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.google.android.material.textfield.TextInputLayout
import com.lt.linc.R
import com.lt.linc.util.SelectDialog
import kotlin.random.Random


/**
 * @Description:
 * @Author: Dennis
 * @CreateDate: 2024/5/20
 */
class AuditView @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0) : ConstraintLayout(context, attrs, defStyleAttr)  {

    private var dataReceiver: AuditDataReceiver = AuditDataReceiver()
    private val labelTextTv: AppCompatTextView
    private val oriTextTv: AppCompatTextView
    private val oriTextLayout: ConstraintLayout
    private val auditEditLayout: TextInputLayout
    private val auditEdit: AppCompatEditText
    private val auditSpinner: AppCompatTextView
    private val checkBox: CheckBox
    private var auditType: Int = AUDIT_TYPE_INPUT
    private var spinnerOptions: List<String> = listOf()
    private val auditColor: Int
    private val checkedColor: Int
    private var hint: String = ""

    companion object {
        const val AUDIT_TYPE_INPUT = 0
        const val AUDIT_TYPE_SELECT = 1
    }

    init {
        val ta = context.obtainStyledAttributes(attrs, R.styleable.AuditView)
        val labelText = ta.getString(R.styleable.AuditView_aud_labelText)
        val labelTextColor = ta.getColor(R.styleable.AuditView_aud_labelTextColor, resources.getColor(R.color.color_ff8d8d8d))
        val oriText = ta.getString(R.styleable.AuditView_aud_oriText)
        val oriTextColor = ta.getColor(R.styleable.AuditView_aud_oriTextColor, resources.getColor(R.color.white))
        checkedColor = ta.getColor(R.styleable.AuditView_aud_checkedColor, resources.getColor(R.color.accent_green_v1))
        val hintText = ta.getString(R.styleable.AuditView_aud_hintText)
        val textBackgroundResId = ta.getResourceId(R.styleable.AuditView_aud_textBackground, R.drawable.rect_525252_r4)
        val auditBackgroundResId = ta.getResourceId(R.styleable.AuditView_aud_auditBackground, R.drawable.rect_393939_border_525252_r4)
        val checkedBackgroundResId = ta.getResourceId(R.styleable.AuditView_aud_checkedBackground, R.drawable.ic_check_box_green_selected)
        val uncheckedBackgroundResId = ta.getResourceId(R.styleable.AuditView_aud_uncheckedBackground, R.drawable.ic_check_box_unselected)
        auditColor = ta.getColor(R.styleable.AuditView_aud_auditColor, resources.getColor(R.color.color_ef4134))
        val isAudited = ta.getBoolean(R.styleable.AuditView_aud_isAudited, false)
        val needCheckBox = ta.getBoolean(R.styleable.AuditView_aud_needCheckBox, true)
        auditType = ta.getInteger(R.styleable.AuditView_aud_type, AUDIT_TYPE_INPUT)
        inflate(context, R.layout.layout_audit_view, this)
        labelTextTv = findViewById(R.id.label_text_tv)
        oriTextTv = findViewById(R.id.ori_text_tv)
        oriTextLayout = findViewById(R.id.ori_text_layout)
        auditEditLayout = findViewById(R.id.audit_edit_layout)
        auditEdit = findViewById(R.id.audit_edit)
        auditSpinner = findViewById(R.id.audit_spinner)
        checkBox = findViewById(R.id.check_box)
        checkBox.setOnCheckedChangeListener { _, isChecked ->
            setCheckBoxBackgroundRes(checkedBackgroundResId, uncheckedBackgroundResId)
            setAuditViewVisibility(!isChecked)
            oriTextTv.setTextColor(if (isChecked) checkedColor else oriTextColor)
            setTextBackgroundRes(if (isChecked) auditBackgroundResId else textBackgroundResId)
            dataReceiver.apply {
                this.isChecked = isChecked
            }
        }
        auditSpinner.setOnClickListener { showSpinnerDialog() }
        auditEdit.addTextChangedListener(object: TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            }

            override fun afterTextChanged(s: Editable?) {
                dataReceiver.apply {
                    data = s.toString().trim()
                }
            }

        })
        setLabelText(labelText)
        setLabelTextColor(labelTextColor)
        setOriText(oriText ?: "")
        setOriTextColor(oriTextColor)
        setHintText(hintText)
        setAuditColor(auditColor)
        setTextBackgroundRes(textBackgroundResId)
        setAuditBackgroundRes(auditBackgroundResId)
        setAuditType(auditType)
        setIsAudited(isAudited)
        setNeedCheckbox(needCheckBox)
        setCheckBoxBackgroundRes(checkedBackgroundResId, uncheckedBackgroundResId)
        ta.recycle()
    }

    fun setLabelText(labelText: String?) {
        labelTextTv.text = labelText?: ""
    }

    fun setLabelText(resId: Int) {
        labelTextTv.text = resources.getString(resId)
    }

    fun setLabelTextColor(labelTextColor: Int) {
        labelTextTv.setTextColor(labelTextColor)
    }

    fun setOriText(text: String?) {
        oriTextTv.text = text?: ""
    }

    fun setOriText(resId: Int) {
        oriTextTv.text = resources.getString(resId)
    }

    fun setOriTextColor(textColor: Int) {
        oriTextTv.setTextColor(textColor)
    }

    fun setHintText(hintText: String?) {
        this.hint = hintText?: ""
        when (auditType) {
            AUDIT_TYPE_INPUT -> auditEditLayout.hint = hintText
            AUDIT_TYPE_SELECT -> {
                auditSpinner.text = hintText
            }
            else -> auditEditLayout.hint = hintText
        }
    }

    fun setHintText(resId: Int) {
        val hintText = resources.getString(resId)
        this.hint = hintText
        when (auditType) {
            AUDIT_TYPE_INPUT -> auditEditLayout.hint = hintText
            AUDIT_TYPE_SELECT -> {
                auditSpinner.text = hintText
            }
            else -> auditEditLayout.hint = hintText
        }
    }

    fun setTextBackgroundRes(backgroundResId: Int) {
        oriTextLayout.setBackgroundResource(backgroundResId)
    }

    fun getOriText(): CharSequence = oriTextTv.text

    fun setAuditColor(color: Int) {
        auditEdit.setTextColor(color)
    }

    fun setAuditText(id: String, content: String?) {
        content?: return
        when (auditType) {
            AUDIT_TYPE_INPUT -> auditEdit.setText(content)
            AUDIT_TYPE_SELECT -> {
                auditSpinner.text = content
                auditSpinner.setTextColor(auditColor)
            }
            else -> auditEdit.setText(content)
        }
        dataReceiver.apply {
            this.id = id
            this.data = content
        }
    }

    fun getAuditText(): CharSequence {
        return when (auditType) {
            AUDIT_TYPE_INPUT -> auditEdit.text?.trim().toString()
            AUDIT_TYPE_SELECT -> auditSpinner.text.toString()
            else -> auditEdit.text?.trim().toString()
        }
    }

    fun setAuditBackgroundRes(backgroundResId: Int) {
        auditEditLayout.setBackgroundResource(backgroundResId)
        auditSpinner.setBackgroundResource(backgroundResId)
    }

    fun setIsAudited(audited: Boolean) {
        checkBox.isChecked = audited
        setAuditViewVisibility(!audited)
    }

    fun isChecked() = checkBox.isChecked

    private fun setAuditViewVisibility(visibility: Boolean) {
        getAuditView().visibility = if (visibility) VISIBLE else GONE
    }

    fun setSpinnerOptions(options: List<String>) {
        this.spinnerOptions = options
    }

    private fun setNeedCheckbox(needCheckBox: Boolean) {
        checkBox.visibility = if (needCheckBox) View.VISIBLE else View.GONE
    }

    private fun setCheckBoxBackgroundRes(checkedBackgroundResId: Int, uncheckedBackgroundResId: Int) {
        if (checkBox.isChecked) {
            checkBox.setBackgroundResource(checkedBackgroundResId)
        } else {
            checkBox.setBackgroundResource(uncheckedBackgroundResId)
        }
    }

    private fun showSpinnerDialog() {
        if (spinnerOptions.isEmpty()) {
            return
        }
        if (context is AppCompatActivity) {
            val selectedOption = getAuditText().toString()
            SelectDialog(
                    title = hint,
                    selectedOption = selectedOption,
                    options = spinnerOptions
            ){ _, selectedReason ->
                auditSpinner.text = selectedReason
                auditSpinner.setTextColor(auditColor)
                dataReceiver.apply {
                    data = selectedReason
                }
            }.show((context as AppCompatActivity).supportFragmentManager, "SelectDialog")
        }
    }

    private fun getAuditView(): View {
        return when (auditType) {
            AUDIT_TYPE_INPUT -> auditEditLayout
            AUDIT_TYPE_SELECT -> auditSpinner
            else -> auditEditLayout
        }
    }

    fun setAuditType(auditType: Int) {
       this.auditType = auditType
        when (auditType) {
            AUDIT_TYPE_INPUT -> {
                auditEditLayout.visibility = VISIBLE
                auditSpinner.visibility = GONE
            }
            AUDIT_TYPE_SELECT -> {
                auditEditLayout.visibility = GONE
                auditSpinner.visibility = VISIBLE
            }
        }
    }

    fun setEnable(enable: Boolean) {
        val view = when (auditType) {
            AUDIT_TYPE_INPUT -> auditEdit
            AUDIT_TYPE_SELECT -> auditSpinner
            else -> auditEdit
        }
        view.isEnabled = enable
    }

    fun setAuditDataReceiver(dataReceiver: AuditDataReceiver) {
        this.dataReceiver = dataReceiver
    }

    fun getAuditDataReceiver() = dataReceiver

    fun getAuditType() = auditType
}

class AuditDataReceiver(var id: String? = Random.nextLong().toString(), var isChecked: Boolean? = false, var label: String? = null, var oriData: String? = null, var data: String? = null)