package com.lt.linc.cyclecount.scanitem

import com.linc.platform.R
import com.linc.platform.common.lp.ScenarioEntry
import com.linc.platform.foundation.api.ItemSpecAPI
import com.linc.platform.foundation.api.ItemUnitApi
import com.linc.platform.foundation.model.ItemSpecEntry
import com.linc.platform.foundation.model.ItemSpecSearchEntry
import com.linc.platform.foundation.model.ItemUnitSearchEntry
import com.linc.platform.foundation.model.UnitEntry
import com.linc.platform.home.more.inventorycount.cyclecount.CountFlowHelper.Companion.isRequireCollectExpirationDate
import com.linc.platform.home.more.inventorycount.cyclecount.CountFlowHelper.Companion.isRequireCollectLotNo
import com.linc.platform.home.more.inventorycount.cyclecount.CountFlowHelper.Companion.isRequireCollectMfgDate
import com.linc.platform.home.more.inventorycount.cyclecount.CountFlowHelper.Companion.isRequireCollectShelfLifeDaysDate
import com.linc.platform.home.more.inventorycount.cyclecount.CountFlowHelper.Companion.isRequireCollectTitle
import com.linc.platform.home.more.inventorycount.cyclecount.api.CountEntryAPI
import com.linc.platform.home.more.inventorycount.cyclecount.cyclecountprocessor.validator.ItemValidator
import com.linc.platform.home.more.inventorycount.cyclecount.cyclecountprocessor.validator.LocationValidator
import com.linc.platform.home.more.inventorycount.cyclecount.model.*
import com.linc.platform.utils.LocationUtil
import com.lt.linc.common.extensions.safeCount
import com.lt.linc.common.mvi.ReactiveViewModel
import com.lt.linc.common.mvvm.kotlin.BaseRepository
import com.lt.linc.common.mvvm.kotlin.extensions.*
import com.lt.linc.cyclecount.CountRecordHandlerViewModel
import com.lt.linc.cyclecount.CycleCountWorkViewModel
import com.lt.linc.cyclecount.ProcessChange
import com.lt.linc.util.SnackType
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.firstOrNull

/**
 * @Description:
 * @Author: Dennis
 * @CreateDate: 2024/5/31
 */
class ScanItemViewModel(
        private val parentViewModel: CycleCountWorkViewModel,
        initialDataState: ScanItemDataState = ScanItemDataState(),
        initialUiState: ScanItemUiState = ScanItemUiState()
) : ReactiveViewModel<ScanItemDataState, ScanItemUiState>(initialDataState, initialUiState) {

    private val isCycleCount = parentViewModel.getActivityViewModel().isCycleCount()
    private val stepEntry = parentViewModel.getActivityViewModel().getStepEntry()
    private val countAction = parentViewModel.getActivityViewModel().getCountAction()
    private val currentLocation = parentViewModel.getCurrentLocation()
    private val countTaskEntry = parentViewModel.getActivityViewModel().getCountTaskEntry()
    private val repository: Repository = Repository()
    private val countRecordHandlerViewModel: CountRecordHandlerViewModel = CountRecordHandlerViewModel()

    fun onItemScanned(itemBarcode: String?, onMultiItemSpec: (List<ItemSpecEntry>) -> Flow<ItemSpecEntry?>) {
        countAction.countFlow().isOverwrite = false
        if (isCycleCount) {
            itemScannedForCycleCount(itemBarcode, onMultiItemSpec)
        } else {
            //recount
        }

    }

    private fun itemScannedForCycleCount(itemBarcode: String?, onMultiItemSpec: (List<ItemSpecEntry>) -> Flow<ItemSpecEntry?>) {
        val cycleCountTaskEntry = countTaskEntry as CycleCountTaskViewEntry
        if (itemBarcode.isNullOrEmpty()) {
            showToast(R.string.msg_please_scan_item_no)
            return
        }
        if (!cycleCountTaskEntry.itemName.isNullOrEmpty() && cycleCountTaskEntry.itemName != itemBarcode) {
            showToast(String.format(getString(R.string.please_input_suggestion_item), cycleCountTaskEntry.itemName, itemBarcode))
            return
        }
        searchItemSpec(itemBarcode, onMultiItemSpec)
    }

    private fun validateLpExistMultiItems(): Boolean {
        val processItems = countAction.countInfo().lpInfo.processItems
        return processItems.map { v -> v.itemSpecId }.distinct().size > 1
    }

    private fun lpHasCounted(): Boolean {
        return countAction.countInfo().lpInfo.processItems.any { v -> v.isCounted }
    }

    private fun generateLpExistMultiCountedItemsMsg(): String {
        val processItemsCounted = countAction.countInfo().lpInfo.processItems.filter { v -> v.isCounted }
        val itemSpecNames = processItemsCounted.joinToString(separator = "/n") { v -> v.itemSpecName }
        return String.format(getString(R.string.msg_there_is_already_items_linked_to_this_lp), itemSpecNames)
    }

    fun searchItemSpec(itemBarcode: String?, onMultiItemSpec: (List<ItemSpecEntry>) -> Flow<ItemSpecEntry?>) {
        val searchEntry = ItemSpecSearchEntry().apply {
            this.eqKeyword = itemBarcode
            this.scenario = ScenarioEntry.AUTO_COMPLETE
            this.customerIds = listOf(countAction.countRecord.customerId)
        }
        launch {
            requestAwait(repository.searchItemSpec(searchEntry)).onSuccess {
                selectItemSpec(it, onMultiItemSpec)
            }
        }
    }

    private suspend fun selectItemSpec(itemSpecs: List<ItemSpecEntry>?, onMultiItemSpec: (List<ItemSpecEntry>) -> Flow<ItemSpecEntry?>) {
        if (itemSpecs == null) {
            showSnack(SnackType.ErrorV2(title = R.string.msg_item_not_found), getString(R.string.msg_please_enter_again))
        } else {
            val itemSpecEntry = when (itemSpecs.safeCount()) {
                0 -> {
                    showSnack(SnackType.ErrorV2(title = R.string.msg_item_not_found), getString(R.string.msg_please_enter_again))
                    null
                }
                1 -> itemSpecs.first()
                else -> onMultiItemSpec(itemSpecs).firstOrNull()
            } ?: return
            onItemConfirmed(itemSpecEntry)
        }
    }

    private fun onItemConfirmed(itemSpecEntry: ItemSpecEntry) {
        val errMsg = ItemValidator.validate(itemSpecEntry, countAction)
        when {
            errMsg.isNullOrEmpty() -> {
                if (validateLpExistMultiItems() && lpHasCounted()) {
                    fireEvent { ScanItemEvent.ShowLpExistMultiItemsDialog(itemSpecEntry, generateLpExistMultiCountedItemsMsg()) }
                } else {
                    searchItemUnits(itemSpecEntry)
                }
            }
            getString(R.string.msg_item_not_matched_new_inventory_found) == errMsg -> {
                fireEvent { ScanItemEvent.ShowNewInventoryFoundDialog(getCurrentLp(), errMsg) }
            }
            else -> showSnack(SnackType.ErrorV2(), errMsg)
        }
    }

    private fun searchItemUnits(itemSpecEntry: ItemSpecEntry) {
        launch {
            requestAwait(repository.searchItemUnit(itemSpecEntry.id)).onSuccess {
                countAction.countFlow().isRecount = false
                countAction.initItemInfo(itemSpecEntry.id, it)
                parentViewModel.onWorkProcessChanged(ProcessChange.CountItem)
                showSnack(SnackType.SuccessV1(), com.lt.linc.R.string.msg_item_has_been_added)
            }
        }
    }

    fun searchItemUnits(itemSpecId: String, action: (List<UnitEntry>) -> Unit) {
        launch {
            requestAwait(repository.searchItemUnit(itemSpecId)).onSuccess {
                it?.let {
                    action.invoke(it)
                }?: showToast(R.string.msg_no_unit)
            }
        }
    }

    fun append(itemSpecEntry: ItemSpecEntry) {
        searchItemUnits(itemSpecEntry)
    }

    fun overwrite(itemSpecEntry: ItemSpecEntry) {
        countAction.countFlow().isOverwrite = true
        searchItemUnits(itemSpecEntry)
    }

    fun delete(entryIds: List<Long>?) {
        if (entryIds.isNullOrEmpty()) {
            return
        }

        launch {
            requestAwait(repository.deleteCountEntryRecord(entryIds)).onSuccess {
                countRecordHandlerViewModel.searchCountHistoryWithUpdateCountEntry(countAction) {
                    showToast(R.string.msg_delete_success)
                    fireEvent { ScanItemEvent.ReRenderView }
                }
            }
        }
    }

    fun hasCountLocationAtLeastOnce(): Boolean {
        return LocationValidator.hasCountedLocation(getCountAction(), getCurrentLocation())
    }

    fun getCountAction() = countAction

    fun getCurrentLocation() = currentLocation

    fun getCurrentLp() = countAction.countInfo().lpInfo.lpId

    fun getCustomer() = countAction.customerViewEntry

    fun generalCountedItemSpecDataList(): List<CountedItemEntry> {
        val lpId = countAction.countInfo().lpInfo.lpId
        val filteredByLpId = countAction.step.countEntries?.filter { v -> v.lpId == lpId }
        return generalItemSpecDataList(filteredByLpId)
    }

    private fun generalItemSpecDataList(countEntryListSameLpId: List<CountEntryViewEntry>?): List<CountedItemEntry> {
        val list = mutableListOf<CountedItemEntry>()
        val groupByItemSpecId = countEntryListSameLpId?.groupBy { v -> v.itemSpecId }
        groupByItemSpecId?.keys?.forEach { itemSpecId ->
            val countEntryListSameItemSpec = groupByItemSpecId[itemSpecId]
            val generalItemPropertyDataList = generalItemPropertyDataList(countEntryListSameItemSpec)
            val countedItemEntry = CountedItemEntry().apply {
                this.itemSpecId = itemSpecId
                this.itemSpecName = countEntryListSameItemSpec!![0].item.name
                this.itemSpec = countEntryListSameItemSpec[0].item
                this.isExpand = true
                this.itemProperties = generalItemPropertyDataList.toMutableList()
            }
            list.add(countedItemEntry)
        }
        return list
    }

    private fun generalItemPropertyDataList(countEntryListSameItemSpecId: List<CountEntryViewEntry>?): List<CountItemPropertyEntry> {
        val list = mutableListOf<CountItemPropertyEntry>()
        countEntryListSameItemSpecId?.forEach { v ->
            val itemSpec = v.item
            val countItemPropertyEntry = CountItemPropertyEntry().apply {
                this.entryId = v.id
                this.itemSpecId = itemSpec.id
                this.lotNo = v.lotNo
                this.qty = v.qty
                this.unitName = v.unit?.name
                this.unitId = v.unitId
                this.mfgDate = v.mfgDate
                this.expirationDate = v.expirationDate
                this.shelfLifeDays = v.shelfLifeDays
                this.requireCollectLotNoOnReceive = itemSpec.requireCollectLotNoOnReceive ?: false
                this.requireCollectExpirationDateOnReceive = itemSpec.requireCollectExpirationDateOnReceive ?: false
                this.requireCollectMfgDateOnReceive = itemSpec.requireCollectMfgDateOnReceive ?: false
                this.requireCollectShelfLifeDaysOnReceive = itemSpec.requireCollectShelfLifeDaysOnReceive ?: false
            }
            list.add(countItemPropertyEntry)
        }
        return list
    }

}

private class Repository : BaseRepository() {
    private val itemSpecAPI by apiServiceLazy<ItemSpecAPI>()
    private val itemUnitApi by apiServiceLazy<ItemUnitApi>()
    private val cuntEntryAPI by apiServiceLazy<CountEntryAPI>()

    fun searchItemSpec(searchEntry: ItemSpecSearchEntry) = rxRequest(itemSpecAPI.search(searchEntry))

    fun searchItemUnit(itemSpecId: String) = rxRequest(itemUnitApi.searchItemUnit(ItemUnitSearchEntry().apply {
        this.itemSpecId = itemSpecId
    }))

    fun deleteCountEntryRecord(entryIds: List<Long>) = rxRequest(cuntEntryAPI.deleteCountEntryRecord(entryIds))
}