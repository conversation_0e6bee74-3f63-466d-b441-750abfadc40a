package com.lt.linc.cyclecount.scanlp

import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.linc.platform.home.more.inventorycount.cyclecount.model.CountedItemEntry
import com.linc.platform.home.more.inventorycount.cyclecount.model.CountedLpEntry
import com.lt.linc.R
import com.lt.linc.common.extensions.setVisibleOrGone
import com.lt.linc.common.mvvm.kotlin.BaseBindingDifferQuickAdapter
import com.lt.linc.common.mvvm.kotlin.BaseBindingViewHolder
import com.lt.linc.cyclecount.scanitem.CountedItemAdapter
import com.lt.linc.databinding.ItemCountedLpBinding

class CountedLpAdapter(
    private val onDeleteListener: (CountedLpEntry) -> Unit,
    private val onAddLotNoListener: ((String, CountedItemEntry) -> Unit)? = null,
    private val isInHistory: Boolean? = false,
) : BaseBindingDifferQuickAdapter<CountedLpEntry, ItemCountedLpBinding>() {

    override fun convert(helper: BaseBindingViewHolder<ItemCountedLpBinding>?, item: CountedLpEntry?) {
        helper?.apply {
            binding.apply {
                item?.let { itemData ->
                    lpTv.text = itemData.lpId
                    val isCountByLPQty = itemData.isCountByLPQty == true
                    lPCountTv.setVisibleOrGone(isCountByLPQty)
                    lPCountTv.text = if (isCountByLPQty) "${itemData.itemSpecs[0].itemProperties[0].qty} LPs" else ""
                    itemRecyclerView.layoutManager = LinearLayoutManager(mContext)
                    val countedItemAdapter = CountedItemAdapter(isInHistory = isInHistory)
                    countedItemAdapter.setOnItemChildClickListener { adapter, view, position -> onItemChildClickListener(itemData.lpId, adapter, view, position) }
                    countedItemAdapter.setDiffList(itemData.itemSpecs)
                    itemRecyclerView.adapter = countedItemAdapter
                    itemRecyclerView.setVisibleOrGone(!isCountByLPQty)
                    deleteIv.setOnClickListener { onDeleteListener.invoke(itemData) }
                }
            }
        }
    }

    private fun onItemChildClickListener(lpId: String?, adapter: BaseQuickAdapter<Any, BaseViewHolder>, view: View, position: Int) {
        val countedItemEntry = adapter.getItem(position) as CountedItemEntry
        if (!countedItemEntry.enableExpand()) {
            return
        }
        when (view.id) {
            R.id.expand_iv -> {
                countedItemEntry.isExpand = !(countedItemEntry.isExpand?: false)
                adapter.notifyDataSetChanged()
            }
            R.id.item_name_tv -> {
                countedItemEntry.isExpand = !(countedItemEntry.isExpand?: false)
                adapter.notifyDataSetChanged()
            }
            R.id.add_lot_no_btn -> {
                onAddLotNoListener?.invoke(lpId!!, countedItemEntry)
            }
        }
    }

    override fun areItemsTheSame(oldItem: CountedLpEntry, newItem: CountedLpEntry): Boolean {
        return oldItem.lpId == newItem.lpId && oldItem.itemSpecs == newItem.itemSpecs
    }

    override fun areContentsTheSame(oldItem: CountedLpEntry, newItem: CountedLpEntry): Boolean {
        return oldItem.lpId == newItem.lpId && oldItem.itemSpecs == newItem.itemSpecs
    }

}