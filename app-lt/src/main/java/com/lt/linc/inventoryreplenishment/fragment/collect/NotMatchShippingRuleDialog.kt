package com.lt.linc.inventoryreplenishment.fragment.collect

import android.annotation.SuppressLint
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.Gravity
import android.view.ViewGroup
import android.view.Window
import android.view.WindowManager
import com.lt.linc.R
import com.lt.linc.common.mvvm.kotlin.BaseBindingDialog
import com.lt.linc.databinding.DialogNotMatchShippingRuleBinding

/**
 * 不符合出货规则(FEFO)警告对话框
 */
class NotMatchShippingRuleDialog : BaseBindingDialog<DialogNotMatchShippingRuleBinding>() {

    private var itemName: String = ""
    private var suggestionLpId: String = ""
    private var locationName: String = ""
    private var onSuggestClickListener: (() -> Unit)? = null
    
    companion object {
        fun newInstance(itemName: String, suggestionLpId: String, locationName: String): NotMatchShippingRuleDialog {
            return NotMatchShippingRuleDialog().apply {
                this.itemName = itemName
                this.locationName = locationName
                this.suggestionLpId = suggestionLpId
            }
        }
    }

    @SuppressLint("SetTextI18n")
    override fun initView(savedInstanceState: Bundle?) {
        binding?.apply {
            warningMessageTv.text = getString(R.string.msg_not_match_shipping_rule, itemName)
            if (suggestionLpId.isNotEmpty() && locationName.isNotEmpty()) {
                // 设置建议LP
                suggestionTv.text = "${getString(R.string.text_suggest)}: $suggestionLpId ($locationName)"
            } else {
                suggestionTv.text = "${getString(R.string.text_suggest)}: ${getString(R.string.text_no_suggestion)}"
            }

            // 设置按钮点击事件
            suggestBtn.setOnClickListener {
                onSuggestClickListener?.invoke()
            }
            
            closeBtn.setOnClickListener {
                dismiss()
            }
        }
    }

    override fun onStart() {
        dialog.window?.apply {
            setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
            setGravity(Gravity.CENTER)
            val displayMetrics = context.resources.displayMetrics
            val screenWidth = displayMetrics.widthPixels
            val width = (screenWidth * 0.9).toInt()
            val params = attributes
            params.width = width
            params.height = WindowManager.LayoutParams.WRAP_CONTENT
            params.gravity = Gravity.CENTER
            attributes = params
        }
        super.onStart()
    }
    
    /**
     * 设置"Suggest"按钮点击监听器
     */
    fun setOnSuggestClickListener(listener: () -> Unit) {
        this.onSuggestClickListener = listener
    }

    @SuppressLint("SetTextI18n")
    fun updateSuggestion(suggestionLpId: String, suggestLocationName: String) {
        if (isDetached || !dialog.isShowing) return
        this.suggestionLpId = suggestionLpId
        this.locationName = suggestLocationName
        binding?.suggestionTv?.text = "${getString(R.string.text_suggest)}: $suggestionLpId ($suggestLocationName)"
    }
} 