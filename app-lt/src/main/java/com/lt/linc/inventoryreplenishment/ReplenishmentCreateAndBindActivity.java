package com.lt.linc.inventoryreplenishment;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.Menu;
import android.view.MenuItem;

import androidx.annotation.Nullable;

import com.linc.platform.common.help.FunctionHelpPresenterImpl;
import com.linc.platform.utils.ConfigurationMapUtil;
import com.lt.linc.R;
import com.lt.linc.inventoryreplenishment.fragment.EquipmentBindFragment;
import com.lt.linc.inventoryreplenishment.fragment.ReplenishmentCreateFragment;
import com.unis.autotrackdispatcher.annotation.Trace;
import com.unis.fragmentlauncher.SupportFragment;
import com.unis.fragmentlauncher.SupportFragmentActivity;

@Trace
public class ReplenishmentCreateAndBindActivity extends SupportFragmentActivity {

    private final FunctionHelpPresenterImpl functionHelpPresenter = new FunctionHelpPresenterImpl();

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initToolBar(getToolbar(), "");
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        menu.add(R.string.action_help);
        return super.onCreateOptionsMenu(menu);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (TextUtils.equals(item.getTitle(), getString(R.string.action_help))) {
            String helpKey = null;
            if (isFragmentVisible(ReplenishmentCreateFragment.class.getName())) {
                helpKey = ConfigurationMapUtil.PAGE_KEY_REPLENISHMENT_TASK_CREATE;
            } else if (isFragmentVisible(EquipmentBindFragment.class.getName())) {
                helpKey = ConfigurationMapUtil.PAGE_KEY_REPLENISHMENT_TASK_BIND_EQUIPMENT;
            }

            if (helpKey != null) {
                functionHelpPresenter.getAndOpenHelpPage(this, helpKey, null, getFacilityId());
            }
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    protected SupportFragment getRootFragment(Intent intent) {
        return ReplenishmentCreateFragment.getInstance();
    }

    @Override
    protected boolean isShowToolbar() {
        return true;
    }
}