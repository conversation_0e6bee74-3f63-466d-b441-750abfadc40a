package com.lt.linc.inventoryreplenishment.fragment.collect;

import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.CompoundButton;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.Switch;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.customer.widget.ItemDividerDecoration;
import com.customer.widget.QuickScanner;
import com.linc.platform.inventory.model.InventoryEntry;
import com.linc.platform.inventoryreplenishment.model.CollectTransmitData;
import com.linc.platform.inventoryreplenishment.model.ReplenishmentCollectModel;
import com.linc.platform.inventoryreplenishment.presenter.impl.ReplenishmentCollectSubmitPresenterImpl;
import com.linc.platform.inventoryreplenishment.view.ReplenishmentCollectFormView;
import com.linc.platform.utils.ToastUtil;
import com.lt.linc.R;
import com.lt.linc.common.Constant;
import com.lt.linc.inventoryreplenishment.adapter.SnAdapter;
import com.unis.autotrackdispatcher.annotation.Scroll;
import com.unis.fragmentlauncher.FragmentLaunchModel;
import com.unis.fragmentlauncher.SupportFragment;

import java.util.ArrayList;
import java.util.List;

public class ReplenishmentCollectSubmitFragment extends SupportFragment implements SnAdapter.OnRemoveListener,
        ReplenishmentCollectFormView, CompoundButton.OnCheckedChangeListener {

    private TextView mTvCollectFrom;
    private TextView mTvCollectItem;
    private Switch mSwitchEntire;
    private LinearLayout mLlEntireCollect;
    private EditText mEtQty;
    private TextView mTvUnit;
    private EditText mEtLotNumber;
    private LinearLayout mLlLotNumber;
    private QuickScanner mQsScannerSn;
    private LinearLayout mLlSn;
    private LinearLayout mLlEntire;
    private TextView mTvSnListLabel;
    private RecyclerView mRecyclerView;
    private TextView mTvItemQty;

    private SnAdapter mSnAdapter;
    private ReplenishmentCollectSubmitPresenterImpl mPresenter;
    private NotMatchShippingRuleDialog dialog;

    public static ReplenishmentCollectSubmitFragment getInstance(ReplenishmentCollectModel replenishmentCollectModel) {
        ReplenishmentCollectSubmitFragment fragment = new ReplenishmentCollectSubmitFragment();
        Bundle bundle = new Bundle();
        bundle.putSerializable(Constant.INTENT_REPLENISHMENT_COLLECT, replenishmentCollectModel);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int getLayoutId() {
        return R.layout.fragment_replenishment_collect_submit;
    }

    @Override
    protected void initView() {
        bindView();
        Bundle bundle = getArguments();
        ReplenishmentCollectModel replenishmentCollectModel = (ReplenishmentCollectModel) bundle.getSerializable(Constant.INTENT_REPLENISHMENT_COLLECT);
        mPresenter = new ReplenishmentCollectSubmitPresenterImpl(replenishmentCollectModel, this);
        initRecyclerView();
        bindDataToView();
        initEvent();
    }

    private void initEvent() {
        mQsScannerSn.setScanEvent((view,data) -> mPresenter.getSn(data));
        mSwitchEntire.setOnCheckedChangeListener(this);
    }

    @Scroll
    private void initRecyclerView() {
        LinearLayoutManager layoutManager = new LinearLayoutManager(getActivity());
        mRecyclerView.setLayoutManager(layoutManager);
        mRecyclerView.addItemDecoration(new ItemDividerDecoration(getActivity()));
        mSnAdapter = new SnAdapter();
        mSnAdapter.setOnRemoveListener(this);
        mRecyclerView.setAdapter(mSnAdapter);
    }

    private void bindDataToView() {
        ReplenishmentCollectModel replenishmentCollectModel = mPresenter.getReplenishmentCollectModel();
        mTvCollectFrom.setText(getTextNoNull(replenishmentCollectModel.getCollectItemFrom()));
        InventoryEntry inventoryEntry = replenishmentCollectModel.getSelectedInventory();
        mLlEntireCollect.setVisibility(replenishmentCollectModel.isFromPickLocation()? View.GONE : View.VISIBLE);
        if (inventoryEntry != null) {
            mTvCollectItem.setText(getTextNoNull(inventoryEntry.itemSpecName));
            mTvUnit.setText(getTextNoNull(inventoryEntry.unitName));
            mTvItemQty.setText(String.format("%1$s%2$s", (int) (inventoryEntry.qty / 1), inventoryEntry.unitName));
        }
        mLlLotNumber.setVisibility(replenishmentCollectModel.hasLotNo() ? View.VISIBLE : View.GONE);
        mLlSn.setVisibility(replenishmentCollectModel.hasSn()? View.VISIBLE : View.GONE);
        mTvSnListLabel.setVisibility(replenishmentCollectModel.hasSn()? View.VISIBLE : View.GONE);
    }

    private void onClick(View view) {
        switch (view.getId()) {
            case R.id.cancel_btn:
                cancel();
                break;
            case R.id.collect_btn:
                collectSubmit();
                break;
        }
    }

    private void collectSubmit() {
        boolean isEntire = mSwitchEntire.isChecked();
        // For entire submit, data don`t need ui provide, presenter for provide
        String qty = isEntire? "" : mEtQty.getText().toString().trim();
        String lotNo = isEntire? "" : mEtLotNumber.getText().toString().trim();
        List<String> snList = isEntire? null : mSnAdapter.getData();

        CollectTransmitData collectTransmitData = new CollectTransmitData();
        collectTransmitData.setEntire(isEntire);
        collectTransmitData.setQty(qty);
        collectTransmitData.setLotNo(lotNo);
        collectTransmitData.setSnList(snList);

        mPresenter.onCollectSubmit(collectTransmitData);
    }

    private void cancel() {
        showCollectFragment();
    }

    private void showCollectFragment() {
        ReplenishmentCollectModel replenishmentCollectModel = mPresenter.getReplenishmentCollectModel();
        startFragment(ReplenishmentCollectFromFragment.getInstance(replenishmentCollectModel.getReplenishmentTaskEntry()), FragmentLaunchModel.SINGLE_TASK);
    }

    private String getTextNoNull(String text) {
        return TextUtils.isEmpty(text) ? "" : text;
    }

    @Override
    public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
        if (isChecked) {
            hideInputSoft(getActivity());
        }
        int visibility = isChecked ? View.INVISIBLE : View.VISIBLE;
        mLlEntire.setVisibility(visibility);
        ReplenishmentCollectModel replenishmentCollectModel = mPresenter.getReplenishmentCollectModel();
        if (replenishmentCollectModel.hasSn()) {
            mTvSnListLabel.setVisibility(visibility);
            mRecyclerView.setVisibility(visibility);
        }
    }

    @Override
    public void onRemove(int position, String item) {
        mSnAdapter.getData().remove(position);
        mSnAdapter.notifyItemRemoved(position);
        List<String> dataList = mSnAdapter.getData();
        if (dataList != null && !dataList.isEmpty()) {
            mEtQty.setText(String.valueOf(dataList.size()));
        } else {
            mEtQty.setText("");
        }
    }

    @Override
    public void getSnSuccessful(String sn) {
        List<String> snList = mSnAdapter.getData();
        if (snList != null && !snList.isEmpty()) {
            if (!snList.contains(sn)) {
                snList.add(sn);
                mSnAdapter.setNewData(snList);
                mEtQty.setText(String.valueOf(snList.size()));
            } else {
                ToastUtil.showToast(getString(R.string.sn_already_exists));
            }
        } else {
            snList = new ArrayList<>();
            snList.add(sn);
            mSnAdapter.setNewData(snList);
            mEtQty.setText(String.valueOf(snList.size()));
        }
        mQsScannerSn.setInputFocus();
    }

    @Override
    public void onCollectSubmitSuccessful() {
        ToastUtil.showToast(getString(R.string.inventory_collect_success));
        showCollectFragment();
    }

    @Override
    public void onCollectSubmitNotMatchShippingRule(String suggestLocationName, String suggestLpId) {
        ReplenishmentCollectModel model = mPresenter.getReplenishmentCollectModel();
        InventoryEntry selectedInventory = model.getSelectedInventory();
        
        // 创建并显示不符合出货规则对话框
        dialog = NotMatchShippingRuleDialog.Companion.newInstance(
                selectedInventory.itemSpecName,
                suggestLpId,
                suggestLocationName
        );
        
        dialog.setOnSuggestClickListener(() ->{
            mPresenter.getSuggestCollectLP();
            return null;
        });
        
        dialog.show(getFragmentManager(), "NotMatchShippingRuleDialog");
    }

    @Override
    public void updateSuggestLocation(String suggestLocationName, String suggestLpId) {
        if (dialog == null) return;
        dialog.updateSuggestion(suggestLpId, suggestLocationName);
    }

    private void bindView() {
        mTvCollectFrom = findViewById(R.id.tv_collect_from);
        mTvCollectItem = findViewById(R.id.tv_collect_item);
        mSwitchEntire = findViewById(R.id.switch_entire_switch);
        mLlEntireCollect = findViewById(R.id.ll_entire_collect);
        mEtQty = findViewById(R.id.input_qty_et);
        mTvUnit = findViewById(R.id.tv_unit);
        mEtLotNumber = findViewById(R.id.input_lot_number_et);
        mLlLotNumber = findViewById(R.id.ll_lot_number);
        mQsScannerSn = findViewById(R.id.sn_scanner_qs);
        mLlSn = findViewById(R.id.ll_sn);
        mLlEntire = findViewById(R.id.ll_entire);
        mTvSnListLabel = findViewById(R.id.tv_sn_list_label);
        mRecyclerView = findViewById(R.id.sn_list_rcv);
        mTvItemQty = findViewById(R.id.tv_item_qty);
        findViewById(R.id.cancel_btn).setOnClickListener(this::onClick);
        findViewById(R.id.collect_btn).setOnClickListener(this::onClick);
    }
}