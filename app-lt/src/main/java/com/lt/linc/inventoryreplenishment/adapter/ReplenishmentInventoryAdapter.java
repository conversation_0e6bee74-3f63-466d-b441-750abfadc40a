package com.lt.linc.inventoryreplenishment.adapter;

import android.text.TextUtils;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.linc.platform.foundation.model.LpEntry;
import com.linc.platform.inventory.model.InventoryEntry;
import com.linc.platform.utils.CollectionUtil;
import com.lt.linc.R;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @Description:
 * @Author: Dennis
 * @CreateDate: 2020/12/31 13:48
 */
public class ReplenishmentInventoryAdapter extends BaseQuickAdapter<InventoryEntry, BaseViewHolder> {

    public ReplenishmentInventoryAdapter() {
        super(R.layout.item_inventory_collect);
    }

    @Override
    protected void convert(BaseViewHolder helper, InventoryEntry item) {
        if (item.isEntire) {
            helper.setText(R.id.item_spec_name_or_ilp_tv, TextUtils.isEmpty(item.lpId) ? "" : item.lpId);
            helper.setText(R.id.qty_tv, "");
            helper.setText(R.id.uom_tv, "");
        } else {
            helper.setText(R.id.item_spec_name_or_ilp_tv, buildItemName(item));
            helper.setText(R.id.qty_tv, String.valueOf(item.qty));
            helper.setText(R.id.uom_tv, TextUtils.isEmpty(item.unitName) ? "" : item.unitName);
        }
        helper.setVisible(R.id.title_tv, !TextUtils.isEmpty(item.titleName));
        helper.setText(R.id.title_tv, item.titleName);
    }

    private String buildItemName(InventoryEntry item) {
        StringBuffer sb = new StringBuffer();
        sb.append(TextUtils.isEmpty(item.itemSpecName) ? "" : item.itemSpecName);
        sb.append(" | ");
        sb.append(TextUtils.isEmpty(item.itemSpecDesc) ? "" : item.itemSpecDesc);
        return sb.toString();
    }

    /**
     * Merge inventories for the same lpId/itemspecName/unitName/title of inventory
     * and qty will be addition
     * only for a adapter to show data
     * @param inventoryEntries Before merge inventories
     * @param lpMap lpMap
     * @return After merge inventories
     */
    public List<InventoryEntry> mergeInventoryListWithEntire(List<InventoryEntry> inventoryEntries, Map<String, LpEntry> lpMap) {
        if (CollectionUtil.isNullOrEmpty(inventoryEntries)) {
            return inventoryEntries;
        }
        List<InventoryEntry> copyInventoryEntries = copyList(inventoryEntries);
        for (int i = 0; i < copyInventoryEntries.size(); i++) {
            InventoryEntry iInventory = copyInventoryEntries.get(i);
            String iKey = iInventory.lpId + iInventory.itemSpecName + iInventory.unitName + iInventory.titleId;
            if (isEntireForInventory(iInventory.lpId, lpMap)) {
                iKey = iInventory.lpId + iInventory.unitName + iInventory.titleId;
                iInventory.isEntire = true;
            }
            for (int j = i + 1; j < copyInventoryEntries.size(); j++) {
                InventoryEntry jInventory = copyInventoryEntries.get(j);
                String jKey = jInventory.lpId + jInventory.itemSpecName + jInventory.unitName + jInventory.titleId;
                if (isEntireForInventory(jInventory.lpId, lpMap)) {
                    jKey = jInventory.lpId + jInventory.unitName + jInventory.titleId;
                }
                if (iKey.equals(jKey)) {
                    iInventory.qty = iInventory.qty + jInventory.qty;
                    copyInventoryEntries.remove(j);
                    j--;
                }
            }
        }
        return copyInventoryEntries;
    }

    private boolean isEntireForInventory(String inventoryLpId, Map<String, LpEntry> lpMap) {
        if (TextUtils.isEmpty(inventoryLpId) || lpMap == null || lpMap.isEmpty()) {
            return false;
        }
        return lpMap.containsKey(inventoryLpId)
                && lpMap.get(inventoryLpId) != null
                && !TextUtils.isEmpty(lpMap.get(inventoryLpId).parentId);
    }

    private List<InventoryEntry> copyList(List<InventoryEntry> srcList) {
        List<InventoryEntry> list = new ArrayList<>();
        if (CollectionUtil.isNotNullOrEmpty(srcList)) {
            for (InventoryEntry item: srcList) {
                list.add(item.clone());
            }
        }
        return list;
    }
}
