package com.lt.linc.pick.batchorderpick

import com.linc.platform.baseapp.model.LocationEntry
import com.linc.platform.pick.model.PickTaskViewEntry
import com.linc.platform.pick.model.batchorderpick.BatchOrderPickIntentData
import com.linc.platform.pick.model.pickbyoreder.PickSuggestEntry
import com.linc.platform.pick.model.picktote.PickToteCartDetailEntry
import com.lt.linc.common.mvi.ReactiveDataState
import com.lt.linc.common.mvi.ReactiveUiState
import com.lt.linc.common.mvi.UiEvent
import com.lt.linc.pick.batchorderpick.auto_allocate.AutoAllocateStrategy
import com.lt.linc.pick.batchorderpick.auto_allocate.MultiOrdersBatchPickFragment
import com.lt.linc.pick.batchorderpick.manual_allocate.BatchOrderPickFragment
import java.io.Serializable

/**
 * <AUTHOR>
 * @date 2022/06
 */
interface BatchOrderPickBaseData {
    val taskEntry: PickTaskViewEntry
    val location: LocationEntry
    val fromLpId: String
    val itemSpecId: String
    val isScanLpToPick: Boolean
}

data class BatchOrderPickDataState(
    val taskEntry: PickTaskViewEntry? = null,
    val pickSuggestEntry: PickSuggestEntry? = null,
    val autoAllocateStrategy: AutoAllocateStrategy? = null,
    val pickToteCartDetailEntry: PickToteCartDetailEntry? = null
) : ReactiveDataState


data class BatchOrderPickUiState(
    val taskId: String?
) : ReactiveUiState


interface BatchOrderPickUiEvent {

    sealed interface Process : UiEvent {
        /*** @see [BatchOrderPickFragment] */
        data class ManuallyChoose(val data: BatchOrderPickIntentData) : Process

        /*** @see [MultiOrdersBatchPickFragment] */
        data class AutoAllocate(val strategy: AutoAllocateStrategy) : Process
    }

    sealed interface FinishEvent : UiEvent {
        /*** Finish with pick issues */
        data class WithPickIssue(val lpId: String, val itemSpecId: String, val desc: String) :
            Serializable, FinishEvent

        /*** Force finish */
        object ForceFinish : FinishEvent

        /*** Finish and mark that all orders of current selected item are picked */
        object AllOrdersPicked : FinishEvent
    }
}