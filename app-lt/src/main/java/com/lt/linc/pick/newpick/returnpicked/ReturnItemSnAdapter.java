package com.lt.linc.pick.newpick.returnpicked;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.lt.linc.R;

/**
 * Created by Gavin
 */

public class ReturnItemSnAdapter extends BaseQuickAdapter<String, BaseViewHolder> {
    public ReturnItemSnAdapter() {
        super(R.layout.item_pick_task_list_sn);
    }

    @Override
    protected void convert(BaseViewHolder holder, String item) {
        holder.setText(R.id.txt_num, holder.getLayoutPosition() + 1 + ".")
                .setText(R.id.txt_sn, item)
                .setVisible(R.id.txt_weight, false)
                .setVisible(R.id.lp_ly, false)
                .addOnClickListener(R.id.remove_btn);
    }
}
