package com.lt.linc.pick.returninventory;

import android.content.Context;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.inputmethod.EditorInfo;
import android.widget.EditText;
import android.widget.TextView;

import com.customer.widget.core.BaseLinearLayout;
import com.lt.linc.R;
import com.lt.linc.pick.OnEditInterface;

/**
 * <AUTHOR>
 */

class EditItem extends BaseLinearLayout {
    protected TextView unitTxt;
    protected TextView unitIdTxt;
    protected TextView baseQtyTxt;
    protected TextView baseUnitTxt;
    protected TextView totalUnitTxt;
    protected TextView totalTxt;
    protected EditText qtyEdit;
    protected TextView multiplicationSignTxt;
    protected TextView equalSignTxt;

    private OnEditInterface onEditInterface;

    public EditItem(Context context, OnEditInterface onEditInterface) {
        super(context);
        this.onEditInterface = onEditInterface;
    }

    @Override
    protected void init(Context context) {
        inflate(context, R.layout.item_order_group_edit, this);
        initView();
        qtyEdit.setInputType(EditorInfo.TYPE_CLASS_NUMBER);
    }

    @Override
    public int getTitleResId() {
        return 0;
    }

    private void initView() {
        unitTxt = (TextView) findViewById(R.id.unit_txt);
        unitIdTxt = (TextView) findViewById(R.id.unit_id_txt);
        baseQtyTxt = (TextView) findViewById(R.id.base_qty_txt);
        baseUnitTxt = (TextView) findViewById(R.id.base_unit_txt);
        totalUnitTxt = (TextView) findViewById(R.id.total_unit_txt);
        totalTxt = (TextView) findViewById(R.id.total_txt);
        qtyEdit = (EditText) findViewById(R.id.qty_edt);
        qtyEdit.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

            }

            @Override
            public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {
                double baseQty = Double.valueOf(baseQtyTxt.getText().toString());
                double total;
                if (!qtyEdit.getText().toString().isEmpty()) {
                    int qty = Integer.valueOf(qtyEdit.getText().toString());
                    total = qty * baseQty;
                    totalTxt.setText(total + "");
                    String unit = unitTxt.getText().toString();
                    String unitId = unitIdTxt.getText().toString();
                    onEditInterface.onEditAllocatedQty(qty, unit, unitId);
                }
            }

            @Override
            public void afterTextChanged(Editable editable) {

            }
        });
        multiplicationSignTxt = (TextView) findViewById(R.id.multiplication_sign_Txt);
        equalSignTxt = (TextView) findViewById(R.id.equal_sign_txt);
    }
}
