package com.lt.linc.pick.v1.component.impl;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.SimpleItemAnimator;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;

import com.customer.widget.core.LincBaseActivity;
import com.linc.platform.pick.v1.model.LPSuggestEntry;
import com.linc.platform.pick.v1.model.TaskSuggestEntry;
import com.linc.platform.pick.v1.presenter.PickWorkPresenter;
import com.linc.platform.utils.CollectionUtil;
import com.lt.linc.R;
import com.lt.linc.pick.v1.component.OrderComponent;
import com.lt.linc.pick.v1.work.PickOrderAdapter;

/**
 * Created by dexter on 2018/12/24.
 */

public class OrderComponentImpl implements OrderComponent {

    private LincBaseActivity activity;
    private PickWorkPresenter pickWorkPresenter;
    private PickOrderAdapter orderAdapter;

    private RecyclerView orderRlv;
    private LinearLayout orderLayout;
    private RelativeLayout showOrderBtn;

    @Override
    public void init(PickWorkPresenter pickWorkPresenter, LincBaseActivity activity) {
        this.activity = activity;
        this.pickWorkPresenter = pickWorkPresenter;

        orderRlv = activity.findView(R.id.recycler_order);
        orderLayout = activity.findView(R.id.layout_order);
        showOrderBtn = activity.findView(R.id.layout_btn_show_pick_order);
        initShowOrderBtn();
        initOrderRecyclerView();
    }

    private void initShowOrderBtn() {
        showOrderBtn.setOnClickListener(view -> {
            if (orderRlv.getVisibility() == View.VISIBLE) {
                orderRlv.setVisibility(View.GONE);
            } else {
                orderRlv.setVisibility(View.VISIBLE);
            }
        });
    }

    private void initOrderRecyclerView() {
        orderAdapter = new PickOrderAdapter();
        LinearLayoutManager lm = new LinearLayoutManager(activity);
        lm.setOrientation(LinearLayout.HORIZONTAL);
        orderRlv.setLayoutManager(lm);
        orderRlv.setAdapter(orderAdapter);
        orderRlv.setNestedScrollingEnabled(false);
        ((SimpleItemAnimator) orderRlv.getItemAnimator()).setSupportsChangeAnimations(false);
        orderAdapter.setOnItemClickListener((adapter, view, position)
                -> orderItemClick(position));
    }

    private void orderItemClick(int position) {
        orderAdapter.setSelected(position);
        pickWorkPresenter.changeOrder(orderAdapter.getSelectedOrderId());
    }

    @Override
    public void onGetTaskSuggestSuccess(TaskSuggestEntry taskSuggest) {
        orderAdapter.setItems(taskSuggest.orderItemRemainings);
    }

    @Override
    public void onGetLPSuggestSuccess(LPSuggestEntry lpSuggest, boolean isClosePickSuggest) {
        if (CollectionUtil.isNullOrEmpty(orderAdapter.getData())) {
            orderAdapter.setItems(lpSuggest.orderItemRemainings);
        }
        if (isClosePickSuggest) {
            orderLayout.setVisibility(View.VISIBLE);
            showOrderBtn.setVisibility(View.VISIBLE);
        }
    }

    @Override
    public void showNoSuggestProcessView() {
        showOrderBtn.setVisibility(View.GONE);
        orderLayout.setVisibility(View.GONE);
    }
}
