package com.lt.linc.pick.allocate.workviews;

import com.customer.widget.core.LincBaseActivity;
import com.linc.platform.pick.presenter.AllocateWorkPresenter;
import com.linc.platform.pick.view.ActPartView;
import com.lt.linc.R;
import com.lt.linc.pick.newpick.work.ScannerLayout;

/**
 * <AUTHOR>
 */

class SNScannerView implements ActPartView<LincBaseActivity, AllocateWorkPresenter> {
    private ScannerLayout snScanner;

    private LincBaseActivity activity;
    private AllocateWorkPresenter presenter;

    @Override
    public void init(LincBaseActivity activity, AllocateWorkPresenter presenter) {
        this.activity = activity;
        this.presenter = presenter;

        bindView();
        initSNScannerView();
    }

    private void initSNScannerView() {
        snScanner.requestEdtFocus();
        snScanner.setListener(new ScannerLayout.ScannerListener() {
            @Override
            public void onEnterDone(String data) {
                onScanDone(data);
            }

            @Override
            public void onScanDone(String data) {
                presenter.handleScannedSN(data);
            }

            @Override
            public void onEmptyDone() {
                activity.speakAndToast(R.string.text_scan_is_empty);
            }
        }).setHintText(R.string.text_scan_sn);
    }

    private void bindView() {
        snScanner = activity.findView(R.id.scanner_layout_sn);
    }

    void onSNScannerSuccess() {
        snScanner.clearText();
    }

    void onScanSNFailed(String error) {
        snScanner.resetEdt();
        activity.speakAndToast(error);
    }

    void onSubmitSuccess() {
        snScanner.requestEdtFocus();
    }
}
