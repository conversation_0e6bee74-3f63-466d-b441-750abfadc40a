package com.lt.linc.pick.batchorderpick.auto_allocate

import android.text.TextUtils
import com.linc.platform.baseapp.model.FacilityEquipmentSearch
import com.linc.platform.baseapp.model.FacilityEquipmentType
import com.linc.platform.baseapp.model.LocationEntry
import com.linc.platform.common.lp.LpTypeEntry
import com.linc.platform.cyclecount.model.OnScreenCountResponseEntry
import com.linc.platform.foundation.model.ItemSpecWithAkaEntry
import com.linc.platform.http.ErrorCode
import com.linc.platform.http.ErrorResponse
import com.linc.platform.inventory.model.LpBatchCreateEntry
import com.linc.platform.inventory.model.LpDetail
import com.linc.platform.pick.BatchOrderPickHelper
import com.linc.platform.pick.model.OrderSoIdEntry
import com.linc.platform.pick.model.PickResultUpdateEntry
import com.linc.platform.pick.model.PickTaskViewEntry
import com.linc.platform.pick.model.batchprint.PickSoIdSearchEntry
import com.linc.platform.pick.model.picktote.PickMode
import com.linc.platform.pick.model.picktote.PickModeData
import com.linc.platform.pick.model.picktote.PickToteCartDetailEntry
import com.linc.platform.pick.model.picktote.PickToteCartWrap
import com.linc.platform.pick.reportissue.ReportPartialPalletIssueHandle
import com.linc.platform.print.commonprintlp.PrintData
import com.linc.platform.print.commonprintlp.PrintData.JobData.ZPL
import com.linc.platform.print.commonprintlp.PrintJobCreate
import com.linc.platform.print.commonprintlp.PrintView
import com.linc.platform.print.commonprintlp.zplJobType
import com.linc.platform.print.model.LabelSizeEntry
import com.linc.platform.print.model.PrintJobType
import com.linc.platform.print.model.PrinterEntry
import com.linc.platform.print.model.SoIdLabelBatchPrintRequestEntry
import com.linc.platform.utils.CollectionUtil
import com.linc.platform.utils.EquipmentUtil
import com.linc.platform.utils.LPUtil
import com.linc.platform.utils.PrintUtil
import com.lt.linc.R
import com.lt.linc.common.Tuple
import com.lt.linc.common.extensions.safeCount
import com.lt.linc.common.extensions.toException
import com.lt.linc.common.mvi.*
import com.lt.linc.common.mvvm.kotlin.InputFocusType
import com.lt.linc.common.mvvm.kotlin.extensions.*
import com.lt.linc.pick.LastPickLocationUtil
import com.lt.linc.pick.batchorderpick.BatchOrderPickViewModel
import com.lt.linc.pick.batchorderpick.auto_allocate.MultiOrdersBatchPickUiEvent.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.firstOrNull
import java.util.regex.Pattern

class MultiOrdersBatchPickViewModel(
    private val activityViewModel: BatchOrderPickViewModel,
    private val allocateStrategy: AutoAllocateStrategy,
    private val pickModeData: PickModeData? = null,
    private val pickMode: PickMode? = pickModeData?.pickMode,
    private val pickToteCartWrap: PickToteCartWrap = PickToteCartWrap(),
    initialState: MultiOrdersBatchPickDataState = MultiOrdersBatchPickDataState(),
    initialUiState: MultiOrdersBatchPickUiState = MultiOrdersBatchPickUiState(),
    private val repository: MultiOrdersBatchPickRepo = MultiOrdersBatchPickRepo(),
) : ReactiveViewModel<MultiOrdersBatchPickDataState, MultiOrdersBatchPickUiState>(
    initialState,
    initialUiState
) {

    companion object {
        private const val INVALID_TOTE_ERROR_CODE = "44036"
    }

    val activityParam = activityViewModel.activityParam
    val task = activityViewModel.dataState.taskEntry?: PickTaskViewEntry()

    private val printerDelegate = object : PrintView {

        private val basePrintData = PrintData.basic(repository.idmUserId, repository.facilityName, LabelSizeEntry.TWO_ONE)
        var printData: PrintData = basePrintData
            set(value) {
                value.apply {
                    idmUserId = basePrintData.idmUserId
                    facilityName = basePrintData.facilityName
                    paperSize = basePrintData.paperSize
                }
                field = value
            }

        val printedNewLps = mutableSetOf<String>()
        val printedSOIDs = mutableSetOf<String>()

        override fun showProgress(show: Boolean) = showLoading(show)

        override fun onPrinterNotSelect() =
            fireEvent { Print.PrinterNotSelected }

        override fun onPrintSuccess(data: PrintData, printerEntry: PrinterEntry) {
            when (data.zplJobType) {
                PrintJobType.LP_LABEL_JOB -> {
                    data.extra.lpIds?.let { printedNewLps.addAll(it) }
                }
                PrintJobType.SOID_LABEL_JOB -> {
                    printData.extra.soIds?.let { printedSOIDs.addAll(it) }
                }
                else -> {}
            }
        }

        override fun onPrintFailed(response: ErrorResponse, printerEntry: PrinterEntry?) {
            val event = when (printData.zplJobType) {
                PrintJobType.LP_LABEL_JOB -> Print.LpPrintFailed(
                    printData,
                    taskEntry.getPickToLPType(),
                    dataState.lpCompose.bindingToOrderId
                )
                PrintJobType.SOID_LABEL_JOB -> Print.SoIdPrintFailed(
                    response.errorMessage
                )
                else -> return
            }
            fireEvent { event }
        }

        fun hasPrintJob() = printData != basePrintData
    }

    private val printUtil = PrintUtil.newInstance(printerDelegate)

    init {
        addStateInterceptors()
        mapDataToUi()
        subscribeToState()
        bindFocus()
    }

    init {
        loadData()
    }

    private fun addStateInterceptors() {
        interceptDataState {
            // Orders
            interceptOnce(MultiOrdersBatchPickDataState::ordersCompose) {
                val suggestOrders = allocateStrategy.ordersWithQty
                it.copy(
                    suggestedOrdersWithQty = suggestOrders,
                    dispatchedOrdersWithQty = suggestOrders
                )
            }
            // Location
            interceptOnce(MultiOrdersBatchPickDataState::locationCompose) {
                it.copy(needValidate = shouldValidateLocation())
            }
            // Item
            interceptOnce(MultiOrdersBatchPickDataState::itemCompose) {
                it.copy(
                    needValidate = shouldValidateItem(),
                    autoIncreasingQtyAfterScanItem = shouldAutoIncreasingQtyAfterScanItem()
                )
            }
            // Qty
            interceptOnce(MultiOrdersBatchPickDataState::qtyCompose) {
                val suggestQty = allocateStrategy.suggestQtySum
                it.copy(
                    suggested = suggestQty,
                    hint = when {
                        shouldAutoIncreasingQtyAfterScanItem() -> 0
                        shouldRestrictInputQty() -> 1
                        else -> suggestQty
                    }
                )
            }
            // SoId
            interceptOnce(MultiOrdersBatchPickDataState::soIdCompose) {
                it.copy(
                    needCollect = shouldCollectSOID(ordersCompose.suggestedOrdersWithQty),
                    needAutoPrint = shouldAutoPrintSOID(ordersCompose.suggestedOrdersWithQty)
                )
            }
            // Lp
            interceptOnce(MultiOrdersBatchPickDataState::lpCompose) {
                it.copy(
                    bindingToOrderId = allocateStrategy.firstOrderId,
                    allowPrintMultiLpForOrder = allowPrintMultipleLPsForOrder()
                )
            }
            // Customer config
            interceptOnce(MultiOrdersBatchPickDataState::configCompose) {
                it.copy(
                    allowManualEntry = allowManualEntry(),
                    enableAutoSubmit = allowPickAutoSubmit()
                )
            }
        }
        interceptUiState {
            // Input location restriction
            interceptOnce(MultiOrdersBatchPickUiState::locationUiCompose) {
                it.copy(shouldRestrictInput = shouldRestrictInputLocation())
            }
            // Input item
            interceptOnce(MultiOrdersBatchPickUiState::itemUiCompose) {
                it.copy(
                    showValidation = shouldShowItemScanner(),
                    shouldRestrictInput = shouldRestrictInputItem()
                )
            }
            // Input qty restriction
            interceptOnce(MultiOrdersBatchPickUiState::qtyUiCompose) {
                it.copy(shouldRestrictInput = shouldRestrictInputQty())
            }
            // Input soId restriction
            interceptOnce(MultiOrdersBatchPickUiState::soIdUiCompose) {
                it.copy(shouldRestrictInput = shouldRestrictInputSOID())
            }
            // Input lp restriction
            interceptOnce(MultiOrdersBatchPickUiState::lpUiCompose) {
                it.copy(
                    shouldRestrictInput = shouldRestrictInputToLp(),
                    showNewLpButton = isAllowPrintNewLP()
                )
            }
        }
    }

    private fun mapDataToUi() {
        // Update display orders
        mapDataToUi(
            MultiOrdersBatchPickDataState::ordersCompose,
            MultiOrdersBatchPickUiState::ordersUiCompose
        ) {
            copy(
                suggestedOrders = it.dispatchedOrdersWithQty.map { o -> o.orderEntry },
                consolidation = it.consolidation
            )
        }
        // Update display location
        mapDataToUi(
            MultiOrdersBatchPickDataState::locationCompose,
            MultiOrdersBatchPickUiState::locationUiCompose
        ) {
            copy(
                showValidation = it.needValidate,
                validatedLocation = it.validatedLocation
            )
        }
        // Update display item
        mapDataToUi(
            MultiOrdersBatchPickDataState::itemCompose,
            MultiOrdersBatchPickUiState::itemUiCompose
        ) {
            copy(
                item = it.item,
                hasValidated = it.hasValidated
            )
        }
        // Update display qty
        mapDataToUi(
            MultiOrdersBatchPickDataState::qtyCompose,
            MultiOrdersBatchPickUiState::qtyUiCompose
        ) {
            val (strategy, suggestion) = allocateStrategy to dataState.ordersCompose.firstSuggestion!!
            val suggestedDisplay = "${it.suggested} ${suggestion.lpUnitName}" +
                    if (!suggestion.qtyDesc.isNullOrEmpty()) "(${suggestion.qtyDesc})"
                    else ""
            val pickedDisplay =
                "${strategy.pickedBaseQtySum} / ${strategy.requiredBaseQtySum} ${suggestion.baseUnitName}"
            copy(
                suggestedDisplay = suggestedDisplay,
                pickedDisplay = pickedDisplay,
                inputted = it.inputted,
                uom = suggestion.lpUnitName,
                hint = it.hint
            )
        }
        // Update display soId list
        mapDataToUi(
            MultiOrdersBatchPickDataState::soIdCompose,
            MultiOrdersBatchPickUiState::soIdUiCompose
        ) {
            val scanned =
                it.dispatched.map { wrapper -> wrapper.copy(hasScanned = wrapper in it.scanned) }
            copy(show = it.needCollect, displayList = scanned)
        }
        // Update display Lp
        mapDataToUi(
            MultiOrdersBatchPickDataState::lpCompose,
            MultiOrdersBatchPickUiState::lpUiCompose
        ) {
            val fromLp = fromLpId.run { if (isNullOrEmpty() || LPUtil.isHLP(this)) "" else this }
            val qtyInLp =
                "${allocateStrategy.remainQtyInLp} ${dataState.ordersCompose.firstSuggestion!!.lpUnitName}"
            copy(
                fromLp = fromLp,
                displayQtyInFromLp = qtyInLp,
                lpsOfConsolidation = it.bindingLpsOfConsolidation,
                bindingLpsOfOrders = it.bindingLpsOfOrders,
                scannedLp = it.scannedLpId
            )
        }
    }

    private fun subscribeToState() {
        // Auto dispatch orders and soId after input qty, then print soId if needed
        subscribe(MultiOrdersBatchPickDataState::qtyCompose) {
            if (it.inputted == null) return@subscribe
            autoDispatchOrdersByQty()
            autoDispatchSOIDByOrders()
            autoPrintSOIDIfNeeded()
        }
    }

    private fun bindFocus() = bindInputFocus {
        bind(
            InputFocusType.FromLocation,
            enable = { it.locationCompose.needValidate },
            onFirstFocus = { speak(R.string.msg_please_scan_location) },
            shouldFocus = { !it.locationCompose.hasValidated }
        )
        bind(
            InputFocusType.Item,
            enable = { it.itemCompose.run { needValidate || autoIncreasingQtyAfterScanItem } },
            onFirstFocus = { speak(R.string.text_verify_pick_item) },
            shouldFocus = {
                it.run {
                    when {
                        itemCompose.needValidate -> !itemCompose.hasValidated
                        itemCompose.autoIncreasingQtyAfterScanItem -> qtyCompose.inputted != qtyCompose.suggested
                        else -> false
                    }
                }
            }
        )
        bind(
            InputFocusType.Qty,
            enable = { !uiState.qtyUiCompose.shouldRestrictInput },
            onFirstFocus = { speak(R.string.message_confirm_quantity) },
            shouldFocus = { it.qtyCompose.inputted == null }
        )
        bind(
            InputFocusType.SoId,
            enable = { it.soIdCompose.needCollect },
            onFirstFocus = { speak(R.string.message_scan_soid) },
            shouldFocus = { !it.soIdCompose.allDispatchScanned }
        )
        bind(
            InputFocusType.ToLp,
            onFirstFocus = { speak(R.string.text_scan_to_lp) },
            shouldFocus = { it.lpCompose.scannedLpId == null }
        )
    }

    @Suppress("UNCHECKED_CAST")
    fun loadData() {
        val (strategy, suggestedOrders, taskId, itemSpecId) = Tuple(
            allocateStrategy,
            dataState.ordersCompose.suggestedOrdersWithQty.map { it.orderEntry },
            taskId,
            itemSpecId
        )
        // Load toteCart
        if (pickMode == PickMode.PICK_TO_TOTE) {
            pickToteCartWrap.setOrderIds(strategy.orderIds)
            val toteCartDetailEntry: PickToteCartDetailEntry? = pickModeData?.toteCartDetail
            pickToteCartWrap.setPickToteCartDetailEntry(toteCartDetailEntry)
        }

        // Load Item and soId
        launch {
            val requestsArray = buildList {
                add(repository.getItemWithAka(itemSpecId))
                if (dataState.soIdCompose.needCollect) {
                    val soIdSearch = PickSoIdSearchEntry(
                        taskId, suggestedOrders.map { it.id },
                        listOf(itemSpecId)
                    )
                    add(repository.getSoID(soIdSearch))
                }
            }.toTypedArray()
            val results = requestAllAwait(requestsArray)
            if (!results.allSuccess) {
                val reload = awaitEvent { AskRetryLoadData } ?: false
                if (reload) {
                    loadData()
                } else {
                    activityViewModel.forceFinish()
                }
                return@launch
            }
            val item = results.first().getOrNull() as? ItemSpecWithAkaEntry?
            val soId = results.getOrNull(1)?.getOrNull() as? List<OrderSoIdEntry>?

            setDataStateAwait {
                copy(
                    itemCompose = itemCompose.copy(item = item),
                    soIdCompose = soIdCompose.copy(all = soId)
                )
            }
            // Init soId
            autoDispatchSOIDByOrders()

            // After orders and item loaded, do setup
            setupAfterDataLoaded()
        }
        // Load lps if picked by consolidation
        if (strategy is AutoAllocateStrategy.Combine.Consolidation) {
            request(
                repository.getBindingLpsOfConsolidation(taskId, strategy.consolidation),
                showLoading = false
            ) { lpIds ->
                val lpNames = strategy.appendToteName(lpIds)?.map { it.second }
                setDataState { copy(lpCompose = lpCompose.copy(bindingLpsOfConsolidation = lpNames)) }
            }
        }
        // Get stage location suggestions
        request(repository.getSuggestStageLocations(taskId), showLoading = false) {
            setUiState { copy(locationUiCompose = locationUiCompose.copy(stageLocationSuggestions = it?.locations)) }
        }
        // Get binding lp
        val orderIds = allocateStrategy.orderIdsOfSameStrategy
        request(repository.getBindingLpsOfOrders(orderIds)) {
            val lpIds = it?.flatMap { lp -> lp.lpIds }?.distinct()
            val bindingLps = allocateStrategy.appendToteName(lpIds)
            if (pickMode == PickMode.PICK_TO_TOTE && bindingLps.isNullOrEmpty()) {
                val toteEntry =
                    pickToteCartWrap.getPickToToteEquipment(dataState.lpCompose.bindingToOrderId)
                toteEntry?.apply {
                    setDataState {
                        copy(
                            lpCompose = lpCompose.copy(
                                bindingLpsOfOrders = listOf(
                                    Tuple(toteEntry.hlpId, toteEntry.barcode)
                                )
                            )
                        )
                    }
                }
                return@request
            }
            setDataState { copy(lpCompose = lpCompose.copy(bindingLpsOfOrders = bindingLps)) }
        }
    }

    private fun setupAfterDataLoaded() {
        // Check Printer if need to print soId
        if (dataState.soIdCompose.needAutoPrint &&
            !PrintUtil.hasPrinter(printerDelegate.printData)
        ) {
            printerDelegate.onPrinterNotSelect()
            return
        }
        autoInputQtyIfNeeded()
        notifyFirstFocus()
    }

    private fun autoInputQtyIfNeeded() = withDataState {
        if (qtyCompose.inputted != null) return@withDataState
        if (locationCompose.needValidate && !locationCompose.hasValidated) return@withDataState
        if (itemCompose.needValidate && !itemCompose.hasValidated) return@withDataState
        if (itemCompose.autoIncreasingQtyAfterScanItem) return@withDataState
        if (qtyCompose.suggested == 1) {
            // Automatically input 1 qty
            setDataState { copy(qtyCompose = qtyCompose.copy(inputted = 1)) }
        }
    }

    private suspend fun autoDispatchOrdersByQty() {
        val (inputtedQty, suggestions, allOrdersOfTask) = Triple(
            dataState.qtyCompose.inputted ?: return,
            dataState.ordersCompose.suggestedOrdersWithQty.map { o -> o.suggestion },
            taskEntry.orders
        )
        val orders = BatchOrderPickHelper.filterPickingOrdersByQty(
            suggestions,
            allOrdersOfTask,
            inputtedQty
        )
        setDataStateAwait { copy(ordersCompose = ordersCompose.copy(dispatchedOrdersWithQty = orders)) }
    }

    private suspend fun autoDispatchSOIDByOrders() {
        if (!dataState.soIdCompose.needCollect) return

        val allSoId = dataState.soIdCompose.all
        if (allSoId.isNullOrEmpty()) return

        val list = mutableListOf<SoIdWrapper>()
        for ((order, _, qty) in dataState.ordersCompose.dispatchedOrdersWithQty) {
            val soIdOfOrder = allSoId.filter { it.orderId == order.orderId }
                .flatMap { it.soIds }
                .mapNotNull {
                    SoIdWrapper(order.orderId, soId = it, hasScanned = false)
                }
            val addSoId = if (soIdOfOrder.size == qty) {
                soIdOfOrder
            } else {
                soIdOfOrder.take(qty)
            }
            list.addAll(addSoId)
        }

        setDataStateAwait {
            val dispatched = list.toList()
            val scanned = soIdCompose.scanned.filter { it in dispatched }
            copy(
                soIdCompose = soIdCompose.copy(dispatched = dispatched, scanned = scanned)
            )
        }
    }

    private fun autoPrintSOIDIfNeeded() {
        if (dataState.qtyCompose.inputted == null) return
        if (!dataState.soIdCompose.needAutoPrint) return
        if (printerDelegate.printedSOIDs.isNotEmpty()) return
        // autoIncreasingQtyAfterScanItem is not compatible with SoId
        if (dataState.itemCompose.autoIncreasingQtyAfterScanItem) return
        printAllSoId()
    }

    private fun printAllSoId() {
        val soId = dataState.soIdCompose.dispatched
        if (soId.isEmpty()) return
        val createEntries = soId.groupBy { it.orderId }.map { (orderId, soIds) ->
            SoIdLabelBatchPrintRequestEntry().apply {
                this.pickTaskId = <EMAIL>
                this.qty = 1.0
                this.orderId = orderId
                this.itemSpecId = <EMAIL>
                this.soIds = soIds.map { it.soId }
            }
        }
        request(repository.batchCreateSoIdPrintJob(createEntries)) {
            printerDelegate.printData = PrintData().apply {
                val jobCreate = PrintJobCreate.SOIDLabel(
                    itemSpecId = <EMAIL>,
                    pickTaskId = <EMAIL>
                )
                this.jobData = ZPL(jobIds = it?.jobIds, jobCreate)
                this.extra.soIds = createEntries.flatMap { it.soIds }
            }
            doPrint()
        }
    }

    fun printSingleSoId(soId: SoIdWrapper) {
        val createEntry = SoIdLabelBatchPrintRequestEntry().apply {
            this.pickTaskId = <EMAIL>
            this.qty = 1.0
            this.orderId = soId.orderId
            this.itemSpecId = <EMAIL>
            this.soIds = listOf(soId.soId)
        }
        request(repository.createSoIdPrintJob(createEntry)) {
            printerDelegate.printData = PrintData().apply {
                this.jobData = ZPL(jobIds = it?.jobIds, jobType = PrintJobType.SOID_LABEL_JOB)
                this.extra.soIds = listOf(soId.soId)
            }
            doPrint()
        }
    }

    fun updatePrinterSetting() {
        when (printerDelegate.printData.zplJobType) {
            PrintJobType.LP_LABEL_JOB, PrintJobType.SOID_LABEL_JOB -> doPrint()
            else -> setupAfterDataLoaded()
        }
    }

    fun createAndPrintNewLp(onShowVerifyPasswordDialog: (List<String>) -> Flow<String?>) {
        val lpType = LpTypeEntry.getLPTypeByName(taskEntry.getPickToLPType())
        val bindingLPs = dataState.lpCompose.bindingLpsOfOrders
        launch {
            // Check need verify password
            if (!bindingLPs.isNullOrEmpty() &&
                dataState.lpCompose.allowPrintMultiLpForOrder
                && dataState.ordersCompose.isAllDispatchNoneSingleQty()
            ) {
                val lpNames = bindingLPs.map { it.second }
                val password = onShowVerifyPasswordDialog(lpNames).firstOrNull()
                when {
                    password.isNullOrEmpty() -> return@launch
                    password != BatchOrderPickHelper.NEW_LP_VERIFY_PASSWORD -> {
                        showToast(R.string.error_incorrect_password)
                        return@launch
                    }
                }
            }
            val bindingOrderId = dataState.lpCompose.bindingToOrderId
            // Print new lp
            val createEntry = LpBatchCreateEntry().apply {
                type = lpType
                count = 1
                orderId = bindingOrderId
            }
            request(repository.createLp(createEntry)) {
                printerDelegate.printData = PrintData().apply {
                    val jobCreate = PrintJobCreate.LpLabel(it?.lpIds)
                    jobCreate.orderId = bindingOrderId
                    jobData = ZPL(null, jobCreate)
                }
                doPrint()
            }
        }
    }

    private fun doPrint() {
        if (!printerDelegate.hasPrintJob()) return
        printUtil.print(printerDelegate.printData)
    }

    fun reprintLastJobs() = doPrint()

    fun scanLocation(
        name: String,
        onMultiLocationFounded: (List<LocationEntry>) -> Flow<LocationEntry?>,
    ) {
        launch {
            val validate = if (name == fromLocation.name) {
                true
            } else {
                val locations = requestAwait(repository.searchLocationByName(name)).getOrNull()
                val location = when (locations.safeCount()) {
                    0 -> {
                        showToast(R.string.location_not_found)
                        return@launch
                    }
                    1 -> locations!!.first()
                    else -> onMultiLocationFounded(locations!!).firstOrNull()
                }
                location?.id == fromLocation.id
            }
            if (!validate) {
                speakAndToast(R.string.msg_location_inconsistency)
                return@launch
            }
            setDataState {
                copy(locationCompose = locationCompose.copy(validatedLocation = fromLocation))
            }
            autoInputQtyIfNeeded()
        }
    }

    fun scanItem(item: String) {
        runCatching { validateItem(item) }.onFailure {
            speakAndToast(it.message!!)
            return
        }

        if (dataState.itemCompose.autoIncreasingQtyAfterScanItem) {
            // Scan item to add qty
            val (suggest, input) = dataState.qtyCompose.run { suggested to (inputted ?: 0) }
            val next = input + 1
            when {
                next > suggest -> { // Over pick
                    speakAndToast(R.string.error_over_picked)
                    return
                }
                next == suggest -> { // Complete
                    speak(R.string.complete)
                }
                else -> { // Not complete
                    speak("$next")
                }
            }
            setDataState { copy(qtyCompose = qtyCompose.copy(inputted = next)) }
            return
        }

        setDataState { copy(itemCompose = itemCompose.copy(hasValidated = true)) }
        autoInputQtyIfNeeded()
    }

    @Throws
    private fun validateItem(keyword: String) {
        val itemSpec = dataState.itemCompose.item?.itemSpec ?: return
        val pass = when (keyword) {
            // ItemSpec
            itemSpec.name, itemSpec.upcCode, itemSpec.upcCodeCase, itemSpec.eanCode, itemSpec.abbreviation -> true
            // Aka
            else -> dataState.itemCompose.item?.akas?.any { it.value == keyword } ?: false
        }
        if (!pass) {
            throw getString(R.string.text_verify_pick_item_fail).toException
        }
    }

    fun inputQty(qtyStr: String?) {
        val qty = qtyStr?.toIntOrNull()
        runCatching { validateQty(qty) }.onFailure {
            speakAndToast(it.message!!)
            return
        }
        setDataState { copy(qtyCompose = this.qtyCompose.copy(inputted = qty)) }
    }

    @Throws
    private fun validateQty(qty: Int?) {
        if (qty == null) {
            throw getString(R.string.msg_invalid_qty_format).toException
        }
        if (qty > dataState.qtyCompose.suggested) {
            throw getString(R.string.label_qty_more_than_suggest_qty).toException
        }
    }

    fun scanSoId(soId: String) {
        val wrapper = runCatching { validateSoId(soId) }.onFailure {
            speakAndToast(it.message!!)
            return
        }
        setDataState {
            copy(soIdCompose = soIdCompose.run { copy(scanned = scanned + wrapper.getOrNull()!!) })
        }
    }

    @Throws
    private fun validateSoId(soId: String): SoIdWrapper {
        val (dispatchedSoIds, scannedSoIds) = dataState.soIdCompose.let { it.dispatched to it.scanned }
        val wrapper = dispatchedSoIds.firstOrNull { it.soId == soId }
            ?: throw getFormattedString(R.string.msg_invalid_soid, soId).toException

        if (wrapper in scannedSoIds) {
            throw getString(R.string.label_so_id_duplicate).toException
        }
        return wrapper
    }

    fun scanToLp(input: String) {
        launch {
            val (isTote, lpId) = if (LPUtil.isLP(input)) {
                false to input
            } else {
                if (pickMode == PickMode.PICK_TO_TOTE) {
                    val result = requestAwait(
                        repository.getEquipment(FacilityEquipmentSearch(input)),
                        error = null
                    )
                    when {
                        result.isFailure && result.errorResponse != null -> {
                            showToast(result.errorResponse!!.errorMessage)
                            return@launch
                        }
                        result.isSuccess -> {
                            val equipments = result.getOrNull()
                            if (equipments.isNullOrEmpty()) {
                                false to null
                            }
                            if (equipments!!.any { it.type == FacilityEquipmentType.TOTE }) {
                                runCatching { validateTote(input) }.onFailure {
                                    showToast(it.message!!)
                                    return@launch
                                }
                                true to equipments!!.first().hlpId
                            } else {
                                showToast(R.string.msg_please_scan_tote)
                                false to null
                            }
                        }
                        else -> false to null
                    }
                } else {
                    val result = requestAwait(
                        repository.getEquipmentHlp(input, taskId),
                        error = null
                    )
                    val errorResponse = result.errorResponse
                    when {
                        result.isFailure && errorResponse!!.code == INVALID_TOTE_ERROR_CODE -> {
                            showToast(errorResponse.errorMessage)
                            return@launch
                        }
                        result.isSuccess && EquipmentUtil.isTote(result.getOrNull()) -> true to result.getOrNull()?.hlpId
                        else -> {
                            val lpResult = requestAwait(repository.autoCompleteLp(input))
                            false to lpResult.getOrNull()?.firstOrNull()
                        }
                    }
                }
            }
            if (lpId == null || (!isTote && LPUtil.isHLP(lpId))) {
                showToast(R.string.msg_tote_lp_not_found)
                return@launch
            }
            val lpResult = requestAwait(repository.getLpDetail(lpId))
            if (lpResult.isFailure) return@launch

            val lpDetail = lpResult.getOrNull() ?: return@launch

            runCatching { validateToLp(lpDetail) }.onFailure {
                showToast(it.message!!)
                return@launch
            }
            setDataStateAwait { copy(lpCompose = lpCompose.copy(scannedLpId = lpDetail.id)) }

            // Auto submit
            tryAutoSubmit()

        }
    }

    @Throws
    private fun validateTote(tote: String) {
        if (!pickToteCartWrap.isToteInToteCart(tote)) {
            throw getFormattedString(
                R.string.msg_tote_is_not_in_tote_cart,
                tote,
                pickToteCartWrap.toteCartBarcode
            ).toException
        }
        if (allocateStrategy is AutoAllocateStrategy.Single.MultiQty) {
            if (pickToteCartWrap.isToteUsedByOrder(tote)) {
                throw getFormattedString(
                    R.string.msg_tote_used,
                    tote,
                    pickToteCartWrap.getOccupiedOrderWithTote(tote)
                ).toException
            }
        }

    }

    @Throws
    private fun validateToLp(lp: LpDetail) {
        val bindingLps = dataState.lpCompose.bindingLpsOfOrders
        // Need use previous lp
        if (dataState.lpCompose.allowPrintMultiLpForOrder
            && dataState.ordersCompose.isAllDispatchNoneSingleQty()
            && !bindingLps.isNullOrEmpty() && lp.id !in bindingLps.map { it.first }
            && lp.id !in printerDelegate.printedNewLps
        ) {
            throw getFormattedString(
                R.string.message_batch_order_pick_to_lp_validate,
                bindingLps.map { it.second }.joinToString(separator = "/") { it }).toException
        }
        // Shipped order
        if (LPUtil.isCLP(lp.id) && lp.isBindedOrderShipped) {
            throw getFormattedString(R.string.message_lp_order_shipped, lp.id, lp.orderId)
                .toException
        }
    }

    private fun tryAutoSubmit() {
        if (dataState.configCompose.run { !allowManualEntry || enableAutoSubmit }) {
            submit()
        }
    }

    fun submit() = launch {
        runCatching { validateSubmit() }.onFailure {
            showToast(it.message!!)
            notifyFirstFocus()
            return@launch
        }
        val dispatchedOrders = dataState.ordersCompose.dispatchedOrdersWithQty
        val isMultiOrder = dispatchedOrders.size > 1
        val updates = dispatchedOrders.map { (order, _, qty) ->
            PickResultUpdateEntry().apply {
                isEntireLPPick = false
                pickedQtyByLPUnit = qty.toDouble()
                toLPId = dataState.lpCompose.scannedLpId
                fromLPId = <EMAIL>
                orderId = order.orderId
                itemSpecId = <EMAIL>
                soIds = dataState.soIdCompose.scanned.filter { it.orderId == order.orderId }
                    .map { it.soId }
                batch = isMultiOrder
            }
        }
        val result = requestAwait(
            repository.batchUpdatePickResult(taskId, updates),
            error = null
        )
        if (result.isFailure) {
            val error = result.errorResponse!!
            when (error.code) {
                ErrorCode.EXCEED_MAXIMUM_ALLOWED_PARTIAL_PALLET -> reportPartialPalletIssue(error.errorMessage)
                else -> {
                    speak(R.string.msg_pick_failed)
                    showToast(error.errorMessage)
                }
            }
            return@launch
        }
        checkToteCart()
        LastPickLocationUtil.saveLastPickLocation(fromLocation)
        showToast(R.string.submit_success)
        checkOnScreenOpCount(fromLocation)
    }

    private suspend fun checkOnScreenOpCount(locationEntry: LocationEntry) {
        val customerId = customerEntry.orgId
        requestAwait(repository.onScreenCountSearch(customerId, locationEntry.id)).onSuccess {
            it?.let {
                if (it.onScreenCount) {
                    fireEvent { ShowOnScreenOpCountDialog(it) }
                } else {
                    actionAfterPickSubmitSuccess()
                }
            }
        }.onFailure {
            it.message?.let { errorMSg -> { showToast(errorMSg) } }
            actionAfterPickSubmitSuccess()
        }
    }

    private fun actionAfterPickSubmitSuccess() {
        activityViewModel.finishOrders()
    }

    fun afterOnScreenOpCount() {
        actionAfterPickSubmitSuccess()
    }

    fun onScreenCountMatch(onScreenCountResponseEntry: OnScreenCountResponseEntry) {
        launch {
            requestAwait(
                repository.onScreenCountMatch(
                    getCustomerIdForOnScreenCount(onScreenCountResponseEntry),
                    onScreenCountResponseEntry.locationId,
                    getItemSpecIdForOnScreenCount(onScreenCountResponseEntry),
                    taskId,
                    taskEntry.taskType
                )
            ).onSuccess {
                showToast(R.string.on_screen_count_complete)
            }
        }
    }

    fun onScreenCountNotMatch(onScreenCountResponseEntry: OnScreenCountResponseEntry) {
        launch {
            requestAwait(
                repository.onScreenCountNotMatch(
                    getCustomerIdForOnScreenCount(onScreenCountResponseEntry),
                    onScreenCountResponseEntry.locationId,
                    getItemSpecIdForOnScreenCount(onScreenCountResponseEntry),
                    taskId,
                    taskEntry.taskType
                )
            ).onSuccess {
                showToast(R.string.on_screen_count_complete)
            }
        }
    }

    private fun getCustomerIdForOnScreenCount(onScreenCountResponseEntry: OnScreenCountResponseEntry): String {
        if (CollectionUtil.isNotNullOrEmpty(onScreenCountResponseEntry.inventories)
            && onScreenCountResponseEntry.inventories.size == 1
        ) {
            val customerId = onScreenCountResponseEntry.inventories[0].customerId
            if (!TextUtils.isEmpty(customerId)) {
                return customerId
            }
        }
        return customerEntry.orgId
    }

    private fun getItemSpecIdForOnScreenCount(onScreenCountResponseEntry: OnScreenCountResponseEntry): String {
        if (CollectionUtil.isNotNullOrEmpty(onScreenCountResponseEntry.inventories)
            && onScreenCountResponseEntry.inventories.size == 1
        ) {
            val itemSpecId = onScreenCountResponseEntry.inventories[0].itemSpecId
            if (!TextUtils.isEmpty(itemSpecId)) {
                return itemSpecId
            }
        }
        return itemSpecId
    }

    @Throws
    private suspend fun validateSubmit() = dataState.apply {
        // Location validation
        if (locationCompose.needValidate && !locationCompose.hasValidated) {
            throw getString(R.string.msg_please_scan_pick_location).toException
        }
        // Item validation
        if (itemCompose.needValidate && !itemCompose.hasValidated) {
            throw getString(R.string.msg_verify_pick_item_first).toException
        }
        // Lp
        if (lpCompose.scannedLpId.isNullOrEmpty()) {
            throw getString(R.string.msg_please_scan_or_input_lp).toException
        }
        // Qty
        val inputtedQty = qtyCompose.inputted ?: qtyCompose.hint
        when {
            inputtedQty <= 0 -> throw getString(R.string.msg_please_input_qty).toException
            inputtedQty > qtyCompose.suggested -> throw getString(R.string.label_qty_more_than_suggest_qty).toException
        }
        // SoId
        if (soIdCompose.needCollect && soIdCompose.scanned.safeCount() != inputtedQty) {
            throw getString(R.string.msg_so_id_qty_not_match).toException
        }
        // Lp for Consolidation
        if (ordersCompose.consolidation != null) {
            val result =
                requestAwait(repository.getConsolidationOfLp(lpCompose.scannedLpId), error = null)
            if (result.isFailure) {
                throw result.errorResponse!!.errorMessage.toException
            }
            val consolidations = result.getOrNull()
            if (!consolidations.isNullOrEmpty() && ordersCompose.consolidation !in consolidations) {
                throw getString(R.string.msg_order_consolidation_key_and_lp_consolidation_key_is_inconsistent).toException
            }
        }
    }

    private suspend fun reportPartialPalletIssue(errorMsg: String) {
        val password =
            awaitEvent { VerifyReportPalletIssue(errorMsg) } ?: return
        val priorityLp = when {
            errorMsg.isEmpty() -> ""
            else -> {
                val matcher = Pattern.compile("(\\w+LP)-(\\d+)").matcher(errorMsg)
                if (matcher.find()) {
                    matcher.group(0)
                } else {
                    ""
                }
            }
        }
        val reportHandler = ReportPartialPalletIssueHandle {
            showToast(R.string.toast_report_partial_pallet_issue_succeed)
        }
        reportHandler.onCheckPwdThenToReportIssue(
            password, repository.facilityId, taskId, priorityLp
        ) { showLoading(it) }
    }

    private fun checkToteCart() {
        if (pickMode == PickMode.PICK_TO_TOTE) {
            val toteCart = pickToteCartWrap.toteCartBarcode
            request(repository.getToteCartDetail(toteCart)) {
                it?.apply {
                    pickToteCartWrap.setPickToteCartDetailEntry(it)
                    activityViewModel.setToteCartDetailEntry(it)
                }
            }
        }
    }

}