package com.lt.linc.pick.pickpull;

import android.content.Context;
import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.appcompat.widget.AppCompatEditText;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.recyclerview.widget.SimpleItemAnimator;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ArrayAdapter;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;

import com.annimon.stream.Stream;
import com.customer.widget.ScanBarcodeDialog;
import com.customer.widget.locationselector.LocationSelectorDialog;
import com.customer.widget.scanner.decoder.DriverLicense;
import com.customer.widget.util.CommUtil;
import com.linc.platform.baseapp.model.LocationEntry;
import com.linc.platform.common.step.StepBaseEntry;
import com.linc.platform.inventory.model.InventoryEntry;
import com.linc.platform.pick.model.PickTaskViewEntry;
import com.linc.platform.pick.model.pickpull.PickPullStepEntry;
import com.linc.platform.pick.model.pickpull.PullResultUpdateEntry;
import com.linc.platform.pick.presenter.PickPullWorkLayoutPresenter;
import com.linc.platform.pick.presenter.impl.PickPullWorkLayoutPresenterImpl;
import com.linc.platform.pick.view.PickPullWorkLayoutView;
import com.linc.platform.utils.CollectionUtil;
import com.linc.platform.utils.ToastUtil;
import com.lt.linc.R;
import com.qmuiteam.qmui.util.QMUIDisplayHelper;
import com.qmuiteam.qmui.widget.dialog.QMUIDialog;
import com.qmuiteam.qmui.widget.dialog.QMUITipDialog;
import com.qmuiteam.qmui.widget.popup.QMUIListPopup;
import com.qmuiteam.qmui.widget.popup.QMUIPopup;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import rx.Observable;
import rx.android.schedulers.AndroidSchedulers;
import rx.schedulers.Schedulers;

/**
 * Created by dexter on 18/5/29.
 */

public class PickPullWorkLayout extends FrameLayout implements PickPullWorkLayoutView, View.OnClickListener {

    private Context ctx;
    private RecyclerView lpRlv;
    private RecyclerView itemRlv;
    private RecyclerView lpSearchRlv;
    private AppCompatTextView lpTxt;
    private AppCompatEditText lpCodeEdt;
    private AppCompatTextView submitTxt;
    private AppCompatTextView doneStepTxt;
    private AppCompatTextView locationTxt;
    private AppCompatTextView goToLocationTxt;
    private AppCompatTextView sortingAreaTxt;
    private AppCompatImageView changeLocationTxt;
    private LinearLayout enterTwoDigitLayout;
    private RelativeLayout locationLayout;
    private RelativeLayout sortingAreaLayout;
    private RelativeLayout submitAndDoneLayout;

    private QMUITipDialog tipDialog;
    private QMUIListPopup mListPopup;
    private ItemLPAdapter lpAdapter;
    private ItemInfoAdapter itemAdapter;
    private LPSearchAdapter lpSearchAdapter;
    private ScanBarcodeDialog scanBarcodeDialog;
    private LocationSelectorDialog locationSelectorDialog;

    private String taskId;
    private String toLocationId;
    private String fromLocationId;
    private PickPullWorkActivity activity;
    private StepBaseEntry stepBaseEntry;
    private PickPullWorkLayoutPresenter presenter;
    private PickPullStepEntry pullStepEntry;
    private PickTaskViewEntry pickTaskViewEntry;
    private List<InventoryEntry> lpList = new ArrayList<>();
    private List<InventoryEntry> lpSearchList = new ArrayList<>();
    private List<LocationEntry> sortingAreaList = new ArrayList<>();

    private OnCloseConfirm onCloseConfirm;

    public PickPullWorkLayout(@NonNull Context context,
                              PickPullStepEntry pullStepEntry,
                              PickTaskViewEntry pickTaskViewEntry,
                              StepBaseEntry stepBaseEntry) {
        super(context);
        this.ctx = context;
        this.pullStepEntry = pullStepEntry;
        this.stepBaseEntry = stepBaseEntry;
        this.pickTaskViewEntry = pickTaskViewEntry;
        activity = (PickPullWorkActivity) context;
        taskId = stepBaseEntry.taskId;
        presenter = new PickPullWorkLayoutPresenterImpl(this, stepBaseEntry, pickTaskViewEntry);
        initView();
    }

    private void initView() {
        LayoutInflater.from(ctx).inflate(R.layout.frame_layout_pick_pull_work,
                this, true);
        bindView();
        initListener();
        initLocation();
        initScanDialog();
        initItemInfoRlv();
        initLpRecyclerView();
        initLpSearchRecyclerView();
        if (presenter != null) {
            presenter.getSortingAreaList(taskId);
        }
        if (pullStepEntry.isFinished) {
            checkPickPullDone();
        }
        if (activity.currentIndex == pullStepEntry.index) {
            //延迟获取焦点
            Observable.timer(100 * (activity.currentPage + 1), TimeUnit.MILLISECONDS)
                    .observeOn(AndroidSchedulers.mainThread())
                    .subscribe((aLong) -> CommUtil.showKeyBoard(lpCodeEdt));
        }
    }

    private void initLocation() {
        fromLocationId = pullStepEntry.locationId;
        locationTxt.setText(pullStepEntry.location);
        CommUtil.setTxtParam(goToLocationTxt, pullStepEntry.location);
        showLocationIsFinish();
    }

    private void showLocationIsFinish() {
        if (pullStepEntry.isFinished) {
            locationLayout.setBackgroundColor(ContextCompat.getColor(ctx, R.color.pick_pull_location_finish));
            goToLocationTxt.setVisibility(View.GONE);
        } else {
            locationLayout.setBackgroundColor(ContextCompat.getColor(ctx, R.color.pick_pull_location_unfinish));
            goToLocationTxt.setVisibility(View.VISIBLE);
            lpRlv.setVisibility(GONE);
            lpSearchRlv.setVisibility(GONE);
        }
    }

    private void initItemInfoRlv() {
        itemAdapter = new ItemInfoAdapter();
        itemAdapter.setNewData(pullStepEntry.itemLines);
        itemRlv.setLayoutManager(new LinearLayoutManager(ctx));
        itemRlv.setAdapter(itemAdapter);
        itemRlv.setNestedScrollingEnabled(false);
        ((SimpleItemAnimator) itemRlv.getItemAnimator()).setSupportsChangeAnimations(false);
    }

    private void initLpRecyclerView() {
        lpAdapter = new ItemLPAdapter();
        lpAdapter.setNewData(lpList);
        lpRlv.setLayoutManager(new LinearLayoutManager(ctx));
        lpRlv.setAdapter(lpAdapter);
        lpRlv.setNestedScrollingEnabled(false);
        ((SimpleItemAnimator) lpRlv.getItemAnimator()).setSupportsChangeAnimations(false);
        lpAdapter.setRemoveBtnClick(position -> {
            lpList = lpAdapter.getData();
        });
    }

    private void initLpSearchRecyclerView() {
        lpSearchAdapter = new LPSearchAdapter();
        lpSearchAdapter.setNewData(lpSearchList);
        lpSearchRlv.setLayoutManager(new LinearLayoutManager(ctx));
        lpSearchRlv.setAdapter(lpSearchAdapter);
        lpSearchRlv.setNestedScrollingEnabled(false);
        ((SimpleItemAnimator) lpSearchRlv.getItemAnimator()).setSupportsChangeAnimations(false);
        lpSearchAdapter.setSelectedBtnClick(position -> {
            presenter.getInventoryByLp(lpSearchList.get(position).lpId);
            lpSearchRlv.setVisibility(View.GONE);
        });
    }


    private void initListener() {
        submitTxt.setOnClickListener(this);
        doneStepTxt.setOnClickListener(this);
        sortingAreaLayout.setOnClickListener(this);
        changeLocationTxt.setOnClickListener(this);
        enterTwoDigitLayout.setOnClickListener(this);
        lpCodeEdt.setOnEditorActionListener((txt, actionId, event) -> {
            if (pullStepEntry.isFinished) {
                showToast(R.string.text_pick_pull_finish);
                return false;
            }
            if (txt.getText().toString().length() < 2 || presenter == null) {
                showToast(R.string.text_enter_two_digit_code);
                return false;
            }
            lpSearchList.clear();
            lpSearchAdapter.notifyDataSetChanged();
            goToLocationTxt.setVisibility(View.GONE);
            presenter.getILPList(fromLocationId, txt.getText().toString());
            return false;
        });
//        RxTextView.textChanges(lpCodeEdt).subscribe(filter -> {
//            if (filter.length() < 2) {
//                lpSearchList.clear();
//                lpSearchRlv.setVisibility(View.GONE);
//            } else {
//                goToLocationTxt.setVisibility(View.GONE);
//                presenter.getILPList(pullStepEntry.locationId, filter.toString());
//            }
//        });
    }

    private void initScanDialog() {
        scanBarcodeDialog = new ScanBarcodeDialog();
        scanBarcodeDialog.setOnBarcodeResult(new ScanBarcodeDialog.OnBarcodeResult() {
            @Override
            public void onBarcode(String barcode) {
                showProgress(true);
                if (TextUtils.isEmpty(barcode)) {
                    ToastUtil.showToast(ctx.getString(R.string.text_barcode_is_empty));
                    return;
                }
                Observable.just(barcode).delay(300, TimeUnit.MILLISECONDS)
                        .subscribeOn(Schedulers.io())
                        .observeOn(AndroidSchedulers.mainThread())
                        .unsubscribeOn(Schedulers.io())
                        .subscribe(result ->
                                presenter.getILPList(fromLocationId, barcode));
            }

            @Override
            public void onDriverLicense(DriverLicense driverLicense) {

            }

            @Override
            public void onDismiss() {

            }
        });
    }

    public <T extends View> T findView(int viewId) {
        return (T) this.findViewById(viewId);
    }

    public void setEntry(PickPullStepEntry pullStepEntry) {
        this.pullStepEntry = pullStepEntry;
    }

    @Override
    public void onClick(View v) {
        if (pullStepEntry.isFinished && v.getId() != R.id.txt_done_pick_pull_step) {
            showToast(R.string.text_pick_pull_finish);
            return;
        }
        switch (v.getId()) {
            case R.id.img_select_location:
                showLocationDialog();
                break;
            case R.id.layout_enter_two_digit_code:
                showScanDialog();
                break;
            case R.id.layout_sorting_area:
                showSortingArea();
                break;
            case R.id.txt_submit:
                pullSubmit();
                break;
            case R.id.txt_done_pick_pull_step:
                doneStepClick();
                break;
            default:

        }
    }

    private void doneStepClick() {
        new QMUIDialog.MessageDialogBuilder(ctx)
                .setTitle(R.string.done_pull_step_title)
                .setMessage(R.string.done_pull_step_message)
                .addAction(ctx.getString(R.string.text_cancel),
                        (dialog, index) -> dialog.dismiss())
                .addAction(ctx.getString(R.string.text_confirm),
                        (dialog, index) -> {
                            dialog.dismiss();
                            activity.donePullStep();
                        })
                .create()
                .show();
    }

    private void showToast(int res) {
        ToastUtil.showToast(ctx.getString(res));
    }

    private void showScanDialog() {
        scanBarcodeDialog.setBarCodeFormat(0);
        scanBarcodeDialog.setShowResult(false);
        scanBarcodeDialog.setRescan(false);
        scanBarcodeDialog.show(activity.getSupportFragmentManager(), "barcode");
    }

    private void showLocationDialog() {
        if (locationSelectorDialog == null) {
            locationSelectorDialog = new LocationSelectorDialog(getContext());
            locationSelectorDialog.setSelectListener(locationEntry -> {
                if (locationEntry == null) {
                    showToast(R.string.text_not_the_location);
                    return;
                }
                locationTxt.setText(locationEntry.name);
                fromLocationId = locationEntry.id;
                goToLocationTxt.setText(String.format(getResources()
                                .getString(R.string.text_go_to_location_enter_two_digit)
                        , locationEntry.getName()));
                //2018-05-31 ,pullRequest的单个item设计与pick pull的多个Item设计有冲突
                if (CollectionUtil.isNotNullOrEmpty(pullStepEntry.itemLines)) {
                    presenter.changeLocation(taskId, fromLocationId,
                            pullStepEntry.itemLines.get(0).itemSpecId);
                }
            });
        }
        locationSelectorDialog.showDialog(true);
    }

    private void showSortingArea() {
        if (CollectionUtil.isNullOrEmpty(sortingAreaList)) {
            ToastUtil.showToast(ctx.getString(R.string.text_sorting_area_is_empty));
            presenter.getSortingAreaList(taskId);
            return;
        }
        if (mListPopup == null) {
            List<String> data = Stream.of(sortingAreaList)
                    .map(e -> e.name).toList();
            ArrayAdapter adapter = new ArrayAdapter<>(ctx,
                    R.layout.simple_list_item, data);

            mListPopup = new QMUIListPopup(ctx, QMUIPopup.DIRECTION_TOP, adapter);
            mListPopup.create(QMUIDisplayHelper.dp2px(ctx, 250),
                    QMUIDisplayHelper.dp2px(ctx, 200),
                    (adapterView, view, i, l) -> {
                        sortingAreaTxt.setText(data.get(i));
                        toLocationId = sortingAreaList.get(i).id;
                        mListPopup.dismiss();
                    });
            mListPopup.setOnDismissListener(() -> {

            });
        }
        mListPopup.setAnimStyle(QMUIPopup.ANIM_GROW_FROM_CENTER);
        mListPopup.setPositionOffsetYWhenTop(20);
        mListPopup.show(sortingAreaLayout);
    }

    @Override
    public void showErrorDialog(String error) {
        CommUtil.getOkDialog(ctx, error).show();
    }

    @Override
    public void showProgress(boolean isShow) {
        showProgress(isShow, "");
    }

    @Override
    public void showProgress(boolean isShow, String message) {
        if (tipDialog == null) {
            tipDialog = CommUtil.getLoadDialog(ctx, message);
            tipDialog.setCancelable(true);
        }
        if (isShow) {
            tipDialog.show();
        } else {
            tipDialog.dismiss();
        }
    }

    @Override
    public void onGetILPListSuccess(List<InventoryEntry> list) {
        if (list.size() == 1) {
            presenter.getInventoryByLp(list.get(0).lpId);
        } else {
            lpSearchList.addAll(list);
            lpSearchAdapter.notifyDataSetChanged();
            lpSearchRlv.setVisibility(View.VISIBLE);
            lpRlv.setVisibility(GONE);
            goToLocationTxt.setVisibility(View.GONE);
        }
    }

    @Override
    public void onGetPullInventorySuccess(InventoryEntry inventoryEntry) {
        lpSearchRlv.setVisibility(View.GONE);
        lpRlv.setVisibility(VISIBLE);
        goToLocationTxt.setVisibility(View.GONE);
        if (CollectionUtil.isNotNullOrEmpty(Stream.of(lpList)
                .filter(e -> e.lpId.equals(inventoryEntry.lpId))
                .toList())) {
            showToast(R.string.text_this_lp_already_in_list);
            return;
        }
        if (CollectionUtil.isNullOrEmpty(Stream.of(pullStepEntry.itemLines)
                .filter(e -> e.itemSpecId.equals(inventoryEntry.itemSpecId))
                .toList())) {
            showToast(R.string.text_this_lp_is_not_this_item);
            return;
        }
        lpList.add(inventoryEntry);
        lpAdapter.notifyDataSetChanged();
    }

    public void updateLpSearchRlv(List<InventoryEntry> list) {
        if (CollectionUtil.isNullOrEmpty(list)) {
            lpSearchRlv.setVisibility(View.GONE);
        } else {
            lpSearchRlv.setVisibility(View.VISIBLE);
            lpSearchAdapter.setNewData(list);
        }
    }

    @Override
    public void onGetSortingAreaListSuccess(List<LocationEntry> list) {
        sortingAreaList = list;
        sortingAreaTxt.setText(list.get(0).name);
        toLocationId = list.get(0).id;
    }

    @Override
    public void onChangeLocationSuccess(List<PickPullStepEntry> list) {
        //修改location后， 更新item的信息
        if (list != null && list.size() == 1) {
            Stream.of(list.get(0).itemLines).forEach(item ->
                    Stream.of(pullStepEntry.itemLines).forEach(bean -> {
                        if (bean.itemSpecId.equals(item.itemSpecId)) {
                            bean = item;
                        }
                    }));
        }
    }

    public void checkPickPullDone() {
        if (presenter != null) {
            presenter.getPickPullStepList(taskId);
        }
    }

    @Override
    public void onGetPickPullStepListSuccess(List<PickPullStepEntry> list) {
        pullStepEntry = Stream.of(list)
                .filter(e -> pullStepEntry != null && e.index == pullStepEntry.index)
                .findSingle().orElse(pullStepEntry);
        showLocationIsFinish();
        updateItemInfo();
        if (!pullStepEntry.isFinished) {
            return;
        }
        List<PickPullStepEntry> unFinishList = Stream.of(list)
                .filter(e -> !e.isFinished).toList();
        if (CollectionUtil.isNullOrEmpty(unFinishList)) {
            showDoneBtnLayout(true);
            if (onCloseConfirm != null) {
                onCloseConfirm.showConfirmDialog(locationTxt.getText().toString());
            }
        } else {
            showDoneBtnLayout(false);
            activity.changeNextPage();
        }
    }

    private void updateItemInfo() {
        itemAdapter.setNewData(pullStepEntry.itemLines);
    }

    private void showDoneBtnLayout(boolean isDonePullStep) {
        if (isDonePullStep) {
            submitAndDoneLayout.setBackgroundColor(ContextCompat.getColor(ctx, R.color.pick_pull_done_step_bg));
            doneStepTxt.setVisibility(View.VISIBLE);
            submitTxt.setVisibility(View.GONE);
        } else {
            submitAndDoneLayout.setBackgroundColor(ContextCompat.getColor(ctx, R.color.pick_transparent));
            doneStepTxt.setVisibility(View.GONE);
            submitTxt.setVisibility(View.VISIBLE);
        }
    }

    private void pullSubmit() {
        if (TextUtils.isEmpty(toLocationId)) {
            showToast(R.string.text_please_select_sorting_area);
            return;
        }
        if (CollectionUtil.isNullOrEmpty(lpList)) {
            showToast(R.string.text_scan_or_input_lp);
            return;
        }
        PullResultUpdateEntry pullResult = new PullResultUpdateEntry();
        pullResult.fromLocationId = fromLocationId;
        pullResult.toLocationId = toLocationId;
        pullResult.pulledLpIds = Stream.of(lpList).map(e -> e.lpId).toList();
        presenter.submit(pullResult);
    }

    @Override
    public void onGetILPListFail() {
        showToast(R.string.text_no_lp_by_this_digit);
    }

    @Override
    public void onStartTaskSuccess() {

    }

    @Override
    public void onPullSubmitSuccess() {
        showToast(R.string.submit_success);
        lpList.clear();
        lpAdapter.notifyDataSetChanged();
        lpCodeEdt.setText("");
        checkPickPullDone();
    }

    @Override
    public void onChangeLocationFail() {
        locationTxt.setText(pullStepEntry.location);
    }

    @Override
    public void onClosePickPullSuccess() {
        activity.onClosePickPullSuccess();
    }

    @Override
    public void onGetPullInventoryFail() {
        showToast(R.string.text_no_find_lp_in_inventory);
        lpRlv.setVisibility(View.VISIBLE);
    }

    private void bindView() {
        lpTxt = findView(R.id.txt_ilp);
        lpRlv = findView(R.id.recycler_lp);
        submitTxt = findView(R.id.txt_submit);
        itemRlv = findView(R.id.recycler_item);
        lpCodeEdt = findView(R.id.edt_lp_code);
        locationTxt = findView(R.id.txt_location);
        locationLayout = findView(R.id.layout_location);
        lpSearchRlv = findView(R.id.recycler_search_lp);
        sortingAreaTxt = findView(R.id.txt_sorting_area);
        goToLocationTxt = findView(R.id.txt_go_to_location);
        doneStepTxt = findView(R.id.txt_done_pick_pull_step);
        changeLocationTxt = findView(R.id.img_select_location);
        sortingAreaLayout = findView(R.id.layout_sorting_area);
        submitAndDoneLayout = findView(R.id.layout_submit_and_done);
        enterTwoDigitLayout = findView(R.id.layout_enter_two_digit_code);
    }

    public void setOnCloseConfirm(OnCloseConfirm onCloseConfirm) {
        this.onCloseConfirm = onCloseConfirm;
    }

    public interface OnCloseConfirm {
        void showConfirmDialog(String locationName);
    }
}
