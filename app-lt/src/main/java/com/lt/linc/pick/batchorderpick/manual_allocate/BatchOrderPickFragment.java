package com.lt.linc.pick.batchorderpick.manual_allocate;

import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.NonNull;
import androidx.fragment.app.DialogFragment;
import androidx.core.content.ContextCompat;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.widget.AppCompatButton;
import androidx.appcompat.widget.AppCompatEditText;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.appcompat.widget.SwitchCompat;
import android.text.InputType;
import android.text.TextUtils;
import android.view.View;
import android.view.inputmethod.EditorInfo;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.ScrollView;

import com.annimon.stream.Collectors;
import com.annimon.stream.Stream;
import com.customer.widget.CircleProgress;
import com.customer.widget.GeneralAlertDialog;
import com.customer.widget.ItemDividerDecoration;
import com.customer.widget.QuickScanner;
import com.customer.widget.core.LincBaseFragment;
import com.customer.widget.util.CommUtil;
import com.jakewharton.rxbinding.view.RxView;
import com.linc.platform.baseapp.model.FacilityEquipmentView;
import com.linc.platform.baseapp.model.LocationEntry;
import com.linc.platform.common.businessview.ClickHandler;
import com.linc.platform.common.customer.ManualInputOperation;
import com.linc.platform.common.customer.ManualInputScenario;
import com.linc.platform.common.customer.ManualInputStepType;
import com.linc.platform.common.customer.ManualInputTaskType;
import com.linc.platform.common.customer.PermissionTag;
import com.linc.platform.common.lp.LpTypeEntry;
import com.linc.platform.cyclecount.model.OPCountActionEntry;
import com.linc.platform.cyclecount.model.OnScreenCountResponseEntry;
import com.linc.platform.http.ErrorResponse;
import com.linc.platform.inventory.model.InventoryEntry;
import com.linc.platform.inventory.model.LpJobEntry;
import com.linc.platform.pick.BatchOrderPickHelper;
import com.linc.platform.pick.model.batchorderpick.BatchOrderPickData;
import com.linc.platform.pick.model.batchorderpick.BatchOrderPickIntentData;
import com.linc.platform.pick.model.batchorderpick.SoIdObject;
import com.linc.platform.pick.model.pickbyoreder.OrdersEntry;
import com.linc.platform.pick.model.pickbyoreder.PickSuggestEntry;
import com.linc.platform.pick.presenter.BatchOrderPickPresenter;
import com.linc.platform.pick.presenter.impl.BatchOrderPickPresenterImpl;
import com.linc.platform.pick.presenter.util.PickingRestrictUtil;
import com.linc.platform.pick.view.BatchOrderPickView;
import com.linc.platform.print.commonprintlp.PrintData;
import com.linc.platform.print.commonprintlp.PrintDataKt;
import com.linc.platform.print.commonprintlp.PrintMsg;
import com.linc.platform.print.model.PrinterEntry;
import com.linc.platform.utils.CollectionUtil;
import com.linc.platform.utils.Logger;
import com.linc.platform.utils.ResUtil;
import com.linc.platform.utils.RxUtil;
import com.linc.platform.utils.StringUtil;
import com.linc.platform.utils.ToastUtil;
import com.lt.linc.R;
import com.lt.linc.pick.LastPickLocationUtil;
import com.lt.linc.pick.batchorderpick.BatchOrderPickActivity;
import com.lt.linc.pick.work.OpportunityCycleCountDialog;
import com.lt.linc.pick.work.SnAdapter;
import com.lt.linc.toolset.print.lp.PrintLpActivity;
import com.lt.linc.toolset.print.setting.PrintSettingActivity;
import com.qmuiteam.qmui.widget.dialog.QMUIDialog;
import com.unis.autotrackdispatcher.annotation.RxClick;
import com.unis.autotrackdispatcher.annotation.Scroll;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import rx.Observable;
import rx.Observer;

/**
 * <AUTHOR>
 */

public class BatchOrderPickFragment extends LincBaseFragment implements BatchOrderPickView {
    public static final String PARAM = "param";
    public static final int SETTING_PRINTER_REQUEST_CODE = 0;
    private AppCompatTextView orderIdTxt;
    private AppCompatTextView pickingItemTxt;
    private AppCompatTextView fromLpTxt;
    private AppCompatTextView qtyInLpTxt;
    private AppCompatTextView suggestQtyTxt;
    private AppCompatTextView pickedQtyTxt;
    private AppCompatEditText pickQtyEdit;
    private AppCompatTextView uomTxt;
    private AppCompatTextView tvLocationValidation;
    private QuickScanner locationValidationScanner;
    private QuickScanner soIdScanner;
    private QuickScanner itemScanner;
    private QuickScanner snScanner;
    private SwitchCompat masterSNSwitch;
    private LinearLayout snContentLayout;
    private RecyclerView snRecyclerView;
    private ProgressBar snProgressBar;
    private AppCompatTextView valueProgressTxt;
    private AppCompatImageView pickItemCheckStatusIv;
    private LinearLayout pickItemCheckLayout;
    private LinearLayout locationValidationLayout;
    private LinearLayout consolidationLayout;
    private AppCompatTextView consolidationTxt;
    private RecyclerView soIdRecyclerView;
    private AppCompatButton newLpBtn;
    private QuickScanner lpScanner;
    private AppCompatTextView bindedLpsTxt, pickToLps;
    private AppCompatButton submitBtn;
    private AppCompatButton backBtn;

    private CircleProgress circleProgress;
    private BatchOrderPickPresenter presenter;
    private SoIdAdapter soIdAdapter;
    private SnAdapter snAdapter;
    private ScrollView scrollView;

    private LinearLayout suggestStageLocationLL;
    private AppCompatTextView suggestStageLocationTv;

    private boolean isPickItemVerifySucc = false;

    public static BatchOrderPickFragment newInstance(BatchOrderPickIntentData data) {
        BatchOrderPickFragment fragment = new BatchOrderPickFragment();
        Bundle bundle = new Bundle();
        bundle.putSerializable(PARAM, data);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int getLayoutId() {
        return R.layout.fragment_batch_order_pick;
    }

    @Override
    protected void initView() {
        bindViews();

        circleProgress = CircleProgress.create(getContext());
        presenter = new BatchOrderPickPresenterImpl(this, getBundleObject(PARAM));

        newLpBtn.setVisibility(presenter.isAllowUserPrintLP() ? View.VISIBLE : View.GONE);
        RxView.clicks(newLpBtn).throttleFirst(3, TimeUnit.SECONDS).subscribe(o -> onNewLpClick(newLpBtn));
        RxView.clicks(backBtn).subscribe(v -> onBackClick(backBtn));
        RxView.clicks(submitBtn).throttleFirst(3, TimeUnit.SECONDS).subscribe(o -> onSubmitPickClick(submitBtn));
        scrollView = findViewById(R.id.scroll_view);
        initQtyEdit();
        initLocationValidationScanner();
        initSoIdScanner();
        initSNContent();
        initItemScanner();
        initLPScanner();
        initSoIdRecyclerView();
    }

    private void initLocationValidationScanner() {
        if (presenter.getCustomer() != null && presenter.getCustomer().enforcePickTaskLocationValidation && !presenter.isScanLpToPick()) {
            locationValidationScanner.setVisibility(View.VISIBLE);
            locationValidationLayout.setVisibility(View.VISIBLE);
            locationValidationScanner.setPermissionTag(PermissionTag.create(presenter.getCustomer(), ManualInputTaskType.PICK, ManualInputStepType.Pick_Picking, ManualInputOperation.LOCATION_VALIDATION));
            locationValidationScanner.setScanEvent((v, data) -> presenter.searchLocation(data));
        }
    }

    private void initSoIdScanner() {
        soIdScanner.setHintText(getString(R.string.hint_scan_so_id));
        soIdScanner.setPermissionTag(PermissionTag.create(presenter.getCustomer(), ManualInputTaskType.PICK,
                ManualInputStepType.Pick_Picking, ManualInputOperation.SCAN_SOID, ManualInputScenario.BATCH_ORDER_PICK));
        soIdScanner.setScanEvent((view, data) -> {
            presenter.onSoIdScanned(data);
            soIdScanner.setInputFocus();
        });
    }

    private void initSNContent() {
        snScanner = findViewById(R.id.scan_sn_scanner);
        initSNScanner();
        masterSNSwitch = findViewById(R.id.switch_scan_master_switch);
        snProgressBar = findViewById(R.id.sn_progressbar);
        valueProgressTxt = findViewById(R.id.value_progress_txt);
        snContentLayout = findViewById(R.id.sn_content_layout);
        snAdapter = new SnAdapter(sn -> {
            snAdapter.removeSn(sn);
            setSNProgress();
        });
        snRecyclerView = findViewById(R.id.sn_recycler_view);
        snRecyclerView.setAdapter(snAdapter);
    }

    private void initSNScanner() {
        snScanner.setHintText(R.string.label_input_or_sn_to_pick);
        snScanner.setPermissionTag(PermissionTag.create(presenter.getCustomer(), ManualInputTaskType.PICK,
                ManualInputStepType.Pick_Picking, ManualInputOperation.SCAN_SN, ManualInputScenario.BATCH_ORDER_PICK));
        snScanner.setScanEvent((view, data) -> {
            if (presenter.suggestPickQty() != snAdapter.getItemCount()) {
                presenter.checkSN(data.toUpperCase(), snAdapter.getSNList(), shouldShowMasterSN() && masterSNSwitch.isChecked());
            } else {
                speakAndToast(R.string.text_number_of_sn_scanned_overage);
            }
        });
    }


    private void initMasterSNSwitch() {
        if (shouldShowMasterSN()) {
            masterSNSwitch.setVisibility(View.VISIBLE);
            masterSNSwitch.setOnCheckedChangeListener(((buttonView, isChecked) -> {
                if (isChecked) {
                    masterSNSwitch.setText(R.string.text_scan_master_sn);
                } else {
                    masterSNSwitch.setText(R.string.text_scan_individual_sn);
                }
            }));
        }
    }

    private void initItemScanner() {
        if (shouldShowItemScanner()) {
            itemScanner.setInputFocus();
            itemScanner.setPermissionTag(PermissionTag.create(presenter.getCustomer(), ManualInputTaskType.PICK,
                    ManualInputStepType.Pick_Picking, ManualInputOperation.SCAN_ITEM, ManualInputScenario.BATCH_ORDER_PICK));
            itemScanner.setScanEvent((view, data) -> {
                if (StringUtil.isNotEmpty(data)) {
                    presenter.verifyItem(data);
                }
            });
            pickItemCheckLayout.setVisibility(View.VISIBLE);
            speakAndToastDelay(R.string.text_verify_pick_item);
            setPickItemVerifyStatus(false);
        } else {
            this.isPickItemVerifySucc = true;
        }
    }

    private void initQtyEdit() {
        if (shouldRestrictInputQty()) {
            pickQtyEdit.setEnabled(false);
        }
        pickQtyEdit.setOnEditorActionListener((v, actionId, event) -> {
            presenter.onPickQtyEnter(pickQtyEdit.getText().toString());
            pickQtyEdit.requestFocus();
            return false;
        });
    }

    @Scroll
    private void initSoIdRecyclerView() {
        soIdAdapter = new SoIdAdapter();
        soIdRecyclerView.addItemDecoration(new ItemDividerDecoration(getContext()));
        soIdRecyclerView.setLayoutManager(new LinearLayoutManager(getContext()));
        soIdRecyclerView.setAdapter(soIdAdapter);
        soIdAdapter.setOnItemChildClickListener((adapter, view, position) -> {
            if (view.getId() == R.id.print_btn) {
                presenter.onPrintSoIdClick(soIdAdapter.getItem(position).id);
            }
        });
    }

    private void initLPScanner() {
        lpScanner.setHintText(getString(R.string.hint_scan_or_input_lp_to_bind_order));
        lpScanner.setPermissionTag(PermissionTag.create(presenter.getCustomer(), ManualInputTaskType.PICK,
                ManualInputStepType.Pick_Picking, ManualInputOperation.SCAN_TO_LP, ManualInputScenario.BATCH_ORDER_PICK));
        lpScanner.setScanEvent((view, barcode) -> {
            presenter.onToLPScanned(barcode);
            lpScanner.setInputFocus();
        });
    }

    @RxClick
    private void onNewLpClick(View view) {
        presenter.printLP(LpTypeEntry.getLPTypeByName(presenter.intentData().task.getPickToLPType()));
    }

    @RxClick
    private void onSubmitPickClick(View view) {
        submitPick();
    }

    @RxClick
    private void onBackClick(View view) {
        getActivity().finish();
    }

    @Override
    public void showCollectSNLayout() {
        int suggestPickBaseQty = (int) Math.floor(presenter.getSuggestPickBaseQty());
        snContentLayout.setVisibility(View.VISIBLE);
        snProgressBar.setMax(suggestPickBaseQty);
        snProgressBar.setProgress(snAdapter.getItemCount());
        String valueTxt = getResources().getString(R.string.lable_progress_colon)
                + snAdapter.getItemCount() + "/" + suggestPickBaseQty;
        valueProgressTxt.setText(valueTxt);
        snScanner.setInputFocus();
        initMasterSNSwitch();
    }

    @Override
    public void onGetSuggestSuccess(PickSuggestEntry suggestEntry) {
        if (suggestEntry != null) {
            refreshPickView(suggestEntry);
        }
    }

    private void refreshPickView(PickSuggestEntry suggestEntry) {
        orderIdTxt.setText(presenter.intentData().orderId);
        consolidationLayout.setVisibility(TextUtils.isEmpty(presenter.getConsolidation()) ? View.GONE : View.VISIBLE);
        consolidationTxt.setText(presenter.getConsolidation());
        pickingItemTxt.setText(suggestEntry.item == null ? "" : suggestEntry.item.itemSpecName);
        fromLpTxt.setText(TextUtils.isEmpty(suggestEntry.fromLp) || suggestEntry.fromLp.startsWith("HLP") ? "" : suggestEntry.fromLp);
        qtyInLpTxt.setText(suggestEntry.getLPUnitTotalQty());

        List<OrdersEntry> ordersEntries = suggestEntry.getSuggestOrders(presenter.intentData().orderId, presenter.intentData().lpUnitId);

        OrdersEntry ordersEntry = CollectionUtil.isNullOrEmpty(ordersEntries) ? null : ordersEntries.get(0);
        if (ordersEntry != null && ordersEntry.suggestPickQty == 0) {
            getActivity().finish();
            return;
        }

        int hintQty = 0;
        if (ordersEntry == null) {
            pickedQtyTxt.setText("0/0");
            suggestQtyTxt.setText("");
            pickQtyEdit.setHint("0");
            hintQty = 0;
            uomTxt.setText("");
        } else {
            pickedQtyTxt.setText(ordersEntry.getPickedQty());
            suggestQtyTxt.setText(ordersEntry.getSuggestPickQty());
            hintQty = shouldAutoIncreasingQtyAfterScanItem()
                    ? 0
                    : shouldRestrictInputQty() ? 1 : ordersEntry.suggestPickQty.intValue();
            uomTxt.setText(TextUtils.isEmpty(ordersEntry.lpUnitName) ? "" : ordersEntry.lpUnitName);
        }
        pickQtyEdit.setHint(hintQty + "");
        if (presenter.businessData().getPickQty() == 0) {
            presenter.businessData().setPickQty(hintQty);
        }

        soIdScanner.setVisibility(presenter.isNeedCollectSoId() ? View.VISIBLE : View.GONE);
        soIdRecyclerView.setVisibility(presenter.isNeedCollectSoId() ? View.VISIBLE : View.GONE);

        setPickAssignLpByOrder(suggestEntry, ordersEntry);// when no consolidation

    }

    private void setPickAssignLpByOrder(PickSuggestEntry suggestEntry, OrdersEntry ordersEntry) {
        if (!TextUtils.isEmpty(presenter.getConsolidation())) {
            return;
        }
        if (ordersEntry == null || CollectionUtil.isNullOrEmpty(ordersEntry.bindedLPIds)) {
            bindedLpsTxt.setText("");
        } else {
            bindedLpsTxt.setText(ordersEntry.getBindingLPIds(suggestEntry.lpToteMap));
            // high light picked equipment, ex. tote
            setPickAssignLpHighlight(Stream.of(ordersEntry.bindedLPIds).anyMatch(StringUtil::isHLP));
        }
    }

    @Override
    public void setPickAssignLp(List<String> lps) {
        if (CollectionUtil.isNotNullOrEmpty(lps)) {
            String lpsJoint;
            if (presenter.businessData().getPickSuggest() != null &&
                presenter.businessData().getPickSuggest().lpToteMap != null) {
                // 使用lpToteMap进行映射，将HLP ID映射为Tote编号
                Map<String, FacilityEquipmentView> lpToteMap = presenter.businessData().getPickSuggest().lpToteMap;
                lpsJoint = Stream.of(lps)
                        .map(lpId -> (lpToteMap.containsKey(lpId) && lpToteMap.get(lpId) != null) ?
                                Objects.requireNonNull(lpToteMap.get(lpId)).barcode : lpId)
                        .collect(Collectors.joining(" | "));
            } else {
                // 如果没有lpToteMap数据，则使用原来的逻辑
                lpsJoint = Stream.of(lps).collect(Collectors.joining(" | "));
            }
            bindedLpsTxt.setText(lpsJoint);
            setPickAssignLpHighlight(Stream.of(lps).anyMatch(StringUtil::isHLP));
        }
    }

    private void setPickAssignLpHighlight(boolean needHighlight) {
        if (!needHighlight) {
            return;
        }
        bindedLpsTxt.setTextColor(ContextCompat.getColor(getContext(), R.color.red));
        bindedLpsTxt.setTextSize(ResUtil.dip2px(8));
        pickToLps.setTextSize(ResUtil.dip2px(6));
    }

    @Override
    public void onGetSuggestFailed(String error) {
        onErrorToast(error);
        finish(false);
    }

    @Override
    public void onEnterQtyFailed(String error) {
        speakMsg(error);
        pickQtyEdit.setText("");
    }

    @Override
    public boolean supportVoice() {
        return true;
    }

    @Override
    public boolean needDestroyTts() {
        return false;
    }

    @Override
    public void refreshSoIdListView(List<SoIdObject> printedSoIdList) {
        if (CollectionUtil.isNullOrEmpty(printedSoIdList)) return;
        soIdAdapter.setNewData(printedSoIdList);
        soIdScanner.setInputFocus();

        if (Stream.of(printedSoIdList).filter(v -> v.isSelected).count() == printedSoIdList.size()) {
            setFocus(lpScanner);
            speakMsg(getString(R.string.text_scan_to_lp));
        }
    }

    private void setFocus(QuickScanner scanner) {
        Observable.timer(300, TimeUnit.MILLISECONDS).compose(RxUtil.asyncSchedulers()).subscribe(s -> scanner.setInputFocus());
    }

    @Override
    public void refreshToLPId(String lpId) {
        lpScanner.setText(lpId);
    }

    @Override
    public void submitPick() {
        if (locationValidationLayout.getVisibility() == View.VISIBLE && !presenter.isValidatedLocation()) {
            ToastUtil.showToast(R.string.msg_please_scan_pick_location);
            locationValidationScanner.setInputFocus();
            return;
        }
        if (shouldVerifyItemOnSubmit() && !isPickItemVerifySucc) {
            ToastUtil.showToast(R.string.msg_verify_pick_item_first);
            itemScanner.setInputFocus();
            return;
        }

        if (TextUtils.isEmpty(lpScanner.getText())) {
            ToastUtil.showToast(getContext(), getString(R.string.msg_please_scan_or_input_lp));
            return;
        }
        if (presenter.isNeedCollectSN()) {
            handleSNPick();
        } else {
            presenter.onSubmitClick(lpScanner.getText().toUpperCase());
        }
    }

    private void handleSNPick() {
        List<String> snList = snAdapter.getSNList();
        presenter.businessData().setSnList(snAdapter.getSNList());
        double suggestQty = presenter.suggestPickQty();

        if (CollectionUtil.isNullOrEmpty(snList)) {
            speakAndToast(R.string.hint_please_scan_sn);
            return;
        }
        if (snList.size() > suggestQty) {
            speakAndToast(R.string.text_number_of_sn_scanned_overage);
            return;
        }
        if (snList.size() < suggestQty) {
            showConfirmDialog();
            return;
        }

        presenter.onSubmitClick(lpScanner.getText().toUpperCase());
    }

    private void showConfirmDialog() {
        new AlertDialog.Builder(getContext())
                .setTitle(R.string.title_confirm)
                .setMessage(String.format(getResources().getString(R.string.msg_pick_confirm),
                        StringUtil.ignorePointZero(presenter.suggestPickQty()),
                        String.valueOf(snAdapter.getSNList().size())))
                .setNegativeButton(R.string.text_no, (dialog, which) -> dialog.dismiss())
                .setPositiveButton(R.string.text_yes, (dialog, which) -> presenter.onSubmitClick(lpScanner.getText().toUpperCase()))
                .create().show();
    }

    @Override
    public void onSNValidateFailed(String error) {
        speakAndToast(error);
        snScanner.setInputFocus();
    }

    @Override
    public void showDuplicateShippingSn(List<String> shippingSns) {
        if (getContext() == null) {
            return;
        }
        String snList = Stream.of(shippingSns).collect(Collectors.joining(" | "));
        String msg = snList + " " + ResUtil.getString(R.string.msg_please_remove_shipping_sn_first);
        GeneralAlertDialog.createAlertDialog(getContext(), msg).show();
        snScanner.setInputFocus();
    }

    @Override
    public void showOnScreenOpCountDialog(OnScreenCountResponseEntry onScreenCountResponseEntry) {
        OpportunityCycleCountDialog dialog = OpportunityCycleCountDialog.newInstance(onScreenCountResponseEntry);
        dialog.setMessageTip(StringUtil.getOnScreenOpCountTipMsg(OPCountActionEntry.SUBMITTING, onScreenCountResponseEntry));
        dialog.setOnActionCallback(new OpportunityCycleCountDialog.OnActionCallback() {
            @Override
            public void onMatch(DialogFragment dialogFragment) {
                presenter.onScreenCountMatch(onScreenCountResponseEntry);
                presenter.afterOnScreenOpCount();
                dialogFragment.dismiss();
            }

            @Override
            public void onNotMatch(DialogFragment dialogFragment) {
                presenter.onScreenCountNotMatch(onScreenCountResponseEntry);
                presenter.afterOnScreenOpCount();
                dialog.dismiss();
            }

            @Override
            public void onCancelDialog(DialogFragment dialogFragment) {
                presenter.afterOnScreenOpCount();
                dialogFragment.dismiss();
            }
        });
        dialog.show(getFragmentManager(), OpportunityCycleCountDialog.class.getSimpleName());
    }

    @Override
    public void inventoryNotFound() {
        snScanner.setInputFocus();
        speakAndToast(R.string.text_inventory_not_found);
    }

    @Override
    public void onFoundInventory(InventoryEntry inventoryEntry, List<String> snList) {
        if (presenter.businessData().getPickSuggest() != null) {
            if (!presenter.businessData().getPickSuggest().fromLp.equals(inventoryEntry.lpId)) {
                snScanner.setInputFocus();
                speakAndToast(String.format(getString(R.string.sn_lp_not_match_from_lp), inventoryEntry.lpId));
                return;
            }

            if (!inventoryEntry.itemSpecId.equals(presenter.businessData().getPickSuggest().item.itemSpecId)) {
                snScanner.setInputFocus();
                speakAndToast(R.string.sn_not_match_item);
                return;
            }
        }

        addSnToList(snList);
    }


    @Override
    public void foundMultipleInventory() {
        snScanner.setInputFocus();
        speakAndToast(R.string.msg_found_multiple_inventory);
    }

    @Override
    public void showSuggestStageLocations(List<LocationEntry> locations) {
        if (CollectionUtil.isNullOrEmpty(locations)) {
            suggestStageLocationLL.setVisibility(View.GONE);
        } else {
            suggestStageLocationLL.setVisibility(View.VISIBLE);
            String locationsText = Stream.of(locations).map(it -> it.name).reduce((pre, next) -> pre + " | " + next).get();
            suggestStageLocationTv.setText(locationsText);
        }
    }

    @Override
    public void onGetLocationSuccess(LocationEntry locationEntry) {
        if (locationEntry.id.equals(presenter.getCurrentLocation().id)) {
            tvLocationValidation.setText(locationEntry.getName());
            presenter.saveValidationLocation(locationEntry);
        } else {
            CommUtil.getOkDialog(getActivity(), getString(R.string.msg_location_inconsistency)).show();
        }
    }

    @Override
    public void onMultipleLocationFound(List<LocationEntry> locationEntries) {
        CommUtil.showListDialog(getActivity(), -1, Stream.of(locationEntries).map(v -> v.name).toList(), (which) -> onGetLocationSuccess(locationEntries.get(which)));
    }

    @Override
    public void addSnToList(List<String> snList) {
        snAdapter.addSnList(snList);
        setSNProgress();
        snScanner.setInputFocus();
        if (presenter.getSuggestPickBaseQty() == snAdapter.getItemCount()) {
            speakAndToast(R.string.btn_complete);
            lpScanner.setInputFocus();
        }
        scrollToBottom();
    }


    private void scrollToBottom() {
        Observable.timer(200, TimeUnit.MILLISECONDS)
                .compose(RxUtil.asyncSchedulers())
                .subscribe(new Observer<Long>() {
                    @Override
                    public void onCompleted() {

                    }

                    @Override
                    public void onError(Throwable e) {
                        Logger.e(e.getMessage());
                    }

                    @Override
                    public void onNext(Long aLong) {
                        scrollView.fullScroll(ScrollView.FOCUS_DOWN);
                    }
                });
    }

    private void setSNProgress() {
        double max = 0;
        try {
            double suggestPickQty = presenter.suggestPickQty();
            if (suggestPickQty != 0) {
                max = suggestPickQty;
            } else {
                max = Double.parseDouble(pickQtyEdit.getText().toString());
            }
        } catch (Exception e) {
            ToastUtil.showErrorToast(getContext(), R.string.txt_qty_format_error);
        }
        int progress = snAdapter.getItemCount();
        snProgressBar.setMax((int) max);
        snProgressBar.setProgress(progress);
        if (snContentLayout.getVisibility() == View.VISIBLE) {
            speakAndToast(String.valueOf(progress));
        }
        String valueTxt = getResources().getString(R.string.lable_progress_colon)
                + progress + "/" + (int) max;
        valueProgressTxt.setText(valueTxt);
        if (progress > max) {
            ToastUtil.showToast(String.format(getString(R.string.label_remove_sn), progress - ((int) max)));
        }
    }


    @Override
    public void onSubmitSuccess() {
        LastPickLocationUtil.saveLastPickLocation(presenter.businessData().getIntentData().location);
        pickQtyEdit.setText("");
        ToastUtil.showToast(getContext(), R.string.submit_success);
    }

    @Override
    public void onSubmitFailed(String error) {
        onErrorToast(error);
    }

    @Override
    public void onErrorToast(String error) {
        ToastUtil.showErrorToast(error);

        if (lpScanner.hasInputRestrict()) {
            lpScanner.setInputFocus();
        }
    }

    @Override
    public void finish(boolean hasInventoryIssue) {
        if (getActivity() != null) {
            circleProgress.dismiss();
            if (hasInventoryIssue) {
                getActivity().setResult(BatchOrderPickActivity.RESULT_CODE_INVENTORY_ISSUE_REPORTED);
            }
            getActivity().finish();
        }
    }

    @Override
    public void resetFocusView(BatchOrderPickData data) {
        if (data.getPickQty() <= 0) {
            pickQtyEdit.requestFocus();
        } else if (presenter.isNeedCollectSoId() && data.getScannedSoIdList().size() < data.getPickQty()) {
            soIdScanner.setInputFocus();
        } else {
            setFocus(lpScanner);
        }
    }

    @Override
    public void onSOIDNotMatch(String soId, BatchOrderPickData data) {
        speakAndToast(String.format(getString(R.string.msg_invalid_soid), soId));
        resetFocusView(data);
    }

    @Override
    public void showReloadOrderConfirmDialog(ClickHandler<Boolean> clickHandler) {
        if (getActivity() == null || getActivity().isFinishing() || getContext() == null) return;

        new AlertDialog.Builder(getContext())
                .setTitle(R.string.title_confirm)
                .setMessage(R.string.msg_order_load_failed_reload)
                .setPositiveButton(R.string.label_yes, (dialog, which) -> {
                    clickHandler.onClick(true);
                    dialog.dismiss();
                })
                .setNegativeButton(R.string.text_cancel, (dialog, which) -> dialog.dismiss())
                .show();
    }

    @Override
    public void showPrintNewLpConfirmDialog(String bindingLPIds, LpTypeEntry lpType) {
        String message = String.format(getContext().getString(R.string.message_batch_order_pick_new_lp_validate), bindingLPIds);

        AppCompatEditText passwordInput = new AppCompatEditText(getActivity());
        passwordInput.setInputType(EditorInfo.TYPE_CLASS_TEXT
                | EditorInfo.TYPE_TEXT_VARIATION_PASSWORD);
        new AlertDialog.Builder(getActivity())
                .setTitle(message)
                .setView(passwordInput)
                .setNegativeButton(R.string.btn_cancel, (dialog, which) -> dialog.dismiss())
                .setPositiveButton(R.string.text_submit, (dialogInterface, i) -> {
                    if (BatchOrderPickHelper.NEW_LP_VERIFY_PASSWORD.equals(passwordInput.getText().toString())) {
                        presenter.generateNewLp(lpType);
                    } else {
                        ToastUtil.showErrorToast(getContext().getString(R.string.error_incorrect_password));
                        dialogInterface.dismiss();
                    }
                }).show();
    }

    @Override
    public void showPriorityToPickDialog(String message, String lp) {
        QMUIDialog.EditTextDialogBuilder builder = new QMUIDialog.EditTextDialogBuilder(getActivity());
        builder.setInputType(InputType.TYPE_CLASS_TEXT)
                .setTitle(message)
                .setPlaceholder(R.string.msg_report_partial_pallet_issue_input_function_pwd)
                .addAction(R.string.btn_report_partial_pallet_issue_cancel, (dialog, index) -> dialog.dismiss())
                .addAction(R.string.btn_report_partial_pallet_issue, (dialog, index) -> {
                    String functionPwd = builder.getEditText().getText().toString();
                    presenter.onReportIssue(functionPwd, lp, getFacilityId());
                    dialog.dismiss();

                }).show();
        builder.getTitleView().setSingleLine(false);
    }

    @Override
    public void verifyPickItemSuccess() {
        if (shouldAutoIncreasingQtyAfterScanItem()) {
            double suggestQty = presenter.suggestPickQty();
            int pickQty = presenter.businessData().getPickQty() + 1;
            if (suggestQty > 0 && pickQty > suggestQty) {
                speakAndToast(R.string.error_over_picked);
                lpScanner.setInputFocus();
                return;
            } else if (pickQty == suggestQty) {
                speak(this.getString(R.string.complete));
                lpScanner.setInputFocus();
            } else {
                String message = pickQty + "";
                speak(message);
                itemScanner.setInputFocus();
            }
            this.isPickItemVerifySucc = true;
            presenter.businessData().setPickQty(pickQty);
            pickQtyEdit.setText(pickQty + "");
        } else if (forceItemCheckOnLpPicking()) {
            setPickItemVerifyStatus(true);
        }
    }

    private void setPickItemVerifyStatus(boolean pickItemVerifyStatus) {
        this.isPickItemVerifySucc = pickItemVerifyStatus;

        if (this.isPickItemVerifySucc) {
            pickItemCheckStatusIv.setVisibility(View.VISIBLE);
            lpScanner.setInputFocus();
        } else {
            pickItemCheckStatusIv.setVisibility(View.GONE);
        }
    }

    @Override
    public void verifyPickItemFail() {
        speakAndToast(R.string.text_verify_pick_item_fail);
        itemScanner.reset();
    }

    @Override
    public void speakMsg(String msg) {
        speakAndToast(msg);
    }

    @Override
    public void onPickFailed(String errorMessage) {
        if (!isAdded()) return;
        speak(getString(R.string.msg_pick_failed));
        onErrorToast(errorMessage);
    }

    private void bindViews() {
        orderIdTxt = findViewById(R.id.order_id_txt);
        consolidationLayout = findViewById(R.id.consolidation_layout);
        consolidationTxt = findViewById(R.id.consolidation_txt);
        pickingItemTxt = findViewById(R.id.picking_item_txt);
        fromLpTxt = findViewById(R.id.from_lp_txt);
        qtyInLpTxt = findViewById(R.id.qty_in_lp_txt);
        suggestQtyTxt = findViewById(R.id.suggest_qty_txt);
        pickedQtyTxt = findViewById(R.id.picked_qty_txt);
        pickQtyEdit = findViewById(R.id.pick_qty_edit);
        uomTxt = findViewById(R.id.uom_txt);
        soIdScanner = findViewById(R.id.scan_so_id_scanner);
        itemScanner = findViewById(R.id.scan_item_scanner);
        locationValidationLayout = findViewById(R.id.ll_location_validation);
        tvLocationValidation = findViewById(R.id.tv_location_validation);
        locationValidationScanner = findViewById(R.id.scan_location_validation_scanner);
        soIdRecyclerView = findViewById(R.id.so_id_recycler_view);
        newLpBtn = findViewById(R.id.new_lp_btn);
        lpScanner = findViewById(R.id.lp_id_edt);
        bindedLpsTxt = findViewById(R.id.binded_lps_txt);
        pickToLps = findViewById(R.id.label_pick_to_lps);
        submitBtn = findViewById(R.id.submit_btn);
        backBtn = findViewById(R.id.back_btn);
        pickItemCheckLayout = findViewById(R.id.l_pick_item_check);
        pickItemCheckStatusIv = findViewById(R.id.iv_pick_item_check_status);
        suggestStageLocationLL = findViewById(R.id.suggest_stage_location_ll);
        suggestStageLocationTv = findViewById(R.id.suggest_stage_location_txt);
    }

    @Override
    public void showProgress(boolean show) {
        if (getActivity() == null || getActivity().isFinishing()) {
            return;
        }
        circleProgress.showProgress(show);
    }

    @Override
    public void onPrinterNotSelect() {
        startPrinterSettingAct();
    }

    private void startPrinterSettingAct() {
        Intent intent = new Intent(getContext(), PrintSettingActivity.class);
        getActivity().startActivityForResult(intent, SETTING_PRINTER_REQUEST_CODE);
    }

    @Override
    public void onResume() {
        super.onResume();
        forceRefresh();
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        presenter.saveValidationLocation(null);
    }

    @Override
    public void forceRefresh() {
        presenter.onViewCreated();
    }

    @Override
    public void onPrintSuccess(@NonNull PrintData data, @NonNull PrinterEntry printerEntry) {
        if (CollectionUtil.isNotNullOrEmpty(data.extra.lpIds)) {
            lpScanner.setInputFocus();
        }

        if (presenter.isPrintSoId() && !presenter.isRePrintSoId()) {
            presenter.refreshSoIdList(presenter.getPrintData().extra.soIds);
        }
    }

    private boolean getSoIdListSuccess(PrinterEntry printerEntry) {
        return presenter.isPrintSoId() && !presenter.isRePrintSoId() && printerEntry.printData != null && CollectionUtil.isNotNullOrEmpty(printerEntry.printData.extra.soIds);
    }

    @Override
    public void onPrintFailed(ErrorResponse response, PrinterEntry printerEntry) {
        if (getSoIdListSuccess(printerEntry)) {
            presenter.refreshSoIdList(presenter.getPrintData().extra.soIds);
        }

        if (printerEntry == null || printerEntry.printData == null || presenter.isRePrintSoId()) {
            ToastUtil.showErrorToast(PrintMsg.formatError(printerEntry, response.getErrorMessage()));
            return;
        }

        if (presenter.isPrintSoId()) {
            showReprintSoIdConfirmDialog(response.getErrorMessage(), printerEntry.printData);
        } else {
            printLpFailed(printerEntry.printData);
        }
    }

    private void showReprintSoIdConfirmDialog(String error, PrintData printData) {
        if (getActivity() == null || getActivity().isFinishing() || getContext() == null) return;

        new AlertDialog.Builder(getContext())
                .setTitle(R.string.label_reprint_so_id)
                .setMessage(error)
                .setNeutralButton(R.string.text_printer_setting, (dialog, which) -> startPrinterSettingAct())
                .setPositiveButton(R.string.label_yes, (dialog, which) -> presenter.rePrintSoId(printData))
                .setNegativeButton(R.string.label_no, (dialog, which) -> dialog.dismiss())
                .show();
    }

    private void printLpFailed(PrintData data) {
        if (getActivity() == null || getActivity().isFinishing() || data == null) return;

        LpJobEntry jobEntry = new LpJobEntry();
        jobEntry.jobIds = PrintDataKt.getZplJobIds(data);
        jobEntry.copyNO = StringUtil.ignorePointZero(data.printQty);
        jobEntry.lpIds = data.extra.lpIds;

        Intent intent = new Intent(getContext(), PrintLpActivity.class);
        intent.putExtra(PrintLpActivity.DEFAULT_LP_TYPE, presenter.intentData().task.getPickToLPType());
        intent.putExtra(PrintLpActivity.PRINT_ORDER_ID, presenter.intentData().orderId);
        intent.putExtra(PrintLpActivity.FAILED_LP_PRINT_JOB, jobEntry);
        getActivity().startActivity(intent);
    }

    private boolean shouldRestrictInputQty() {
        return PickingRestrictUtil.shouldRestrictInputQty(presenter.getCustomer(), ManualInputScenario.BATCH_ORDER_PICK);
    }

    private boolean shouldShowItemScanner() {
        // If pick location, only show when shouldAutoIncreasingQtyAfterScanItem
        // otherwise show when shouldAutoIncreasingQtyAfterScanItem or forceItemCheckOnLpPicking
        return this.shouldAutoIncreasingQtyAfterScanItem() || this.forceItemCheckOnLpPicking() && !presenter.intentData().fromLpId.startsWith("HLP");
    }

    private boolean shouldVerifyItemOnSubmit() {
        return this.shouldShowItemScanner() && !this.shouldAutoIncreasingQtyAfterScanItem();
    }

    private boolean shouldAutoIncreasingQtyAfterScanItem() {
        return PickingRestrictUtil.shouldAutoIncreasingQtyAfterScanItem(presenter.getCustomer(), ManualInputScenario.BATCH_ORDER_PICK);
    }

    private boolean forceItemCheckOnLpPicking() {
        return presenter.getCustomer() != null && presenter.getCustomer().forceItemCheckOnLpPicking;
    }

    private boolean shouldShowMasterSN() {
        return presenter.getCustomer() != null && presenter.getCustomer().masterSNImport;
    }

}
