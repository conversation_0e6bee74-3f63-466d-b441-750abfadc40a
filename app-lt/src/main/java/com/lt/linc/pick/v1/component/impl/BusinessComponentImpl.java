package com.lt.linc.pick.v1.component.impl;

import com.lt.linc.pick.v1.component.BusinessComponent;

/**
 * Created by dexter on 2018/12/24.
 */

public abstract class BusinessComponentImpl<Presenter, V> implements BusinessComponent<Presenter, V> {

    private Presenter presenter;
    private V view;


    @Override
    public void init(Presenter presenter, V v) {
        this.presenter = presenter;
        this.view = v;
        initView();
    }

    public abstract void initView();
}
