package com.lt.linc.pick.batchorderpick

import android.content.Intent
import android.os.Bundle
import android.view.Menu
import android.view.MenuItem
import com.linc.platform.baseapp.model.LocationEntry
import com.linc.platform.pick.model.PickTaskViewEntry
import com.linc.platform.pick.model.batchorderpick.BatchOrderPickIntentData
import com.linc.platform.pick.model.pickbyoreder.PickSuggestEntry
import com.linc.platform.pick.model.picktote.PickModeData
import com.lt.linc.R
import com.lt.linc.common.mvi.ReactiveActivity
import com.lt.linc.common.mvi.ReactiveViewScope
import com.lt.linc.common.mvi.onEvent
import com.lt.linc.common.mvvm.kotlin.extensions.UniversalActivityParam
import com.lt.linc.common.mvvm.kotlin.extensions.findFragmentByTag
import com.lt.linc.common.mvvm.kotlin.extensions.replaceFragment
import com.lt.linc.common.mvvm.kotlin.extensions.universalParam
import com.lt.linc.databinding.ActivityBatchOrderPickBinding
import com.lt.linc.pick.batchorderpick.auto_allocate.AutoAllocateStrategy
import com.lt.linc.pick.batchorderpick.auto_allocate.MultiOrdersBatchPickFragment
import com.lt.linc.pick.batchorderpick.manual_allocate.BatchOrderPickFragment
import com.lt.linc.pick.labelprinthistory.LabelPrintHistoryActivity
import com.lt.linc.toolset.print.setting.PrintSettingActivity
import com.unis.autotrackdispatcher.annotation.Trace

/**
 * <AUTHOR>
 */
@Trace(value = "Batch Order Pick")
class BatchOrderPickActivity :
    ReactiveActivity<BatchOrderPickViewModel, BatchOrderPickUiState, ActivityBatchOrderPickBinding>() {

    companion object {
        const val RESULT_CODE_INVENTORY_ISSUE_REPORTED = 2001
        const val RESULT_CODE_WITH_PICK_ISSUE = 2002
        const val RESULT_BUNDLE_PICK_ISSUE = "bundlePickIssue"
    }

    sealed interface Param : BatchOrderPickBaseData, UniversalActivityParam {

        /**
         * For process that user has selected which order to pick. This process supports only one
         * order at a time
         */
        class ManuallyChoose(val data: BatchOrderPickIntentData) : Param {
            override val taskEntry: PickTaskViewEntry get() = data.task
            override val location: LocationEntry get() = data.location
            override val itemSpecId: String get() = data.itemSpecId
            override val fromLpId: String get() = data.fromLpId
            override val isScanLpToPick: Boolean get() = data.isScanLpToPick
        }

        /**
         * For process that the mobile phone will automatically allocate orders to the user to pick.
         * This process supports pick multi orders at a time.
         */
        class AutoAllocate @JvmOverloads constructor(
            override val taskEntry: PickTaskViewEntry,
            override val location: LocationEntry,
            override val fromLpId: String,
            override val itemSpecId: String,
            override val isScanLpToPick: Boolean,
            val pickSuggest: PickSuggestEntry? = null,
            val pickModeData: PickModeData? = null
        ) : Param
    }

    private val fragmentContainerId get() = binding.containerLayout.id

    override fun createViewModel(): BatchOrderPickViewModel {
        return BatchOrderPickViewModel(universalParam as Param)
    }

    override fun initView(savedInstanceState: Bundle?) {
        // Process change
        onEvent<BatchOrderPickUiEvent.Process> {
            when (this) {
                is BatchOrderPickUiEvent.Process.ManuallyChoose -> {
                    data.task = viewModel.dataState.taskEntry
                    showManualChooseProcess(data)
                }
                is BatchOrderPickUiEvent.Process.AutoAllocate -> showAutoAllocateProcess(strategy)
            }
        }
        // Finish
        onEvent<BatchOrderPickUiEvent.FinishEvent> {
            when (this) {
                is BatchOrderPickUiEvent.FinishEvent.WithPickIssue -> finishWithPickIssue(this)
                is BatchOrderPickUiEvent.FinishEvent.AllOrdersPicked -> finish()
                is BatchOrderPickUiEvent.FinishEvent.ForceFinish -> finish()
            }
        }
    }

    override fun ReactiveViewScope.subscribeToUiState() {
        subscribeNotNull(BatchOrderPickUiState::taskId) {
            initToolBarWithSubTitle(binding.toolbar, it, getString(R.string.label_pick))
        }
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        menu.apply {
            add(getString(R.string.text_reprint_so_id))
            add(getString(R.string.text_printer_setting))
        }
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        when (item.title.toString()) {
            getString(R.string.text_reprint_so_id) -> {
                val intent = Intent(this, LabelPrintHistoryActivity::class.java)
                intent.putExtra(LabelPrintHistoryActivity.TASK_ID, taskId)
                startActivity(intent)
            }
            getString(R.string.text_printer_setting) -> {
                val intent = Intent(this, PrintSettingActivity::class.java)
                startActivity(intent)
            }
        }
        return true
    }

    public override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode == RESULT_OK && requestCode == BatchOrderPickFragment.SETTING_PRINTER_REQUEST_CODE) {
            findFragmentByTag<BatchOrderPickFragment>()?.forceRefresh()
        }
    }

    private fun showManualChooseProcess(data: BatchOrderPickIntentData) {
        val fragment = BatchOrderPickFragment.newInstance(data)
        replaceFragment(fragmentContainerId, fragment)
    }

    private fun showAutoAllocateProcess(strategy: AutoAllocateStrategy) {
        var pickModeData: PickModeData? = (universalParam as Param.AutoAllocate).pickModeData
        val pickToteCartDetailEntry = viewModel.dataState.pickToteCartDetailEntry
        if (pickToteCartDetailEntry != null) {
            pickModeData = PickModeData(pickModeData?.pickMode, pickToteCartDetailEntry)
        }
        val fragment = MultiOrdersBatchPickFragment.newInstance(strategy, pickModeData)
        replaceFragment(fragmentContainerId, fragment)
    }

    private fun finishWithPickIssue(issue: BatchOrderPickUiEvent.FinishEvent.WithPickIssue) {
        val intent = Intent().apply {
            putExtra(RESULT_BUNDLE_PICK_ISSUE, issue)
        }
        setResult(RESULT_CODE_WITH_PICK_ISSUE, intent)
        finish()
    }

}

