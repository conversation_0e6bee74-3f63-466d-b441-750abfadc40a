package com.lt.linc.pick.batchorderpick.auto_allocate

import com.linc.platform.common.customer.*
import com.linc.platform.pick.BatchOrderPickHelper
import com.linc.platform.pick.model.PickingOrderWithQty
import com.linc.platform.pick.presenter.util.PickingRestrictUtil
import com.linc.platform.utils.CustomerConfigUtil

val MultiOrdersBatchPickViewModel.taskId get() = activityParam.taskEntry.id!!
val MultiOrdersBatchPickViewModel.taskEntry get() = task
val MultiOrdersBatchPickViewModel.itemSpecId get() = activityParam.itemSpecId
val MultiOrdersBatchPickViewModel.fromLpId get() = activityParam.fromLpId
val MultiOrdersBatchPickViewModel.fromLocation get() = activityParam.location
val MultiOrdersBatchPickViewModel.customerEntry get() = activityParam.taskEntry.customerEntry!!

fun MultiOrdersBatchPickViewModel.shouldValidateLocation(): Boolean {
    if (activityParam.isScanLpToPick) return false
    return customerEntry.enforcePickTaskLocationValidation
}

fun MultiOrdersBatchPickViewModel.shouldRestrictInputLocation() =
    CustomerConfigUtil.shouldRestrictInput(
        PermissionTag.create(
            customerEntry,
            ManualInputTaskType.PICK,
            ManualInputStepType.Pick_Picking,
            ManualInputOperation.LOCATION_VALIDATION,
            ManualInputScenario.BATCH_ORDER_PICK
        )
    )

fun MultiOrdersBatchPickViewModel.shouldShowItemScanner() =
    shouldAutoIncreasingQtyAfterScanItem() || forceItemCheckOnLpPicking()

fun MultiOrdersBatchPickViewModel.forceItemCheckOnLpPicking() =
    customerEntry.forceItemCheckOnLpPicking && !activityParam.fromLpId.startsWith("HLP")

fun MultiOrdersBatchPickViewModel.shouldAutoIncreasingQtyAfterScanItem() =
    PickingRestrictUtil.shouldAutoIncreasingQtyAfterScanItem(
        customerEntry,
        ManualInputScenario.BATCH_ORDER_PICK
    )

fun MultiOrdersBatchPickViewModel.shouldValidateItem() =
    forceItemCheckOnLpPicking() && !shouldAutoIncreasingQtyAfterScanItem()


fun MultiOrdersBatchPickViewModel.shouldRestrictInputItem() =
    CustomerConfigUtil.shouldRestrictInput(
        PermissionTag.create(
            customerEntry,
            ManualInputTaskType.PICK,
            ManualInputStepType.Pick_Picking,
            ManualInputOperation.SCAN_ITEM,
            ManualInputScenario.BATCH_ORDER_PICK
        )
    )

fun MultiOrdersBatchPickViewModel.shouldRestrictInputQty() =
    PickingRestrictUtil.shouldRestrictInputQty(
        customerEntry,
        ManualInputScenario.BATCH_ORDER_PICK
    )


fun MultiOrdersBatchPickViewModel.shouldRestrictInputSOID() =
    CustomerConfigUtil.shouldRestrictInput(
        PermissionTag.create(
            customerEntry,
            ManualInputTaskType.PICK,
            ManualInputStepType.Pick_Picking,
            ManualInputOperation.SCAN_SOID,
            ManualInputScenario.BATCH_ORDER_PICK
        )
    )

fun MultiOrdersBatchPickViewModel.isAllowPrintNewLP() = !taskEntry.forbidGenerateLPLabelAtPickTask

fun MultiOrdersBatchPickViewModel.allowPrintMultipleLPsForOrder() =
    CustomerConfigUtil.allowPrintMultipleLPsForOrderWhenPicking(customerEntry)

fun MultiOrdersBatchPickViewModel.shouldRestrictInputToLp() =
    CustomerConfigUtil.shouldRestrictInput(
        PermissionTag.create(
            customerEntry,
            ManualInputTaskType.PICK,
            ManualInputStepType.Pick_Picking,
            ManualInputOperation.SCAN_TO_LP,
            ManualInputScenario.BATCH_ORDER_PICK
        )
    )

fun MultiOrdersBatchPickViewModel.allowManualEntry() = taskEntry.isAllowManualEntry

fun MultiOrdersBatchPickViewModel.allowPickAutoSubmit() =
    CustomerConfigUtil.allowPickAutoSubmit(customerEntry)

fun MultiOrdersBatchPickViewModel.shouldCollectSOID(orders: List<PickingOrderWithQty>) =
    !taskEntry.requireScanSoldAtPackWorkstation && BatchOrderPickHelper.shouldCollectSOID(orders.map { it.orderEntry }, customerEntry)

fun MultiOrdersBatchPickViewModel.shouldAutoPrintSOID(orders: List<PickingOrderWithQty>) =
    !taskEntry.printSoIdAtPackWorkstation && BatchOrderPickHelper.shouldAutoPrintSOID(orders.map { it.orderEntry }, customerEntry)
