package com.lt.linc.pick.batchorderpick

import com.linc.platform.R
import com.linc.platform.http.ErrorResponse
import com.linc.platform.pick.BatchOrderPickHelper
import com.linc.platform.pick.api.OperateWorkItemApi
import com.linc.platform.pick.api.PickTaskCenterApi
import com.linc.platform.pick.model.PickTaskSearchEntry
import com.linc.platform.pick.model.pickbyoreder.OrdersEntry
import com.linc.platform.pick.model.pickbyoreder.PickSuggestEntry
import com.linc.platform.pick.model.picktote.PickToteCartDetailEntry
import com.linc.platform.pick.presenter.impl.OperateWorkItemPresenterImpl
import com.lt.linc.common.Tuple
import com.lt.linc.common.mvi.ReactiveViewModel
import com.lt.linc.common.mvi.subscribeNotNull
import com.lt.linc.common.mvvm.kotlin.BaseRepository
import com.lt.linc.common.mvvm.kotlin.extensions.*
import com.lt.linc.pick.batchorderpick.BatchOrderPickActivity.Param
import com.lt.linc.pick.batchorderpick.BatchOrderPickUiEvent.FinishEvent
import com.lt.linc.pick.batchorderpick.BatchOrderPickUiEvent.Process
import com.lt.linc.pick.batchorderpick.auto_allocate.AutoAllocateStrategy

/**
 * <AUTHOR>
 * @date 2022/06
 */
class BatchOrderPickViewModel(
    val activityParam: Param,
    initialState: BatchOrderPickDataState = BatchOrderPickDataState(),
    initialUiState: BatchOrderPickUiState = BatchOrderPickUiState(activityParam.taskEntry.id),
    private val repository: BatchOrderPickRepo = BatchOrderPickRepo()
) : ReactiveViewModel<BatchOrderPickDataState, BatchOrderPickUiState>(
    initialState,
    initialUiState
) {

    companion object {
        const val ERROR_LP_NO_ENOUGH_QTY = "no enough qty"
    }

    init {
        initWork()
    }

    private fun initWork() {
        launch {
            val tasks = requestAwait(repository.getPickTask(activityParam.taskEntry.id)).getOrNull()
            if (tasks.isNullOrEmpty()) {
                showToast(R.string.error_task_not_found)
                return@launch
            }
            setDataStateAwait { copy(taskEntry = tasks.first()) }
            when (activityParam) {
                is Param.ManuallyChoose -> fireEvent { Process.ManuallyChoose(activityParam.data) }
                is Param.AutoAllocate -> refreshPickOrdersSuggestions(activityParam.pickSuggest)
            }
            subscribeToUpdateUiState()
        }
    }

    private fun subscribeToUpdateUiState() {
        // Auto allocate strategy
        subscribeNotNull(BatchOrderPickDataState::autoAllocateStrategy) {
            fireEvent { Process.AutoAllocate(it) }
        }
    }

    private fun refreshPickOrdersSuggestions(pickSuggest: PickSuggestEntry? = null) = launch {
        val (taskId, fromLp, itemSpecId) = activityParam.run {
            Tuple(taskEntry.id, fromLpId, itemSpecId)
        }

        val (suggestionOfItem, errorMessage) = if (pickSuggest != null) {
            pickSuggest to null
        } else {
            val result = requestAwait(
                repository.getPickOrderSuggestionsByItem(taskId, fromLp, itemSpecId),
                error = null
            )
            // Pick enough
            if (result.isFailure && result.errorResponse!!.isPickEnough) {
                fireEvent { FinishEvent.AllOrdersPicked }
                return@launch
            }

            result.getOrNull() to result.exceptionOrNull()?.toErrorResponse()?.errorMessage
        }
        suggestionOfItem?.removePickedOrders()

        // Error or no suggestions
        if (suggestionOfItem == null || suggestionOfItem.orders.isNullOrEmpty()) {
            val message = errorMessage ?: getString(R.string.label_no_suggest)
            showToast(message)
            fireEvent { FinishEvent.WithPickIssue(fromLp, itemSpecId, message) }
            return@launch
        }

        val pickSuggestEntry = suggestionOfItem.apply {
            this.fromLp = fromLp
            this.taskId = taskId
            companyId = activityParam.taskEntry.companyId
        }
        val strategy = autoAllocatePickingOrders(pickSuggestEntry)
        setDataState {
            copy(autoAllocateStrategy = strategy, pickSuggestEntry = pickSuggestEntry)
        }
    }

    private val ErrorResponse.isPickEnough
        get() = errorMessage.run {
            OperateWorkItemPresenterImpl.ERROR_PICKED_ENOUGH in this || ERROR_LP_NO_ENOUGH_QTY in this
        }

    private fun PickSuggestEntry?.removePickedOrders() = this?.apply {
        orders?.removeAll { it.suggestPickQty == 0.0 }
    }

    private fun autoAllocatePickingOrders(pickSuggestEntry: PickSuggestEntry): AutoAllocateStrategy {
        val ordersOfTask = activityParam.taskEntry.orders
        val lpRemainQty = pickSuggestEntry.lpTotalQty?.toInt() ?: 0
        assert(lpRemainQty > 0)

        val buildMultiOrdersFunc = { pickingOrders: List<OrdersEntry> ->
            BatchOrderPickHelper.filterPickingOrdersByQty(
                pickingOrders,
                ordersOfTask,
                lpRemainQty
            )
        }
        // All orders are single item, pick these orders at a time
        if (activityParam.taskEntry.isForSingleQty) {
            return AutoAllocateStrategy.Combine.SingleItem(
                pickSuggestEntry,
                buildMultiOrdersFunc(pickSuggestEntry.orders),
                activityParam.taskEntry.orderIds
            )
        }
        // If has consolidation, combine orders of same consolidation and pick them at a time
        val consolidationGroup = pickSuggestEntry.orders
            .groupBy(keySelector = { if (it.consolidationNo.isNullOrEmpty()) "" else it.consolidationNo })
            .filter { (key, _) -> key.isNotEmpty() }
        if (consolidationGroup.isNotEmpty()) {
            val (consolidation, list) = consolidationGroup.entries.first()
            val orderIdsOfConsolidation = activityParam.taskEntry.orders
                .filter { it.consolidationNo == consolidation }
                .map { it.id }
            return AutoAllocateStrategy.Combine.Consolidation(
                pickSuggestEntry,
                buildMultiOrdersFunc(list),
                orderIdsOfConsolidation
            )
        }

        val buildSingleOrderFunc = { pickingOrder: OrdersEntry ->
            BatchOrderPickHelper.filterPickingOrdersByQty(
                listOf(pickingOrder),
                ordersOfTask,
                lpRemainQty
            ).first()
        }
        // If no consolidation, priority pick multiple qty, and then single qty
        val multiQtyOrder = pickSuggestEntry.orders.firstOrNull { !it.isSingleQty }
        if (multiQtyOrder != null) {
            return AutoAllocateStrategy.Single.MultiQty(
                pickSuggestEntry,
                buildSingleOrderFunc(multiQtyOrder)
            )
        }
        // Single qty
        return AutoAllocateStrategy.Single.SingleQty(
            pickSuggestEntry,
            buildSingleOrderFunc(pickSuggestEntry.orders.first())
        )
    }

    fun finishOrders() {
        // Refresh pick orders suggestion to allocate next picking orders
        refreshPickOrdersSuggestions()
    }

    fun forceFinish() = fireEvent { FinishEvent.ForceFinish }

    fun setToteCartDetailEntry(pickToteCartDetailEntry: PickToteCartDetailEntry) {
        setDataState { copy(pickToteCartDetailEntry = pickToteCartDetailEntry) }
    }

}

class BatchOrderPickRepo : BaseRepository() {

    private val operateWorkItemApi by apiServiceLazy<OperateWorkItemApi>()
    private val pickTaskCenterApi by apiServiceLazy<PickTaskCenterApi>()

    fun getPickOrderSuggestionsByItem(taskId: String, lpId: String, itemSpecId: String) =
        rxRequest(operateWorkItemApi.batchOrderPickSuggestByOrders(taskId, lpId, itemSpecId))

    fun getPickTask(taskId: String)
    = rxRequest(pickTaskCenterApi.loadPickTask(PickTaskSearchEntry().apply {
        this.taskIds = listOf(taskId)
    }))
}