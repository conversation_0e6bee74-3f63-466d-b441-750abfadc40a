package com.lt.linc.equipmentinquiry.transfer

import android.text.TextUtils
import com.linc.platform.baseapp.api.FacilityEquipmentApi
import com.linc.platform.baseapp.model.FacilityEquipmentSearch
import com.linc.platform.baseapp.model.FacilityEquipmentType
import com.linc.platform.common.lp.LpApi
import com.linc.platform.common.lp.LpTypeEntry
import com.linc.platform.inventory.api.InventoryApi
import com.linc.platform.inventory.model.InventorySearchEntry
import com.linc.platform.inventory.model.InventoryStatusEntry
import com.linc.platform.inventory.model.LpBatchCreateEntry
import com.linc.platform.inventory.model.TransferInventoryEntry
import com.linc.platform.print.commonprintlp.PrintData
import com.linc.platform.print.commonprintlp.PrintMsg
import com.linc.platform.print.commonprintlp.PrintResult
import com.linc.platform.print.model.LabelSizeEntry
import com.linc.platform.utils.CollectionUtil
import com.linc.platform.utils.LPUtil
import com.linc.platform.utils.PrintUtil
import com.lt.linc.R
import com.lt.linc.common.extensions.safeCount
import com.lt.linc.common.mvi.ReactiveViewModel
import com.lt.linc.common.mvi.mapDataToUi
import com.lt.linc.common.mvvm.kotlin.BaseRepository
import com.lt.linc.common.mvvm.kotlin.extensions.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.firstOrNull

/**
 * @Description:
 * @Author: Dennis
 * @CreateDate: 2023/3/10
 */
class TransferConfirmViewModel(
    initialDataState: TransferConfirmDataState,
    initialUiState: TransferConfirmUiState = TransferConfirmUiState()
) : ReactiveViewModel<TransferConfirmDataState, TransferConfirmUiState>(initialDataState, initialUiState) {

    private val repository = Repository()
    private val printUtil = PrintUtil.newInstance()

    init {
        autoUpdateDataToUi()
        getPickedInventory()
    }

    private fun autoUpdateDataToUi() {
        mapDataToUi(TransferConfirmDataState::facilityEquipmentView, TransferConfirmUiState::facilityEquipmentView) {
            it
        }
        mapDataToUi(TransferConfirmDataState::toteOrLPId, TransferConfirmUiState::toteOrLPId) {
            it
        }
        mapDataToUi(TransferConfirmDataState::needTransferLPId, TransferConfirmUiState::needTransferLPId) {
            it
        }
    }

    private fun getPickedInventory() {
        val facilityEquipmentView = dataState.facilityEquipmentView
        val relatedTaskIds = facilityEquipmentView.relatedTaskIds
        if (CollectionUtil.isNotNullOrEmpty(relatedTaskIds)) {
            launch {
                requestAwait(repository.searchPickedInventory(relatedTaskIds[0])).onSuccess {
                    val existInventoryEquipmentLp = it?.inventories?.map { inventoryEntry -> inventoryEntry.lpId }?.filter { lpId -> lpId == facilityEquipmentView.hlpId }
                    if (existInventoryEquipmentLp?.isNotEmpty() == true) {
                        setDataState { copy(needTransferLPId = existInventoryEquipmentLp[0]) }
                    } else {
                        showToast(String.format(getString(R.string.msg_no_picked_inventory_need_to_be_transferred), facilityEquipmentView.type.name, facilityEquipmentView.id))
                    }
                }
            }
        }
    }

    fun setLabelSize(labelSize: LabelSizeEntry) {
        setDataState { copy(labelSize = labelSize) }
    }

    fun getLabelSize(): LabelSizeEntry = dataState.labelSize

    fun printClp(printData: PrintData, onReprintConfirm: (message: String) -> Flow<Boolean?>) {
        launch {
            val requestAwait = requestAwait(repository.getEquipmentLocationId(dataState.facilityEquipmentView.hlpId))
            val locationId = requestAwait.getOrNull()?.locationId ?: return@launch
            val lpBatchCreateEntry = LpBatchCreateEntry().apply {
                this.count = 1
                this.type = LpTypeEntry.CLP
                this.locationId = locationId
            }
            val request = if (printData.paperSize == LabelSizeEntry.FOUR_SIX) {
                repository.create4X6Lp(lpBatchCreateEntry)
            } else {
                repository.create2X1Lp(lpBatchCreateEntry)
            }
            requestAwait(request).onSuccess {
                it?.let { lpJobEntry ->
                    printData.jobData = PrintData.JobData.ZPL(jobIds = lpJobEntry.jobIds)
                    printData.printQty = 1.toDouble()
                    printLps(printData, onReprintConfirm).onSuccess { isSuccess ->
                        if (isSuccess) {
                            showToast(getString(R.string.print_success))
                        }
                    }
                }
            }
        }
    }

    private suspend fun printLps(
        printData: PrintData,
        onReprintConfirm: (message: String) -> Flow<Boolean?>,
    ): Result<Boolean> {
        val result = printUtil.printWithFlow(printData, onShowProgress = { showLoading(it) }).firstOrNull()
        return when (result) {
            null -> Result.success(false)
            PrintResult.NoPrinter -> {
                Result.success(false)
            }
            is PrintResult.Success -> Result.success(true)
            is PrintResult.Failed -> {
                val reprint = onReprintConfirm(PrintMsg.formatError(result.printerEntry, result.response.errorMessage)).firstOrNull()
                if (reprint == true) {
                    printLps(printData, onReprintConfirm)
                } else {
                    Result.success(false)
                }
            }
        }
    }

    fun checkToteAndLp(data: String?) {
        data ?: return
        launch {
            requestAwait(repository.searchTote(data)).onSuccess {
                it?.let { equipments ->
                    val tote = when (equipments.safeCount()) {
                        0 -> {
                            checkLp(data)
                            null
                        }
                        else -> equipments.first()
                    }
                    if (tote != null) {
                        setDataState { copy(toteOrLPId = tote.barcode) }
                    }
                }
            }
        }
    }

    private suspend fun checkLp(lpId: String) {
        if (!LPUtil.isLP(lpId)) {
            showToast(R.string.msg_tote_lp_not_found)
            return
        }
        requestAwait(repository.checkLp(lpId)).onSuccess {
            if (CollectionUtil.isNotNullOrEmpty(it)) {
                setDataState { copy(toteOrLPId = it?.first()) }
            } else {
                showToast(R.string.msg_tote_lp_not_found)
            }
        }
    }

    fun confirmTransfer() {
        if (!TextUtils.isEmpty(dataState.toteOrLPId) && !TextUtils.isEmpty(dataState.needTransferLPId)) {
            launch {
                val fromLPId = dataState.needTransferLPId!!
                val toLPId = dataState.toteOrLPId!!
                requestAwait(repository.transferPickedInventory(fromLPId, toLPId)).onSuccess {
                    showToast(R.string.msg_transfer_success)
                    fireEvent { TransferEvent.TransferComplete(dataState.facilityEquipmentView.barcode) }
                }
            }
        }
    }

}

private class Repository : BaseRepository() {

    private val lpApi by apiServiceLazy<LpApi>()
    private val facilityEquipmentApi by apiServiceLazy<FacilityEquipmentApi>()
    private val inventoryApi by apiServiceLazy<InventoryApi>()

    fun create2X1Lp(entry: LpBatchCreateEntry) = rxRequest(lpApi.newLpPrintJob(entry))

    fun create4X6Lp(entry: LpBatchCreateEntry) = rxRequest(lpApi.newInventoryLpPrintJob(entry))

    fun getEquipmentLocationId(clp: String) = rxRequest(lpApi.get(clp))

    fun searchTote(barcode: String) = rxRequest(facilityEquipmentApi.search(FacilityEquipmentSearch().apply {
        this.barcode = barcode
        this.type = FacilityEquipmentType.TOTE
    }))

    fun checkLp(lpId: String) = rxRequest(lpApi.idAutoComplete(listOf(lpId)))

    fun transferPickedInventory(fromLPId: String, toLPId: String) =
        rxRequest(inventoryApi.transferPickedInventory(TransferInventoryEntry(fromLPId, toLPId)))

    fun searchPickedInventory(taskId: String) = rxRequest(inventoryApi.searchInventoryByPaging(InventorySearchEntry().apply {
        this.pickTaskId = taskId
        this.statuses = listOf(InventoryStatusEntry.PICKED)
    }))
}