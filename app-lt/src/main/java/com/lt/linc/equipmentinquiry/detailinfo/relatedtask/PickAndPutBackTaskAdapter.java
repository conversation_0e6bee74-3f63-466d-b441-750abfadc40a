package com.lt.linc.equipmentinquiry.detailinfo.relatedtask;

import android.content.Context;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.text.TextUtils;
import android.view.View;

import com.annimon.stream.Collectors;
import com.annimon.stream.Stream;
import com.chad.library.adapter.base.BaseMultiItemQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.customer.widget.RecyclerViewItemSpace;
import com.linc.platform.common.step.StepBaseEntry;
import com.linc.platform.common.step.StepTypeEntry;
import com.linc.platform.common.task.PriorityEntry;
import com.linc.platform.foundation.model.OrderTypeEntry;
import com.linc.platform.generaltask.model.GeneralTaskViewEntry;
import com.linc.platform.pick.model.PickTaskViewEntry;
import com.linc.platform.pick.model.PickTypeEntry;
import com.linc.platform.pick.model.PickWayEntry;
import com.linc.platform.putback.model.PutBackReferenceEntry;
import com.linc.platform.putback.model.PutBackTaskEntry;
import com.linc.platform.utils.CollectionUtil;
import com.linc.platform.utils.Lists;
import com.linc.platform.utils.Logger;
import com.linc.platform.utils.StringUtil;
import com.lt.linc.R;
import com.lt.linc.pick.tasklist.PickTaskSuggestPalletMaterialAdapter;

import java.util.ArrayList;
import java.util.List;

/**
 * @Description:
 * @Author: Dennis
 * @CreateDate: 2023/1/31
 */
public class PickAndPutBackTaskAdapter extends BaseMultiItemQuickAdapter<GeneralTaskViewEntry, BaseViewHolder> {

    public static final int PICK_TASK_ITEM = 1;
    public static final int PUT_BACK_TASK_ITEM = 2;

    private Context context;

    public PickAndPutBackTaskAdapter(Context context, List<GeneralTaskViewEntry> data) {
        super(data);
        this.context = context;
        addItemType(PICK_TASK_ITEM, R.layout.item_pick_task_center);
        addItemType(PUT_BACK_TASK_ITEM, R.layout.item_list_put_back_task_item);
    }

    @Override
    protected void convert(BaseViewHolder holder, GeneralTaskViewEntry taskEntry) {
        switch (taskEntry.getItemType()) {
            case PICK_TASK_ITEM:
                bindPickTaskItem(holder, taskEntry);
                break;
            case PUT_BACK_TASK_ITEM:
                bindPutBackTaskItem(holder, taskEntry);
                break;
        }
    }

    private void bindPickTaskItem(BaseViewHolder holder, GeneralTaskViewEntry taskEntry) {
        PickTaskViewEntry task = (PickTaskViewEntry) taskEntry;
        switch (task.status) {
            case EXCEPTION:
                holder.setBackgroundRes(R.id.title_layout, R.color.exception_red);
                holder.setBackgroundRes(R.id.title_img, R.drawable.ic_filter_btn_error);
                holder.setBackgroundRes(R.id.status_image, R.drawable.ic_filter_btn_error_green);
                break;
            case IN_PROGRESS:
                holder.setBackgroundRes(R.id.title_layout, R.color.inprogress_black);
                holder.setBackgroundRes(R.id.title_img, R.drawable.ic_filter_btn_progress);
                holder.setBackgroundRes(R.id.status_image, R.drawable.ic_filter_btn_progress_green);
                break;
            case NEW:
                holder.setBackgroundRes(R.id.title_layout, R.color.status_unlock);
                holder.setBackgroundRes(R.id.title_img, R.drawable.ic_status_new);
                holder.setBackgroundRes(R.id.status_image, R.drawable.ic_status_new_green);
                break;
            case CLOSED:
            case FORCE_CLOSED:
                holder.setBackgroundRes(R.id.title_layout, R.color.status_done);
                holder.setBackgroundRes(R.id.title_img, R.drawable.ic_filter_btn_done);
                holder.setBackgroundRes(R.id.status_image, R.drawable.ic_filter_btn_done_green);
                break;
            default:
                Logger.e("Task status error");
        }

        holder.setVisible(R.id.rush_img, task.isRush);
        holder.setText(R.id.type_txt, getPickType(task.pickType));

        holder.setText(R.id.kind_txt, getPickWay(task.pickWay));

        holder.setText(R.id.task_status_txt, getTaskStatus(task));

        holder.setVisible(R.id.planned_start_time_layout, task.plannedStartTime != null);
        holder.setText(R.id.planned_start_time_txt, StringUtil.dateFormat(task.plannedStartTime));

        holder.setVisible(R.id.planned_end_time_layout, task.plannedEndTime != null);
        holder.setText(R.id.planned_end_time_txt, StringUtil.dateFormat(task.plannedEndTime));

        String createMsg = " | " + task.createdBy + " | " + StringUtil.dateTimeFormat(task.createdWhen);
        holder.setText(R.id.priority_txt, getPriority(task.priority) + createMsg);
        holder.setTextColor(R.id.priority_txt, context.getResources().getColor(getTxtColor(task)));

        holder.setText(R.id.order_no_txt, Stream.of(Lists.ensureNotNull(task.orderIds)).collect(Collectors.joining(", ")));

        holder.setVisible(R.id.pick_mul_dn_to_one_lp_flag_txt, task.isDNForSingleItem() || task.isDNForConsolidation());
        holder.setText(R.id.pick_mul_dn_to_one_lp_flag_txt, task.isDNForSingleItem() ? getString(R.string.title_brackets_single_item) : task.isDNForConsolidation() ? getString(R.string.title_brackets_consolidation) : "");
        holder.setVisible(R.id.carrier_pick_up_tags_ly, needShowCarrierPickUp(task));
        holder.setText(R.id.carrier_pick_up_tags_txt, getCarrierPickupInfo(task));

        holder.setText(R.id.entry_id_txt, task.id)
                .setText(R.id.note_edt, task.description)
                .setVisible(R.id.order_note_ly, Stream.of(task.orders).anyMatch(order -> StringUtil.isNotEmpty(order.pickNote) || StringUtil.isNotEmpty(order.packNote)))
                .setText(R.id.orders_note_txt, getOrderNote(task))
                .addOnClickListener(R.id.detail_btn);

        holder.setVisible(R.id.item_material_info_ly, needShowItemMaterial(task));
        RecyclerView recyclerView = holder.getView(R.id.recycler_view);
        recyclerView.setVisibility(View.GONE);

        PickTaskSuggestPalletMaterialAdapter adapter = new PickTaskSuggestPalletMaterialAdapter();
        adapter.setNewData(task.pickStrategies);

        initRecyclerView(recyclerView, adapter);
        initItemMaterialInfoLy(holder, recyclerView);
    }

    private String getCarrierPickupInfo(PickTaskViewEntry task) {
        List<String> orderCarrierPickUp = getTaskCarrierPickUp(task);
        return (CollectionUtil.isNullOrEmpty(orderCarrierPickUp) ? "" : Stream.of(orderCarrierPickUp).collect(Collectors.joining(", "))) +
                (CollectionUtil.isNullOrEmpty(task.tags) ? "" : " | " + Stream.of(task.tags).collect(Collectors.joining(", ")));
    }

    private List<String> getTaskCarrierPickUp(PickTaskViewEntry task) {
        return Stream.of(task.orders).map(order -> order.truckPickUp).filter(StringUtil::isNotEmpty).distinct().collect(Collectors.toList());
    }

    private String getOrderNote(PickTaskViewEntry task) {
        return Stream.of(task.orders).map(order -> {
            StringBuilder builder = new StringBuilder();
            if (TextUtils.isEmpty(order.pickNote) && TextUtils.isEmpty(order.packNote)) return "";
            builder.append(order.id).append("\r\n").append(TextUtils.isEmpty(order.pickNote) ? "" : "Pick Note:" + order.pickNote + "\r\n").append(TextUtils.isEmpty(order.packNote) ? "" : "Pack Note:" + order.packNote + "\r\n");
            return builder.toString();
        }).collect(Collectors.joining("\r\n"));
    }

    private boolean needShowCarrierPickUp(PickTaskViewEntry task) {
        List<String> orderCarrierPickUp = getTaskCarrierPickUp(task);
        return CollectionUtil.isNotNullOrEmpty(orderCarrierPickUp) || CollectionUtil.isNotNullOrEmpty(task.tags);
    }

    private boolean needShowItemMaterial(PickTaskViewEntry task) {
        return PickWayEntry.ORDER_PICK.equals(task.pickWay) &&
                Stream.of(task.orders).allMatch(order -> OrderTypeEntry.RG.equals(order.orderType))
                && CollectionUtil.isNotNullOrEmpty(task.pickStrategies);
    }

    private int getTaskStatus(PickTaskViewEntry pickTaskView) {
        if (pickTaskView == null || pickTaskView.isNew()) {
            return R.string.label_new;
        }

        if (pickTaskView.isTaskDone()) {
            return R.string.btn_complete;
        }

        StepBaseEntry pickStep = pickTaskView.getStep(StepTypeEntry.PICK);
        if (isStepNew(pickStep)) {
            return R.string.label_new;
        }

        if (isStepInProgress(pickStep)) {
            return R.string.text_picking;
        }

        StepBaseEntry stageStep = pickTaskView.getStep(StepTypeEntry.STAGE);
        if (isStepDone(pickStep) && !isStepDone(stageStep)) {
            return R.string.text_picked;
        }

        if (isStepDone(pickStep) && isStepDone(stageStep)) {
            return R.string.text_staged;
        }

        return R.string.label_new;
    }

    private boolean isStepNew(StepBaseEntry step) {
        return step != null && step.isNew();
    }

    private boolean isStepInProgress(StepBaseEntry step) {
        return step != null && !step.isNew() && !step.isDone();
    }

    private boolean isStepDone(StepBaseEntry step) {
        return step != null && step.isDone();
    }

    private String getPriority(PriorityEntry priority) {
        if (priority == null) {
            return getString(R.string.label_priority_middle);
        }

        switch (priority) {
            case HIGH:
                return getString(R.string.label_priority_high);
            case LOW:
                return getString(R.string.label_priority_low);
            case TOP:
                return getString(R.string.label_priority_top);
            case MIDDLE:
            default:
                return getString(R.string.label_priority_middle);
        }
    }

    private int getPickWay(PickWayEntry pickWay) {
        if (pickWay == null) return R.string.text_none;

        switch (pickWay) {
            case ORDER_PICK:
                return R.string.text_order_pick;
            case WAVE_PICK:
                return R.string.text_wave_pick;
            case WAVE_PICK_BY_ORDER:
                return R.string.text_wave_pick_by_order;
            case WAVE_PICK_BY_ITEM:
                return R.string.text_wave_pick_by_item;
            case BATCH_ORDER_PICK:
                return R.string.text_batch_order_pick;
            case NONE:
            default:
                return R.string.text_none;
        }
    }

    private int getPickType(PickTypeEntry pickType) {
        if (pickType == null) return R.string.text_none;

        switch (pickType) {
            case BULK_PICK:
                return R.string.text_bulk_pick;
            case PALLET_PICK:
                return R.string.text_pallet_pick;
            case PIECE_PICK:
                return R.string.text_piece_pick;
            case CASE_PICK:
                return R.string.text_case_pick;
            case NONE:
            default:
                return R.string.text_none;
        }
    }

    private void initItemMaterialInfoLy(BaseViewHolder holder, RecyclerView recyclerView) {
        holder.getView(R.id.item_material_info_ly).setOnClickListener(v -> recyclerView.setVisibility(recyclerView.getVisibility() == View.GONE ? View.VISIBLE : View.GONE));
    }

    private void initRecyclerView(RecyclerView recyclerView, PickTaskSuggestPalletMaterialAdapter adapter) {
        recyclerView.setLayoutManager(new LinearLayoutManager(context));
        recyclerView.addItemDecoration(new RecyclerViewItemSpace(context));
        recyclerView.setAdapter(adapter);
    }

    private int getTxtColor(PickTaskViewEntry pickTaskView) {
        return (PriorityEntry.HIGH == pickTaskView.priority || PriorityEntry.TOP == pickTaskView.priority) ? R.color.red : R.color.black;
    }

    private String getString(int resId) {
        return context.getResources().getString(resId);
    }

    private void bindPutBackTaskItem(BaseViewHolder holder, GeneralTaskViewEntry itemTask) {
        PutBackTaskEntry taskEntry = (PutBackTaskEntry) itemTask;
        holder.setText(R.id.task_id_txt, taskEntry.id)
                .setText(R.id.create_by_txt, taskEntry.createdBy)
                .setText(R.id.status_txt, taskEntry.status.toString())
                .setText(R.id.reference_txt, getReferenceTxt(taskEntry.reference))
                .setText(R.id.start_time_txt, StringUtil.dateTimeFormat(taskEntry.startTime))
                .setText(R.id.create_time_txt, StringUtil.dateTimeFormat(taskEntry.createdWhen))
                .addOnClickListener(R.id.detail_btn);

        switch (taskEntry.priority) {
            case HIGH:
                holder.setText(R.id.priority_txt, R.string.label_priority_high);
                break;
            case MIDDLE:
                holder.setText(R.id.priority_txt, R.string.label_priority_middle);
                break;
            case LOW:
                holder.setText(R.id.priority_txt, R.string.label_priority_low);
                break;
            case TOP:
                holder.setText(R.id.priority_txt, R.string.label_priority_top);
                break;
            default:
                holder.setText(R.id.priority_txt, R.string.label_priority_middle);

        }

        holder.setTextColor(R.id.priority_txt, context.getResources().getColor(getTxtColor(taskEntry)));

        switch (taskEntry.status) {
            case NEW:
                holder.setBackgroundRes(R.id.title_ly, R.color.status_new);
                break;
            case IN_PROGRESS:
                holder.setBackgroundRes(R.id.title_ly, R.color.status_progress);
                break;
            case CLOSED:
                holder.setBackgroundRes(R.id.title_ly, R.color.status_done);
                break;
            default:
                break;
        }
    }

    private int getTxtColor(PutBackTaskEntry taskEntry) {
        return (PriorityEntry.HIGH.name().equals(taskEntry.priority.name())
                || PriorityEntry.TOP.name().equals(taskEntry.priority.name()))
                ? R.color.red : R.color.black;
    }

    private String getReferenceTxt(PutBackReferenceEntry reference) {
        if (reference == null) {
            return "";
        }
        List<String> strings = new ArrayList<>();
        if (!TextUtils.isEmpty(reference.orderId)) {
            strings.add(reference.orderId);
        }
        for (String pickTaskId : reference.pickTaskIds) {
            strings.add(context.getString(R.string.title_pick_task) + ":" + pickTaskId);
        }
        for (String packTaskId : reference.packTaskIds) {
            strings.add(context.getString(R.string.title_pack_task) + ":" + packTaskId);
        }
        if (reference.loadId != null) {
            strings.add(reference.loadId);
        }
        if (reference.entryId != null) {
            strings.add(reference.entryId);
        }
        return strings.isEmpty() ? "" : StringUtil.combineWithVerticalLine(strings);
    }
}
