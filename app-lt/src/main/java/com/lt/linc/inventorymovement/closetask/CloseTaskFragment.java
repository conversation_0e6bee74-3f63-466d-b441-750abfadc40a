package com.lt.linc.inventorymovement.closetask;

import static com.lt.linc.home.more.assetmanagment.assetassigned.AssetAssignedFragmentKt.FLAG_WHEN_TASK_DONE_ENTER;

import android.content.Intent;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatButton;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.customer.widget.GeneralAlertDialog;
import com.customer.widget.StateButton;
import com.customer.widget.core.LincBaseFragment;
import com.linc.platform.common.task.worktime.TaskWorkTimeDal;
import com.linc.platform.inventorymovement.model.FinishActivityAfterCloseInventoryMovementTaskEventBus;
import com.linc.platform.inventorymovement.model.InventoryMovementTaskEntry;
import com.lt.linc.R;
import com.lt.linc.common.Constant;
import com.lt.linc.home.more.assetmanagment.assetassigned.AssetAssignedActivity;
import com.lt.linc.util.v1styledialog.CenterDialog;

import org.greenrobot.eventbus.EventBus;

/**
 * Author: wujf
 * Time: 2020/11/6
 * Description:
 */
public class CloseTaskFragment extends LincBaseFragment implements CloseTaskContract.View {

    private AppCompatButton closeBtn;
    private StateButton closeTaskBtn;

    public static final String EXTRA_TASK = "extra_task";
    public static final String EXTRA_STYLE = "extra_style";
    public static final int THEME_STYLE_DEFAULT = 0;
    public static final int THEME_STYLE_V1 = 1;
    public static final int THEME_STYLE_HIDE = 2;// Hide close button, system auto trigger close task
    private CloseTaskPresenter mPresenter;
    private IOnCloseTaskSucceedListener mListener;

    public void setOnCloseTaskSucceedListener(IOnCloseTaskSucceedListener listener) {
        if (listener != null) {
            this.mListener = listener;
        }
    }

    public static CloseTaskFragment newInstance(InventoryMovementTaskEntry taskEntry) {

        Bundle args = new Bundle();
        args.putSerializable(EXTRA_TASK, taskEntry);
        CloseTaskFragment fragment = new CloseTaskFragment();
        fragment.setArguments(args);
        return fragment;
    }

    public static CloseTaskFragment newInstance(InventoryMovementTaskEntry taskEntry, int themeStyle) {
        Bundle args = new Bundle();
        args.putSerializable(EXTRA_TASK, taskEntry);
        args.putInt(EXTRA_STYLE, themeStyle);
        CloseTaskFragment fragment = new CloseTaskFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    protected int getLayoutId() {
        return R.layout.fragment_inv_movement_close_task;
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View rootView = super.onCreateView(inflater, container, savedInstanceState);
        bindView();
        return rootView;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        if (null != mPresenter) {
            mPresenter.onDestroy();
            mPresenter = null;
        }
    }

    private void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.close_btn:
                mPresenter.onCloseCollectStep();
                break;
            case R.id.close_task_btn:
                mPresenter.onValidateCloseTask(getFacility());
                break;
        }
    }

    public void onCloseTaskEvent() {
        if (mPresenter != null) {
            mPresenter.onCloseCollectStep();
        }
    }

    @Override
    protected void initView() {
    }

    @Override
    public void closeTaskSucceed(InventoryMovementTaskEntry taskEntry) {
        //IN-45 阶段2启用
//        showAssetAssignedActivity();// Show asset check in page(Asset Assign)
        if (mListener!=null){
            mListener.onCloseTaskSucceed();
        }
        TaskWorkTimeDal.newInstance(taskEntry).endWorkTime();
        EventBus.getDefault().post(new FinishActivityAfterCloseInventoryMovementTaskEventBus());
        getActivity().finish();
    }

    @Override
    public void onViewCreated(View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        Bundle arguments = getArguments();
        mPresenter = new CloseTaskPresenter(this, (InventoryMovementTaskEntry) arguments.getSerializable(EXTRA_TASK));
        mPresenter.onStart();
        if (arguments.getInt(EXTRA_STYLE) == THEME_STYLE_V1) {
            closeTaskBtn.setVisibility(View.VISIBLE);
            closeBtn.setVisibility(View.GONE);
        } else if (arguments.getInt(EXTRA_STYLE) == THEME_STYLE_HIDE) {
            closeTaskBtn.setVisibility(View.GONE);
            closeBtn.setVisibility(View.GONE);
            mPresenter.onCloseCollectStep();
        } else {
            closeTaskBtn.setVisibility(View.GONE);
            closeBtn.setVisibility(View.VISIBLE);
        }
    }

    @Override
    public void showForceCloseCollectStepDialog(String responseErrorMessage, String taskId, String collectId) {
        String contentMessage;
        String titleMessage = getString(R.string.label_inv_movement_force_close);
        contentMessage = responseErrorMessage + getResources().getString(R.string.msg_inv_movement_force_close_collect_step);
        GeneralAlertDialog.createAlertDialog(getActivity(),
                titleMessage,
                contentMessage,
                (dialog, which) -> dialog.dismiss(),
                (dialog, which) -> {
                    mPresenter.onForceCloseCollectStep(taskId, collectId);
                    dialog.dismiss();
                }).show();
    }

    @Override
    public void showForceCloseDropStepDialog(String responseErrorMessage, String taskId, String dropId) {
        String contentMessage;
        String titleMessage = getString(R.string.label_inv_movement_force_close);
        contentMessage = responseErrorMessage + getResources().getString(R.string.msg_inv_movement_force_close_drop_step);
        GeneralAlertDialog.createAlertDialog(getActivity(),
                titleMessage,
                contentMessage,
                (dialog, which) -> dialog.dismiss(),
                (dialog, which) -> {
                    mPresenter.onForceCloseDropStep(taskId, dropId);
                    dialog.dismiss();
                }).show();
    }

    @Override
    public void showForceCloseTaskDialog(String responseErrorMessage, String taskId) {
        if (getActivity() == null || getActivity().isFinishing()) {
            return;
        }
        String contentMessage;
        String titleMessage = getString(R.string.label_inv_movement_force_close);
        contentMessage = responseErrorMessage + getResources().getString(R.string.msg_inv_movement_force_close_task);
        GeneralAlertDialog.createAlertDialog(getActivity(),
                titleMessage,
                contentMessage,
                (dialog, which) -> dialog.dismiss(),
                (dialog, which) -> {
                    mPresenter.onForceCloseTask(taskId);
                    dialog.dismiss();
                }).show();
    }

    @Override
    public void showFindItemInLocationDialog(String itemSpecName) {
        String msg = String.format(getString(R.string.msg_some_lps_with_item_in_this_location), itemSpecName);
        CenterDialog.confirm(getContext(), () -> {},
                        () -> mPresenter.onCloseCollectStep(),
                        null, msg, true,
                        getString(R.string.text_find),
                        getString(R.string.text_not_find),
                        null, null, null, true)
                .show();
    }

    private void showAssetAssignedActivity() {
        Intent intent = new Intent(getActivity(), AssetAssignedActivity.class);
        intent.putExtra(Constant.INTENT_FLAG, FLAG_WHEN_TASK_DONE_ENTER);
        startActivity(intent);
    }

    private void bindView() {
        closeBtn = findViewById(R.id.close_btn);
        closeTaskBtn = findViewById(R.id.close_task_btn);
        findViewById(R.id.close_btn).setOnClickListener(this::onViewClicked);
        findViewById(R.id.close_task_btn).setOnClickListener(this::onViewClicked);
    }
}
