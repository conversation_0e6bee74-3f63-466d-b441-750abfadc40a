package com.lt.linc.inventorymovement.closetask;

import com.linc.platform.core.ProgressView;
import com.linc.platform.inventorymovement.model.InventoryMovementTaskEntry;

/**
 * Author: wujf
 * Time: 2020/11/6
 * Description:
 */
public interface CloseTaskContract {
    interface View extends ProgressView {
        void showForceCloseCollectStepDialog(String responseErrorMessage, String taskId, String collectId);

        void showForceCloseDropStepDialog(String responseErrorMessage, String taskId, String dropId);

        void showForceCloseTaskDialog(String responseErrorMessage, String taskId);

        void showFindItemInLocationDialog(String itemSpecName);

        void closeTaskSucceed(InventoryMovementTaskEntry taskEntry);
    }

    interface Presenter {

        void onStart();

        void onDestroy();

        void onCloseCollectStep();

        void onForceCloseCollectStep(String taskId, String collectId);

        void onCloseDropStep();

        void onForceCloseDropStep(String taskId, String collectId);

        void onCloseTask();

        void onForceCloseTask(String taskId);

    }
}
