package com.lt.linc.inventorymovement.adapter;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.linc.platform.inventory.model.InventoryEntry;
import com.lt.linc.R;

/**
 * @Description:
 * @Author: Owen
 * @CreateDate: 2020/9/14 14:11
 */
public class CollectItemAdapter extends BaseQuickAdapter<InventoryEntry,BaseViewHolder> {
    public CollectItemAdapter() {
        super(R.layout.item_collect_scan_item);
    }

    @Override
    protected void convert(BaseViewHolder helper, InventoryEntry item) {
        helper.setText(R.id.collect_item_text, item.itemSpecName + " | " +item.itemSpecDesc + " | " +item.titleName +" | " +item.qty + item.unitName);
    }
}
