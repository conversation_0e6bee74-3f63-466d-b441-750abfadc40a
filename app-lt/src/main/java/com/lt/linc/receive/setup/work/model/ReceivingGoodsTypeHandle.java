package com.lt.linc.receive.setup.work.model;

import androidx.fragment.app.FragmentManager;

import com.annimon.stream.Stream;
import com.linc.platform.foundation.model.CustomerViewEntry;
import com.linc.platform.receive.model.LpDetailEntry;
import com.linc.platform.receive.model.LpItemEntry;
import com.linc.platform.receive.model.ReceiptItemLineEntry;
import com.linc.platform.utils.CollectionUtil;
import com.linc.platform.utils.Constant;
import com.linc.platform.utils.ResUtil;
import com.lt.linc.R;
import com.lt.linc.receive.setup.work.SelectGoodsTypeDialogFragment;

import java.util.ArrayList;
import java.util.List;

/**
 * Author: Kyle
 * Time: 2021/1/20
 * Description:WISE2018-6539-Inbound Receiving Condition -Android
 */
public class ReceivingGoodsTypeHandle {

    private ReceiptItemLineEntry mItemLineEntry;
    private OnItemLineConditionListener mListener;

    public ReceivingGoodsTypeHandle(OnItemLineConditionListener listener) {
        this.mListener = listener;
    }

    public void setItemLineEntry(ReceiptItemLineEntry itemLineEntry, String defaultGoodsType) {
        this.mItemLineEntry = itemLineEntry;
        if (mListener != null) {
            mListener.onSetItemLineOriginalGoodsType(defaultGoodsType);
        }
    }

    public void initMultipleItemReceivingGoodsType(String defaultGoodsType) {
        if (mListener != null) {
            mListener.onSetItemLineOriginalGoodsType(defaultGoodsType);
        }
    }

    public void onChangeCondition(FragmentManager fragmentManager, CustomerViewEntry customers) {
        boolean canChangeCondition = canChangeCondition();
        if (!canChangeCondition) {
            if (mListener != null) {
                String message = ResUtil.getString(R.string.msg_not_allow_change_condition);
                mListener.onNotAllowToChangeCondition(message);
            }
            return;
        }
        showSelectConditionDialog(fragmentManager, customers);
    }

    public void onMixItemChangeCondition(FragmentManager fragmentManager, CustomerViewEntry customer) {
        showSelectConditionDialog(fragmentManager, customer);
    }

    public void onCratonChangeCondition(FragmentManager fragmentManager, CustomerViewEntry customer) {
        showSelectConditionDialog(fragmentManager, customer);
    }

    public void onPutAwayChangeCondition(FragmentManager fragmentManager, CustomerViewEntry customer) {
        showSelectConditionDialog(fragmentManager, customer);
    }

    public void onLpSetupChangeCondition(FragmentManager fragmentManager, CustomerViewEntry customer, List<LpDetailEntry> lpDetailEntries) {
        if (CollectionUtil.isSingleItemCollection(lpDetailEntries)) {
            LpDetailEntry lpDetailEntry = lpDetailEntries.get(0);
            List<LpItemEntry> itemEntries = lpDetailEntry.itemEntries;
            if (CollectionUtil.isSingleItemCollection(itemEntries)) {
                String itemLineGoodsType = itemEntries.get(0).itemLineGoodsType;
                if (!Stream.of(getUnChangeConditions()).anyMatch(value -> value.equalsIgnoreCase(itemLineGoodsType))) {
                    showSelectConditionDialog(fragmentManager, customer);
                } else {
                    if (mListener != null) {
                        String message = ResUtil.getString(R.string.msg_not_allow_change_condition);
                        mListener.onNotAllowToChangeCondition(message);
                    }
                }
                return;
            }
        }
        showSelectConditionDialog(fragmentManager, customer);
    }

    private List<String> getLpGoodsTypeEntrys(CustomerViewEntry customer) {
        if (customer == null) {
            return new ArrayList<>();
        }
        return customer.getAllowedReceivingGoodsTypes();
    }


    private void showSelectConditionDialog(FragmentManager fragmentManager, CustomerViewEntry customer) {
        SelectGoodsTypeDialogFragment dialogFragment = SelectGoodsTypeDialogFragment.newInstance(getLpGoodsTypeEntrys(customer));
        dialogFragment.setOnSelectItemListener(goodsType -> {
            if (mListener != null) {
                mListener.onChangedCondition(goodsType);
            }
        });
        dialogFragment.show(fragmentManager, SelectGoodsTypeDialogFragment.TAG);

    }

    /**
     * if goodstype is  good,can change condition
     *
     * @return
     */
    private boolean canChangeCondition() {
        if (mItemLineEntry == null) {
            return true;
        }

        if (mItemLineEntry.goodsType == null) {
            return true;
        }
        // GoodsType is Good and customize type can change
        return !Stream.of(getUnChangeConditions()).anyMatch(value -> value.equalsIgnoreCase(mItemLineEntry.goodsType));
    }

    private List<String> getUnChangeConditions() {
        List<String> conditions = new ArrayList<>();
        conditions.add(Constant.GOODS_TYPE_DAMAGE);
        conditions.add(Constant.GOODS_TYPE_CONTAIN_DAMAGE);
        conditions.add(Constant.GOODS_TYPE_ON_HOLD);
        conditions.add(Constant.GOODS_TYPE_REWORK_NEEDED);
        conditions.add(Constant.GOODS_TYPE_QC);
        conditions.add(Constant.GOODS_TYPE_FDA);
        conditions.add(Constant.GOODS_TYPE_RETURN);
        conditions.add(Constant.GOODS_TYPE_B_GRADE);
        conditions.add(Constant.GOODS_TYPE_C_GRADE);
        return conditions;
    }

    public interface OnItemLineConditionListener {
        void onSetItemLineOriginalGoodsType(String goodsType);

        void onChangedCondition(String goodsType);

        void onNotAllowToChangeCondition(String message);
    }

}
