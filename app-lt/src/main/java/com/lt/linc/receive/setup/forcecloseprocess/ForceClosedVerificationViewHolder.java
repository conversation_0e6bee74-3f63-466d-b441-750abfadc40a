package com.lt.linc.receive.setup.forcecloseprocess;

import android.content.Context;
import androidx.appcompat.widget.AppCompatButton;
import androidx.appcompat.widget.AppCompatEditText;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;
import android.view.inputmethod.EditorInfo;
import android.widget.LinearLayout;

import com.customer.widget.RecyclerViewItemSpace;
import com.customer.widget.photo.PhotoWidget;
import com.customer.widget.photo.PhotoWidgetParam;
import com.customer.widget.photo.UploadedCallback;
import com.linc.platform.receive.model.ForceClosedVerificationData;
import com.linc.platform.receive.model.QTYNotMatchResolution;
import com.linc.platform.receive.model.QtyNotMatchItemLine;
import com.linc.platform.utils.StringUtil;
import com.lt.linc.R;

import java.util.List;

/**
 * Created by Gavin
 */
public class ForceClosedVerificationViewHolder extends RecyclerView.ViewHolder {
    protected AppCompatButton submitBtn;
    private RecyclerView recyclerView;
    private AppCompatImageView arrowImg;
    private AppCompatTextView requiredImg;
    private AppCompatTextView reasonTxt;
    private LinearLayout reasonLy;
    private AppCompatEditText commentEdt;
    private PhotoWidget packingListPhotoWidget;
    private AppCompatTextView headerTxt;
    private LinearLayout headerLy;
    private Context context;
    private ForceClosedReasonAdapter adapter;
    private ForceClosedVerificationData data;

    public ForceClosedVerificationViewHolder(View view) {
        super(view);

        recyclerView = (RecyclerView) view.findViewById(R.id.reason_recyclerView);
        arrowImg = (AppCompatImageView) view.findViewById(R.id.img_arrow);
        requiredImg = (AppCompatTextView) view.findViewById(R.id.img_required);
        reasonTxt = (AppCompatTextView) view.findViewById(R.id.force_close_reason_txt);
        reasonLy = (LinearLayout) view.findViewById(R.id.force_close_reason_ly);
        commentEdt = (AppCompatEditText) view.findViewById(R.id.comment_edt);
        packingListPhotoWidget = (PhotoWidget) view.findViewById(R.id.packing_list_photo);
        headerTxt = (AppCompatTextView) view.findViewById(R.id.header);
        headerLy = (LinearLayout) view.findViewById(R.id.header_layout);
        submitBtn = (AppCompatButton) view.findViewById(R.id.submit_btn);
    }

    public void initView(Context context, QtyNotMatchItemLine qtyNotMatchItemLine, String taskId, String receiptId) {
        this.context = context;
        initHeader(qtyNotMatchItemLine, receiptId);
        initData(qtyNotMatchItemLine, taskId);
        initAdapter();
        initRecyclerView();
        initPhotoWidget();
        initCommentEdt();

        reasonLy.setOnClickListener(view1 -> onDropDownStatusChange(recyclerView.getVisibility() == View.VISIBLE));
    }

    private void initHeader(QtyNotMatchItemLine qtyNotMatchItemLine, String receiptId) {
        headerTxt.setText(String.format(context.getString(R.string.text_received_qty_expect_qty), receiptId, qtyNotMatchItemLine.itemName,
                StringUtil.ignorePointZero(qtyNotMatchItemLine.receivedQty), qtyNotMatchItemLine.unitName,
                StringUtil.ignorePointZero(qtyNotMatchItemLine.expectedQty), qtyNotMatchItemLine.unitName));
    }

    private void initCommentEdt() {
        commentEdt.setOnEditorActionListener((v, actionId, event) -> {
            if (EditorInfo.IME_ACTION_DONE == actionId) {
                data.setForceClosedComment(commentEdt.getText().toString());
            }
            return false;
        });
    }

    private void initData(QtyNotMatchItemLine qtyNotMatchItemLine, String taskId) {
        data = new ForceClosedVerificationData();
        data.setQtyNotMatchItemLine(qtyNotMatchItemLine);
        data.setTaskId(taskId);
        data.setForceClosedReasons(QTYNotMatchResolution.getAllReasons());
    }

    private void initPhotoWidget() {
        PhotoWidgetParam param = new PhotoWidgetParam();
        param.setApiInfo("wms", "receive", "takephoto");
        packingListPhotoWidget.setTabParam(param);
        packingListPhotoWidget.setText(R.string.text_packing_list_photo_capture);

        packingListPhotoWidget.setUploadedCallback(new UploadedCallback() {
            @Override
            public void onUploadDone(List<String> fileIds) {
                data.setPackingListPhotos(packingListPhotoWidget.getPhotoNames(true));
            }

            @Override
            public void onDeleteDone(List<String> fileIds) {
                data.setPackingListPhotos(packingListPhotoWidget.getPhotoNames(true));
            }

            @Override
            public void onCaptureDone() {

            }
        });
    }

    private void onReasonSelected(QTYNotMatchResolution reason) {
        onDropDownStatusChange(true);
        data.setForceClosedReason(reason);
        reasonTxt.setText(reason.getName());

        if (QTYNotMatchResolution.OTHER.getName().equals(reason.getName())) {
            requiredImg.setVisibility(View.VISIBLE);
        } else {
            requiredImg.setVisibility(View.GONE);
        }
    }

    private void onDropDownStatusChange(boolean isShowing) {
        recyclerView.setVisibility(isShowing ? View.GONE : View.VISIBLE);
        arrowImg.setBackground(isShowing ? context.getResources().getDrawable(R.drawable.ic_arrow_right) : context.getResources().getDrawable(R.drawable.ic_arrow_down));
    }

    private void initAdapter() {
        List<QTYNotMatchResolution> forceClosedReasons = data.getForceClosedReasons();
        adapter = new ForceClosedReasonAdapter();
        adapter.setNewData(forceClosedReasons);

        adapter.setOnItemChildClickListener((adapter1, view, position) -> {
            if (view.getId() == R.id.reason_view) {
                if (position < 0 || position >= forceClosedReasons.size()) return;
                onReasonSelected(forceClosedReasons.get(position));
            }
        });
    }

    private void initRecyclerView() {
        recyclerView.setLayoutManager(new LinearLayoutManager(context));
        recyclerView.addItemDecoration(new RecyclerViewItemSpace(context));
        recyclerView.setAdapter(adapter);
    }

    public ForceClosedVerificationData getData() {
        return data;
    }

    public List<String> getPhotoIds() {
        return packingListPhotoWidget.getPhotoNames(true);
    }

    public int getUploadingFilesNum() {
        return packingListPhotoWidget.getUploadingFilesNum();
    }

    public String getComment() {
        return commentEdt.getText().toString();
    }

    public void setHeaderColor(int color) {
        headerLy.setBackgroundColor(color);
    }

    public void setSubmitBtnGone() {
        submitBtn.setVisibility(View.GONE);
    }
}
