package com.lt.linc.receive.parcelreceive;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import androidx.appcompat.widget.AppCompatButton;
import androidx.appcompat.widget.AppCompatEditText;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.constraintlayout.widget.Group;

import com.annimon.stream.Collectors;
import com.annimon.stream.Stream;
import com.customer.widget.core.OnBackListener;
import com.customer.widget.photo.PhotoBean;
import com.customer.widget.photo.PhotoWidget;
import com.customer.widget.photo.PhotoWidgetParam;
import com.customer.widget.util.CommUtil;
import com.linc.platform.baseapp.model.LocationEntry;
import com.linc.platform.common.handler.SuccessHandler;
import com.linc.platform.foundation.model.CustomerViewEntry;
import com.linc.platform.foundation.model.ItemSpecEntry;
import com.linc.platform.putaway.work.mulitemputaway.model.ToteObject;
import com.linc.platform.receive.ReceiveCommonHelper;
import com.linc.platform.receive.model.ParcelReceiveWorkData;
import com.linc.platform.receive.model.ReceiveTaskEntry;
import com.linc.platform.receive.presenter.ParcelReceiveWorkPresenter;
import com.linc.platform.receive.presenter.impl.ParcelReceiveWorkPresenterImpl;
import com.linc.platform.receive.view.ParcelReceiveWorkView;
import com.linc.platform.utils.CollectionUtil;
import com.linc.platform.utils.Constant;
import com.linc.platform.utils.CustomerConfigUtil;
import com.linc.platform.utils.Lists;
import com.linc.platform.utils.ToastUtil;
import com.lt.linc.R;
import com.lt.linc.common.BusinessFragment;
import com.lt.linc.pick.newpick.work.ScannerLayout;
import com.lt.linc.receive.setup.work.receivetoputaway.ScannerTool;
import com.lt.linc.toolset.print.lp.PrintLpActivity;
import com.lt.linc.util.UiUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class ParcelReceiveWorkFragment extends BusinessFragment implements OnBackListener, ParcelReceiveWorkView {
    private ConstraintLayout createRNLayout;
    private ScannerLayout createRNLocationScanner;
    private ScannerLayout trackingNoScanner;
    private ScannerLayout referenceScanner;
    private AppCompatButton confirmBtn;
    private AppCompatButton closeStepBtn;

    private ConstraintLayout submitLayout;
    private ScannerLayout locationScanner;
    private AppCompatImageView locationCheckedImg;
    private ScannerLayout itemScanner;
    private AppCompatImageView itemCheckedImg;
    private AppCompatEditText qtyEdit;
    private AppCompatTextView unitTxt;
    private AppCompatTextView itemConditionTxt;
    private PhotoWidget itemPhotoWidget;
    private ScannerLayout lpScanner;
    private AppCompatButton printLPBtn;
    private AppCompatButton submitBtn;
    private Group itemGoodsTypeGroup;

    private ParcelReceiveWorkPresenter presenter;

    public static ParcelReceiveWorkFragment newInstance(ReceiveTaskEntry task) {
        ParcelReceiveWorkFragment fragment = new ParcelReceiveWorkFragment();
        Bundle bundle = new Bundle();
        bundle.putSerializable(ReceiveTaskEntry.TAG, task);
        fragment.setArguments(bundle);
        return fragment;
    }

    @Override
    protected int getLayoutId() {
        return R.layout.fragment_parcel_receive_work;
    }

    @Override
    public int getTabTitleResource() {
        return R.string.title_work;
    }

    @Override
    protected void initView() {
        super.initView();
        bindView();
        presenter = new ParcelReceiveWorkPresenterImpl(this, getBundleObject(ReceiveTaskEntry.TAG));

        //create or get RN
        ScannerTool.onScanned(createRNLocationScanner, barcode -> presenter.processScannedLocation(barcode));
        ScannerTool.onScanned(trackingNoScanner, barcode -> presenter.processScannedTrackingNo(barcode));
        ScannerTool.onScanned(referenceScanner, barcode -> presenter.processScannedReference(barcode));
        confirmBtn.setOnClickListener(v -> presenter.createOrGetRN());
        closeStepBtn.setOnClickListener(v -> presenter.closeStep());

        //submit
        ScannerTool.onScanned(locationScanner, barcode -> presenter.processScannedLocation(barcode));
        ScannerTool.onScanned(itemScanner, barcode -> presenter.processScannedItem(barcode));
        itemConditionTxt.setOnClickListener(v -> {
            List<String> goodsTypes = getGoodsTypes();
            CommUtil.showListDialog(getContext(), R.string.msg_select_item_condition, -1, goodsTypes, which -> presenter.changeItemCondition(goodsTypes.get(which)));
        });
        ScannerTool.onScanned(lpScanner, barcode -> presenter.processScannedLP(barcode));
        printLPBtn.setOnClickListener(v -> {
            Intent intent = new Intent(getContext(), PrintLpActivity.class);
            ParcelReceiveWorkData data = presenter.data();
            intent.putExtra(PrintLpActivity.LP_TYPE, PrintLpActivity.LP_TYPE_ILP_CLP);
            intent.putExtra(PrintLpActivity.REPRINT_CUSTOMER, data.getCustomer());
            intent.putExtra(PrintLpActivity.RECEIPT_ID, data.getReceiptId());
            intent.putExtra(PrintLpActivity.ITEM_SPEC_ID, data.getItemLineItemId());
            getContext().startActivity(intent);
        });
        submitBtn.setOnClickListener(v -> presenter.submitReceive(qtyEdit.getText().toString(), itemPhotoWidget.getPhotoNames(true)));

        presenter.onViewCreated();
    }

    private List<String> getGoodsTypes() {
        CustomerViewEntry customer = presenter.data().getCustomer();
        if (customer != null && CollectionUtil.isNotNullOrEmpty(customer.allowedReceivingGoodsTypes)) {
            return customer.getAllowedReceivingGoodsTypes();
        }

        List<String> items = new ArrayList<>();
        items.add(Constant.GOODS_TYPE_GOOD);
        items.add(Constant.GOODS_TYPE_DAMAGE);
        items.add(Constant.GOODS_TYPE_NEAR_EXPIRY);
        items.add(Constant.GOODS_TYPE_EXPIRED);
        items.add(Constant.GOODS_TYPE_CONTAIN_DAMAGE);
        items.add(Constant.GOODS_TYPE_ON_HOLD);
        items.add(Constant.GOODS_TYPE_REWORK_NEEDED);
        items.add(Constant.GOODS_TYPE_QC);
        items.add(Constant.GOODS_TYPE_FDA);
        items.add(Constant.GOODS_TYPE_RETURN);
        items.add(Constant.GOODS_TYPE_B_GRADE);
        items.add(Constant.GOODS_TYPE_C_GRADE);
        return items;
    }

    @Override
    protected void onTakeOverClick() {
        presenter.takeOverStep();
    }

    @Override
    protected void onStartStepClick() {
        presenter.startStep();
    }

    @Override
    public void toastMsg(String msg) {
        ToastUtil.showToast(msg);
    }

    @Override
    public void showCreateRNView() {
        showView(createRNLayout);
    }

    @Override
    public void refreshCreateRNView(ParcelReceiveWorkData data) {
        refreshLocation(data.getLocation());
        refreshReference(data.getReference());
        refreshTrackingNo(data.getTrackingNo());
        refreshFocus(data);
    }

    @Override
    public void refreshFocus(ParcelReceiveWorkData data) {
        if (UiUtil.isVisible(createRNLayout)) {

            if (data.getLocation() == null) {
                createRNLocationScanner.requestEdtFocus();
            } else if (TextUtils.isEmpty(data.getTrackingNo())) {
                focusTrackingNoScanner();
            } else if (TextUtils.isEmpty(data.getReference())) {
                focusReferenceScanner();
            }

            return;
        }

        if (data.getLocation() == null) {
            locationScanner.requestEdtFocus();
        } else if (data.getItem() == null) {
            itemScanner.requestEdtFocus();
        } else if (TextUtils.isEmpty(qtyEdit.getText())) {
            UiUtil.requestFocus(qtyEdit);
        } else if (data.getLPObject() == null) {
            lpScanner.requestEdtFocus();
        }
    }

    @Override
    public void refreshTrackingNo(String trackingNo) {
        trackingNoScanner.setText(trackingNo);
    }

    @Override
    public void focusTrackingNoScanner() {
        trackingNoScanner.requestEdtFocus();
    }

    @Override
    public void refreshReference(String reference) {
        referenceScanner.setText(reference);
    }

    @Override
    public void focusReferenceScanner() {
        referenceScanner.requestEdtFocus();
    }

    @Override
    public void showSubmitReceiveView() {
        showView(submitLayout);
    }

    @Override
    public void refreshSubmitReceiveView(ParcelReceiveWorkData data) {
        refreshLocation(data.getLocation());
        refreshItem(data.getItem());
        refreshLP(data.getLPObject());
        refreshItemCondition(data.getItemCondition());
        setManualEntry(data.getCustomer());
        refreshUnit(data.getItemLine().unitName);
        refreshFocus(data);
    }

    @Override
    public void refreshLocation(LocationEntry location) {
        locationScanner.setText(location == null ? "" : location.name);
        locationCheckedImg.setVisibility(location == null ? View.GONE : View.VISIBLE);
        createRNLocationScanner.setText(location == null ? "" : location.name);
    }

    @Override
    public void showSelectLocationDialog(List<LocationEntry> locations, SuccessHandler<LocationEntry> handler) {
        List<String> locationNames = Stream.of(locations).map(location -> location.name + " (" + location.type.name() + ")").collect(Collectors.toList());
        CommUtil.showListDialog(getContext(), R.string.title_select_location, -1, locationNames, which -> handler.onSuccess(locations.get(which)));
    }

    @Override
    public void showSelectItemDialog(List<ItemSpecEntry> items, SuccessHandler<ItemSpecEntry> handler) {
        List<String> itemNames = Stream.of(items).map(item -> item.name).collect(Collectors.toList());
        CommUtil.showListDialog(getContext(), R.string.select_item, -1, itemNames, which -> handler.onSuccess(items.get(which)));
    }

    @Override
    public void refreshItem(ItemSpecEntry item) {
        itemScanner.setText(item == null ? "" : item.name);
        itemCheckedImg.setVisibility(item == null ? View.GONE : View.VISIBLE);
    }

    @Override
    public void refreshUnit(String unitName) {
        unitTxt.setText(unitName);
    }

    @Override
    public void refreshItemCondition(String itemCondition) {
        itemConditionTxt.setText(itemCondition);
        if (isShowPhotoWidget(itemCondition)) {
            itemPhotoWidget.setVisibility(View.VISIBLE);
        } else {
            itemPhotoWidget.setVisibility(View.GONE);
            for (PhotoBean photoBean : Lists.ensureNotNull(itemPhotoWidget.photoScrollBar.getPhotoBeans())) {
                itemPhotoWidget.photoScrollBar.delPhotoFiles(photoBean);
            }
        }
    }

    private boolean isShowPhotoWidget(String itemCondition) {
        return Constant.GOODS_TYPE_DAMAGE.equalsIgnoreCase(itemCondition)
                || Constant.GOODS_TYPE_CONTAIN_DAMAGE.equalsIgnoreCase(itemCondition);
    }

    @Override
    public void refreshLP(ToteObject lpObject) {
        lpScanner.setText(lpObject == null ? "" : lpObject.barcode);
    }

    @Override
    public void setManualEntry(CustomerViewEntry customer) {
        boolean allowManualEntry = CustomerConfigUtil.allowManualEntry(customer);
        createRNLocationScanner.allowManualEntry(allowManualEntry);
        trackingNoScanner.allowManualEntry(allowManualEntry);
        referenceScanner.allowManualEntry(allowManualEntry);

        locationScanner.allowManualEntry(allowManualEntry);
        itemScanner.allowManualEntry(allowManualEntry);
        qtyEdit.setEnabled(allowManualEntry);
        lpScanner.allowManualEntry(allowManualEntry);
        refreshItemCondition(ReceiveCommonHelper.getDefaultGoodsType(presenter.data().getItemLine(), customer));
        if (ReceiveCommonHelper.enableShowGoodsTypeView(customer)) {
            itemGoodsTypeGroup.setVisibility(View.VISIBLE);
        } else {
            itemGoodsTypeGroup.setVisibility(View.GONE);
        }
    }

    private void bindView() {
        createRNLayout = bindLayout(findViewById(R.id.create_rn_layout));
        createRNLocationScanner = findViewById(R.id.create_rn_location_scanner);
        trackingNoScanner = findViewById(R.id.tracking_no_scanner);
        referenceScanner = findViewById(R.id.reference_scanner);
        confirmBtn = findViewById(R.id.confirm_btn);
        closeStepBtn = findViewById(R.id.close_step_btn);

        submitLayout = bindLayout(findViewById(R.id.submit_parcel_receive_layout));
        locationScanner = findViewById(R.id.location_scanner);
        locationCheckedImg = findViewById(R.id.location_checked_img);
        itemScanner = findViewById(R.id.item_scanner);
        itemCheckedImg = findViewById(R.id.item_checked_img);
        qtyEdit = findViewById(R.id.receive_qty_edit);
        unitTxt = findViewById(R.id.unit_txt);
        itemConditionTxt = findViewById(R.id.item_condition_txt);
        itemPhotoWidget = findViewById(R.id.photo_widget);
        lpScanner = findViewById(R.id.lp_scanner);
        printLPBtn = findViewById(R.id.print_lp_btn);
        submitBtn = findViewById(R.id.submit_btn);
        itemGoodsTypeGroup = findViewById(R.id.item_goods_type_group);

        initItemPhotoWidget();
    }

    private void initItemPhotoWidget() {
        PhotoWidgetParam param = new PhotoWidgetParam();
        param.setApiInfo("wms", "receive", "takephoto");
        itemPhotoWidget.setTabParam(param);
    }

    @Override
    public void onBackPressed() {
        if (UiUtil.isVisible(submitLayout)) {
            showView(createRNLayout);
        } else {
            getActivity().finish();
        }
    }
}
