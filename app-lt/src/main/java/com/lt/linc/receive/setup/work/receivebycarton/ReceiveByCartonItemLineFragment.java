package com.lt.linc.receive.setup.work.receivebycarton;

import android.content.Intent;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.view.View;

import com.annimon.stream.Stream;
import com.customer.widget.QuickScanner;
import com.customer.widget.RecyclerViewItemSpace;
import com.customer.widget.core.LincBaseActivity;
import com.customer.widget.core.LincBaseFragment;
import com.customer.widget.util.CommUtil;
import com.linc.platform.common.customer.ManualInputOperation;
import com.linc.platform.common.customer.ManualInputStepType;
import com.linc.platform.common.customer.ManualInputTaskType;
import com.linc.platform.common.customer.PermissionTag;
import com.linc.platform.common.handler.DoneHandler;
import com.linc.platform.core.LocalPersistence;
import com.linc.platform.foundation.model.UnitEntry;
import com.linc.platform.receive.model.ReceiptItemLineEntry;
import com.linc.platform.receive.presenter.ReceiveByCartonPresenter;
import com.linc.platform.receive.view.ReceiveByCaronItemLineView;
import com.linc.platform.toolset.scansn.ScanSNParam;
import com.linc.platform.utils.StringUtil;
import com.linc.platform.utils.ToastUtil;
import com.lt.linc.R;
import com.lt.linc.receive.item.CollectItemDimensionActivity;
import com.lt.linc.receive.setup.ItemPropertiesDialogFragment;
import com.lt.linc.toolset.scansn.ScanSNDialog;
import com.qmuiteam.qmui.widget.dialog.QMUIDialog;

import java.util.Date;
import java.util.List;

/**
 * Created by Gavin
 */
public class ReceiveByCartonItemLineFragment extends LincBaseFragment implements ReceiveByCaronItemLineView {
    private ReceiveByCartonPresenter presenter;
    private ReceiveByCartonItemLineAdapter adapter;
    private QuickScanner scanEdt;
    private QuickScanner itemFilterScanner;
    private RecyclerView recyclerView;

    public static ReceiveByCartonItemLineFragment newInstance() {
        return new ReceiveByCartonItemLineFragment();
    }

    @Override
    protected int getLayoutId() {
        return R.layout.fragment_receive_by_carton_item_list;
    }

    @Override
    protected void initView() {
        initScanItemEdt();
        initItemFilterScanner();
        initAdapter();
        initRecyclerView();
        initBtn();
    }

    private void initItemFilterScanner() {
        itemFilterScanner = findViewById(R.id.item_filter_edt);
        itemFilterScanner.setPermissionTag(PermissionTag.create(presenter.getCustomer(), ManualInputTaskType.RECEIVE, ManualInputStepType.Receive_SetUp, ManualInputOperation.SCAN_ITEM));
        itemFilterScanner.setHintText(getContext().getString(R.string.hint_please_scan_item_label));
        itemFilterScanner.setScanEvent((view,data)  -> {
            itemFilterScanner.reset();
            presenter.sortItemLineByItem(data);
        });
    }

    private void initBtn() {
        findViewById(R.id.back_btn).setOnClickListener(v -> {
            if (presenter.isBackToWorkView()) {
                presenter.showReceiveByCaronWorkFragment();
            } else {
                presenter.showCartonReceiveMethodFragment();
            }
        });

        findViewById(R.id.submit_btn).setOnClickListener(v -> presenter.submitSelectedItemLines());
    }

    private void initAdapter() {
        adapter = new ReceiveByCartonItemLineAdapter();
        adapter.setOnItemChildClickListener((adapter1, view, position) ->
        {
            switch (view.getId()) {
                case R.id.remove_btn:
                    showRemoveItemConfirmDialog(position);
                    break;

                case R.id.item_root_layout:
                    if (!presenter.isManualEntryRestriction()) {
                        onSelectItemProperties(presenter.getSelectedItemLines().get(position), position);
                    }
                    break;

                case R.id.collect_sn_btn:
                    showScanSNDialog(position);
                    break;

                default:
            }
        });
        adapter.setCOOScanEvent((position, coo) -> presenter.processScannedCOO(coo, position, false));
    }

    private void showScanSNDialog(int position) {
        ReceiptItemLineEntry itemLine = adapter.getItem(position);
        if (itemLine == null) return;

        ScanSNParam param = new ScanSNParam();
        param.requireQty = (int) itemLine.addedQty;
        param.itemSpecId = itemLine.getItem().id;
        param.scannedSNList.addAll(itemLine.scannedSNList);

        ScanSNDialog dialog = ScanSNDialog.newInstance(param, R.layout.dialog_lp_setup_scan_sn);

        dialog.needValidateSN = true;
        if(presenter.getCustomer()!=null){
            dialog.customerId = presenter.getCustomer().orgId;
        }
        dialog.setSubmitListener(snList -> presenter.processScannedSNList(position, snList));
        dialog.show(getActivity().getSupportFragmentManager(), "scanSN");
    }

    private void showRemoveItemConfirmDialog(int position) {
        ReceiptItemLineEntry item = adapter.getItem(position);
        if (item == null) return;

        CommUtil.getConfirmDialog(getContext(), String.format(getString(R.string.confirm_remove_item_xx), item.name),
                (dialog, index) -> dialog.dismiss(),
                (dialog, index) -> {
                    presenter.removeSelectedItemLine(position);
                    dialog.dismiss();
                }).show();
    }

    private void initRecyclerView() {
        recyclerView = findViewById(R.id.item_recycler_view);
        recyclerView.setLayoutManager(new LinearLayoutManager(getContext()));
        recyclerView.addItemDecoration(new RecyclerViewItemSpace(getContext()));
        recyclerView.setAdapter(adapter);
    }

    private void initScanItemEdt() {
        scanEdt = findViewById(R.id.item_label_edt);
        scanEdt.setPermissionTag(PermissionTag.create(presenter.getCustomer(), ManualInputTaskType.RECEIVE, ManualInputStepType.Receive_SetUp, ManualInputOperation.SCAN_ITEM));
        scanEdt.setHintText(getContext().getString(R.string.hint_please_scan_item_label));
        scanEdt.setScanEvent((view,data)  -> {
            scanEdt.reset();
            presenter.onItemScanned(data, LocalPersistence.isReceiveOneQtySubmit(getContext()));
        });
    }

    @Override
    public void onResume() {
        super.onResume();
        presenter.loadTaskItemLineDetail();

        adapter.setNewData(presenter.getSelectedItemLines());
        if (scanEdt != null) {
            scanEdt.reset();
            scanEdt.setVisibility(presenter.getCarton().isNotEmpty() ? View.GONE : View.VISIBLE);
        }

        if (itemFilterScanner != null) {
            itemFilterScanner.setVisibility(presenter.getCarton().isNotEmpty() ? View.VISIBLE : View.GONE);
        }
    }

    public void setPresenter(ReceiveByCartonPresenter presenter) {
        this.presenter = presenter;
        presenter.setReceiveByCaronItemLineView(this);
    }

    @Override
    public void onItemNotFound(String itemLabel) {
        ToastUtil.showToast(String.format(getString(R.string.msg_item_line_not_found_format), itemLabel));
    }

    @Override
    public void onFoundMultiItemLine(List<ReceiptItemLineEntry> filterItemLine) {
        if (getActivity() == null || getActivity().isFinishing()) return;
        List<String> items = Stream.of(filterItemLine).map(v -> v.name + " " + StringUtil.ignorePointZero(v.qty) + v.unitName + " (" + v.receiptId + ")").toList();
        CommUtil.showListDialog(getContext(), R.string.title_select_item_line, -1, items,
                which -> presenter.onItemLineFound(filterItemLine.get(which), LocalPersistence.isReceiveOneQtySubmit(getContext())));
    }

    @Override
    public void refreshCartonItemLineList(List<ReceiptItemLineEntry> itemLines) {
        adapter.setNewData(itemLines);
    }

    @Override
    public void onItemLineFound(ReceiptItemLineEntry receiptItemLineEntry, List<ReceiptItemLineEntry> selectedItemLines) {
        adapter.setNewData(selectedItemLines);
    }

    @Override
    public void onItemLineAlreadyAdded(ReceiptItemLineEntry receiptItemLineEntry) {
        ToastUtil.showToast(String.format(getString(R.string.msg_item_line_already_added), receiptItemLineEntry.itemSpecInfo.itemSpecName));
    }

    private void onSelectItemProperties(ReceiptItemLineEntry receiptItemLineEntry, int index) {
        ItemPropertiesDialogFragment uomQtyFragmentDialog
                = ItemPropertiesDialogFragment.newInstance(receiptItemLineEntry.itemSpecInfo.itemUoms, null,
                receiptItemLineEntry.addedQty, null, null);
        uomQtyFragmentDialog.setItemUomQtyCheck(new ItemPropertiesDialogFragment.ItemUomQtyCheck() {
            @Override
            public void onSubmit(UnitEntry unitEntry, String qty, String lotNo, String selfLifDays, Date expirationDate, Date mfgDate) {
                presenter.validateSelectedItemLine(receiptItemLineEntry, unitEntry, Double.parseDouble(qty), index);
            }

            @Override
            public void onCancel() {
            }
        });

        uomQtyFragmentDialog.setCancelable(false);
        uomQtyFragmentDialog.show(getFragmentManager(), "uom");
    }

    @Override
    public void validatePassed(ReceiptItemLineEntry receiptItemLineEntry, UnitEntry unitEntry, double addedQty, int index) {
        receiptItemLineEntry.addedQty = addedQty;
        receiptItemLineEntry.addedUnit = unitEntry.getName();
        receiptItemLineEntry.itemSpecInfo.selectUom = unitEntry;

        recyclerView.scrollToPosition(index);
        adapter.notifyItemChanged(index);
    }

    @Override
    public void validateFailed(String error, Integer index) {
        ToastUtil.showToast(error);
        if (index != null) {
            adapter.remove(index);
        }
    }

    @Override
    public void startCollectItemDimension(ReceiptItemLineEntry receiptItemLineEntry) {
        if (getActivity() == null || getActivity().isFinishing()) return;
        CommUtil.getConfirmDialog(getContext(), getString(R.string.msg_item_dimension_has_not_been_updated), (dialog, index) -> dialog.dismiss(), (dialog, index) -> {
            Intent intent = new Intent();
            intent.setClass(getContext(), CollectItemDimensionActivity.class);
            intent.putExtra(ReceiptItemLineEntry.TAG, receiptItemLineEntry);
            startActivity(intent);
            dialog.dismiss();
        }).show();
    }

    @Override
    public void showIQCItemAlert() {
        if (getActivity() == null || getActivity().isFinishing()) return;
        CommUtil.getOkDialog(getContext(), getString(R.string.msg_iqc_item_receive_alert)).show();
    }

    @Override
    public void showToast(String errorMessage) {
        ToastUtil.showToast(errorMessage);
    }

    @Override
    public void showRemoveIQCItemConfirmDialog() {
        CommUtil.getOkDialog(getContext(), getString(R.string.msg_please_remove_iqc_item)).show();
    }

    @Override
    public void showScanCOODialog(int itemLinePosition) {
        ScanCOODialog dialog = ScanCOODialog.newInstance();
        dialog.setOnCOOScanEvent(coo -> presenter.processScannedCOO(coo, itemLinePosition, true));
        dialog.show(((LincBaseActivity) getContext()).getSupportFragmentManager(), "scanCOO");
    }

    @Override
    public void showScanSNConfirmDialog(String msg, DoneHandler doneHandler) {
        new QMUIDialog.MessageDialogBuilder(getContext())
                .setTitle(R.string.msg_scan_sn)
                .setMessage(msg)
                .addAction(R.string.btn_skip, (dlg, index) -> {
                    doneHandler.onDone();
                    dlg.dismiss();
                })
                .addAction(R.string.title_btn_continue, (dlg, index) -> dlg.dismiss())
                .create()
                .show();
    }

    @Override
    public void startScanSN(int index) {
        showScanSNDialog(index);
    }
}
