package com.lt.linc.receive_v1.lpsetup.work.collectiteminfo

import android.text.TextUtils
import com.annimon.stream.Stream
import com.customer.widget.GridChipItem
import com.linc.platform.baseapp.model.BaseItemDimension
import com.linc.platform.baseapp.model.CsMeasureData
import com.linc.platform.baseapp.model.FacilityEquipmentStatus
import com.linc.platform.baseapp.model.FacilityEquipmentView
import com.linc.platform.common.lp.SceneEntry
import com.linc.platform.foundation.model.*
import com.linc.platform.foundation.model.lptemplate.SingleItemLpTemplateEntry
import com.linc.platform.foundation.model.lptemplate.SingleItemLpTemplateSearchEntry
import com.linc.platform.foundation.model.lptemplate.SingleItemLpTemplateStatusEntry
import com.linc.platform.foundation.model.lptemplate.SingleItemLpTemplateUpdateEntry
import com.linc.platform.http.ShortConnectionInterceptor
import com.linc.platform.print.api.PrinterWhApi
import com.linc.platform.print.model.ItemDimension
import com.linc.platform.utils.StringUtil
import com.linc.platform.utils.UnitConvetUtil
import com.lt.linc.R
import com.lt.linc.common.extensions.safeCount
import com.lt.linc.common.extensions.toException
import com.lt.linc.common.mvi.ReactiveViewModel
import com.lt.linc.common.mvi.mapDataToUi
import com.lt.linc.common.mvi.subscribe
import com.lt.linc.common.mvi.subscribeNotNull
import com.lt.linc.common.mvvm.kotlin.extensions.*
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.firstOrNull
import java.util.*
import kotlin.math.ceil

/**
 * <AUTHOR>
 * @date 2022/09
 */

class CollectItemInfoViewModel(
    val itemSpec: ItemSpecEntry,
    val recurringStorageRateByPallet: Boolean,
    val needCollectItemSmallParcelPackaging: Boolean,
    val canNewUom: Boolean,
    val isCollectItemGroupAtReceiving: Boolean,
    initialDataState: CollectItemInfoDataState = CollectItemInfoDataState(),
    initialUiState: CollectItemInfoUiState = CollectItemInfoUiState(),
    private val repository: CollectItemInfoRepository = CollectItemInfoRepository(),
) : ReactiveViewModel<CollectItemInfoDataState, CollectItemInfoUiState>(initialDataState, initialUiState) {

    private val itemSpecId get() = itemSpec.id
    private var cacheDimension: BaseItemDimension? = null

    init {
        setInitialState()
        initData()
        autoUpdateShowInsideQty()
        autoUpdateUnitInfo()
        mapDataToUi()
    }

    private fun setInitialState() {
        setDataState {
            copy(
                description = itemSpec.desc,
                shortDescription = itemSpec.shortDescription,
                upcCode = itemSpec.upcCode,
                upcCodeCase = itemSpec.upcCodeCase,
                smallParcelPackaging = itemSpec.smallParcelPackaging
            )
        }
    }

    private fun initData() = launch {
        setDataState { copy(newUomDefinitionName = null, isNewUom = false) }
        if (isCollectItemGroupAtReceiving) {
            requestAwait(repository.getItemGroups(itemSpec.customerId)).onSuccess {
                setDataState { copy(selectItemGroup = it?.find { item->  item.id == itemSpec.groupId}, itemGroups = it?: listOf()) }
            }
        }
        val (uomResult, unitResult) = requestAllAwait(repository.getUomDefinition(itemSpecId), repository.getItemUnit(itemSpecId))
        val uomList = uomResult.getOrNull()
        if (canNewUom) {
            UomEntry().apply {
                name = getString(R.string.new_uom)
                uomList?.add(this)
            }
        }
        val unitList = unitResult.getOrNull()
        if (uomList.isNullOrEmpty() || unitList.isNullOrEmpty()) return@launch
        val baseUnit = unitList.firstOrNull { it.isBaseUnit } ?: unitList.first()
        val isContain = Stream.ofNullable(uomList).anyMatch { uom -> uom.id == baseUnit.id }
        if (!isContain) {
            uomList.add(0, UomEntry().apply { this.id = baseUnit.id; this.name = baseUnit.name })
        }
        val defaultUom = uomList.firstOrNull { it.name == baseUnit.name } ?: uomList.first()
        setDataState {
            copy(uomDefinitions = uomResult.getOrNull() ?: listOf(), unitList = unitResult.getOrNull() ?: listOf(), selectedUomDefinition = defaultUom)
        }
        if (recurringStorageRateByPallet) {
            awaitDataState()
            updateUoms(unitList[0])
            unitList.let {
                val itemUoms = it.map { unit ->
                    GridChipItem<UnitEntry>(
                        id = unit.id,
                        item = unit,
                        title = unit.name,
                        selected = TextUtils.equals(unitList[0].id, unit.id))
                }
                setUiStateAwait { copy(itemUoms = itemUoms) }
            }
            loadItemLpTemplate()
        }
    }

    private fun autoUpdateShowInsideQty() = subscribe(CollectItemInfoDataState::unitOfSelectedUom) {
        setDataState {
            val needCollectInsideQty = selectedUomDefinition != null && it == null
            copy(insideQtyCompose = insideQtyCompose.copy(needCollect = needCollectInsideQty))
        }
    }

    private fun autoUpdateUnitInfo() = subscribeNotNull(CollectItemInfoDataState::unitOfSelectedUom) {
        if (dataState.isCubicScannerConnected) return@subscribeNotNull
        val length = UnitConvetUtil.convertToIN(it.length, it.linearUnit)
        val width = UnitConvetUtil.convertToIN(it.width, it.linearUnit)
        val height = UnitConvetUtil.convertToIN(it.height, it.linearUnit)
        val weight = UnitConvetUtil.convertToPound(it.weight, it.weightUnit)
        setDataState { copy(length = length, width = width, height = height, weight = weight) }
    }

    private fun mapDataToUi() {
        mapDataToUi(CollectItemInfoDataState::description, CollectItemInfoUiState::description) { it }
        mapDataToUi(CollectItemInfoDataState::shortDescription, CollectItemInfoUiState::shortDescription) { it }
        mapDataToUi(CollectItemInfoDataState::upcCode, CollectItemInfoUiState::upcCode) { it }
        mapDataToUi(CollectItemInfoDataState::upcCodeCase, CollectItemInfoUiState::upcCodeCase) { it }
        mapDataToUi(CollectItemInfoDataState::smallParcelPackaging, CollectItemInfoUiState::smallParcelPackaging) { it }
        mapDataToUi(CollectItemInfoDataState::selectedUomDefinition, CollectItemInfoUiState::selectedUomDefinition) { it }
        mapDataToUi(CollectItemInfoDataState::length, CollectItemInfoUiState::length) { it }
        mapDataToUi(CollectItemInfoDataState::width, CollectItemInfoUiState::width) { it }
        mapDataToUi(CollectItemInfoDataState::height, CollectItemInfoUiState::height) { it }
        mapDataToUi(CollectItemInfoDataState::weight, CollectItemInfoUiState::weight) { it }
        mapDataToUi(CollectItemInfoDataState::insideQtyCompose, CollectItemInfoUiState::insideQtyCompose) { it }
        mapDataToUi(CollectItemInfoDataState::isNewUom, CollectItemInfoUiState::isNewUom) { it }
        mapDataToUi(CollectItemInfoDataState::selectItemGroup, CollectItemInfoUiState::selectItemGroup) { it }
        mapDataToUi(CollectItemInfoDataState::isCubicScannerConnected, CollectItemInfoUiState::isCubicScannerConnected) { it }
        mapDataToUi(CollectItemInfoDataState::selectedCubicServerName, CollectItemInfoUiState::selectedCubicServerName) { it }
        mapDataToUi(CollectItemInfoDataState::layer, CollectItemInfoUiState::layer) { it }
    }

    fun addItemPhoto(photoId: String) {
        request(repository.createItemPhoto(photoId, itemSpecId)) {
            setDataState { copy(itemInfoUpdated = true) }
        }
    }

    fun deleteItemPhotos(photoIds: List<String>) {
        val requests = photoIds.map { repository.deleteItemPhoto(it) }.toTypedArray()
        requestAll(*requests) {
            setDataState { copy(itemInfoUpdated = true) }
        }
    }

    fun setNewUomName(uomName: String) {
        setDataState { copy(newUomDefinitionName = uomName) }
    }

    fun setIsNewUom(isNewUom: Boolean) {
        setDataState { copy(isNewUom = isNewUom) }
    }

    fun setLayerValue(layer: String) {
        setDataState { copy(layer = layer) }
        updateDimensionForLayer()
    }

    fun setDescription(desc: String) {
        setDataState { copy(description = desc) }
    }

    fun setShortDescription(shortDesc: String) {
        setDataState { copy(shortDescription = shortDesc) }
    }

    fun setUpc(upc: String) {
        setDataState { copy(upcCode = upc) }
    }

    fun setUpcCase(upcCase: String) {
        setDataState { copy(upcCodeCase = upcCase) }
    }

    fun setSmallParcelPackaging(smallParcelPackaging: SmallParcelPackaging) {
        setDataState { copy(smallParcelPackaging = smallParcelPackaging) }
    }

    fun getCubeScannerByBarcode(barcode: String?) {
        barcode ?: return
        launch {
            val result = requestAwait(repository.getCubeScannerByBarcode(barcode))
            if (result.isFailure) return@launch
            setDataStateAwait { copy(selectedCubicScanner = result.getOrNull()) }
            refreshItemDimension()
        }
    }

    fun toggleCubitScanner(onMultiServer: (List<FacilityEquipmentView>) -> Flow<FacilityEquipmentView?>) {
        launch { getItemDimensionOfCubicScanner(onMultiServer) }
    }

    fun removeCubeScanner() {
        setDataState { copy(selectedCubicScanner = null) }
        setLayerValue("1")
    }

    fun toggleCubitScannerRefresh() {
        launch { refreshItemDimension() }
    }

    private fun isV3Version(version: String?): Boolean {
        return version?.startsWith("V3", ignoreCase = true) == true
    }

    private suspend fun refreshItemDimension(): Boolean {
        val selectedCubicScanner = dataState.selectedCubicScanner
        val api = selectedCubicScanner?.let { initCubitScanHost(it) }
        if (api == null || repository.getItemDimension(selectedCubicScanner.version, api, selectedCubicScanner.barcode) == null) {
            showToast(dataState.selectedCubicServerName + " "+ getString(R.string.msg_no_server_for_version))
            return false
        }

        val result = requestAwait(repository.getItemDimension(selectedCubicScanner.version, api, selectedCubicScanner.barcode)!!)
        val dimension = when (val response = result.getOrNull()) {
            is ItemDimension -> response
            is CsMeasureData -> response.csMeasureData
            is ItemDimensionCollectEntry -> response
            else -> null
        }
        return if (dimension != null) {
            cacheDimension = dimension
            updateDimension()
            true
        } else {
            if (!isV3Version(selectedCubicScanner.version)){
                showToast(R.string.cubit_scan_request_data_error)
            }
            cacheDimension = null
            false
        }
    }

    /**
     * Get item dimension of cubic scanner.
     * @return true when success, false when fail.
     */
    private suspend fun getItemDimensionOfCubicScanner(
        onMultiServer: (List<FacilityEquipmentView>) -> Flow<FacilityEquipmentView?>,
    ): Boolean {
        val result = requestAwait(repository.searchCubitScanner()).onFailure { return false }
        val servers = result.getOrNull()?.equipments?.filter { it.status == FacilityEquipmentStatus.ACTIVE }
        val server = when (servers.safeCount()) {
            0 -> {
                showToast(R.string.msg_server_not_found)
                return false
            }

            1 -> {
                servers!!.first()
            }

            else -> {
                onMultiServer(servers!!).firstOrNull() ?: return false
            }
        }
        setDataStateAwait { copy(selectedCubicScanner = server) }
        setLayerValue("1")
        return refreshItemDimension()
    }

    private fun updateDimension() {
        withDataState {
            cacheDimension?.apply {
                val length = convertToIN(getBaseLength())
                val width = convertToIN(getBaseWidth())
                val layer = (dataState.layer ?: "1").toInt()
                val resultH = getBaseHeight() / layer
                val resultW = getBaseWeight() / layer
                val height = convertToIN(resultH)
                val weight = convertToLB(resultW)
                setDataState {
                    copy(length = length, width = width, height = height, weight = weight)
                }
            }
        }
    }

    private fun updateDimensionForLayer() {
        withDataState {
            cacheDimension?.apply {
                val length = dataState.length
                val width = dataState.width
                val layer = (dataState.layer ?: "1").toInt()
                val resultH = getBaseHeight() / layer
                val resultW = getBaseWeight() / layer
                val height = convertToIN(resultH)
                val weight = convertToLB(resultW)
                setDataState {
                    copy(length = length, width = width, height = height, weight = weight)
                }
            }
        }
    }

    private fun initCubitScanHost(server: FacilityEquipmentView): Any?=
        if (server.version.equals("V3", ignoreCase = true) && !server.barcode.isNullOrEmpty()) {
            repository.itemDimensionApi
        } else if (server.version.equals("V2", ignoreCase = true) && server.printServer != null) {
            server.printServer?.initItemDimensionHostWithInterceptorV2(null)
        } else if (server.version.equals("V1", ignoreCase = true) && server.printServer != null) {
            server.printServer?.initItemDimensionHostWithInterceptor(listOf(ShortConnectionInterceptor()))
        } else
            null

    fun setUom(uom: UomEntry) {
        setDataState { copy(selectedUomDefinition = uom) }
    }

    fun setItemGroup(group: ItemGroupEntry) {
        setDataState { copy(selectItemGroup = group) }
    }

    fun setLength(length: String) {
        setDataState { copy(length = length) }
    }

    fun setWidth(width: String) {
        setDataState { copy(width = width) }
    }

    fun setHeight(height: String) {
        setDataState { copy(height = height) }
    }

    fun setWeight(weight: String) {
        setDataState { copy(weight = weight) }
    }

    fun setInsideQty(qty: String) {
        setDataState { copy(insideQtyCompose = insideQtyCompose.copy(qty = qty)) }
    }

    fun setInsideUom(unit: UnitEntry) {
        setDataState { copy(insideQtyCompose = insideQtyCompose.copy(selectedUnit = unit)) }
    }

    fun update() {
        runCatching { validateUpdate() }.onFailure {
            showToast(it.message!!)
            return
        }
        launch {
            if (dataState.isNewUom) requestAwait(repository.createUomDefinition(UomRequestBody(listOf(itemSpec.customerId), dataState.newUomDefinitionName?: "")))
            val (itemSpecResult, updateResult) = requestAllAwait(getUpdateItemSpecRequest(), getCreateOrUpdateUnitRequest())
            val (uomResult, unitResult) = requestAllAwait(repository.getUomDefinition(itemSpecId), repository.getItemUnit(itemSpecId))
            if (itemSpecResult.isFailure || updateResult.isFailure || uomResult.isFailure) return@launch
            val baseUnit = dataState.unitList.firstOrNull { it.isBaseUnit } ?: dataState.unitList.first()
            val defaultUom = dataState.uomDefinitions.firstOrNull { it.name == baseUnit.name } ?: dataState.uomDefinitions.first()
            val uomList = uomResult.getOrNull()
            if (canNewUom) {
                UomEntry().apply {
                    name = getString(R.string.new_uom)
                    uomList?.add(this)
                }
            }
            setDataState {
                copy(
                    uomDefinitions = uomList?: listOf(),
                    unitList = unitResult.getOrNull() ?: listOf(),
                    itemInfoUpdated = true,
                    selectedCubicScanner = null,
                    selectedUomDefinition = uomDefinitions.firstOrNull(),
                    layer = "1",
                    length = null,
                    width = null,
                    height = null,
                    weight = null,
                    insideQtyCompose = CollectItemInfoDataState.InsideQtyCompose(),
                    newUomDefinitionName = null,
                    isNewUom = false,
                    selectItemGroup = null
                )
            }
            fireEvent { CollectItemInfoUiEvent.RefreshCubitScan }
            fireEvent { CollectItemInfoUiEvent.ItemUpdateSuccess }
        }
    }

    private fun getUpdateItemSpecRequest(): Flow<Void?> {
        val updateEntry = ItemSpecUpdateEntry().apply {
            this.name = itemSpec.name
            this.tags = itemSpec.tags?.map { GroupTypeEntry.valueOf(it.uppercase(Locale.US)) } ?: listOf()
            this.hasSerialNumber = itemSpec.hasSerialNumber
            this.customerId = itemSpec.customerId
            this.updateFrom = UpdateFromEntry.ANDROID
            this.desc = dataState.description
            this.shortDescription = dataState.shortDescription
            this.upcCode = dataState.upcCode
            this.upcCodeCase = dataState.upcCodeCase
            if (isCollectItemGroupAtReceiving) {
                this.groupId = dataState.selectItemGroup?.id
            }
            if (dataState.smallParcelPackaging != null) {
                this.smallParcelPackaging = dataState.smallParcelPackaging
            }
            this.bundle = itemSpec.bundle
            this.status = ItemSpecStatusEntry.ACTIVE
        }
        return repository.updateItemSpec(itemSpecId, updateEntry)
    }

    private fun getCreateOrUpdateUnitRequest(): Flow<Any?> {
        val length = dataState.length!!.toDouble()
        val width = dataState.width!!.toDouble()
        val height = dataState.height!!.toDouble()
        val volume = StringUtil.twoDecimalPointKeep(length * width * height * 0.0005787).toDoubleOrNull()
        return if (dataState.insideQtyCompose.needCollect) {
            val unitEntry = UnitEntry().apply {
                this.itemSpecId = <EMAIL>
                if (dataState.isNewUom) {
                    this.name = dataState.newUomDefinitionName
                } else {
                    this.name = dataState.selectedUomDefinition!!.name
                }
                this.length = length
                this.width = width
                this.height = height
                this.weight = dataState.weight!!.toDouble()
                this.volume = volume
                this.linearUnit = ItemLinearUnit.INCH
                this.weightUnit = ItemWeightUnit.POUND
                this.volumeUnit = ItemVolumeUnit.CU_FT

                this.qty = dataState.insideQtyCompose.qty!!.toDouble()
                this.insideUnitId = dataState.insideQtyCompose.selectedUnit!!.id
            }
            repository.createUnit(unitEntry)
        } else {
            val unitEntry = dataState.unitOfSelectedUom!!.apply {
                this.length = length
                this.width = width
                this.height = height
                this.weight = dataState.weight!!.toDouble()
                this.volume = volume
                this.status = StatusEntry.ENABLE
                this.linearUnit = ItemLinearUnit.INCH
                this.weightUnit = ItemWeightUnit.POUND
                this.volumeUnit = ItemVolumeUnit.CU_FT
            }
            repository.updateUnit(unitEntry)
        }
    }

    @Throws
    private fun validateUpdate() {
        if (dataState.description.isNullOrEmpty()) throw getString(R.string.hint_collect_item_info_scan_or_enter_desc).toException
        if (dataState.shortDescription.isNullOrEmpty()) throw getString(R.string.hint_collect_item_info_scan_or_enter_short_desc).toException
        if (dataState.upcCode.isNullOrEmpty() && dataState.upcCodeCase.isNullOrEmpty()) throw getString(R.string.hint_collect_item_info_scan_or_enter_upc).toException
        if (needCollectItemSmallParcelPackaging && dataState.smallParcelPackaging == null) throw getString(R.string.label_select_small_parcel_packaging).toException
        if (dataState.isNewUom) {
            if (dataState.newUomDefinitionName.isNullOrEmpty()) throw getString(R.string.toast_input_item_base_uom).toException
        } else {
            if (dataState.selectedUomDefinition?.name.isNullOrEmpty()) throw getString(R.string.msg_please_select_oum).toException
        }

        val lengthString = dataState.length
        if (lengthString.isNullOrEmpty()) throw getString(R.string.toast_input_item_length).toException
        val length = lengthString.toDoubleOrNull()
        if (length == null || length <= 0) throw getString(R.string.toast_input_item_length_greater_zero).toException

        val widthString = dataState.width
        if (widthString.isNullOrEmpty()) throw getString(R.string.toast_input_item_width).toException
        val width = widthString.toDoubleOrNull()
        if (width == null || width <= 0) throw getString(R.string.toast_input_item_width_greater_zero).toException

        val heightString = dataState.height
        if (heightString.isNullOrEmpty()) throw getString(R.string.toast_input_item_height).toException
        val height = heightString.toDoubleOrNull()
        if (height == null || height <= 0) throw getString(R.string.toast_input_item_height_greater_zero).toException

        val weightString = dataState.weight
        if (weightString.isNullOrEmpty()) throw getString(R.string.toast_input_item_weight).toException
        val weight = weightString.toDoubleOrNull()
        if (weight == null || weight <= 0) throw getString(R.string.toast_input_item_weight_greater_zero).toException
        if (dataState.insideQtyCompose.needCollect) {
            val insideQtyString = dataState.insideQtyCompose.qty
            if (insideQtyString.isNullOrEmpty()) throw getString(R.string.toast_input_item_base_qty).toException
            val insideQty = insideQtyString.toDoubleOrNull()
            if (insideQty == null || insideQty <= 0) throw getString(R.string.toast_input_item_qty_greater_zero).toException
            if (dataState.insideQtyCompose.selectedUnit == null) {
                throw getString(R.string.please_select_inside_uom).toException
            }
        }
        val defaultTemplates = dataState.itemLpConfigs.filter { template -> template.isDefault }
        if (recurringStorageRateByPallet && defaultTemplates.size < dataState.unitList.size) {
            dataState.itemLpConfigs.find { lpConfig -> lpConfig.isDefault }
                ?: throw getString(R.string.msg_collect_item_info_new_lp_config_tip).toException
        }

        val uom = dataState.unitList.filter { unitEntry -> unitEntry.name == dataState.newUomDefinitionName}
        if (uom.isNotEmpty() && dataState.isNewUom) throw getString(R.string.toast_input_uom_already_exists).toException

        if (isCollectItemGroupAtReceiving && dataState.selectItemGroup == null)
            throw getString(R.string.please_select_item_group).toException
    }


    fun loadItemLpTemplate() {
        launch {
            val singleItemLpTemplateSearchEntry = SingleItemLpTemplateSearchEntry()
            singleItemLpTemplateSearchEntry.itemSpecId = itemSpecId
            singleItemLpTemplateSearchEntry.scene = SceneEntry.INBOUND
            requestAwait(repository.loadItemLpTemplate(singleItemLpTemplateSearchEntry)).onSuccess {
                setDataStateAwait { copy(itemLpConfigs = it ?: listOf()) }
                updateLpTemplatesForUom(dataState.currentUnitForConfig)
            }
        }
    }


    fun updateItemLpTemplate(select: Boolean?, itemLpTemplateEntry: SingleItemLpTemplateEntry?) {
        if (null == itemLpTemplateEntry || itemLpTemplateEntry.isDefault) {
            return
        }
        launch {
            val updateEntry = SingleItemLpTemplateUpdateEntry()
            updateEntry.isDefault = select
            updateEntry.name = itemLpTemplateEntry.name
            updateEntry.unitId = itemLpTemplateEntry.unitId
            updateEntry.lpConfigurationTemplateId = itemLpTemplateEntry.lpConfigurationTemplateId
            updateEntry.itemSpecId = itemLpTemplateEntry.itemSpecId
            updateEntry.status = SingleItemLpTemplateStatusEntry.ENABLE
            requestAwait(repository.updateItemLpTemplate(itemLpTemplateEntry.id, updateEntry)).onSuccess {
                loadItemLpTemplate()
            }.onFailure {
                loadItemLpTemplate()
            }
        }
    }

    fun createUom() {
        runCatching { validateCreateUom() }.onFailure {
            showToast(it.message!!)
            return
        }
        val length = dataState.length!!.toDouble()
        val width = dataState.width!!.toDouble()
        val height = dataState.height!!.toDouble()
        val volume = StringUtil.twoDecimalPointKeep(length * width * height * 0.0005787).toDoubleOrNull()
        val unitEntry = UnitEntry().apply {
            this.itemSpecId = <EMAIL>
            if (dataState.isNewUom) {
                this.name = dataState.newUomDefinitionName
            } else {
                this.name = dataState.selectedUomDefinition!!.name
            }
            this.length = length
            this.width = width
            this.height = height
            this.weight = dataState.weight!!.toDouble()
            this.volume = volume
            this.linearUnit = ItemLinearUnit.INCH
            this.weightUnit = ItemWeightUnit.POUND
            this.volumeUnit = ItemVolumeUnit.CU_FT
            this.qty = dataState.insideQtyCompose.qty!!.toDouble()
            this.insideUnitId = dataState.insideQtyCompose.selectedUnit!!.id
        }
        launch {
            if (dataState.isNewUom) {
                requestAwait(repository.createUomDefinition(UomRequestBody(listOf(itemSpec.customerId), unitEntry.name))).onSuccess {
                    requestAwait(repository.createUnit(unitEntry)).onSuccess {
                        setDataState { copy(insideQtyCompose = CollectItemInfoDataState.InsideQtyCompose()) }
                        initData()
                    }
                }
            } else {
                requestAwait(repository.createUnit(unitEntry)).onSuccess {
                    setDataState { copy(insideQtyCompose = CollectItemInfoDataState.InsideQtyCompose()) }
                    initData()
                }
            }
        }
    }

    @Throws
    private fun validateCreateUom() {
        if (dataState.isNewUom) {
            if (dataState.newUomDefinitionName.isNullOrEmpty()) throw getString(R.string.toast_input_item_base_uom).toException
        } else {
            val uomString = dataState.selectedUomDefinition?.name
            if (uomString.isNullOrEmpty()) throw getString(R.string.toast_input_item_base_uom).toException
        }
        val insideQtyString = dataState.insideQtyCompose.qty
        if (insideQtyString.isNullOrEmpty()) throw getString(R.string.toast_input_item_base_qty).toException
        val insideQty = insideQtyString.toDoubleOrNull()
        if (insideQty == null || insideQty <= 0) throw getString(R.string.toast_input_item_qty_greater_zero).toException
        if (dataState.insideQtyCompose.selectedUnit == null) {
            throw getString(R.string.please_select_inside_uom).toException
        }
        val uom = dataState.unitList.filter { unitEntry -> unitEntry.name == dataState.newUomDefinitionName}
        if (uom.isNotEmpty() && dataState.isNewUom) throw getString(R.string.toast_input_uom_already_exists).toException
    }

    fun updateLpTemplatesForUom(unit: UnitEntry?) {
        unit?.let {
            updateUoms(unit)
            val templatesForUom = dataState.itemLpConfigs.filter { template -> TextUtils.equals(template.unitId, it.id) } ?: listOf()
            val itemLpTemplates = templatesForUom.map { itemLpTemplate ->
                var tempName = ""
                tempName = if (null == itemLpTemplate.singleLpTemplateEntry) {
                    "${itemLpTemplate.name} (${itemLpTemplate.getName()})"
                } else {
                    val ti = ceil(itemLpTemplate.singleLpTemplateEntry.totalQty / itemLpTemplate.singleLpTemplateEntry.layer)
                    "${ti.toInt()}x${itemLpTemplate.singleLpTemplateEntry.layer.toInt()} (${itemLpTemplate.getName()})"
                }
                GridChipItem(id = itemLpTemplate.id, item = itemLpTemplate, title = tempName, selected = itemLpTemplate.isDefault)
            }
            setUiState { copy(itemLpTemplates = itemLpTemplates) }
        }
    }

    private fun updateUoms(unitEntry: UnitEntry) {
        dataState.unitList.let {
            val itemUoms = it.map { unit ->
                GridChipItem<UnitEntry>(id = unit.id, item = unit, title = unit.name, selected = TextUtils.equals(unitEntry.id, unit.id))
            }
            setUiState { copy(itemUoms = itemUoms) }
            setDataState { copy(currentUnitForConfig = unitEntry) }
        }
    }

}