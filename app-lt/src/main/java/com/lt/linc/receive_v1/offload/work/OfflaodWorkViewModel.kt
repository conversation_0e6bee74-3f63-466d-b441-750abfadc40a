package com.lt.linc.receive_v1.offload.work

import android.content.Context
import android.text.TextUtils
import com.linc.platform.baseapp.api.LocationApi
import com.linc.platform.baseapp.model.DockOperateEntry
import com.linc.platform.common.help.FunctionHelpPresenterImpl
import com.linc.platform.common.step.StepStatusEntry
import com.linc.platform.foundation.model.FileTypeEntry
import com.linc.platform.foundation.model.NoteEntry
import com.linc.platform.foundation.model.PhotoGroupEntry
import com.linc.platform.genericstep.api.GenericStepApi
import com.linc.platform.home.more.material.api.MaterialLineCenterApi
import com.linc.platform.home.more.material.model.MaterialLineSearchEntry
import com.linc.platform.home.more.takeover.api.TakeOverTaskApi
import com.linc.platform.home.more.takeovermanage.TaskTakeOverCreateEntry
import com.linc.platform.home.more.taskassign.model.StepAssignmentEntry
import com.linc.platform.idm.api.IdmApi
import com.linc.platform.parcelreceive.model.ReceiptUpdateEntry
import com.linc.platform.receive.api.ReceiptApi
import com.linc.platform.receive.api.ReceiptExceptionApi
import com.linc.platform.receive.api.ReceiptOffloadApi
import com.linc.platform.receive.api.ReceiveTaskApi
import com.linc.platform.receive.model.*
import com.linc.platform.step.api.TaskStepProcessTimeApi
import com.linc.platform.step.model.TaskStepProcessTimeEntry
import com.lt.linc.R
import com.lt.linc.common.mvi.ReactiveViewModel
import com.lt.linc.common.mvvm.kotlin.BaseRepository
import com.lt.linc.common.mvvm.kotlin.extensions.*
import com.lt.linc.common.mvvm.kotlin.shoot
import com.lt.linc.receive_v1.offload.OffloadViewModel
import com.lt.linc.util.SnackType
import com.lt.linc.util.v1widget.ItemType
import com.lt.linc.util.v1widget.UploadPhotoBean

class OffloadWorkViewModel(
    private val repository: WorkStageRepo = WorkStageRepo(),
    private val activityViewModel: OffloadViewModel,
    initialState: OffloadWorkState = OffloadWorkState(taskEntry = activityViewModel.dataState.taskEntry),
    initialUiState: OffloadWorkUiState = OffloadWorkUiState(taskEntry = activityViewModel.dataState.taskEntry.shoot()),
) : ReactiveViewModel<OffloadWorkState, OffloadWorkUiState>(initialState, initialUiState) {

    private val functionHelpPresenterImpl by lazy { FunctionHelpPresenterImpl() }

    private var isHandShotgun = false
    init {
        loadDetail()
    }


    private fun loadDetail() {
        launch {
            requestAwait(repository.getOffloadDetail(activityViewModel.dataState.stepId)).onSuccess { offloadDetailEntry ->
                offloadDetailEntry?.let { detail ->
                    isHandShotgun = detail.isHandShotgun
                    setDataStateAwait { dataState.copy(detailEntry = detail) }
                    var photos: List<UploadPhotoBean> = mutableListOf()
                    detail.photoGroupEntries.forEach { item ->
                        if (TextUtils.equals(item.type, PhotoGroupEntry.PHOTO_TYPE_OFFLOAD)) {
                            photos =
                                item.photoIds.filter { !TextUtils.isEmpty(it) }.mapNotNull { it ->
                                    detail.photoMap[it]?.let { photo ->
                                        UploadPhotoBean(
                                            photoServerId = photo.id,
                                            itemType = if (photo.fileType == FileTypeEntry.MP4) ItemType.VIDEO_DATA else ItemType.PHOTO_DATA,
                                            videoName = photo.name,
                                        )
                                    }
                                }
                        }
                    }
                    setUiStateAwait { copy(taskId = detail.id) }
                    setUiStateAwait { copy(note = activityViewModel.dataState.taskEntry.description) }
                    if (activityViewModel.dataState.customerEntry.collectSlipSheets) {
                        setUiStateAwait { copy(slipSheetsAmount = detail.slipSheetsAmount) }
                    }
                    setUiStateAwait { copy(detailInfo = OffloadWorkUiState.LoadInfo.DetailInfo(detail).shoot()) }
                    fireEvent { OffloadWorkUiEvent.InitLoadImg(photos) }
                    isCheckButtonEnable()
                }
            }
        }
    }


    /**
     * 图片更新
     */
    fun updateOffloadPhotos(photos: List<String>, isRemove: Boolean) {
        val offloadUpdateEntry = OffloadUpdateEntry()
        if (dataState.detailEntry != null) {
            offloadUpdateEntry.offloadType = dataState.detailEntry!!.offloadType
        }
        val existPhotos =
            dataState.detailEntry?.photoGroupEntries?.firstOrNull { it.type == PhotoGroupEntry.PHOTO_TYPE_OFFLOAD }?.photoIds ?: listOf()
        val photoGroupEntry = PhotoGroupEntry().apply {
            this.photoIds = if (isRemove) existPhotos - photos.toSet() else existPhotos + photos
            this.type = PhotoGroupEntry.PHOTO_TYPE_OFFLOAD
        }
        offloadUpdateEntry.photoGroups = listOf(photoGroupEntry)
        offloadUpdateEntry.isHandShotgun = isHandShotgun
        launch {
            requestAwait(repository.updateOffload(activityViewModel.dataState.stepId, offloadUpdateEntry)).onSuccess {
                if (isRemove) {
                    val tip: String = String.format(getString(R.string.text_offload_remove_photo_msg), "")
                    showSnack(SnackType.SuccessV1(), tip)
                }
                val find = dataState.detailEntry!!.photoGroupEntries.find { it.type == PhotoGroupEntry.PHOTO_TYPE_OFFLOAD }
                if (null == find) {
                    dataState.detailEntry!!.photoGroupEntries.add(photoGroupEntry)
                } else {
                    find.photoIds = photoGroupEntry.photoIds
                }
                setDataStateAwait { copy(detailEntry = dataState.detailEntry) }
                isCheckButtonEnable()
            }.onFailure {
                val photos = existPhotos.filter { !TextUtils.isEmpty(it) }.mapNotNull { it ->
                    dataState.detailEntry?.photoMap?.get(it)?.let { photo ->
                        UploadPhotoBean(
                            photoServerId = photo.id,
                            itemType = if (photo.fileType == FileTypeEntry.MP4) ItemType.VIDEO_DATA else ItemType.PHOTO_DATA,
                        )
                    }
                }
                fireEvent { OffloadWorkUiEvent.InitLoadImg(photos) }
            }
        }
    }

    fun updateOffloadType(offloadType: OffloadTypeEntry) {
        val offloadUpdateEntry = OffloadUpdateEntry()
        offloadUpdateEntry.offloadType = offloadType
        launch {
            requestAwait(repository.updateOffload(activityViewModel.dataState.stepId, offloadUpdateEntry)).onSuccess {
                dataState.detailEntry?.let {
                    dataState.detailEntry!!.offloadType = offloadType
                    setDataStateAwait { copy(detailEntry = dataState.detailEntry) }
                }
                isCheckButtonEnable()
            }
        }
    }

    fun updateEquipment(type: OffloadEquipmentType) {
        launch {
            dataState.detailEntry?.let {
                dataState.detailEntry!!.equipmentType = type
                setUiStateAwait { copy(selectType = OffloadWorkUiState.SelectType.EquipmentType(type).shoot()) }
                setDataStateAwait {
                    copy(detailEntry = dataState.detailEntry)
                }
                isCheckButtonEnable()
            }
        }
    }


    fun updateShipMethod(shippingMethod: ShippingMethodEntry) {
        val offloadUpdateEntry = OffloadUpdateEntry()
        offloadUpdateEntry.shippingMethod = shippingMethod
        offloadUpdateEntry.isHandShotgun = isHandShotgun
        launch {
            requestAwait(repository.updateOffload(activityViewModel.dataState.stepId, offloadUpdateEntry)).onSuccess {
                dataState.detailEntry?.let {
                    dataState.detailEntry!!.shippingMethod = shippingMethod
                    setUiStateAwait { copy(selectType = OffloadWorkUiState.SelectType.ShipMethodType.shoot()) }
                    setDataStateAwait {
                        copy(detailEntry = dataState.detailEntry)
                    }
                    isCheckButtonEnable()
                }
            }
        }
    }

    fun updateEquipmentSize(size: String) {
        dataState.detailEntry?.let {
            if (null == dataState.detailEntry!!.equipmentType) {
                showToast(R.string.text_offload_type_equipment_tip)
                return
            }
            val offloadUpdateEntry = OffloadUpdateEntry()
            offloadUpdateEntry.equipmentType = dataState.detailEntry!!.equipmentType
            offloadUpdateEntry.isHandShotgun = isHandShotgun
            when (offloadUpdateEntry.equipmentType) {
                OffloadEquipmentType.TRAILER -> {
                    val trailerSize = TrailerSizeEntry.getSizeByfter(size)
                    if (TrailerSizeEntry.NA == trailerSize) {
                        offloadUpdateEntry.nullifyFields.add("trailerSize")
                    } else {
                        offloadUpdateEntry.trailerSize = trailerSize
                    }
                }
                OffloadEquipmentType.CONTAINER -> offloadUpdateEntry.containerSize = ContainerSizeEntry.getSizeByfter(size)
                OffloadEquipmentType.CART -> {}
            }
            launch {
                requestAwait(repository.updateOffload(activityViewModel.dataState.stepId, offloadUpdateEntry)).onSuccess {
                    when (offloadUpdateEntry.equipmentType) {
                        OffloadEquipmentType.TRAILER -> dataState.detailEntry!!.trailerSize = TrailerSizeEntry.getSizeByfter(size)
                        OffloadEquipmentType.CONTAINER -> dataState.detailEntry!!.containerSize = ContainerSizeEntry.getSizeByfter(size)
                        OffloadEquipmentType.CART -> {}
                    }
                    setUiStateAwait { copy(selectType = OffloadWorkUiState.SelectType.EquipmentSize(size).shoot()) }
                    setDataStateAwait {
                        copy(detailEntry = dataState.detailEntry)
                    }
                    isCheckButtonEnable()
                }
            }
        }
    }

    fun updateSlipSheetsAmount(slipSheetsAmount: Int) {
        val offloadUpdateEntry = OffloadUpdateEntry()
        offloadUpdateEntry.slipSheetsAmount = slipSheetsAmount
        offloadUpdateEntry.isHandShotgun = isHandShotgun
        launch {
            requestAwait(repository.updateOffload(activityViewModel.dataState.stepId, offloadUpdateEntry)).onSuccess {
                dataState.detailEntry?.let {
                    dataState.detailEntry!!.slipSheetsAmount = slipSheetsAmount
                    setUiStateAwait { copy(slipSheetsAmount = slipSheetsAmount) }
                    setDataStateAwait {
                        copy(detailEntry = dataState.detailEntry)
                    }
                    isCheckButtonEnable()
                }
            }
        }
    }

    fun updateCartonQuality(receiptEntries: List<ReceiptEntry>) {
        val updateEntry = receiptEntries.map { receiptEntry ->
            ReceiptUpdateEntry().apply {
                this.id = receiptEntry.id
                this.totalPalletQty = receiptEntry.totalPalletQty
            }
        }
        launch {
            requestAwait(repository.batchUpdateReceipt(updateEntry)).onSuccess {
                val taskEntry = dataState.taskEntry
                taskEntry.receiptEntries = receiptEntries
                setUiStateAwait { copy(taskEntry = taskEntry.shoot()) }
                setDataStateAwait { copy(taskEntry = taskEntry) }
                isCheckButtonEnable()
            }
        }
    }

    private fun updateStepStatus(stepStatus: StepStatusEntry) {
        val status = ReceiveStepStatusUpdateEntry()
        status.status = stepStatus
        launch {
            request(
                repository.updateStepStatus(activityViewModel.dataState.taskEntry.id, activityViewModel.dataState.stepId, status),
                success = {
                    showToast(R.string.msg_submit_success)
                    activityViewModel.dataState.stepEntry.status = stepStatus
                    if (stepStatus == StepStatusEntry.IN_PROGRESS) {
                        loadDetail()
                    }
                    if (StepStatusEntry.DONE == stepStatus) {
                        activityViewModel.doneStep()
                    }
                },
                error = {
                    if (StepStatusEntry.DONE == stepStatus) {
                        setUiState { copy(progressStatus = OffloadWorkUiState.ProgressStatus.Error.shoot()) }
                    }

                })
        }
    }


    /**
     * 按钮是否可点击
     */
    private fun isCheckButtonEnable() {
        dataState.detailEntry?.let {
            val detailEntry = dataState.detailEntry!!
            var hasPhoto = false
            val find = detailEntry.photoGroupEntries.find { TextUtils.equals(it.type, PhotoGroupEntry.PHOTO_TYPE_OFFLOAD) }
            find?.let {
                hasPhoto = it.photoIds.any { item -> !TextUtils.isEmpty(item) }
            }
            detailEntry.shippingMethod != null
            var isEquiomentSize = false
            if (detailEntry.equipmentType != null) {
                when (detailEntry.equipmentType) {
                    OffloadEquipmentType.CONTAINER -> {
                        isEquiomentSize = null != detailEntry.containerSize
                    }
                    OffloadEquipmentType.TRAILER -> {
                        isEquiomentSize = null != detailEntry.trailerSize
                    }

                    OffloadEquipmentType.CART -> {
                    }
                }
            }
            setUiState {
                copy(
                    isButtonEnabled = detailEntry.offloadType != null
                            && detailEntry.equipmentType != null
                            && (isEquiomentSize || detailEntry.equipmentType == OffloadEquipmentType.CART)
                            && detailEntry.shippingMethod != null
                            && hasInputSlipSheetsAmount()
                            && hasInputCartonQuality()
                            && hasPhoto
                )
            }
        }
    }

    private fun hasInputSlipSheetsAmount(): Boolean {
        val collectSlipSheets = activityViewModel.dataState.customerEntry.collectSlipSheets
        if (collectSlipSheets) {
            dataState.detailEntry?.let {
                return dataState.detailEntry!!.slipSheetsAmount != null && dataState.detailEntry!!.slipSheetsAmount >= 0
            }
        }
        return true
    }

    private fun hasInputCartonQuality(): Boolean {
        val collectCartonCount = activityViewModel.dataState.customerEntry.collectCartonCount
        if (collectCartonCount) {
            val receiptEntries = dataState.taskEntry.receiptEntries
            return receiptEntries.all { receiptEntry -> receiptEntry.totalPalletQty != null && receiptEntry.totalPalletQty > 0 }
        }
        return true
    }

    fun loadMaterials() {
        var searchEntry: MaterialLineSearchEntry = MaterialLineSearchEntry()
        searchEntry.receiveTaskIds = listOf<String>(activityViewModel.dataState.taskEntry.id)
        launch {
            request(repository.searchMaterials(searchEntry), success = {
                setUiState { copy(detailInfo = it?.let { it1 -> OffloadWorkUiState.LoadInfo.MaterialsInfo(it1).shoot() }) }
            })
        }
    }

    fun removeMaterial(position: Int, id: String) {
        launch {
            requestAwait(repository.removeMaterials(id)).onSuccess {
                setUiState { copy(detailInfo = OffloadWorkUiState.LoadInfo.RemoveMaterial(position).shoot()) }
            }
        }
    }

    fun finishStep(sizePosition: Int, photos: Int) {
        if (dataState.detailEntry!!.status != StepStatusEntry.IN_PROGRESS) {
            setUiState { copy(progressStatus = OffloadWorkUiState.ProgressStatus.Close.shoot()) }
            return
        }
        dataState.detailEntry?.let {
            val detailEntry = dataState.detailEntry!!

            if (null == detailEntry.offloadType) {
                showToast(R.string.text_offload_type_offloadtype_tip)
                return
            }

            if (null == detailEntry.equipmentType) {
                showToast(R.string.text_offload_type_equipment_tip)
                return
            }

            if (sizePosition <= -1) {
                showToast(R.string.hint_please_select_equipment_size)
                return
            }

            if (null == detailEntry.shippingMethod) {
                showToast(R.string.hint_please_select_ship_method)
                return
            }
            if (activityViewModel.dataState.customerEntry.collectSlipSheets && null == detailEntry.slipSheetsAmount) {
                showToast(R.string.hint_please_input_slip_sheets_quantity)
                return
            }
            if (photos <= 0) {
                showToast(R.string.msg_please_capture_photo)
                return
            }
            updateStepStatus(StepStatusEntry.DONE)
        }
    }

    fun forceClose(reason: String?) {
        val receiveTaskEntry = activityViewModel.dataState.taskEntry
        val closeEntry = ReceiptForceCloseEntry()
        closeEntry.reason = reason
        launch {
            request(repository.forceClose(closeEntry, receiveTaskEntry.id, activityViewModel.dataState.stepId), success = {
                setUiState { copy(progressStatus = OffloadWorkUiState.ProgressStatus.Close.shoot()) }
            })
        }
    }


    fun isOffloadTypeSelect(): Boolean {
        if (null == dataState.detailEntry) {
            return false
        }
        return null != dataState.detailEntry!!.offloadType
    }


    fun isEquipmentSelect(): Boolean {
        if (null == dataState.detailEntry) {
            return false
        }
        return null != dataState.detailEntry!!.equipmentType
    }

    fun isShipMethodSelected(): Boolean {
        if (null == dataState.detailEntry) {
            return false
        }
        return null != dataState.detailEntry!!.shippingMethod
    }

    fun reportException(note: String?, fileIds: List<String?>?) {
        val exceptionEntry = ReceiptExceptionEntry()
        exceptionEntry.taskId = activityViewModel.dataState.taskEntry.id
        exceptionEntry.notes = ArrayList()
        exceptionEntry.stepId = activityViewModel.dataState.stepId
        var noteEntry = NoteEntry()
        noteEntry.type = NoteEntry.NOTE_TYPE_TEXT
        noteEntry.content = note
        exceptionEntry.notes.add(noteEntry)
        noteEntry = NoteEntry()
        noteEntry.type = NoteEntry.NOTE_TYPE_PHOTO
        noteEntry.fileIds = fileIds
        exceptionEntry.notes.add(noteEntry)
        launch {
            request(repository.reportException(exceptionEntry))
        }
    }

    fun reopenStep() {
        launch {
            if (requestAwait(
                    repository.reopenStep(
                        activityViewModel.dataState.stepEntry.taskId,
                        activityViewModel.dataState.stepEntry.id
                    )
                ).isSuccess
            ) {
                val requestAwait = requestAwait(repository.reloadTask(activityViewModel.dataState.stepEntry.taskId))
                if (requestAwait.isSuccess) {
                    loadDetail()
                }
            }
        }
    }

    fun getAndOpenHelpPage(context: Context?, helpPageKey: String?, facilityId: String?) {
        functionHelpPresenterImpl.getAndOpenHelpPage(
            context, helpPageKey, activityViewModel.dataState.taskEntry.customerId, facilityId
        )
    }


    fun isCaptureMaterial(): Boolean {
        return activityViewModel.dataState.customerEntry.askForCapturingMaterial
    }

    class WorkStageRepo : BaseRepository() {
        private val receiptOffloadApi by apiServiceLazy<ReceiptOffloadApi>()
        private val locationApi by apiServiceLazy<LocationApi>()
        private val receiveTaskApi by apiServiceLazy<ReceiveTaskApi>()
        private val receiptApi by apiServiceLazy<ReceiptApi>()
        private val materialLineCenterApi by apiServiceLazy<MaterialLineCenterApi>()
        private val genericStepApi by apiServiceLazy<GenericStepApi>()
        private val receiptExceptionApi by apiServiceLazy<ReceiptExceptionApi>()
        private val takeOverTaskApi by apiServiceLazy<TakeOverTaskApi>()
        private val taskStepProgressTimeApi by apiServiceLazy<TaskStepProcessTimeApi>()
        private val idmApi by apiServiceLazy<IdmApi>()


        fun getOffloadDetail(id: String) = rxRequest(receiptOffloadApi.getOffloadDetail(id))
        fun updateOffload(stepId: String, updateEntry: OffloadUpdateEntry) = rxRequest(receiptOffloadApi.updateOffload(stepId, updateEntry))
        fun updateStepStatus(taskId: String, stepId: String, statusUpdate: ReceiveStepStatusUpdateEntry) =
            rxRequest(receiveTaskApi.updateStepStatus(taskId, stepId, statusUpdate))

        fun searchMaterials(search: MaterialLineSearchEntry) = rxRequest(materialLineCenterApi.search(search))
        fun removeMaterials(id: String) = rxRequest(materialLineCenterApi.removeMaterialLine(id))
        fun release(entry: DockOperateEntry) = rxRequest(locationApi.release(entry))
        fun dockCheckout(id: String, entry: DockOperateEntry) = rxRequest(receiveTaskApi.dockCheckout(id, entry))
        fun forceClose(close: ReceiptForceCloseEntry, taskId: String, stepId: String) =
            rxRequest(receiveTaskApi.forceCloseStep(taskId, stepId, close))

        fun start(stepId: String) = rxRequest(genericStepApi.start(stepId))
        fun reportException(exception: ReceiptExceptionEntry) = rxRequest(receiptExceptionApi.create(exception))
        fun reopenStep(taskId: String, stepId: String) = rxRequest(receiveTaskApi.reopenStep(taskId, stepId))
        fun reloadTask(taskId: String) = rxRequest(receiveTaskApi.getTask(taskId))
        fun assignStep(id: String, stepAssignmentEntries: List<StepAssignmentEntry>) =
            rxRequest(genericStepApi.assignStep(id, stepAssignmentEntries))

        fun takeOver(take: TaskTakeOverCreateEntry) = rxRequest(takeOverTaskApi.takeOverApply(take))
        fun logTaskStepProcessStartTime(taskStepProcessTimeEntry: TaskStepProcessTimeEntry) =
            rxRequest(taskStepProgressTimeApi.create(taskStepProcessTimeEntry))

        fun updateExitTime(taskStepProcessTimeEntry: TaskStepProcessTimeEntry) =
            rxRequest(taskStepProgressTimeApi.updateExitTime(taskStepProcessTimeEntry))

        fun batchUpdateReceipt(receiptUpdateEntries: List<ReceiptUpdateEntry>) =
                rxRequest(receiptApi.batchUpdateReceipt(receiptUpdateEntries))
    }
}

