package com.lt.linc.receive_v1.lpsetup.work.bypallet.threepartypalletid

import com.lt.linc.common.mvi.ReactiveDataState
import com.lt.linc.common.mvi.ReactiveUiState

/**
 * @Description:
 * @Author: Dennis
 * @CreateDate: 2023/2/28
 */
data class ThreePartyPalletIdDataState(
    val threePartyPalletId: String? = null
) : ReactiveDataState {}

data class ThreePartyPalletIdUiState(
    val threePartyPalletId: String? = null
) : ReactiveUiState {}
