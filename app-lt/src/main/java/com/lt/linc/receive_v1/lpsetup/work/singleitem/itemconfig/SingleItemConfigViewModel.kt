package com.lt.linc.receive_v1.lpsetup.work.singleitem.itemconfig

import android.text.TextUtils
import com.annimon.stream.Collectors
import com.annimon.stream.Stream
import com.linc.platform.baseapp.model.LocationInventoryStatus
import com.linc.platform.cartontracker.model.CartonEntry
import com.linc.platform.cartontracker.model.CartonItemEntry
import com.linc.platform.cartontracker.model.CartonSearchEntry
import com.linc.platform.common.lp.LpTypeEntry
import com.linc.platform.common.lp.ScenarioEntry
import com.linc.platform.common.lp.SceneEntry
import com.linc.platform.foundation.model.InvoiceStorageEntry
import com.linc.platform.foundation.model.InvoiceStorageViewEntry
import com.linc.platform.foundation.model.ItemSpecEntry
import com.linc.platform.foundation.model.ReceiveMethodEntry
import com.linc.platform.foundation.model.UnitEntry
import com.linc.platform.foundation.model.getThirdPartyLpNoPattern
import com.linc.platform.foundation.model.lptemplate.SingleItemLpTemplateCreateEntry
import com.linc.platform.foundation.model.lptemplate.SingleItemLpTemplateEntry
import com.linc.platform.foundation.model.lptemplate.SingleItemLpTemplateSearchEntry
import com.linc.platform.foundation.model.lptemplate.SingleItemLpTemplateStatusEntry
import com.linc.platform.inventory.model.LpBatchCreateEntry
import com.linc.platform.inventory.model.LpCreateResponseEntry
import com.linc.platform.inventory.model.LpJobEntry
import com.linc.platform.print.commonprintlp.PrintData
import com.linc.platform.print.commonprintlp.PrintMsg
import com.linc.platform.print.commonprintlp.PrintResult
import com.linc.platform.print.commonprintlp.PrinterConfig
import com.linc.platform.print.model.LabelSizeEntry
import com.linc.platform.receive.ReceiveCommonHelper
import com.linc.platform.receive.dal.ItemMFGDateDal
import com.linc.platform.receive.model.LPSetupResultEntry
import com.linc.platform.receive.model.ReceiptItemLineEntry
import com.linc.platform.receive.model.ReceiveTypeEntry
import com.linc.platform.utils.BarcodeUtil
import com.linc.platform.utils.CollectionUtil
import com.linc.platform.utils.Constant
import com.linc.platform.utils.LPUtil
import com.linc.platform.utils.Lists
import com.linc.platform.utils.PrintUtil
import com.linc.platform.utils.StringUtil
import com.lt.linc.R
import com.lt.linc.common.Tuple
import com.lt.linc.common.Tuple3
import com.lt.linc.common.extensions.deepCopy
import com.lt.linc.common.extensions.ifNullOrEmpty
import com.lt.linc.common.extensions.mapToLinkedMap
import com.lt.linc.common.extensions.toException
import com.lt.linc.common.mvi.ReactiveViewModel
import com.lt.linc.common.mvi.interceptDataState
import com.lt.linc.common.mvi.interceptUiState
import com.lt.linc.common.mvi.mapDataToUi
import com.lt.linc.common.mvi.subscribeInner
import com.lt.linc.common.mvi.subscribeTo
import com.lt.linc.common.mvvm.kotlin.extensions.facilityEntry
import com.lt.linc.common.mvvm.kotlin.extensions.facilityId
import com.lt.linc.common.mvvm.kotlin.extensions.getFormattedString
import com.lt.linc.common.mvvm.kotlin.extensions.getString
import com.lt.linc.common.mvvm.kotlin.extensions.launch
import com.lt.linc.common.mvvm.kotlin.extensions.showLoading
import com.lt.linc.common.mvvm.kotlin.extensions.showSnack
import com.lt.linc.common.mvvm.kotlin.extensions.showToast
import com.lt.linc.receive_v1.lpsetup.work.LpSetupWorkDataState
import com.lt.linc.receive_v1.lpsetup.work.LpSetupWorkProcess
import com.lt.linc.receive_v1.lpsetup.work.LpSetupWorkViewModel
import com.lt.linc.receive_v1.lpsetup.work.collectiteminfo.ItemInfoChecker
import com.lt.linc.receive_v1.lpsetup.work.collectiteminfo.ItemInfoStatus
import com.lt.linc.receive_v1.lpsetup.work.singleitem.itemconfig.SingleItemConfigDataState.ConfigurationMode
import com.lt.linc.receive_v1.lpsetup.work.singleitem.itemconfig.SingleItemConfigDataState.ReceiveItemLineCompose
import com.lt.linc.receive_v1.lpsetup.work.singleitem.itemconfig.components.QtyConfirmDialog
import com.lt.linc.util.SnackType
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.onEach
import java.text.SimpleDateFormat
import java.util.Date
import kotlin.math.ceil
import kotlin.math.floor

/**
 * <AUTHOR>
 * @date 2022/07
 */
class SingleItemConfigViewModel(
    initialDataState: SingleItemConfigDataState = SingleItemConfigDataState(),
    initialUiState: SingleItemConfigUiState = SingleItemConfigUiState(),
    private val parentViewModel: LpSetupWorkViewModel,
) : ReactiveViewModel<SingleItemConfigDataState, SingleItemConfigUiState>(initialDataState, initialUiState) {

    private val selectedLocation = parentViewModel.selectedLocation!!
    private val stepEntry = parentViewModel.stepEntry
    private val customerEntry get() = parentViewModel.customerEntry
    private val userLevelConfig get() = parentViewModel.userLevelConfig
    private val lpLabelSize get() = parentViewModel.lpLabelSize
    private val repository = SingleItemConfigRepository(customerEntry)

    private val itemInfoChecker by lazy { ItemInfoChecker() }
    private val printUtil = PrintUtil.newInstance()

    init {
        interceptDataState {
            interceptOnce(SingleItemConfigDataState::configCompose) {
                it.copy(
                    needCollectGoodsType = ReceiveCommonHelper.enableShowGoodsTypeView(customerEntry),
                    allAllowedGoodsTypes = if (customerEntry.allowedReceivingGoodsTypes.isNullOrEmpty()) ReceiveCommonHelper.defaultGoodsTypes
                    else customerEntry.getAllowedReceivingGoodsTypes(),
                    needCollectPhotoGoodsTypes = ReceiveCommonHelper.needCollectPhotoGoodsTypes,
                    forceScanLotNumber = customerEntry.isForceScanLotNo,
                    lotNumberRegex = customerEntry.defaultLotNoValidateRegex,
                    needCollectCustomerPallet = customerEntry.requireCollectPalletNoOnReceive,
                    forceDefaultUOM = customerEntry.forceDefaultUOMReceiving,
                    enableLpCountSuggestion = customerEntry.enableSelectLPTemplateByTiHiOnReceive,
                    needAlertHighValueItem = customerEntry.needAlertHighValueItem,
                    needCollectItemInfo = customerEntry.collectItemInfoAtReceiving,
                    needCollectItemSmallParcelPackaging = customerEntry.collectItemSmallParcelPackagingAtReceiving,
                    enableModifyPalletNo = parentViewModel.getReceiveMethod() != ReceiveMethodEntry.RECEIVE_BY_PALLET
                )
            }
        }
        interceptUiState {
            interceptDiffer(SingleItemConfigUiState::receiptItemLines, ::interceptUiReceiptItemLines)
        }
    }

    /*** Intercept and update [ReceiveItemLineCompose.canExpand] */
    @Suppress("UNUSED_PARAMETER")
    private fun interceptUiReceiptItemLines(_state: SingleItemConfigUiState, it: List<ReceiveItemLineCompose>) = it.map { compose ->
        compose.copy(canExpand = if (userLevelConfig.forceScanningUPCToChooseItem) compose.isFiltered else true)
    }

    init {
        launch {
            loadConvertMfgDateConfiguration()
            if (dataState.configCompose.autoConvertMfgDateByLotNumber) updateMfgDateByLotNumber()
        }
        launch {
            delay(350) // For smoothing the transition animation of Fragment, delay waiting for it to complete.
            subscribeTo(parentViewModel, LpSetupWorkDataState::receiptItemLines, ::autoUpdateReceiptItemLines)
        }
        autoUpdateUiItemLines()
        if (!com.lt.linc.util.Constant.isLenovoBuildType() && !com.lt.linc.util.Constant.isSaasBuildType()) {
            getInvoiceStorage()
        }
        setUiState { copy(showAddNewItem = customerEntry.allowNewItemInReceiving) }
    }

    private suspend fun loadConvertMfgDateConfiguration() {
        val result = requestAwait(repository.getConvertMfgDateConfiguration(customerEntry.orgId), showLoading = false)
        if (result.isFailure) return
        val shouldConvertMfgDate = result.getOrNull()!!
        setDataStateAwait { copy(configCompose = configCompose.copy(autoConvertMfgDateByLotNumber = shouldConvertMfgDate)) }
    }

    /**
     * When enable automatically convert mfgDate by lot#, should convert mfgDate after lot# changed.
     * @see ItemMFGDateDal
     */
    private fun updateMfgDateByLotNumber() =
        subscribeInner(SingleItemConfigDataState::receiptItemLineMap, ReceiveItemLineCompose::lotNumber) {
            val mfgDates = it.mapNotNull { (itemLineId, lotNumber) ->
                val shouldCollectMfgDate = dataState.receiptItemLineMap[itemLineId]!!.requireCollectMfgDate
                if (lotNumber.isNullOrEmpty() || !shouldCollectMfgDate) {
                    return@mapNotNull null
                }
                val mfgStr = ItemMFGDateDal.convertMFGDateByLotNo(lotNumber, true)
                val mfgDate = runCatching { SimpleDateFormat("MM/dd/yy").parse(mfgStr) }.getOrNull() ?: return@mapNotNull null
                itemLineId to mfgDate
            }.toMap()
            setDataState {
                withItemLines(mfgDates.keys) { copy(manufactureDate = mfgDates[itemLineId]) }
            }
        }

    private suspend fun autoUpdateReceiptItemLines(list: List<ReceiptItemLineEntry>) {
        val receiveMethod = parentViewModel.getReceiveMethod()
        var filterList = list
        if (receiveMethod == ReceiveMethodEntry.RECEIVE_BY_PALLET) {
            val receiveByPalletDataCompose = parentViewModel.getReceiveByPalletDataCompose()
            receiveByPalletDataCompose?.let {
                val itemNumber = it.palletDetailInfoCompose?.itemNumber
                val dateStamp = it.dateStamp
                val palletNo = it.threePartyPalletId
                filterList = list.filter { itemLine -> itemLine.name == itemNumber }
                filterList.forEach { itemLine ->
                    itemLine.lotNo = dateStamp
                    itemLine.palletNo = palletNo
                }
            }
        }
        val config = dataState.configCompose
        val map = filterList.mapToLinkedMap {
            val oldItem = dataState.receiptItemLineMap[it.itemLineId]
            val item = if (oldItem != null) {
                val enableUnits = it.itemSpecInfo?.enableUnits
                val (units, selectedUnit) = if (config.forceDefaultUOM) {
                    val defaultUnits = enableUnits?.filter { unit -> unit.isDefaultUnit }
                    defaultUnits to defaultUnits?.firstOrNull()
                } else {
                    enableUnits to it.defaultLPTemplateUnitEntry
                }
                oldItem.copy(
                    itemLine = it, allUnits = units ?: oldItem.allUnits,
                    selectedUnit = selectedUnit ?: oldItem.selectedUnit
                )
            } else {
                if (it.isBaseUnitUnConfigured) showToast(R.string.no_base_unit_configured_for_item, it.itemSpecInfo?.name ?: "")
                val enableUnits = it.itemSpecInfo?.enableUnits
                val (units, selectedUnit) = if (config.forceDefaultUOM) {
                    val defaultUnits = enableUnits?.filter { unit -> unit.isDefaultUnit }
                    if (defaultUnits.isNullOrEmpty()) showToast(R.string.please_setup_default_uom, it.itemSpecInfo?.name ?: "")
                    defaultUnits to defaultUnits?.firstOrNull()
                } else {
                    if (enableUnits.isNullOrEmpty()) showToast(R.string.no_uom_found_for_item, it.itemSpecInfo?.name ?: "")
                    enableUnits to it.defaultLPTemplateUnitEntry
                }
                ReceiveItemLineCompose(
                    itemLine = it,
                    receiveMethod = receiveMethod,
                    lotNumber = when {
                        config.forceScanLotNumber -> null
                        it.lotNo.isNullOrEmpty() -> null
                        else -> it.lotNo
                    },
                    needCollectCustomerPallet = config.needCollectCustomerPallet,
                    customerPalletNumber = when {
                        it.palletNo.isNullOrEmpty() -> null
                        else -> it.palletNo
                    },
                    expirationDate = it.expirationDate,
                    manufactureDate = it.manufactureDate,
                    needCollectGoodsType = config.needCollectGoodsType,
                    allAllowedGoodsTypes = config.allAllowedGoodsTypes,
                    selectedGoodsType = it.goodsType.ifNullOrEmpty { ReceiveCommonHelper.getDefaultGoodsType(it, customerEntry) },
                    needCollectPhotoGoodsTypes = config.needCollectPhotoGoodsTypes,
                    canCreateTemplate = userLevelConfig.displayAddNewConfiguration,
                    allUnits = units ?: listOf(),
                    selectedUnit = selectedUnit ?: units?.firstOrNull(),
                    needAlertHighValue = config.needAlertHighValueItem && (it.itemSpecInfo?.grade?.isNotEmpty() ?: false),
                    enableModifyPalletNo = config.enableModifyPalletNo,
                    enableSelectLPTemplateByTiHiOnReceive = config.enableLpCountSuggestion,
                    selectedConfigurationMode = if (config.enableLpCountSuggestion) ConfigurationMode.TixHi else ConfigurationMode.QtyPerPallet
                )
            }
            it.itemLineId to item
        }
        setDataStateAwait { copy(receiptItemLineMap = map) }
        getAndSetLpTemplates(false)
    }

    private fun getUnitsAndSelectedUnit(receiptItemLineEntry: ReceiptItemLineEntry): Pair<List<UnitEntry>?, UnitEntry?> {
        val enableUnits = receiptItemLineEntry.itemSpecInfo?.enableUnits
        return if (dataState.configCompose.forceDefaultUOM) {
            val defaultUnits = enableUnits?.filter { unit -> unit.isDefaultUnit }
            defaultUnits to defaultUnits?.firstOrNull()
        } else {
            enableUnits to receiptItemLineEntry.defaultLPTemplateUnitEntry
        }
    }

    private fun autoUpdateUiItemLines() = mapDataToUi(
        SingleItemConfigDataState::receiptItemLineMap,
        SingleItemConfigDataState::itemLineFilter,
        SingleItemConfigUiState::receiptItemLines
    ) { list, filter ->
        if (filter.isNullOrEmpty()) list.values.toList() else list.values.filter { it.isFiltered }
    }

    private fun updateQtyPerPalletSuggestions(itemLineId: String, checkedTemplate: SingleItemLpTemplateEntry?) {
        val receiptItemLine = dataState.receiptItemLineMap[itemLineId]
        val unit = receiptItemLine?.selectedUnit
        val request = repository.getQtyPerPalletSuggestion(
            stepId = stepEntry.id,
            itemLineId = itemLineId,
            customerId = customerEntry.orgId,
            templateId = checkedTemplate?.lpConfigurationTemplateId,
            unitId = unit?.id
        )
        launch {
            requestAwait(request).onSuccess { result ->
                if (result == null) return@launch
                setDataState {
                    withItemLines(listOf(result.first)) {
                        val qtyPerPallet = result.second
                        val qty = if (qtyPerPallet == null || qtyPerPallet <= 0) {
                            "0"
                        } else {
                            qtyPerPallet.toInt().toString()
                        }
                        copy(qtyPerPallet = qty)
                    }
                }
            }
        }
    }


    private fun getAndSetLpTemplates(isSuggestQty: Boolean = true) {
        val search = SingleItemLpTemplateSearchEntry().apply {
            itemSpecIds = dataState.receiptItemLineMap.values.map { it.itemSpecId }
            scene = SceneEntry.INBOUND
        }
        val request = repository.searchSingleItemLpTemplate(search).onEach { list ->
            val receivedTemplateMap = parentViewModel.dataState.receivedLpTemplateIdsMap
            val map = list?.groupBy { it.itemSpecId }?.mapValues { (key, value) ->
                val receivedTemplateIds = receivedTemplateMap[key]
                val newList = if (!receivedTemplateIds.isNullOrEmpty()) {
                    value.filter { it.lpConfigurationTemplateId in receivedTemplateIds }
                } else {
                    value
                }
                newList.distinctBy { it.lpConfigurationTemplateId }
            } ?: mapOf()
            setDataState {
                copy(receiptItemLineMap = this.receiptItemLineMap.mapValues { (_, value) ->
                    val templates = map.getOrDefault(value.itemSpecId, listOf())
                    val checkedTemplate = when {
                        null != value.checkedTemplate && templates.any { template ->
                            template.lpConfigurationTemplateId == value.checkedTemplate.lpConfigurationTemplateId
                        } -> value.checkedTemplate
                        templates.any {
                            it.isDefault && TextUtils.equals(
                                it.unitId, value.selectedUnit?.id
                            )
                        } -> templates.first {
                            it.isDefault && TextUtils.equals(
                                it.unitId, value.selectedUnit?.id
                            )
                        }
                        else -> templates.firstOrNull()
                    }
                    value.copy(
                        allTemplates = templates, checkedTemplate = checkedTemplate
                    ).updateSelectedUnitByTemplate(isSuggestQty)
                })
            }
        }
        request(request)
    }

    private fun ReceiveItemLineCompose.updateSelectedUnitByTemplate(isSuggestQty: Boolean = true): ReceiveItemLineCompose {
        val checkedTemplate = this.checkedTemplate ?: return this
        return if (dataState.configCompose.forceDefaultUOM && checkedTemplate.unitEntry != null && !checkedTemplate.unitEntry.isDefaultUnit) {
            this
        } else {
            if (isSuggestQty) updateQtyPerPalletSuggestions(this.itemLineId, checkedTemplate)
            this.copy(selectedUnit = checkedTemplate.unitEntry)
        }
    }

    fun filterItemLine(keyword: String) {
        val filter = BarcodeUtil.clipBarcodeByPrefixSuffix(keyword, customerEntry)
        setDataState {
            copy(itemLineFilter = filter, receiptItemLineMap = receiptItemLineMap.asIterable().associate { (key, value) ->
                val isFiltered = if (filter.isNullOrEmpty()) false else {
                    value.itemSpec?.getItemSpecId().contentEquals(filter, true)
                            || value.itemSpec?.name.contentEquals(filter, true)
                            || value.itemSpec?.upcCode.contentEquals(filter, true)
                            || value.itemSpec?.upcCodeCase.contentEquals(filter, true)
                            || value.itemSpec?.eanCode.contentEquals(filter, true)
                            || value.itemSpec?.abbreviation.contentEquals(filter, true)
                            || value.itemLine.itemCodes?.any { code -> code.value.contentEquals(filter, true) } ?: false
                }
                val isExpanded = if (isFiltered && userLevelConfig.allowScanToExpand) true else value.isExpanded
                key to value.copy(isFiltered = isFiltered, isExpanded = isExpanded)
            })
        }
    }

    fun loadCartonItem(keyword: String) {
        if (StringUtil.isValidCartonId(keyword, parentViewModel.customerEntry.customerCode)) {// input cartonId
            loadCartonItemByCartonId(keyword)
        } else if (isThirdPartyLPNo(keyword)) {
            loadCartonItemFromRn(keyword)
        } else {// input item label
            loadCartonItemByItemLabel(keyword)
        }
    }

    private fun loadCartonItemByCartonId(cartonId: String) {
        launch {
            requestAwait(repository.getCartonItem(cartonId)).onSuccess {
                it?.let {
                    val isContain = Stream.ofNullable(dataState.receiptItemLineMap).anyMatch { (_, value) -> isContainSpecialItemSpec(it, value) }
                    if (!isContain) {
                        showToast(R.string.msg_receive_item_line_no_relation_to_the_carton)
                        return@launch
                    }
                    val mergedCartonItem = mergeCartonItem(it)
                    val upcList = Stream.ofNullable(mergedCartonItem.cartonItemEntries).map { cartItem -> cartItem.upc }.toList()
                    setDataState {
                        copy(itemLineFilter = cartonId, receiptItemLineMap = receiptItemLineMap.asIterable().associate { (key, value) ->
                            val isFiltered = if (cartonId.isNullOrEmpty()) false else isMathItemSpec(value, upcList)
                            val isExpanded = if (isFiltered && userLevelConfig.allowScanToExpand) true else value.isExpanded
                            key to value.copy(
                                    isFiltered = isFiltered,
                                    isExpanded = isExpanded,
                                    isScanCartonId = true,
                                    isMixItemForCarton = upcList.size > 1,
                                    cartonId = mergedCartonItem.id,
                                    qtyPerPallet = getQtyInCartonByItemSpec(mergedCartonItem, value).toString()
                            )
                        })
                    }
                }
            }
        }
    }

    private fun loadCartonItemFromRn(cartonId: String) {
        val isContain = Stream.ofNullable(dataState.receiptItemLineMap).anyMatch { (_, value) -> isMathCartonIdAtItemLine(value, cartonId) }
        if (!isContain) {
            loadCartonItemByCartonId(cartonId)
            return
        }
        val itemSpecId = getItemSpecIdFromRn(cartonId)
        setDataState {
            copy(itemLineFilter = cartonId, receiptItemLineMap = receiptItemLineMap.asIterable().associate { (key, value) ->
                val isFiltered = if (cartonId.isNullOrEmpty()) false else isMathItemSpec(value, itemSpecId)
                val isExpanded = if (isFiltered && userLevelConfig.allowScanToExpand) true else value.isExpanded
                key to value.copy(
                        isFiltered = isFiltered,
                        isExpanded = isExpanded,
                        isScanCartonId = true,
                        isMixItemForCarton = false,
                        cartonId = cartonId,
                        qtyPerPallet = getQtyInCartonFromRn(cartonId).toString()
                )
            })
        }
    }

    private fun loadCartonItemByItemLabel(itemLabel: String) {
        val isContain = Stream.ofNullable(dataState.receiptItemLineMap).anyMatch { (_, value) -> isMathItemSpec(value, itemLabel) }
        if (!isContain) {
            showToast(String.format(getString(R.string.msg_receive_item_line_no_relation_to_item_label), itemLabel))
            return
        }
        val itemUpc = getItemUpcFromReceiveItemLineCompose(itemLabel)
        launch {
            requestAwait(repository.searchCartonItem(CartonSearchEntry().apply {
                this.upc = itemUpc
            })).onSuccess {
                if (it.isNullOrEmpty()) {
                    showToast(R.string.msg_no_carton_info_please_use_single_item_receive)
                    return@launch
                }
                if (it.size > 1) {
                    showToast(String.format(getString(R.string.msg_there_are_multiple_packages_containing_item), itemLabel))
                    return@launch
                }
                if (it.size == 1) {
                    val mergedCartonItem = mergeCartonItem(it[0])
                    setDataState {
                        copy(itemLineFilter = itemLabel, receiptItemLineMap = receiptItemLineMap.asIterable().associate { (key, value) ->
                            val isFiltered = if (itemLabel.isNullOrEmpty()) false else isMathItemSpec(value, itemUpc)
                            val isExpanded = if (isFiltered && userLevelConfig.allowScanToExpand) true else value.isExpanded
                            key to value.copy(
                                    isFiltered = isFiltered,
                                    isExpanded = isExpanded,
                                    isScanCartonId = false,
                                    isMixItemForCarton = if (CollectionUtil.isNotNullOrEmpty(mergedCartonItem.cartonItemEntries)) mergedCartonItem.cartonItemEntries!!.size > 1 else false,
                                    cartonId = mergedCartonItem.id,
                                    qtyPerPallet = getQtyInCartonByItemSpec(mergedCartonItem, value).toString()
                            )
                        })
                    }
                }
            }
        }
    }

    private fun mergeCartonItem(cartonEntry: CartonEntry): CartonEntry {
        val cartonEntryCopy = cartonEntry.deepCopy()
        val groupByUpc = cartonEntry.cartonItemEntries?.groupBy { cartonItem -> cartonItem.upc }
        groupByUpc?.let {groupByUpcMap ->
            val keys = groupByUpcMap.keys
            val cartonItemMergedList = mutableListOf<CartonItemEntry>()
            keys.forEach { upc ->
                run {
                    val cartonItemMerged = groupByUpc[upc]?.get(0)
                    cartonItemMerged?.qty = groupByUpc[upc]!!.sumOf { cartonEntry -> cartonEntry.qty ?: 0.0 }
                    cartonItemMerged?.let { cartonItem -> cartonItemMergedList.add(cartonItem) }
                }
            }
            cartonEntryCopy?.cartonItemEntries = cartonItemMergedList
        }
        return cartonEntryCopy?: cartonEntry
    }

    private fun isMathItemSpec(receiveItemLineCompose: ReceiveItemLineCompose, keywords: List<String?>?): Boolean {
        return Stream.ofNullable(keywords).anyMatch { keyword -> isMathItemSpec(receiveItemLineCompose, keyword) }
    }

    private fun isMathItemSpec(receiveItemLineCompose: ReceiveItemLineCompose, keyword: String?): Boolean {
        return receiveItemLineCompose.itemSpec?.getItemSpecId().contentEquals(keyword, true)
                || receiveItemLineCompose.itemSpec?.name.contentEquals(keyword, true)
                || receiveItemLineCompose.itemSpec?.upcCode.contentEquals(keyword, true)
                || receiveItemLineCompose.itemSpec?.upcCodeCase.contentEquals(keyword, true)
                || receiveItemLineCompose.itemSpec?.eanCode.contentEquals(keyword, true)
                || receiveItemLineCompose.itemSpec?.abbreviation.contentEquals(keyword, true)
                || receiveItemLineCompose.itemLine.itemCodes?.any { code -> code.value.contentEquals(keyword, true) } ?: false
    }

    private fun isMathCartonIdAtItemLine(receiveItemLineCompose: ReceiveItemLineCompose, cartonId: String?): Boolean {
        return receiveItemLineCompose.itemLine.cartons?.any { code -> code.cartonNo.contentEquals(cartonId, true) } ?: false
    }

    private fun isContainSpecialItemSpec(carton: CartonEntry, receiveItemLineCompose: ReceiveItemLineCompose): Boolean {
        return Stream.ofNullable(carton.cartonItemEntries)
                .anyMatch { cartonItem -> isMathItemSpec(receiveItemLineCompose, cartonItem.upc) }
    }

    private fun getQtyInCartonByItemSpec(carton: CartonEntry, receiveItemLineCompose: ReceiveItemLineCompose): Int {
        val findFirst = Stream.ofNullable(carton.cartonItemEntries)
                .filter { cartonItem -> isMathItemSpec(receiveItemLineCompose, cartonItem.upc) }
                .findFirst()
        if (findFirst.isPresent) {
            return floor(findFirst.get().qty ?: 0.0).toInt()
        }
        return 0
    }

    private fun getItemUpcFromReceiveItemLineCompose(itemLabel: String): String {
        val findFirst = Stream.ofNullable(dataState.receiptItemLineMap)
                .filter { (_, value) -> isMathItemSpec(value, itemLabel) }
                .findFirst()
        if (findFirst.isPresent) {
            val itemSpec = findFirst.get().value.itemSpec
            return itemSpec!!.upcCode ?: itemSpec.upcCodeCase
        }
        return ""
    }

    private fun getItemSpecIdFromRn(cartonId: String): String {
        val findFirst = Stream.ofNullable(dataState.receiptItemLineMap)
                .filter { (_, value) -> isMathCartonIdAtItemLine(value, cartonId) }
                .findFirst()
        if (findFirst.isPresent) {
            return findFirst.get().value.itemLine.itemSpecId
        }
        return ""
    }

    private fun getQtyInCartonFromRn(cartonId: String): Double {
        val findFirst = Stream.ofNullable(dataState.receiptItemLineMap)
                .filter { (_, value) -> isMathCartonIdAtItemLine(value, cartonId) }
                .findFirst()
        if (findFirst.isPresent) {
            return Stream.ofNullable(findFirst.get().value.itemLine.cartons)
                    .filter { v -> v.cartonNo == cartonId }
                    .findFirst()
                    .get()
                    .qty
        }
        return 0.toDouble()
    }

    fun interceptExpand(itemLineId: String): Boolean {
        val itemLineCompose = dataState.receiptItemLineMap[itemLineId]!!
        val itemSpec = itemLineCompose.itemSpec ?: return false
        val needCollectItemInfo = dataState.configCompose.needCollectItemInfo
        val needCollectItemSmallParcelPackaging = dataState.configCompose.needCollectItemSmallParcelPackaging
        return when (itemInfoChecker.check(
            itemLineCompose.itemLine, itemSpec.itemUoms, needCollectItemInfo, dataState.recurringStorageRateByPallet,
            needCollectItemSmallParcelPackaging, parentViewModel.customerEntry.collectItemGroupAtReceiving
        )) {
            is ItemInfoStatus.Available -> false
            is ItemInfoStatus.Inactive -> {
                fireEvent {
                    SingleItemConfigUiEvent.ShowAlert(message = getFormattedString(R.string.error_item_status_is_inactive, itemSpec.name))
                }
                true
            }
            is ItemInfoStatus.NeedActivate -> {
                activateItemAndExpand(itemLineCompose.itemLineId, itemSpec)
                true
            }
            is ItemInfoStatus.NeedCollect -> {
                fireEvent { SingleItemConfigUiEvent.AskCollectItemInfo(itemLineCompose, needCollectItemSmallParcelPackaging) }
                true
            }
        }
    }

    /**
     * Activate ItemSpec and then expand panel.
     */
    private fun activateItemAndExpand(itemLineId: String, itemSpec: ItemSpecEntry) = request(itemInfoChecker.activateRequest(itemSpec)) {
        setDataState {
            withItemLine(itemLineId) {
                itemLine.itemSpec?.status = ItemSpecEntry.STATUS_ACTIVE
                itemLine.itemSpecInfo?.status = ItemSpecEntry.STATUS_ACTIVE
                copy(isExpanded = true)
            }
        }
    }

    fun goCollectItemInfo(itemLineCompose: ReceiveItemLineCompose, needCollectItemSmallParcelPackaging: Boolean) {
        val itemSpec = itemLineCompose.itemSpec ?: return
        parentViewModel.startProcess(
            LpSetupWorkProcess.CollectItemInfo(
                itemSpec,
                itemLineCompose.itemLine.pictureIds,
                dataState.recurringStorageRateByPallet,
                itemLineCompose.itemLine.customerName,
                needCollectItemSmallParcelPackaging
            )
        )
    }

    fun changeExpandStatus(itemLineId: String, isExpand: Boolean) {
        if (isExpand) {
            val itemLineCompose = dataState.receiptItemLineMap[itemLineId]!!
            itemLineCompose.considerShowAlertAtFirstExpand()
            if (!isScanCartonIdReceive(itemLineCompose)) {
                updateQtyPerPalletSuggestions(itemLineId, itemLineCompose.checkedTemplate)
            }
        }
        setDataState {
            withItemLine(itemLineId) { copy(isFirstExpand = false, isExpanded = isExpand) }
        }
    }

    private fun isScanCartonIdReceive(itemLineCompose: ReceiveItemLineCompose): Boolean {
        return itemLineCompose.receiveMethod == ReceiveMethodEntry.RECEIVE_BY_CARTON && itemLineCompose.isScanCartonId
    }

    private fun ReceiveItemLineCompose.considerShowAlertAtFirstExpand() {
        if (!isFirstExpand) return // Alert Seasonal Pack
        if (requireCollectSeasonalPack) fireEvent { SingleItemConfigUiEvent.ShowAlert(title = getString(R.string.text_remind_seasonal_pack)) } // Alert High Value Item
        if (needAlertHighValue) fireEvent { SingleItemConfigUiEvent.ShowAlert(title = getString(R.string.msg_alert_high_value_item)) }
    }

    fun selectGoodsType(itemLineId: String, goodsType: String) {
        setDataState {
            withItemLine(itemLineId) { copy(selectedGoodsType = goodsType) }
        }
    }

    fun updateGoodsTypePhotos(itemLineId: String, photos: List<String>) {
        setDataState {
            withItemLine(itemLineId) { copy(damagedPhotos = photos) }
        }
    }

    fun selectConfigurationMode(itemLineId: String, mode: ConfigurationMode) {
        setDataState {
            withItemLine(itemLineId) { copy(selectedConfigurationMode = mode) }
        }
    }

    fun selectTiHi(itemLineId: String, template: SingleItemLpTemplateEntry?) {
        setDataState {
            withItemLine(itemLineId) { copy(checkedTemplate = template).updateSelectedUnitByTemplate() }
        }
    }

    fun addTiHi(itemLineId: String, ti: String?, hi: String?) {
        ti ?: return
        hi ?: return
        val tiQty = ti.toIntOrNull()
        val hiQty = hi.toIntOrNull()
        if (tiQty == null || hiQty == null) {
            showToast(R.string.msg_invalid_qty_format)
            return
        }
        val itemLineCompose = dataState.receiptItemLineMap[itemLineId]!!
        launch {
            val itemTemplateName = "${tiQty}x$hiQty" // First search lpTemplate by ti & hi
            val lpTemplateResult = requestAwait(repository.searchLpTemplate(tiQty.toDouble(), hiQty.toDouble())).onFailure {
                return@launch
            }
            val lpTemplate = lpTemplateResult.getOrNull()?.run {
                val templateOfCustomer = this.firstOrNull { it.customerId == customerEntry.orgId }
                templateOfCustomer ?: this.firstOrNull()
            }

            val createEntry = SingleItemLpTemplateCreateEntry().apply {
                this.itemSpecId = itemLineCompose.itemSpecId
                this.unitId = itemLineCompose.selectedUnit?.id
                this.scene = SceneEntry.INBOUND
            }
            val result = if (lpTemplate == null) { // No lpTemplate of same ti & hi, should create one
                createEntry.apply {
                    this.name = itemTemplateName
                    this.description = itemTemplateName
                    this.layer = hiQty.toString()
                    this.totalQty = (tiQty * hiQty).toString()
                    this.customerId = customerEntry.orgId
                }
                requestAwait(repository.createSingleItemAndLpTemplate(createEntry))
            } else { // LpTemplate exist
                // Search SingleItemLpTemplate of disable status, enable it and use it as result if exist
                val itemLpTemplateResult = requestAwait(repository.searchSingleItemLpTemplate(SingleItemLpTemplateSearchEntry().apply {
                    itemSpecId = createEntry.itemSpecId
                    this.unitIds = listOf(createEntry.unitId)
                    scene = SceneEntry.INBOUND
                    this.lpConfigurationTemplateId = lpTemplate.id
                    this.name = lpTemplate.name
                    status = SingleItemLpTemplateStatusEntry.DISABLE
                }))

                if (itemLpTemplateResult.isFailure) return@launch
                val disabledItemLpTemplate = itemLpTemplateResult.getOrNull()?.firstOrNull()
                if (disabledItemLpTemplate == null) { // create a singleItemLpTemplate by lpTemplate
                    createEntry.apply {
                        this.lpConfigurationTemplateId = lpTemplate.id
                        this.name = lpTemplate.name
                    }
                    requestAwait(repository.createSingleItemLpTemplate(createEntry))
                } else {
                    requestAwait(repository.enableSingleItemLpTemplate(disabledItemLpTemplate))
                }
            }
            if (result.isSuccess) {
                getAndSetLpTemplates()
            }
        }
    }

    fun removeTiHi(itemLineId: String, template: SingleItemLpTemplateEntry) {
        request(repository.removeSingleItemLpTemplate(template.id)) {
            setDataState {
                withItemLine(itemLineId) {
                    copy(
                        allTemplates = allTemplates - template,
                        checkedTemplate = if (checkedTemplate == template) null else checkedTemplate
                    ).updateSelectedUnitByTemplate()
                }
            }
        }
    }

    fun updateQtyPerPallet(itemLineId: String, qtyStr: String?, inputDone: Boolean) {
        if (inputDone) {
            val itemLineCompose = dataState.receiptItemLineMap[itemLineId]!!
            runCatching { validateQtyPerPallet(itemLineCompose, qtyStr) }.onFailure {
                showToast(it.message!!)
                return
            }
        }
        setDataState {
            withItemLine(itemLineId) { copy(qtyPerPallet = qtyStr) }
        }
    }

    @Throws
    private fun validateQtyPerPallet(itemLineCompose: ReceiveItemLineCompose, qtyStr: String?): Double {
        qtyStr ?: throw getString(R.string.msg_input_total_qty_per_pallet).toException
        val qty = qtyStr.toDoubleOrNull() ?: throw getString(R.string.msg_invalid_qty_format).toException
        if (qty <= 0.0) {
            throw getString(R.string.msg_ti_hi_cannot_be_zero).toException
        }
        if (itemLineCompose.selectedTemplate?.unitEntry != null && itemLineCompose.selectedUnit != null) {
            val templateUnit = itemLineCompose.selectedTemplate!!.unitEntry
            val templateTotalQty = itemLineCompose.selectedTemplate!!.singleLpTemplateEntry?.totalQty ?: 0.0
            if (templateUnit.id == itemLineCompose.selectedUnit.id && qty > templateTotalQty) {
                throw getFormattedString(
                    R.string.exceeds_lp_template_qty, qtyStr, templateTotalQty.toInt().toString()
                ).toException
            }
        }
        return qty
    }

    fun selectUom(itemLineId: String, unit: UnitEntry) {
        setDataState {
            withItemLine(itemLineId) { copy(selectedUnit = unit) }
        }
        setDataState {
            copy(receiptItemLineMap = this.receiptItemLineMap.mapValues { (_, value) ->
                val templates = value.allTemplates
                val checkedTemplate = when {
                    templates.any {
                        it.isDefault && TextUtils.equals(
                            it.unitId, unit.id
                        )
                    } -> templates.first { it.isDefault && TextUtils.equals(it.unitId, unit.id) }
                    else -> templates.firstOrNull()
                }
                value.copy(checkedTemplate = checkedTemplate)
            })
        }
        withDataState { updateQtyPerPalletSuggestions(itemLineId, this.receiptItemLineMap[itemLineId]?.checkedTemplate) }
    }

    fun updateLotNumber(itemLineId: String, lotNumber: String) {
        runCatching { validateLotNumber(itemLineId, lotNumber) }.onFailure {
            showToast(it.message!!)
            return
        }
        setDataState {
            withItemLine(itemLineId) { copy(lotNumber = lotNumber) }
        }
    }

    fun updateCustomerPalletNumber(itemLineId: String, palletNumber: String) {
        setDataState {
            withItemLine(itemLineId) { copy(customerPalletNumber = palletNumber) }
        }
    }

    @Throws
    private fun validateLotNumber(itemLineId: String, lotNumber: String?) {
        lotNumber ?: throw getString(R.string.msg_please_input_lot_no).toException

        val regex = dataState.configCompose.lotNumberRegex
        if (!regex.isNullOrEmpty() && !lotNumber.matches(Regex(regex))) {
            throw getFormattedString(R.string.toast_receiving_lot_violation, regex).toException
        }
        val itemLineCompose = dataState.receiptItemLineMap[itemLineId] ?: return
        if (!itemLineCompose.itemLine.lotNo.isNullOrEmpty() && lotNumber != itemLineCompose.itemLine.lotNo) {
            throw getString(R.string.msg_lot_no_not_match_and_wait).toException
        }
    }

    fun updateShelfLifeDays(itemLineId: String, days: String?, inputDone: Boolean) {
        if (inputDone) {
            runCatching { validateShelfLifeDays(days) }.onFailure {
                showToast(it.message!!)
                return
            }
        }
        setDataState {
            withItemLine(itemLineId) { copy(shelfLifeDays = days) }
        }
    }

    private fun validateShelfLifeDays(days: String?) {
        if (days.isNullOrEmpty()) throw getString(R.string.msg_self_life_day_require).toException
        days.toIntOrNull() ?: throw getString(R.string.msg_invalid_qty_format).toException
    }

    fun updateMfgDate(itemLineId: String, mfgDate: Date) {
        setDataState {
            withItemLine(itemLineId) { copy(manufactureDate = mfgDate) }
        }
    }

    fun updateExpirationDate(itemLineId: String, expDate: Date) {
        setDataState {
            withItemLine(itemLineId) { copy(expirationDate = expDate) }
        }
    }

    fun printLpAndSubmit(
        itemLineId: String,
        onShowQtyConfirm: (param: QtyConfirmDialog.Param) -> Flow<QtyConfirmDialog.Param?>,
        onReprintConfirm: (message: String) -> Flow<Boolean?>,
    ) {
        val itemLineCompose = dataState.receiptItemLineMap[itemLineId] ?: return
        runCatching { validateConfiguration(itemLineCompose) }.onFailure {
            showToast(it.message!!)
            return
        }
        launch { // check location and item
            parentViewModel.checkLocationItem(itemLineCompose).onFailure {
                it.message?.let { message -> showToast(message) }
                return@launch
            } // check printer
            val printData = repository.getPrintData(lpLabelSize)
            if (!PrintUtil.hasPrinter(printData)) {
                fireEvent { SingleItemConfigUiEvent.SetupPrinter }
                return@launch
            }

            // confirm lp count
            val lpParam = showLpCountConfirmDialog(itemLineCompose, onShowQtyConfirm) ?: return@launch

            // print
            val result = if (itemLineCompose.receiveMethod == ReceiveMethodEntry.RECEIVE_BY_CARTON
                && itemLineCompose.isScanCartonId
                && !itemLineCompose.cartonId.isNullOrEmpty()
            ) {
                updateLpAndPrintWhenByCartonReceive(itemLineCompose, printData, onReprintConfirm)
            } else {
                updateLpAndPrint(itemLineCompose, lpParam, printData, onReprintConfirm)
            }
            if (!result) return@launch

            // all done, refresh data
            changeExpandStatus(itemLineId, false)
            showSnack(SnackType.SuccessV1(), R.string.toast_update_success)
            delay(300)
            parentViewModel.refreshTaskItemLines()
        }
    }

    private suspend fun showLpCountConfirmDialog(
        itemLineCompose: ReceiveItemLineCompose,
        onShowQtyConfirm: (param: QtyConfirmDialog.Param) -> Flow<QtyConfirmDialog.Param?>,
    ): QtyConfirmDialog.Param? {
        val (suggestLpCount, hasNotFullSubmit, notFullSubmitQty) = getSuggestLpCount(itemLineCompose)
        val hasReceived = itemLineCompose.itemLine.receivedQty > 0
        var toReceiveQty =
            if (hasReceived) itemLineCompose.itemLine.needReceiveBaseQty.toInt() else itemLineCompose.itemLine.qty.toInt()
        if (toReceiveQty < 0) toReceiveQty = 0
        val toReceiveUnitName =
            if (hasReceived) (itemLineCompose.itemLine.receivedBaseUnit?.name ?: "") else itemLineCompose.itemLine.itemUnit.name
        val qtyPerPallet = itemLineCompose.qtyPerPallet?.toDoubleOrNull()?.toInt()?: 0
        val lpUnitName = itemLineCompose.selectedUnit!!.name
        val lpParam = onShowQtyConfirm(
            QtyConfirmDialog.Param(
                lpCount = suggestLpCount,
                qtyPerPallet = qtyPerPallet,
                initialReceivingText = if (hasNotFullSubmit) "${(suggestLpCount - 1) * qtyPerPallet}+${notFullSubmitQty.toInt()}" else "${suggestLpCount * qtyPerPallet}",
                lpUnitName = lpUnitName,
                toReceiveText = "$toReceiveQty $toReceiveUnitName",
                receivingQty = if (hasNotFullSubmit) ((suggestLpCount - 1) * qtyPerPallet + notFullSubmitQty.toInt()) else suggestLpCount * qtyPerPallet
            )
        ).firstOrNull() ?: return null

        if (lpParam.lpCount <= 0) {
            showToast(R.string.lp_count_must_greater_than_zero)
            return null
        }
        return lpParam
    }

    private suspend fun updateLpAndPrintWhenByCartonReceive(
        itemLineCompose: ReceiveItemLineCompose,
        printData: PrintData,
        onReprintConfirm: (message: String) -> Flow<Boolean?>
    ): Boolean {
        createAndAddLpsWithPrint(
            isCartonReceive = true,
            lpCount = 1,
            isNotFullSubmit = false,
            notFullSubmitQty = -1.0,
            itemLineCompose = itemLineCompose.apply { isPartial = false },
            printData = printData,
            onReprintConfirm = onReprintConfirm
        ).onFailure {
            parentViewModel.refreshTaskItemLines()
            return false
        }
        return true
    }

    private suspend fun updateLpAndPrint(
        itemLineCompose: ReceiveItemLineCompose,
        lpParam: QtyConfirmDialog.Param,
        printData: PrintData,
        onReprintConfirm: (message: String) -> Flow<Boolean?>
    ) : Boolean {
        val qtyPerPallet = itemLineCompose.qtyPerPallet?.toDouble()?:0.0
        val fullLpCount = floor(lpParam.receivingQty / qtyPerPallet).toInt()
        val isPartialFirstPallet = fullLpCount == 0
        val notFullQty = lpParam.receivingQty % qtyPerPallet
        val needSubmitNotFullLp = notFullQty > 0
        if (isPartialFirstPallet || !needSubmitNotFullLp) {// Only Partical or Full Pallet
            createAndAddLpsWithPrint(
                    isCartonReceive = false,
                    lpCount = if (isPartialFirstPallet) 1 else lpParam.lpCount,
                    isNotFullSubmit = isPartialFirstPallet,
                    notFullSubmitQty = if (isPartialFirstPallet) notFullQty else -1.0,
                    itemLineCompose = itemLineCompose.apply { isPartial = isPartialFirstPallet },
                    printData = printData,
                    onReprintConfirm = onReprintConfirm
            ).onFailure {
                parentViewModel.refreshTaskItemLines()
                return false
            }
        } else {//Full Pallet with Partical Pallet(only one Partical Pallet)
            val fullPalletResult = createAndAddLps(
                    lpCount = lpParam.lpCount - 1,// minus one Partical Pallet count
                    isNotFullSubmit = false,
                    itemLineCompose = itemLineCompose.apply { isPartial = false },
                    printData = printData
            )
            var particalPalletResult:Result<LpJobEntry?>? = null
            if (fullPalletResult.isSuccess && fullPalletResult.getOrNull() != null) {
                particalPalletResult = createAndAddLps(
                        lpCount = 1,
                        isNotFullSubmit = true,
                        notFullSubmitQty = notFullQty.toDouble(),
                        itemLineCompose = itemLineCompose.apply { isPartial = true },
                        printData = printData
                )
            }

            val printJobEntry = getPrintJobEntry(fullPalletResult, particalPalletResult)
            if (printJobEntry == null) {
                parentViewModel.refreshTaskItemLines()
                return false
            }
            printLps(printJobEntry, printData, onReprintConfirm)
        }
        return true
    }

    private fun getPrintJobEntry(fullPalletResult: Result<LpJobEntry?>, particalPalletResult: Result<LpJobEntry?>?): LpJobEntry? {
        if (fullPalletResult.isSuccess && fullPalletResult.getOrNull() != null) {
            val lpJobEntry = fullPalletResult.getOrNull()
            particalPalletResult?.let {
                val particalJobEntry = it.getOrNull()
                if (it.isSuccess && particalJobEntry != null) {
                    lpJobEntry?.jobIds?.addAll(particalJobEntry.jobIds)
                    lpJobEntry?.zplCode = joinZplCode(lpJobEntry!!, particalJobEntry)
                }
            }
            return lpJobEntry
        }
        return null
    }

    private fun joinZplCode(fullPalletLpCode: LpJobEntry, particalPalletLpCode: LpJobEntry?): String {
        var zplCode = ""
        if (!TextUtils.isEmpty(fullPalletLpCode.zplCode)) {
            zplCode = fullPalletLpCode.zplCode
        }
        if (!TextUtils.isEmpty(particalPalletLpCode?.zplCode)) {
            zplCode += particalPalletLpCode?.zplCode
        }
        return zplCode
    }

    fun printILPForReceiveAll(printType: ReceiveAllPrintType, onReprintConfirm: (message: String) -> Flow<Boolean?>) {
        if (CollectionUtil.isNullOrEmpty(dataState.receiptItemLineMap.values)) {
            return
        }

        launch {
            val printData = repository.getPrintData(lpLabelSize)
            if (!PrintUtil.hasPrinter(printData)) {
                fireEvent { SingleItemConfigUiEvent.SetupPrinter }
                return@launch
            }
            createAndAddLpsForReceiveAll(printData, onReprintConfirm, printType).onSuccess {
                completeStep()
            }
        }
    }

    private suspend fun createAndAddLpsWithPrint(
        isCartonReceive: Boolean,
        lpCount: Int,
        isNotFullSubmit: Boolean,
        notFullSubmitQty: Double = -1.0,
        itemLineCompose: ReceiveItemLineCompose,
        printData: PrintData,
        onReprintConfirm: (message: String) -> Flow<Boolean?>,
    ): Result<Boolean> {
        val failsResult = Result.failure<Boolean>("".toException)
        val createLpResult = if (isCartonReceive) {
            getLpForCartonId(lpCount, isNotFullSubmit, notFullSubmitQty, itemLineCompose)
        } else {
            //crate lps
            batchCreateLps(
                lpCount, isNotFullSubmit, notFullSubmitQty, itemLineCompose
            ).onFailure { return failsResult }
        }
        val lpCreateResponseEntry = createLpResult.getOrNull() ?: return failsResult // add lps
        //setup lps
        val addLpResult =
            batchAddLps(isNotFullSubmit, notFullSubmitQty, lpCreateResponseEntry.lpIds, itemLineCompose).onFailure { return failsResult }
        val failedLps = addLpResult.getOrNull()!!.filter { !it.success }
        if (failedLps.isNotEmpty()) {
            val error = failedLps.joinToString {
                getFormattedString(R.string.label_lp_setup_submit_respond, it.lpId, it.errorMessage)
            }
            fireEvent { SingleItemConfigUiEvent.ShowAlert(title = getString(R.string.title_alert), message = error) }
            return failsResult
        }
        //create print jobs
        val printJobResult = batchCreatePrintJobs(lpCount, isNotFullSubmit, notFullSubmitQty, itemLineCompose, printData.paperSize!!,
                lpCreateResponseEntry.lpIds).onFailure { return failsResult }
        val printJobs = printJobResult.getOrNull() ?: return failsResult
        //print
        return printLps(printJobs, printData, onReprintConfirm)
    }

    private suspend fun createAndAddLps(
            lpCount: Int,
            isNotFullSubmit: Boolean,
            notFullSubmitQty: Double = -1.0,
            itemLineCompose: ReceiveItemLineCompose,
            printData: PrintData,
    ): Result<LpJobEntry?> {
        val failsResult = Result.failure<LpJobEntry>("".toException)
        //crate lps
        val createLpResult = batchCreateLps(
                lpCount, isNotFullSubmit, notFullSubmitQty, itemLineCompose
        ).onFailure { return failsResult }
        val lpCreateResponseEntry = createLpResult.getOrNull() ?: return failsResult // add lps
        //setup lps
        val addLpResult =
                batchAddLps(isNotFullSubmit, notFullSubmitQty, lpCreateResponseEntry.lpIds, itemLineCompose).onFailure { return failsResult }
        val failedLps = addLpResult.getOrNull()!!.filter { !it.success }
        if (failedLps.isNotEmpty()) {
            val error = failedLps.joinToString {
                getFormattedString(R.string.label_lp_setup_submit_respond, it.lpId, it.errorMessage)
            }
            fireEvent { SingleItemConfigUiEvent.ShowAlert(title = getString(R.string.title_alert), message = error) }
            return failsResult
        }
        //create print jobs
        return batchCreatePrintJobs(lpCount, isNotFullSubmit, notFullSubmitQty, itemLineCompose, printData.paperSize!!,
                lpCreateResponseEntry.lpIds).onFailure { return failsResult }
    }

    private suspend fun createAndAddLpsForReceiveAll(
        printData: PrintData,
        onReprintConfirm: (message: String) -> Flow<Boolean?>,
        printType: ReceiveAllPrintType
    ): Result<Boolean> {
        val failsResult = Result.failure<Boolean>("".toException)
        val createLpsResult =
            (if (printType == PrintBySku) createLpsBySku(printData.paperSize) else createLpsByReceipt(printData.paperSize)).onFailure { return failsResult }
        val printJobs =
            if (CollectionUtil.isNotNullOrEmpty(createLpsResult.getOrNull())) createLpsResult.getOrNull() else return failsResult
        val addLpResult = batchAddLpsForReceiveAll(printJobs!!, printType).onFailure { return failsResult }
        val failedLps = addLpResult.getOrNull()!!.filter { !it.success }
        if (failedLps.isNotEmpty()) {
            val error = failedLps.joinToString {
                getFormattedString(R.string.label_lp_setup_submit_respond, it.lpId, it.errorMessage)
            }
            fireEvent { SingleItemConfigUiEvent.ShowAlert(title = getString(R.string.title_alert), message = error) }
            return failsResult
        }
        val printJob = LpJobEntry()
        printJob.jobIds = mutableListOf()
        if (printType == PrintBySku || printData.paperSize == LabelSizeEntry.TWO_ONE) {// PrintBySku or 2X1 -> print by jobId
            printJobs.forEach {
                printJob.jobIds.addAll(it.jobIds)
            }
        } else {// PrintByReceipt and 4X6 -> print by commands
            printJob.zplCode =
                Stream.ofNullable(printJobs.map { lpJobEntry -> lpJobEntry.zplCode }.toList()).collect(Collectors.joining("\r\n"))
        }
        return printLps(printJob, printData, onReprintConfirm)
    }

    private suspend fun batchAddLpsForReceiveAll(
        printJobs: List<LpJobEntry>,
        printType: ReceiveAllPrintType
    ): Result<List<LPSetupResultEntry>?> {
        return when (printType) {
            PrintBySku -> requestAwait(parentViewModel.batchAddLpsRequestForSku(printJobs, dataState.receiptItemLineMap))
            PrintByReceipt -> requestAwait(parentViewModel.batchAddLpsRequestForReceipt(printJobs, dataState.receiptItemLineMap))
        }
    }

    private suspend fun getLpForCartonId(
        lpCount: Int,
        isNotFullSubmit: Boolean,
        notFullSubmitQty: Double = -1.0,
        itemLineCompose: ReceiveItemLineCompose
    ): Result<LpCreateResponseEntry?> {
        val failsResult = Result.failure<LpCreateResponseEntry>("".toException)
        val cartonId = itemLineCompose.cartonId!!
        val getLpForCartonIdResult = requestAwait(repository.getLpForCartonId(cartonId)).onFailure { return failsResult }
        val lpViews = getLpForCartonIdResult.getOrNull()
        if (!lpViews.isNullOrEmpty()) {
            return Result.success(LpCreateResponseEntry().apply {
                this.lpIds = listOf(lpViews[0].id)
            })
        }
        val batchCreateLpsResult = batchCreateLps(lpCount, isNotFullSubmit, notFullSubmitQty, itemLineCompose).onFailure { return failsResult }
        val lpIdResponse = batchCreateLpsResult.getOrNull()?: return failsResult
        requestAwait(repository.updateLpForCartonId(cartonId, lpIdResponse.lpIds[0])).onSuccess {
            return Result.success(LpCreateResponseEntry().apply {
                this.lpIds = listOf(lpIdResponse.lpIds[0])
            })
        }
        return failsResult
    }

    private suspend fun batchCreateLps(
        lpCount: Int,
        isNotFullSubmit: Boolean,
        notFullSubmitQty: Double = -1.0,
        itemLineCompose: ReceiveItemLineCompose,
    ): Result<LpCreateResponseEntry?> {
        val lpType = if (itemLineCompose.receiveMethod == ReceiveMethodEntry.RECEIVE_BY_CARTON && itemLineCompose.isMixItemForCarton) {
            LpTypeEntry.CLP
        } else {
            LpTypeEntry.ILP
        }
        val lpBatchCreateEntry = LpBatchCreateEntry().apply {
            this.count = lpCount
            this.type = lpType
            this.receiptId = itemLineCompose.itemLine.receiptId
            this.lpItemSpecId = itemLineCompose.itemSpecId
            this.confId = if (isNotFullSubmit) null else itemLineCompose.selectedTemplate?.singleLpTemplateEntry?.id // 4*6
            this.locationId = selectedLocation.id // 4*6
            this.itemLineId = itemLineCompose.itemLineId // 4*6
            this.lpItemQty =
                if (isNotFullSubmit) notFullSubmitQty.toString() else itemLineCompose.qtyPerPallet?.toDouble()?.toString()?: "0.0" // //
            // 4*6
            this.lotNo = itemLineCompose.lotNumber ?: "" // 4*6
            this.mfgDate = itemLineCompose.manufactureDate // 2*1
            this.expirationDate = itemLineCompose.expirationDate // 2*1
            this.printScenario = ScenarioEntry.RECEIVE_WITH_SINGLE_ITEM // 2*1
        }
        val request = repository.batchCreateLp(lpBatchCreateEntry)
        return requestAwait(request)
    }

    private suspend fun batchCreatePrintJobs(
        lpCount: Int,
        isNotFullSubmit: Boolean,
        notFullSubmitQty: Double = -1.0,
        itemLineCompose: ReceiveItemLineCompose,
        labelSize: LabelSizeEntry,
        lpIds: List<String>
    ): Result<LpJobEntry?> {
        val lpType = if (itemLineCompose.receiveMethod == ReceiveMethodEntry.RECEIVE_BY_CARTON && itemLineCompose.isMixItemForCarton) {
            LpTypeEntry.CLP
        } else {
            LpTypeEntry.ILP
        }
        val lpBatchCreateEntry = LpBatchCreateEntry().apply {
            this.count = lpCount
            this.type = lpType
            this.receiptId = itemLineCompose.itemLine.receiptId
            this.lpItemSpecId = itemLineCompose.itemSpecId
            this.confId = if (isNotFullSubmit) null else itemLineCompose.selectedTemplate?.singleLpTemplateEntry?.id // 4*6
            this.locationId = selectedLocation.id // 4*6
            this.itemLineId = itemLineCompose.itemLineId // 4*6
            this.lpItemQty =
                if (isNotFullSubmit) notFullSubmitQty.toString() else itemLineCompose.qtyPerPallet?.toDouble()?.toString()?: "0.0" // 4*6
            this.lotNo = itemLineCompose.lotNumber ?: "" // 4*6
            this.mfgDate = itemLineCompose.manufactureDate // 2*1
            this.expirationDate = itemLineCompose.expirationDate // 2*1
            this.printScenario = ScenarioEntry.RECEIVE_WITH_SINGLE_ITEM // 2*1
            this.lpIds = lpIds
        }
        val request = if (labelSize == LabelSizeEntry.FOUR_SIX) {
            repository.createPrintJobs4X6(lpBatchCreateEntry)
        } else {
            repository.createPrintJobs2X1(lpBatchCreateEntry)
        }
        return requestAwait(request)
    }

    private suspend fun createLpsBySku(labelSize: LabelSizeEntry?): Result<List<LpJobEntry>?> {
        val list = mutableListOf<LpBatchCreateEntry>()
        dataState.receiptItemLineMap.values.forEach {
            val createEntry = LpBatchCreateEntry().apply {
                this.count = 1
                this.type = LpTypeEntry.ILP
                this.receiptId = it.itemLine.receiptId
                this.lpItemSpecId = it.itemSpecId
                this.locationId = selectedLocation.id
                this.itemLineId = it.itemLineId
                this.lotNo = it.itemLine.lotNo
                this.lpItemQty = (it.itemLine.qty / 1).toInt().toString()
            }
            list.add(createEntry)
        }
        val request = if (labelSize == LabelSizeEntry.FOUR_SIX) {
            repository.create4X6Lp(list)
        } else {
            repository.create2X1Lp(list)
        }
        return requestAwait(request)
    }

    private suspend fun createLpsByReceipt(labelSize: LabelSizeEntry?): Result<List<LpJobEntry>?> {
        val list = mutableListOf<LpBatchCreateEntry>()
        val receiptList = Lists.uniq(dataState.receiptItemLineMap.values.map { it.itemLine.receiptId }.toList())
        receiptList.forEach { receiptId ->
            val receiveItemLineComposes = dataState.receiptItemLineMap.values.filter { it.itemLine.receiptId == receiptId }
            val createEntry = LpBatchCreateEntry().apply {
                this.count = 1
                this.type = LpTypeEntry.ILP
                this.receiptId = receiptId
                this.lpItemSpecId = receiveItemLineComposes[0].itemSpecId
                this.locationId = selectedLocation.id
                val lpItemQty = countLpItemQty(receiveItemLineComposes)
                if (isSameUnitForItemLine(receiveItemLineComposes) && lpItemQty > 0) {
                    this.lpItemQty = lpItemQty.toString()
                }
            }
            list.add(createEntry)
        }
        val request = if (labelSize == LabelSizeEntry.FOUR_SIX) {
            repository.printILPByReceipt(list)
        } else {
            repository.create2X1Lp(list)
        }
        return requestAwait(request)
    }

    private fun countLpItemQty(receiveItemLineComposes: List<ReceiveItemLineCompose>): Int {
        var qtyCount = 0
        for (receiveItemLineCompose in receiveItemLineComposes) {
            qtyCount += (receiveItemLineCompose.itemLine.qty / 1).toInt()
        }
        return qtyCount
    }

    private suspend fun printLps(
        printJob: LpJobEntry,
        printData: PrintData,
        onReprintConfirm: (message: String) -> Flow<Boolean?>,
    ): Result<Boolean> {
        printData.jobData = if (TextUtils.isEmpty(printJob.zplCode)) PrintData.JobData.ZPL(jobIds = printJob.jobIds)
        else PrintData.JobData.ZPL(printCommands = printJob.zplCode)

        val result = printUtil.printWithFlow(printData, onShowProgress = { showLoading(it) }).firstOrNull()
        return when (result) {
            null -> Result.success(false)
            PrintResult.NoPrinter -> {
                fireEvent { SingleItemConfigUiEvent.SetupPrinter }
                Result.success(false)
            }
            is PrintResult.Success -> Result.success(true)
            is PrintResult.Failed -> {
                val reprint = onReprintConfirm(PrintMsg.formatError(result.printerEntry, result.response.errorMessage)).firstOrNull()
                if (reprint == true) {
                    printLps(printJob, printData, onReprintConfirm)
                } else {
                    Result.success(false)
                }
            }
        }
    }

    private suspend fun batchAddLps(
        isNotFullSubmit: Boolean,
        notFullSubmitQty: Double,
        lpIds: List<String>,
        itemLineCompose: ReceiveItemLineCompose,
    ): Result<List<LPSetupResultEntry>?> {
        val lpTemplateId = if (isNotFullSubmit) null else itemLineCompose.selectedTemplate?.lpConfigurationTemplateId
        val qty = if (isNotFullSubmit) notFullSubmitQty else itemLineCompose.qtyPerPallet?.toDouble()?:0.0
        return requestAwait(parentViewModel.batchAddLpsRequest(lpIds, itemLineCompose, qty, lpTemplateId))
    }

    @Throws
    private fun validateConfiguration(selectedItemLine: ReceiveItemLineCompose) {
        val itemLineId = selectedItemLine.itemLineId
        if (selectedItemLine.requireCollectLotNumber) {
            validateLotNumber(itemLineId, selectedItemLine.lotNumber)
        }
        if (selectedItemLine.needCollectCustomerPallet && selectedItemLine.customerPalletNumber.isNullOrEmpty()) {
            throw getString(R.string.msg_remind_input_customer_pallet_no).toException
        }
        if (selectedItemLine.requireCollectShelfLife) {
            validateShelfLifeDays(selectedItemLine.shelfLifeDays)
        }
        if (selectedItemLine.requireCollectMfgDate && selectedItemLine.manufactureDate == null) {
            throw getString(R.string.msg_mfg_date_require).toException
        }
        if (selectedItemLine.requireCollectExpDate && selectedItemLine.expirationDate == null) {
            throw getString(R.string.msg_expiration_date_require).toException
        }
        validateGoodsType(selectedItemLine)
        if (selectedItemLine.selectedUnit == null) {
            throw getString(R.string.msg_please_select_oum).toException
        }
        validateQtyPerPallet(selectedItemLine, selectedItemLine.qtyPerPallet)

    }

    @Throws
    private fun validateGoodsType(itemLineCompose: ReceiveItemLineCompose) {
        val goodsType = itemLineCompose.selectedGoodsType ?: throw getString(R.string.msg_select_item_condition).toException
        val location = parentViewModel.selectedLocation!!
        if (location.inventoryStatus == LocationInventoryStatus.REWORK && Constant.GOODS_TYPE_REWORK_NEEDED != goodsType) {
            throw getFormattedString(R.string.msg_rework_location_xx_support_item_condition_rework_needed, location.name).toException
        }
        if (location.inventoryStatus != LocationInventoryStatus.REWORK && Constant.GOODS_TYPE_REWORK_NEEDED == goodsType) {
            throw getString(R.string.msg_rework_needed_condition_only_allow_select_rework_location).toException
        }
        if (itemLineCompose.needCollectPhoto && itemLineCompose.damagedPhotos.isEmpty()) {
            throw getString(R.string.msg_please_capture_photo).toException
        }
    }

    private fun isSameUnitForItemLine(receiptItemLines: List<ReceiveItemLineCompose>?): Boolean {
        receiptItemLines?.let {
            val unitName: String? = it[0].itemLine.unitName
            return Stream.ofNullable(it).allMatch { itemLineCompose -> itemLineCompose.itemLine.unitName == unitName }
        }
        return false
    }

    /**
     * Get Lp Count Suggestion.
     * Return <lpCount:Int, hasNotFullSubmit:Boolean, notFullSubmitQty:Double>
     */
    private fun getSuggestLpCount(itemLineCompose: ReceiveItemLineCompose): Tuple3<Int, Boolean, Double> {
        var suggestLpCount = 1
        var hasNotFullSubmit = false
        var notFullSubmitQty = -1.0

        if (!dataState.configCompose.enableLpCountSuggestion) return Tuple(suggestLpCount, hasNotFullSubmit, notFullSubmitQty)

        requireNotNull(itemLineCompose.qtyPerPallet)
        val qtyPerPallet = itemLineCompose.qtyPerPallet.toDoubleOrNull()
        requireNotNull(qtyPerPallet)
        requireNotNull(itemLineCompose.selectedUnit)
        val selectedBaseQty = itemLineCompose.selectedUnit.baseQty
        val needReceiveBaseQty = itemLineCompose.itemLine.needReceiveBaseQty
        val lpCount = needReceiveBaseQty / (qtyPerPallet * selectedBaseQty)

        if (lpCount <= 1) {
            suggestLpCount = 1
            return Tuple(suggestLpCount, hasNotFullSubmit, notFullSubmitQty)
        }

        hasNotFullSubmit = lpCount != ceil(lpCount)
        if (hasNotFullSubmit) {
            val batchSubmitBaseQty = needReceiveBaseQty % (qtyPerPallet * selectedBaseQty)
            notFullSubmitQty = ceil(batchSubmitBaseQty / selectedBaseQty)
        }
        suggestLpCount = ceil(lpCount).toInt()

        return Tuple(suggestLpCount, hasNotFullSubmit, notFullSubmitQty)
    }

    private fun isReceiveOn4x6Lp() = LabelSizeEntry.FOUR_SIX == PrinterConfig.lpLabelSize(customerEntry, repository.facilityEntry)

    fun goToSummary(itemLineId: String) {
        val itemLineCompose = dataState.receiptItemLineMap[itemLineId]!!
        runCatching { validateConfiguration(itemLineCompose) }.onFailure {
            showToast(it.message!!)
            return
        }
        parentViewModel.startProcess(LpSetupWorkProcess.SingleItemSummary(itemLineCompose.copy()))
    }

    fun completeStep() {
        parentViewModel.closeStep()
    }

    fun addNewItemLine() {
        parentViewModel.startProcess(LpSetupWorkProcess.NewItemLine)
    }

    fun collapseAll() {
        setDataState {
            copy(receiptItemLineMap = receiptItemLineMap.mapValues { it.value.copy(isExpanded = false) })
        }
    }

    fun updateItemInfo(itemLineId: String) {
        val itemLineCompose = dataState.receiptItemLineMap[itemLineId]!!
        val needCollectItemSmallParcelPackaging = dataState.configCompose.needCollectItemSmallParcelPackaging
        fireEvent { SingleItemConfigUiEvent.UpdateItemInfo(itemLineCompose, needCollectItemSmallParcelPackaging) }
    }

    private fun getInvoiceStorage() {
        launch {
            val invoiceStorageEntry = InvoiceStorageEntry()
            invoiceStorageEntry.customerId = customerEntry.orgId
            invoiceStorageEntry.facilityId = repository.facilityId
            requestAwait(repository.getInvoiceStorage(invoiceStorageEntry)).onSuccess {
                val invoiceStorageEntry = it ?: InvoiceStorageViewEntry()
//                if (!invoiceStorageEntry.status) {
//                    if (!TextUtils.isEmpty(invoiceStorageEntry.errorMsg)) {
//                        showToast(invoiceStorageEntry.errorMsg)
//                    }
//                    return@launch
//                }
                setDataStateAwait { copy(recurringStorageRateByPallet = invoiceStorageEntry.recurringStorageRateByPallet) }
            }
        }
    }

    fun isShowReceiveAll(): Boolean {
        return parentViewModel.taskEntry.receiveType == ReceiveTypeEntry.SMALL_PARCEL_RECEIVING
    }

    fun isEnableReceiveAll(): Boolean {
        return Stream.ofNullable(dataState.receiptItemLineMap.values)
            .allMatch { receiptItemLine -> !receiptItemLine.requireCollectLotNumber && !receiptItemLine.requireCollectExpDate && !receiptItemLine.requireCollectMfgDate }
    }

    fun isAllGoodTypes(): Boolean {
        return Stream.ofNullable(dataState.receiptItemLineMap.values)
            .allMatch { receiptItemLine -> Constant.GOODS_TYPE_GOOD.equals(receiptItemLine.itemLine.goodsType, true) }
    }

    private fun isThirdPartyLPNo(lp: String?): Boolean = LPUtil.isThirdPartyLPNo(lp, customerEntry.getThirdPartyLpNoPattern())
}

