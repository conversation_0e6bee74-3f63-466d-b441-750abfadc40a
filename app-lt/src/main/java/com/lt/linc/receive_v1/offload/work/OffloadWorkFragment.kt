package com.lt.linc.receive_v1.offload.work

import android.content.DialogInterface
import android.content.Intent
import android.graphics.Color
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.text.InputType
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.Animation
import android.view.animation.Transformation
import androidx.appcompat.app.AlertDialog
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.annimon.stream.Collectors
import com.annimon.stream.Stream
import com.chad.library.adapter.base.BaseQuickAdapter
import com.customer.widget.GeneralAlertDialog
import com.linc.platform.generaltask.model.GeneralTaskViewEntry
import com.linc.platform.home.more.material.model.MaterialLineViewEntry
import com.linc.platform.receive.ReceiveCommonHelper
import com.linc.platform.receive.model.*
import com.linc.platform.utils.ToastUtil
import com.lt.linc.R
import com.lt.linc.common.extensions.safeCount
import com.lt.linc.common.mvi.ReactiveFragment
import com.lt.linc.common.mvi.ReactiveViewScope
import com.lt.linc.common.mvi.onEvent
import com.lt.linc.common.mvvm.kotlin.extensions.getActivityViewModel
import com.lt.linc.common.mvvm.kotlin.extensions.newFragmentInstance
import com.lt.linc.databinding.FragmentOffloadWorkBinding
import com.lt.linc.dock.dockcheckin.DockReceivePhotoActivity
import com.lt.linc.home.more.material.MaterialCenterActivity
import com.lt.linc.receive_v1.offload.OffloadViewModel
import com.lt.linc.receive_v1.offload.work.adapter.CartonQualityAdapter
import com.lt.linc.receive_v1.offload.work.adapter.OffloadEquipmentSizeAdapter
import com.lt.linc.receive_v1.offload.work.adapter.OffloadMaterialAdapter
import com.lt.linc.receive_v1.offload.work.adapter.OffloadTypeAdapter
import com.lt.linc.util.isTablet
import com.lt.linc.util.v1styledialog.CenterDialog
import com.lt.linc.util.v1widget.UploadPhotoBean
import com.lt.linc.util.v1widget.UploadPhotoCallBack
import com.unis.autotrackdispatcher.annotation.Scroll
import rx.Observable
import java.util.concurrent.TimeUnit

class OffloadWorkFragment : ReactiveFragment<OffloadWorkViewModel, OffloadWorkUiState, FragmentOffloadWorkBinding>(), View.OnClickListener {
    private val activityViewModel by lazy { getActivityViewModel<OffloadViewModel>()!! }
    private lateinit var down: Drawable
    private lateinit var up: Drawable
    private lateinit var finish: Drawable
    private lateinit var mAdapter: OffloadMaterialAdapter
    private lateinit var mEquipmentAdapter: OffloadEquipmentSizeAdapter
    private lateinit var mOffloadEquipmentAdapter: OffloadTypeAdapter
    private lateinit var mOffloadShipAdapter: OffloadTypeAdapter

    companion object {
        fun newInstance() = newFragmentInstance<OffloadWorkFragment>()
    }

    @Scroll
    override fun initView(savedInstanceState: Bundle?) {
        onEvents()
        down = context?.getDrawable(R.drawable.ic_baseline_keyboard_arrow_down_24)!!
        down.setBounds(0, 0, down.minimumWidth, down.minimumHeight)
        up = context?.getDrawable(R.drawable.ic_baseline_keyboard_arrow_up_24)!!
        up.setBounds(0, 0, up.minimumWidth, up.minimumHeight)
        finish = context?.getDrawable(R.drawable.ic_baseline_check_circle_20)!!
        finish.setBounds(0, 0, down.minimumWidth, down.minimumHeight)
        binding?.apply {
            if (isTablet()) {
                setRecycleViewManagerForTablet()
            } else {
                setRecycleViewManager()
            }
            rvOffloadTypeMaterials.layoutManager = LinearLayoutManager(context)
            rvOffloadTypeMaterials.isNestedScrollingEnabled = false
            mAdapter = OffloadMaterialAdapter(res = R.layout.item_offload_type_material)
            rvOffloadTypeMaterials.adapter = mAdapter
            mAdapter.setOnItemChildClickListener(BaseQuickAdapter.OnItemChildClickListener { adapter, _, position ->
                viewModel.removeMaterial(position, (adapter.getItem(position) as MaterialLineViewEntry).id)
            })
            mOffloadEquipmentAdapter = OffloadTypeAdapter(R.layout.item_offload_type)
            rvOffloadTypeEquipment.adapter = mOffloadEquipmentAdapter
            rvOffloadTypeEquipment.isNestedScrollingEnabled = false
            val listEquipment = mutableListOf<OffloadTypeBean>()
            listEquipment.add(OffloadTypeBean(R.drawable.icon_offload_type_container, getString(R.string.text_offload_type_Container)))
            listEquipment.add(OffloadTypeBean(R.drawable.icon_offload_type_trailer, getString(R.string.text_offload_type_trailer)))
            listEquipment.add(OffloadTypeBean(R.drawable.icon_offload_type_cart, getString(R.string.text_offload_type_cart)))
            mOffloadEquipmentAdapter.setNewData(listEquipment)
            mOffloadEquipmentAdapter.setOnItemClickListener { _, _, position ->
                when (position) {
                    0 -> {
                        viewModel.updateEquipment(OffloadEquipmentType.CONTAINER)
                        binding?.rvEquipmentSize?.visibility = View.VISIBLE
                    }
                    1 -> {
                        viewModel.updateEquipment(OffloadEquipmentType.TRAILER)
                        binding?.rvEquipmentSize?.visibility = View.VISIBLE
                    }
                    2 -> {
                        viewModel.updateEquipment(OffloadEquipmentType.CART)
                        binding?.rvEquipmentSize?.visibility = View.GONE
                    }
                }
                mOffloadEquipmentAdapter.setClickPosition(position)
            }

            mOffloadShipAdapter = OffloadTypeAdapter(R.layout.item_offload_type)
            rvOffloadTypeShip.adapter = mOffloadShipAdapter
            rvOffloadTypeShip.isNestedScrollingEnabled = false
            val listShip = mutableListOf<OffloadTypeBean>()
            listShip.add(OffloadTypeBean(R.drawable.icon_offload_type_ship_tl, getString(R.string.text_shipping_method_tl)))
            listShip.add(OffloadTypeBean(R.drawable.icon_offload_type_ship_ltl, getString(R.string.text_shipping_method_ltl)))
            listShip.add(OffloadTypeBean(R.drawable.icon_offload_type_ship_small_parcel, getString(R.string.text_shipping_method_small_parcel)))
            listShip.add(OffloadTypeBean(R.drawable.icon_offload_type_ship_lcl, getString(R.string.text_shipping_method_lcl)))
            mOffloadShipAdapter.setNewData(listShip)
            mOffloadShipAdapter.setOnItemClickListener { _, _, position ->
                if (!isClickLastShipMethod(position)) {
                    when (position) {
                        0 -> viewModel.updateShipMethod(ShippingMethodEntry.TRUCKLOAD)
                        1 -> viewModel.updateShipMethod(ShippingMethodEntry.LTL)
                        2 -> viewModel.updateShipMethod(ShippingMethodEntry.SMALL_PARCEL)
                        3 -> viewModel.updateShipMethod(ShippingMethodEntry.LCL)
                    }
                    mOffloadShipAdapter.setClickPosition(position)
                    removeSelectedEquipmentSize()
                }
            }
            rvEquipmentSize.layoutManager = GridLayoutManager(context, 6)
            mEquipmentAdapter = OffloadEquipmentSizeAdapter(res = R.layout.item_offload_type_enquipment_size)
            rvEquipmentSize.adapter = mEquipmentAdapter
            rvEquipmentSize.isNestedScrollingEnabled = false
            mEquipmentAdapter.setOnItemClickListener { adapter, _, position ->
                viewModel.updateEquipmentSize(adapter.getItem(position) as String)
                mEquipmentAdapter.setClickPosition(position)
            }
            addMaterialOffloadBtn.setOnClickListener(this@OffloadWorkFragment)
            offloadTypeMaterialTitleLl.setOnClickListener(this@OffloadWorkFragment)
            moreMaterialsOffloadTv.setOnClickListener(this@OffloadWorkFragment)
            offloadTypeEquipmentTitleLl.setOnClickListener(this@OffloadWorkFragment)
            offloadTypeShipTitleLl.setOnClickListener(this@OffloadWorkFragment)
            offloadTypeNoteTitleLl.setOnClickListener(this@OffloadWorkFragment)
            offloadTypeSlipSheetsAmountLl.setOnClickListener(this@OffloadWorkFragment)
            offloadTypeCartonQualityLl.setOnClickListener(this@OffloadWorkFragment)
            offloadTypePhotoTitleLl.setOnClickListener(this@OffloadWorkFragment)
            offloadFinishBtn.setOnClickListener(this@OffloadWorkFragment)
            tvOffloadTypeSlipSheetsAmountEdit.setOnClickListener(this@OffloadWorkFragment)
            tvOffloadTypeCartonQualityEdit.setOnClickListener(this@OffloadWorkFragment)
            addMaterialOffloadBtn.visibility =
                if (activityViewModel.dataState.userLevelConfig.DisplayAddMaterial) View.VISIBLE else View.GONE
            expandOperationContent(llOffloadTypeEquipmentContent, offloadTypeEquipmentTitleLl)
            if (activityViewModel.dataState.customerEntry.collectSlipSheets) {
                offloadTypeSlipSheetsAmountLl.visibility = View.VISIBLE
            } else {
                offloadTypeSlipSheetsAmountLl.visibility = View.GONE
            }
            if (activityViewModel.dataState.customerEntry.collectCartonCount) {
                offloadTypeCartonQualityLl.visibility = View.VISIBLE
            } else {
                offloadTypeCartonQualityLl.visibility = View.GONE
            }
        }
        initPhotoView()
    }

    private fun isClickLastShipMethod(position: Int): Boolean {
        val shippingMethod = when (position) {
            0 -> ShippingMethodEntry.TRUCKLOAD
            1 -> ShippingMethodEntry.LTL
            2 -> ShippingMethodEntry.SMALL_PARCEL
            3 -> ShippingMethodEntry.LCL
            else -> ShippingMethodEntry.TRUCKLOAD
        }
        return viewModel.dataState.detailEntry?.shippingMethod == shippingMethod
    }

    private fun removeSelectedEquipmentSize() {
        binding?.apply {
            ivOffloadTypeShipTitle.setImageDrawable(if (llOffloadTypeShipContent.visibility == View.VISIBLE) up else down)
            viewModel.updateEquipmentSize("")
            mEquipmentAdapter.setClickPosition(-1)
        }
    }

    override fun ReactiveViewScope.subscribeToUiState() {
        subscribe(OffloadWorkUiState::note) {
            loadNote(note = it)
        }
        subscribe(OffloadWorkUiState::slipSheetsAmount) {
            showSlipSheetsAmount(slipSheetsAmount = it)
        }
        subscribe(OffloadWorkUiState::taskEntry) {
            it.tryGet { taskEntry ->
                showCartonQuality(taskEntry = taskEntry)
            }
        }
        subscribe(OffloadWorkUiState::isButtonEnabled) {
            binding?.offloadFinishBtn?.isEnabled = it
        }
        subscribe(OffloadWorkUiState::detailInfo) {
            it?.tryGet { detail ->
                when (detail) {
                    is OffloadWorkUiState.LoadInfo.DetailInfo -> loadDetailInfo(detail.detailBean)
                    is OffloadWorkUiState.LoadInfo.MaterialsInfo -> loadMaterial(detail.materials)
                    is OffloadWorkUiState.LoadInfo.RemoveMaterial -> removeMaterialByPosition(detail.position)
                    else -> {
                    }
                }
            }
        }
        subscribe(OffloadWorkUiState::selectType) {
            it?.tryGet { type ->
                when (type) {
                    is OffloadWorkUiState.SelectType.EquipmentType -> afterEquipmentType(type.type)
                    is OffloadWorkUiState.SelectType.ShipMethodType -> {}
                    is OffloadWorkUiState.SelectType.EquipmentSize -> afterEquipmentSize(type.size)
                }
            }
        }
        subscribe(OffloadWorkUiState::progressStatus) {
            it?.tryGet { detail ->
                when (detail) {
                    is OffloadWorkUiState.ProgressStatus.Close -> autoCloseStepView()
                    is OffloadWorkUiState.ProgressStatus.Error -> onForceClose()
                }
            }
        }
    }

    override fun createViewModel() = OffloadWorkViewModel(activityViewModel = activityViewModel)

    override fun onClick(v: View) {
        binding?.apply {
            if (v.id != R.id.offload_type_slip_sheets_amount_ll && v.id != R.id.tv_offload_type_slip_sheets_amount_edit) {
                collapseSlipSheetsAmountContent()
            }
            if (v.id != R.id.offload_type_carton_quality_ll && v.id != R.id.tv_offload_type_carton_quality_edit) {
                collapseCartonQualityContent()
            }
            when (v.id) {
                R.id.add_material_offload_btn -> {
                    offloadTypeMaterialTitleLl.visibility = View.VISIBLE
                    if (llOffloadTypeMaterialContent.visibility == View.GONE) {
                        expandOperationContent(llOffloadTypeMaterialContent, offloadTypeMaterialTitleLl)
                    }
                    addMaterialOffloadBtn.visibility = View.GONE
                }
                R.id.offload_finish_btn -> {
                    refreshMaterialLine()
                }
                R.id.offload_type_material_title_ll -> {
                    if (mAdapter.data.isEmpty()) {
                        ivOffloadTypeMaterialTitle.setImageDrawable(if (llOffloadTypeMaterialContent.visibility == View.VISIBLE) down else up)
                    }
                    if (llOffloadTypeMaterialContent.visibility == View.VISIBLE) {
                        collapseOperationContent(llOffloadTypeMaterialContent, offloadTypeMaterialTitleLl)
                    } else {
                        expandOperationContent(llOffloadTypeMaterialContent, offloadTypeMaterialTitleLl)
                    }
                }
                R.id.more_materials_offload_tv -> {
                    addMaterials()
                }
                R.id.offload_type_equipment_title_ll -> {
                    if (!viewModel.isEquipmentSelect()) {
                        ivOffloadTypeEquipmentTitle.setImageDrawable(if (llOffloadTypeEquipmentContent.visibility == View.VISIBLE) down else up)
                    }
                    if (llOffloadTypeEquipmentContent.visibility == View.VISIBLE) {
                        collapseOperationContent(llOffloadTypeEquipmentContent, offloadTypeEquipmentTitleLl)
                    } else {
                        expandOperationContent(llOffloadTypeEquipmentContent, offloadTypeEquipmentTitleLl)
                    }
                }
                R.id.offload_type_ship_title_ll -> {
                    if (!viewModel.isShipMethodSelected()) {
                        ivOffloadTypeShipTitle.setImageDrawable(if (llOffloadTypeShipContent.visibility == View.VISIBLE) down else up)
                    }
                    if (llOffloadTypeShipContent.visibility == View.VISIBLE) {
                        collapseOperationContent(llOffloadTypeShipContent, offloadTypeShipTitleLl)
                    } else {
                        expandOperationContent(llOffloadTypeShipContent, offloadTypeShipTitleLl)
                    }
                }
                R.id.offload_type_note_title_ll -> {
                    if (llOffloadTypeNoteContent.visibility == View.VISIBLE) {
                        collapseOperationContent(llOffloadTypeNoteContent, offloadTypeNoteTitleLl)
                    } else {
                        expandOperationContent(llOffloadTypeNoteContent, offloadTypeNoteTitleLl)
                    }
                }
                R.id.offload_type_slip_sheets_amount_ll -> {
                    if (llOffloadTypeSlipSheetsAmountContent.visibility == View.VISIBLE) {
                        collapseOperationContent(llOffloadTypeSlipSheetsAmountContent, offloadTypeSlipSheetsAmountLl)
                    } else {
                        expandOperationContent(llOffloadTypeSlipSheetsAmountContent, offloadTypeSlipSheetsAmountLl)
                    }
                }
                R.id.offload_type_carton_quality_ll -> {
                    if (llOffloadTypeCartonQualityContent.visibility == View.VISIBLE) {
                        collapseOperationContent(llOffloadTypeCartonQualityContent, offloadTypeCartonQualityLl)
                    } else {
                        expandOperationContent(llOffloadTypeCartonQualityContent, offloadTypeCartonQualityLl)
                    }
                }
                R.id.offload_type_photo_title_ll -> {
                    if (rvOffloadTypePhoto.getAvailablePhotoIds().isNullOrEmpty()) {
                        ivOffloadTypePhotoTitle.setImageDrawable(if (llOffloadTypePhotoContent.visibility == View.VISIBLE) down else up)
                    }
                    if (llOffloadTypePhotoContent.visibility == View.VISIBLE) {
                        collapseOperationContent(llOffloadTypePhotoContent, offloadTypePhotoTitleLl)
                    } else {
                        expandOperationContent(llOffloadTypePhotoContent, offloadTypePhotoTitleLl)
                    }
                }
                R.id.tv_offload_type_slip_sheets_amount_edit -> {
                    binding?.apply {
                        val slipSheetsAmount = tvOffloadTypeSlipSheetsAmount.text.toString()
                        showSlipSheetsAmountInputDialog(slipSheetsAmount)
                    }
                }
                R.id.tv_offload_type_carton_quality_edit -> {
                    binding?.apply {
                        showCartonQualityDialog()
                    }
                }
            }
        }
    }

    private fun showCartonQualityDialog() {
        val receiptEntries = viewModel.dataState.taskEntry.receiptEntries
        val contentView = LayoutInflater.from(context).inflate(R.layout.layout_carton_quality, null)
        val recyclerView = contentView.findViewById<RecyclerView>(R.id.recycler_view)
        val headerView = LayoutInflater.from(context).inflate(R.layout.header_carton_quality, null)
        recyclerView.layoutManager = LinearLayoutManager(context)
        val cartonQualityAdapter = CartonQualityAdapter()
        cartonQualityAdapter.bindToRecyclerView(recyclerView)
        cartonQualityAdapter.addHeaderView(headerView)
        recyclerView.adapter = cartonQualityAdapter
        cartonQualityAdapter.setDiffList(receiptEntries)
        CenterDialog.customized(
                context!!,
                title = getString(R.string.label_carton_quality),
                contentView = contentView,
                positiveClick = {
                    val adapterData = cartonQualityAdapter.getAdapterData()
                    val isContainEmptyQty = adapterData.any { receiptEntry -> receiptEntry.totalPalletQty <= 0 }
                    if (isContainEmptyQty) {
                        ToastUtil.showCenterErrorToast(
                                getString(R.string.msg_please_input_carton_qty),
                                16f,
                                Color.WHITE
                        )
                        return@customized
                    }
                    viewModel.updateCartonQuality(adapterData)
                }).show()
    }

    private fun showSlipSheetsAmountInputDialog(slipSheetsAmount: String) {
        CenterDialog.singleInput(
            context!!,
            title = getString(R.string.label_slip_sheets_quantity),
            titleTextSize = 20f,
            defaultValue = slipSheetsAmount,
            inputType = InputType.TYPE_CLASS_NUMBER,
            hint = getString(R.string.hint_please_input_slip_sheets_quantity),
            autoShowKeyboard = true,
            positiveClick = {
                if (it.isEmpty()) {
                    ToastUtil.showCenterErrorToast(
                        getString(R.string.hint_please_input_slip_sheets_quantity),
                        16f,
                        Color.WHITE
                    )
                    return@singleInput
                }
                viewModel.updateSlipSheetsAmount(it.toInt())
            }).show()
    }

    private fun collapseSlipSheetsAmountContent() {
        binding?.apply {
            if (llOffloadTypeSlipSheetsAmountContent.visibility == View.VISIBLE) {
                collapseOperationContent(llOffloadTypeSlipSheetsAmountContent, offloadTypeSlipSheetsAmountLl)
            }
        }

    }

    private fun collapseCartonQualityContent() {
        binding?.apply {
            if (llOffloadTypeCartonQualityContent.visibility == View.VISIBLE) {
                collapseOperationContent(llOffloadTypeCartonQualityContent, offloadTypeCartonQualityLl)
            }
        }

    }

    private fun initPhotoView() {
        binding?.tvOffloadTypePhotoTitle?.text =
            if (activityViewModel.dataState.customerEntry.allowTakeVideo) getString(R.string.text_offload_photos_or_video) else getString(
                R.string.text_offload_photos
            )
        binding?.rvOffloadTypePhoto?.apply {
            val rn = activityViewModel.dataState.taskEntry.receiptEntries?.get(0)?.id
            val maxTaskVideoLengthInSeconds =
                activityViewModel.dataState.customerEntry.maxTaskVideoLengthInSeconds
            val allowTakeVideo = activityViewModel.dataState.customerEntry.allowTakeVideo
            setVideoParam(rn, maxTaskVideoLengthInSeconds, allowTakeVideo)
            initParamInfo(
                ReceiveCommonHelper.PHOTO_UPLOAD_PARAM_APP,
                ReceiveCommonHelper.PHOTO_UPLOAD_PARAM_MODULE,
                ReceiveCommonHelper.PHOTO_UPLOAD_PARAM_SERVICE
            )
            setUploadPhotoCallBack(object : UploadPhotoCallBack {
                override fun addPhotos(photoIds: List<String>) {
                    updatePhotoIcon(binding?.rvOffloadTypePhoto?.getAvailablePhotoIds() ?: listOf())
                    viewModel.updateOffloadPhotos(photoIds, false)
                }

                override fun deletePhotos(list: List<String>?, isRemoveAll: Boolean) {
                    list ?: return
                    updatePhotoIcon(binding?.rvOffloadTypePhoto?.getAvailablePhotoIds() ?: listOf())
                    viewModel.updateOffloadPhotos(list, true)
                }

            })
        }
    }

    private fun expandOperationContent(v: View, bg: View) {
        when (v.id) {
            R.id.ll_offload_type_equipment_content -> binding!!.tvOffloadSelectTitle.text =
                getString(R.string.text_offload_type_equipment_title)
            R.id.ll_offload_type_ship_content -> binding!!.tvOffloadSelectTitle.text = getString(R.string.text_offload_type_ship_title)
            R.id.ll_offload_type_note_content -> binding!!.tvOffloadSelectTitle.text = getString(R.string.label_note)
            R.id.ll_offload_type_slip_sheets_amount_content -> binding!!.tvOffloadSelectTitle.text = getString(R.string.label_slip_sheets_quantity)
            R.id.ll_offload_type_material_content -> binding!!.tvOffloadSelectTitle.text = getString(R.string.text_offload_type_materials)
            R.id.ll_offload_type_photo_content -> binding!!.tvOffloadSelectTitle.text = getString(R.string.text_offload_photos)
        }
        val matchParentMeasureSpec = View.MeasureSpec.makeMeasureSpec((v.parent as View).width, View.MeasureSpec.EXACTLY)
        val wrapContentMeasureSpec = View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED);
        v.measure(matchParentMeasureSpec, wrapContentMeasureSpec)
        val targetHeight = v.measuredHeight
        v.layoutParams.height = 1
        v.visibility = View.VISIBLE
        bg.setBackgroundResource(R.drawable.ripple_offload_tablet_title_top)
        val a = object : Animation() {
            override fun applyTransformation(interpolatedTime: Float, t: Transformation) {
                v.layoutParams.height = if (interpolatedTime == 1f) ViewGroup.LayoutParams.WRAP_CONTENT
                else (targetHeight * interpolatedTime).toInt()
                v.requestLayout()
            }

            override fun willChangeBounds(): Boolean {
                return true
            }
        }
        a.duration = 300
        v.startAnimation(a)
    }

    private fun collapseOperationContent(v: View, bg: View) {
        val initialHeight = v.measuredHeight
        val a: Animation = object : Animation() {
            override fun applyTransformation(interpolatedTime: Float, t: Transformation) {
                if (interpolatedTime == 1f) {
                    v.visibility = View.GONE
                    bg.setBackgroundResource(R.drawable.ripple_offload_tablet_title)
                } else {
                    v.layoutParams.height = initialHeight - (initialHeight * interpolatedTime).toInt()
                    v.requestLayout()
                }
            }

            override fun willChangeBounds(): Boolean {
                return true
            }
        }
        a.duration = 300
        v.startAnimation(a)
    }

    private fun refreshMaterialLine() {
        if (viewModel.isCaptureMaterial() && mAdapter.data.isEmpty()) {
            showAddMaterialConfirmDialog()
        } else {
            viewModel.finishStep(mEquipmentAdapter.getClickPosition(), binding!!.rvOffloadTypePhoto.getAvailablePhotoIds().safeCount())
        }
    }

    private fun showAddMaterialConfirmDialog() {
        AlertDialog.Builder(fragmentActivity).setTitle(R.string.title_confirm).setMessage(R.string.msg_do_you_want_to_add_materials)
            .setNegativeButton(R.string.no) { dialog, which ->
                dialog.dismiss()
                viewModel.finishStep(
                    mEquipmentAdapter.getClickPosition(), binding!!.rvOffloadTypePhoto.getAvailablePhotoIds().safeCount())
            }.setPositiveButton(R.string.text_yes) { dialog, which ->
                dialog.dismiss()
                if (mAdapter.data.isEmpty()) {
                    binding!!.ivOffloadTypeMaterialTitle.setImageDrawable(up)
                }
                expandOperationContent(binding!!.llOffloadTypeMaterialContent, binding!!.offloadTypeMaterialTitleLl)
                addMaterials()
            }.show()
    }

    private fun addMaterials() {
        val intent = Intent(fragmentActivity, MaterialCenterActivity::class.java)
        intent.putExtra(GeneralTaskViewEntry.TAG, activityViewModel.dataState.taskEntry)
        val customerIds: List<String?> = Stream.of<ReceiptEntry>(activityViewModel.dataState.taskEntry.receiptEntries)
            .map<String> { receiptEntry: ReceiptEntry -> receiptEntry.customerId }.distinct().collect(Collectors.toList<String>())
        intent.putStringArrayListExtra(MaterialCenterActivity.CUSTOMER_IDS, customerIds as ArrayList<String?>)
        startActivity(intent)
    }

    private fun loadNote(note: String?) {
        binding?.apply {
            tvOffloadTypeNoteContent.text = if (TextUtils.isEmpty(note)) DockReceivePhotoActivity.NA else note
            ivOffloadTypeNoteTitle.setImageDrawable(finish)
        }
    }

    private fun showSlipSheetsAmount(slipSheetsAmount: Int?) {
        binding?.apply {
            if (slipSheetsAmount != null && slipSheetsAmount >= 0) {
                tvOffloadTypeSlipSheetsAmount.text = slipSheetsAmount.toString()
                tvOffloadTypeSlipSheetsAmountEdit.text = getString(R.string.text_edit)
                ivOffloadTypeSlipSheetsAmount.setImageDrawable(finish)
            }
        }
    }


    private fun showCartonQuality(taskEntry: ReceiveTaskEntry) {
        binding?.apply {
            if (taskEntry.receiptEntries.any { receiptEntry -> receiptEntry.totalPalletQty != null && receiptEntry.totalPalletQty > 0 }) {
                val showText = taskEntry.receiptEntries.filter { receiptEntry -> receiptEntry.totalPalletQty != null && receiptEntry.totalPalletQty > 0 }
                        .map { receiptEntry -> "${receiptEntry.id} (${receiptEntry.totalPalletQty.toInt()})" }
                        .toList()
                        .joinToString(separator = "\n")
                tvOffloadTypeCartonQualityAmount.text = showText
                tvOffloadTypeCartonQualityEdit.text = getString(R.string.text_edit)
                ivOffloadTypeCartonQuality.setImageDrawable(finish)
            }
        }
    }

    private fun loadPhoto(photos: List<UploadPhotoBean>) {
        binding?.apply {
            photos.mapNotNull { it.photoServerId }?.let { updatePhotoIcon(it) }
            rvOffloadTypePhoto.initPhotoListByAdapter(photos)
        }
    }

    private fun loadDetailInfo(detailEntry: OffloadDetailEntry) {
        binding?.apply {
            offloadSv.visibility = View.VISIBLE
            llBtn.visibility = View.VISIBLE
            var isEquiomentSize = false
            if (detailEntry.equipmentType != null) {
                ivOffloadTypeEquipmentTitle.setImageDrawable(finish)
                when (detailEntry.equipmentType) {
                    OffloadEquipmentType.CONTAINER -> {
                        updateSizeSelectList(OffloadEquipmentType.CONTAINER)
                        mOffloadEquipmentAdapter.setClickPosition(0)
                        val indexOfLast = mEquipmentAdapter.data.indexOfLast {
                            null != detailEntry.containerSize && TextUtils.equals(it, detailEntry.containerSize.fter)
                        }
                        isEquiomentSize = indexOfLast != -1
                        mEquipmentAdapter.setClickPosition(indexOfLast)
                    }
                    OffloadEquipmentType.TRAILER -> {
                        updateSizeSelectList(OffloadEquipmentType.TRAILER)
                        mOffloadEquipmentAdapter.setClickPosition(1)
                        val indexOfLast = mEquipmentAdapter.data.indexOfLast {
                            null != detailEntry.trailerSize && TextUtils.equals(it, detailEntry.trailerSize.fter)
                        }
                        isEquiomentSize = indexOfLast != -1
                        mEquipmentAdapter.setClickPosition(indexOfLast)
                    }
                    OffloadEquipmentType.CART -> {
                        updateSizeSelectList(OffloadEquipmentType.CART)
                        mOffloadEquipmentAdapter.setClickPosition(2)
                        rvEquipmentSize.visibility = View.GONE
                    }
                }
            }
            if (detailEntry.shippingMethod != null) {
                if (detailEntry.trailerSize != null) {
                    ivOffloadTypeShipTitle.setImageDrawable(finish)
                }
                val shipPosition = when (detailEntry.shippingMethod) {
                    ShippingMethodEntry.TRUCKLOAD -> 0
                    ShippingMethodEntry.LTL -> 1
                    ShippingMethodEntry.SMALL_PARCEL -> 2
                    ShippingMethodEntry.LCL -> 3
                    else -> -1
                }
                mOffloadShipAdapter.setClickPosition(shipPosition)
            }
        }
    }

    private fun updateSizeSelectList(type: OffloadEquipmentType) {
        val strings2 = mutableListOf<String>()
        when (type) {
            OffloadEquipmentType.CONTAINER -> {
                strings2.add("20'")
                strings2.add("40'")
                strings2.add("40'H")
                strings2.add("45'")
            }
            OffloadEquipmentType.TRAILER -> {
                strings2.add("48'")
                strings2.add("53'")
                strings2.add("N/A")
            }
            OffloadEquipmentType.CART -> {
                // Cart类型完全不需要size选择
            }
        }
        mEquipmentAdapter.setNewData(strings2)
    }

    private fun loadMaterial(materials: List<MaterialLineViewEntry>) {
        binding?.apply {
            if (materials.isNotEmpty()) {
                ivOffloadTypeMaterialTitle.setImageDrawable(finish)
                offloadTypeMaterialTitleLl.visibility = View.VISIBLE
                addMaterialOffloadBtn.visibility = View.GONE
            }
        }
        mAdapter.setNewData(materials)

    }

    private fun removeMaterialByPosition(position: Int) {
        binding?.apply {
            mAdapter.remove(position)
            if (mAdapter.data.isEmpty()) {
                ivOffloadTypeMaterialTitle.setImageDrawable(up)
            }
        }
    }

    private fun afterEquipmentType(type: OffloadEquipmentType) {
        binding?.apply {
            ivOffloadTypeEquipmentTitle.setImageDrawable(finish)
            if (llOffloadTypeShipContent.visibility == View.GONE) {
                expandOperationContent(llOffloadTypeShipContent, offloadTypeShipTitleLl)
            }
            collapseOperationContent(llOffloadTypeEquipmentContent, offloadTypeEquipmentTitleLl)
            updateSizeSelectList(type)
        }

    }

    private fun afterShipMethod() {
        binding?.apply {
            viewModel.dataState.detailEntry?.let {
                if (null != viewModel.dataState.detailEntry!!.containerSize || null != viewModel.dataState.detailEntry!!.trailerSize) {
                    ivOffloadTypeShipTitle.setImageDrawable(finish)
                    if (llOffloadTypePhotoContent.visibility == View.GONE) {
                        expandOperationContent(llOffloadTypePhotoContent, offloadTypePhotoTitleLl)
                    }
                    collapseOperationContent(llOffloadTypeShipContent, offloadTypeShipTitleLl)
                }
            }

        }
    }

    private fun afterEquipmentSize(size: String) {
        binding?.apply {
            viewModel.dataState.detailEntry?.let {
                if (null != viewModel.dataState.detailEntry!!.shippingMethod && !TextUtils.isEmpty(size)) {
                    ivOffloadTypeShipTitle.setImageDrawable(finish)
                    if (llOffloadTypePhotoContent.visibility == View.GONE) {
                        expandOperationContent(llOffloadTypePhotoContent, offloadTypePhotoTitleLl)
                    }
                    collapseOperationContent(llOffloadTypeShipContent, offloadTypeShipTitleLl)
                }
            }
        }
    }

    private fun autoCloseStepView() {
        Observable.timer(500, TimeUnit.MILLISECONDS).subscribe { fragmentActivity.finish() }
    }

    private fun onForceClose() {
        GeneralAlertDialog.createAlertDialog(fragmentActivity,
            getForceCloseMsg(),
            { _: DialogInterface?, _: Int -> }) { dialog: DialogInterface, _: Int ->
            dialog.dismiss()
            viewModel.forceClose("seal or container not match")
        }.show()
    }

    private fun getForceCloseMsg(): String {
        return String.format(
            getString(R.string.msg_seal_or_equipment_not_match_do_force_close),
            if (mOffloadEquipmentAdapter.getClickPosition() == 0) getString(R.string.text_container) else getString(R.string.text_offload_type_trailer))
    }


    override fun onResume() {
        super.onResume()
        viewModel.loadMaterials()
    }

    private fun setRecycleViewManager() {
        binding?.apply {
            rvOffloadTypeEquipment.layoutManager = LinearLayoutManager(context)
            rvOffloadTypeShip.layoutManager = LinearLayoutManager(context)
        }
    }

    private fun setRecycleViewManagerForTablet() {
        binding?.apply {
            rvOffloadTypeEquipment.layoutManager = GridLayoutManager(context, 2)
            rvOffloadTypeShip.layoutManager = GridLayoutManager(context, 2)
        }
    }

    private fun updatePhotoIcon(photos: List<String>) {
        binding?.apply {
            if (photos.isEmpty()) {
                ivOffloadTypePhotoTitle.setImageDrawable(if (llOffloadTypePhotoContent.visibility == View.VISIBLE) up else down)
            } else {
                ivOffloadTypePhotoTitle.setImageDrawable(finish)
            }
        }
    }

    private fun onEvents() {
        onEvent<OffloadWorkUiEvent.InitLoadImg> {
            loadPhoto(photos)
        }
    }
}