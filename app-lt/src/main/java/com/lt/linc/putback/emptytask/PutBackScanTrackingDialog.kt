package com.lt.linc.putback.emptytask

import android.annotation.SuppressLint
import android.os.Bundle
import androidx.appcompat.widget.AppCompatButton
import android.widget.FrameLayout
import com.linc.platform.pick.model.PickToTrackingNoEntry
import com.linc.platform.putback.view.EmptyPutBackLpEntry
import com.lt.linc.R
import com.lt.linc.common.extensions.*
import com.lt.linc.common.mvvm.kotlin.BaseBindingDialog
import com.lt.linc.common.mvvm.kotlin.SimpleTextAdapter
import com.lt.linc.common.mvvm.kotlin.extensions.newFragmentInstance
import com.lt.linc.databinding.DialogPutBackScanTrackingBinding
import kotlin.jvm.Throws

/**
 * <AUTHOR>
 * @date 2022/4/12
 */
class PutBackScanTrackingDialog : BaseBindingDialog<DialogPutBackScanTrackingBinding>() {

    companion object {
        private const val PARAM_QTY = "qty"
        private const val PARAM_ITEM_NAME = "itemSpecName"
        private const val PARAM_SCANNED_IDS = "scannedIds"
        private const val PARAM_LP_ENTRY = "lpEntry"

        @JvmStatic
        fun newInstance(
                qty: Int, itemSpecName: String?, scannedIds: List<String>,
                lpEntry: EmptyPutBackLpEntry
        ) =
                newFragmentInstance<PutBackScanTrackingDialog>(
                        PARAM_QTY to qty,
                        PARAM_ITEM_NAME to itemSpecName,
                        PARAM_SCANNED_IDS to scannedIds.toMutableList(),
                        PARAM_LP_ENTRY to lpEntry
                )
    }

    private val qty: Int by lazy { getBundleObject(PARAM_QTY) }

    private val itemSpecName: String? by lazy { getBundleObject(PARAM_ITEM_NAME) }

    private val validTrackingMap: Map<PickToTrackingNoEntry, Int> by lazy {
        val entry = (getBundleObject(PARAM_LP_ENTRY) as EmptyPutBackLpEntry)
        entry.trackingEntries!!.mapToLinkedMap { it to entry.itemQtyOfTracking(it.trackingNo, it.itemSpecId) }
    }

    private var onDone: OnDone? = null

    private val adapter = SimpleTextAdapter(
            titleModifier = { setMargin(right = 8.dp) },
            trailingBuilder = {
                AppCompatButton(context).apply {
                    text = getString(R.string.btn_remove)
                    setTextColor(context.getColor(R.color.white))
                    layoutParams =
                            FrameLayout.LayoutParams(FrameLayout.LayoutParams.WRAP_CONTENT, 26.dp)
                    setPadding(5.dp, 0, 5.dp, 0)
                    setBackgroundColor(context.getColor(R.color.extended_red_400))
                    setOnClickListener { _ -> onDeleteTracking(it) }
                }
            }
    )

    private fun onDeleteTracking(tracking: String) {
        val index = adapter.data.indexOf(tracking)
        adapter.remove(index)
        updateCount()
        toastScannedCount()
    }

    override fun initView(savedInstanceState: Bundle?) {
        binding?.apply {
            itemDetailTv.text = itemSpecName
            scanner.setScanEvent { _, data -> onScan(data) }

            recyclerView.adapter = adapter
            adapter.setNewData(getBundleObject(PARAM_SCANNED_IDS))
            updateCount()

            cancelButton.setOnClickListener { dismiss() }
            confirmButton.setOnClickListener { onSubmit() }
        }
    }

    @SuppressLint("SetTextI18n")
    private fun updateCount() {
        binding?.countTv?.text = "${adapter.scannedItemQty} / $qty"
    }

    private fun toastScannedCount() {
        speakAndToast("${adapter.scannedItemQty}")
    }

    private fun onScan(tracking: String) {
        runCatching { validateScanned(tracking) }.onFailure {
            showToast(it.message!!)
            return
        }

        adapter.addData(0, tracking)
        updateCount()

        if (adapter.scannedItemQty == qty) {
            speakAndToast(getString(R.string.label_finish))
        } else {
            toastScannedCount()
        }
    }

    @Throws
    private fun validateScanned(tracking: String) {
        if (adapter.data.contains(tracking)) {
            throw getString(R.string.msg_duplicate).toException
        }
        if (tracking !in validTrackingMap.keys.map { it.trackingNo }) {
            throw getString(R.string.error_scanned_tracking_not_match, tracking).toException
        }
    }

    private fun onSubmit() {
        onDone?.done(adapter.data)
        dismiss()
    }

    fun setOnDoneListener(listener: OnDone) {
        onDone = listener
    }

    fun interface OnDone {
        fun done(ids: List<String>)
    }

    private val SimpleTextAdapter.scannedItemQty: Int
        get() {
            return data.sumOf { tracking -> validTrackingMap.firstNotNullOf { if (it.key.trackingNo == tracking) it.value else null } }
        }

}

 