package com.lt.linc.pack.taskoperate.work;

import android.app.Dialog;
import android.content.Context;
import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatButton;
import androidx.appcompat.widget.AppCompatEditText;
import androidx.appcompat.widget.AppCompatSpinner;
import android.text.TextUtils;
import android.view.View;
import android.widget.ArrayAdapter;
import android.widget.LinearLayout;

import com.customer.widget.ScanBarcodeDialog;
import com.customer.widget.core.LincBaseActivity;
import com.customer.widget.scanner.decoder.DriverLicense;
import com.linc.platform.foundation.model.UnitEntry;
import com.linc.platform.pack.model.SLPViewEntry;
import com.linc.platform.pack.model.SlpItemEntry;
import com.linc.platform.pack.model.UnpackItemRequestEntry;
import com.linc.platform.pack.presenter.PackTaskPresenter;
import com.linc.platform.utils.ToastUtil;
import com.lt.linc.R;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by devinc on 2017/6/9.
 */

public class ReturnItemDialog extends Dialog {
    private Context context;
    private PackTaskPresenter presenter;
    private AppCompatEditText lpEdt;
    private AppCompatEditText returnQytEdt;
    private AppCompatButton returnBtn;
    private AppCompatButton cancelBtn;
    private AppCompatButton returnAllBtn;
    private SlpItemEntry item;
    private AppCompatButton scanBtn;
    private ScanBarcodeDialog scanBarcodeDialog;
    private SLPViewEntry slpViewEntry;
    private boolean isReturnSlp = false;
    private LinearLayout uomQtyLy;
    private AppCompatSpinner uomSpinner;

    public ReturnItemDialog(@NonNull Context context, PackTaskPresenter presenter) {
        super(context);
        this.context = context;
        this.presenter = presenter;
        setContentView(R.layout.dialog_pack_return_item);
        bindView();

        cancelBtn.setOnClickListener(view -> {
            clearDialog();
            dismiss();
        });

        scanBtn.setOnClickListener(view ->
                scanBarcodeDialog.show(((LincBaseActivity) context).getSupportFragmentManager(), "barcode"));

        initScanDialog();
        returnBtn.setOnClickListener(view -> toReturnItem(false));
        returnAllBtn.setOnClickListener(view -> toReturnItem(true));
    }

    private void toReturnItem(boolean isReturnAll) {
        String returnToLp = lpEdt.getText().toString();
        if (TextUtils.isEmpty(returnToLp)) {
            ToastUtil.showToast(context, R.string.hint_please_scan_or_input_lp);
            return;
        }

        if (isReturnSlp) {
            remove(returnToLp);
            lpEdt.setText("");
            dismiss();
        } else {
            UnpackItemRequestEntry entry = new UnpackItemRequestEntry();
            entry.toLPId = returnToLp;
            entry.itemSpecId = item.itemSpecId;
            UnitEntry unit = findUnit(uomSpinner.getSelectedItem().toString());
            entry.unitId = unit.unitId;
            if (isReturnAll) {
                entry.qty = unit.qty;
            } else {
                String qtyStr = returnQytEdt.getText().toString();
                if (TextUtils.isEmpty(qtyStr)) {
                    ToastUtil.showErrorToast(context.getString(R.string.hint_input_qty));
                    return;
                }
                double qty = Double.parseDouble(qtyStr);

                if (qty > unit.qty) {
                    ToastUtil.showErrorToast(context.getString(R.string.error_qty_overflow));
                    return;
                }
                entry.qty = qty;
            }
            presenter.returnItem(entry, item.slp);
        }
    }

    private void remove(String returnToLp) {
        if (slpViewEntry.isWholeLpPack) {
            presenter.removeInnerLp(slpViewEntry.slpId, returnToLp);
        } else {
            presenter.removeSlp(slpViewEntry, returnToLp);
        }
    }

    private UnitEntry findUnit(String itemName) {
        for (UnitEntry itemDetail : item.itemDetails) {
            if (itemName.equals(itemDetail.name)) {
                return itemDetail;
            }
        }
        return null;
    }

    public void clearDialog() {
        item = null;
        lpEdt.setText("");
        returnQytEdt.setText("");
    }

    private void initScanDialog() {
        scanBarcodeDialog = new ScanBarcodeDialog();
        scanBarcodeDialog.setOnBarcodeResult(new ScanBarcodeDialog.OnBarcodeResult() {
            @Override
            public void onBarcode(String barcode) {
                lpEdt.setText(barcode);
            }

            @Override
            public void onDriverLicense(DriverLicense driverLicense) {
            }

            @Override
            public void onDismiss() {

            }
        });
    }

    private void bindView() {
        lpEdt = (AppCompatEditText) findViewById(R.id.lp_edt);
        returnQytEdt = (AppCompatEditText) findViewById(R.id.return_qyt_edt);
        returnBtn = (AppCompatButton) findViewById(R.id.return_btn);
        scanBtn = (AppCompatButton) findViewById(R.id.scan_btn);
        returnAllBtn = (AppCompatButton) findViewById(R.id.return_all_btn);
        cancelBtn = (AppCompatButton) findViewById(R.id.cancel_btn);
        uomQtyLy = (LinearLayout) findViewById(R.id.uom_qty_ly);
        uomSpinner = (AppCompatSpinner) findViewById(R.id.uom_spinner);
    }

    public void setItem(SlpItemEntry item) {
        this.item = item;
        lpEdt.setText(item.fromLp);
        if (item.fromLpIds != null
                && !item.fromLpIds.isEmpty()) {
            lpEdt.setText(item.fromLpIds.get(0));
        }

        List<String> strings = new ArrayList<>();
        for (UnitEntry itemDetail : item.itemDetails) {
            strings.add(itemDetail.name);
        }
        ArrayAdapter<String> spinnerAdapter = new ArrayAdapter<String>(context,
                R.layout.item_spinner_text, strings);
        spinnerAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        uomSpinner.setAdapter(spinnerAdapter);
    }

    public void showAsReturnLp(SLPViewEntry entry) {
        this.slpViewEntry = entry;
        isReturnSlp = true;
        returnAllBtn.setVisibility(View.GONE);
        uomQtyLy.setVisibility(View.GONE);
        this.show();
    }
}
