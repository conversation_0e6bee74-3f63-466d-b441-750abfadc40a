package com.lt.linc.pack.kittingoperate;

import androidx.recyclerview.widget.RecyclerView;
import android.view.View;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.lt.linc.R;

/**
 * Created by devinc on 2016/11/14.
 */
public class UnpackedViewHolder extends RecyclerView.ViewHolder {
    protected TextView itemNameTxt;
    protected LinearLayout titleLy;
    protected LinearLayout componentLy;
    protected LinearLayout rootLy;

    public UnpackedViewHolder(View itemView) {
        super(itemView);
        itemNameTxt = (TextView) itemView.findViewById(R.id.item_name_txt);
        titleLy = (LinearLayout) itemView.findViewById(R.id.title_ly);
        componentLy = (LinearLayout) itemView.findViewById(R.id.component_ly);
        rootLy = (LinearLayout) itemView.findViewById(R.id.root_layout);
    }
}
