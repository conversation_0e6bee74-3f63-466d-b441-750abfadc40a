package com.lt.linc.putawayauditing.work

import com.linc.platform.common.step.StepStatusEntry
import com.linc.platform.common.step.StepStatusUpdateEntry
import com.linc.platform.common.task.TaskTypeEntry
import com.linc.platform.cyclecount.api.CycleCountApi
import com.linc.platform.cyclecount.model.OpCountEntry
import com.linc.platform.foundation.api.CustomerQuestionnaireApi
import com.linc.platform.foundation.model.questionnaire.QuestionnaireResultSearch
import com.linc.platform.print.api.PrintApi
import com.linc.platform.print.commonprintlp.PrintData
import com.linc.platform.print.commonprintlp.PrintMsg
import com.linc.platform.print.commonprintlp.PrintResult
import com.linc.platform.putawayauditing.api.PutAwayAuditingTaskApi
import com.linc.platform.putawayauditing.model.PutAwayAuditingDetailEntry
import com.linc.platform.putawayauditing.model.PutAwayAuditingStatusEntry
import com.linc.platform.utils.ArithmeticUtil
import com.linc.platform.utils.PrintUtil
import com.linc.platform.utils.TimeUtil
import com.lt.linc.R
import com.lt.linc.common.extensions.addToNewList
import com.lt.linc.common.extensions.safeCount
import com.lt.linc.common.mvi.ReactiveViewModel
import com.lt.linc.common.mvi.subscribeNotNull
import com.lt.linc.common.mvvm.kotlin.BaseRepository
import com.lt.linc.common.mvvm.kotlin.extensions.apiServiceLazy
import com.lt.linc.common.mvvm.kotlin.extensions.facilityEntry
import com.lt.linc.common.mvvm.kotlin.extensions.idmUserId
import com.lt.linc.common.mvvm.kotlin.extensions.idmUserName
import com.lt.linc.common.mvvm.kotlin.extensions.launch
import com.lt.linc.common.mvvm.kotlin.extensions.showLoading
import com.lt.linc.common.mvvm.kotlin.extensions.showToast
import com.lt.linc.putawayauditing.PutAwayAuditingViewModel
import com.lt.linc.putawayauditing.work.PutAwayAuditingWorkUiEvent.ChooseCaseUom
import com.lt.linc.putawayauditing.work.PutAwayAuditingWorkUiEvent.EnabledToSubmitButton
import com.lt.linc.putawayauditing.work.PutAwayAuditingWorkUiEvent.MakeRedOfEmptyActualFullPalletQty
import com.lt.linc.putawayauditing.work.PutAwayAuditingWorkUiEvent.MakeRedOfEmptyInputLpCaseQty
import com.lt.linc.putawayauditing.work.PutAwayAuditingWorkUiEvent.SelectLpToRequestCaseInput
import com.lt.linc.putawayauditing.work.PutAwayAuditingWorkUiEvent.SetPreviousAndNextBtnState
import com.lt.linc.putawayauditing.work.PutAwayAuditingWorkUiEvent.StartAnswerQuestionnaire
import com.lt.linc.receive_v1.lpsetup.work.singleitem.itemconfig.SingleItemConfigRepository
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.firstOrNull

/**
 * <AUTHOR>
 * @Date 2022/10/27
 */
class PutAwayAuditingWorkViewModel(
    private val parentViewModel: PutAwayAuditingViewModel,
    private val repository: PutAwayAuditingWorkRepo = PutAwayAuditingWorkRepo(),
    initialDataState: PutAwayAuditingWorkState,
    initialUiState: PutAwayAuditingWorkUiState = PutAwayAuditingWorkUiState()) :
    ReactiveViewModel<PutAwayAuditingWorkState, PutAwayAuditingWorkUiState>(initialDataState, initialUiState) {

    val stepEntry get() = parentViewModel.dataState.stepEntry
    val task get() = parentViewModel.dataState.taskEntry
    private val printUtil = PrintUtil.newInstance()

    init {
        subscribeData()
        initData()
    }

    private fun initData() {
        stepEntry.putAwayAuditResults.let {
            setDataState {
                copy(auditingDetailList = it,
                    curAuditingDetailId = (it?.firstOrNull { detail -> !detail.isAudited } ?: it?.getOrNull(0))?.id,
                    customer = task?.customer)
            }
        }
    }

    private fun subscribeData() {
        subscribeNotNull(PutAwayAuditingWorkState::curDetailIndex) {
            val listCount = dataState.auditingDetailList.safeCount()
            when (it) {
                0 -> fireEvent {
                    SetPreviousAndNextBtnState(isPreviousEnable = false, isNextEnable = listCount > 1)
                }
                listCount - 1 -> fireEvent {
                    SetPreviousAndNextBtnState(isPreviousEnable = true, isNextEnable = false)
                }
                else -> fireEvent { SetPreviousAndNextBtnState(isPreviousEnable = true, isNextEnable = true) }
            }
        }

        subscribeNotNull(PutAwayAuditingWorkState::curAuditingDetail) {
            val actualFullPalletQtyInput =
                if (it.actualFullPalletQty > 0 || it.isAudited) it.actualFullPalletQty.toInt().toString() else null

            val scannedLpList = if (it.isAudited) {
                it.partialPalletDetailList?.map { partialPalletEntry ->
                    val auditingDetails = partialPalletEntry.auditingDetails?.map { itemDetailEntry -> itemDetailEntry.copy() }
                    partialPalletEntry.copy(auditingDetails = auditingDetails)
                }
            } else null

            val totalScanLpOfFullPalletQty = dataState.totalScanLpOfFullPalletQty;
            //update ui
            setUiState { copy(auditingDetail = it, actualFullPalletQtyInput = actualFullPalletQtyInput, enablePutawayAudition = dataState.enablePutawayAudition,
                totalScanLpOfFullPalletQty = totalScanLpOfFullPalletQty,scannedFullLpList = null,scannedLpList = scannedLpList) }

            //check to choose case uom
            val itemUomCaseWrappers = if (!it.isAudited) {
                it.partialPalletDetailList?.filter { partialPalletEntry -> !partialPalletEntry.itemDetails.isNullOrEmpty() }
                    ?.flatMap { partialPalletEntry -> partialPalletEntry.itemDetails!! }?.distinctBy { itemDetailEntry ->
                        itemDetailEntry.itemSpecId
                    }?.filter { itemDetailEntry ->
                        !it.isItemUomByCase(itemDetailEntry.unitId) && it.itemCaseUnits(itemDetailEntry.itemSpecId).safeCount() > 1
                    }?.map { itemDetailEntry ->
                        ItemUomCaseWrapper(itemDetailEntry = itemDetailEntry,
                            itemSpecEntry = it.getItemSpecEntry(itemDetailEntry.itemSpecId),
                            itemBaseUnit = it.itemBaseUnit(itemDetailEntry.itemSpecId),
                            caseUnits = it.itemCaseUnits(itemDetailEntry.itemSpecId)!!)
                    }?.let { list ->
                        if (list.isEmpty()) return@let null
                        //await worker choose case uom
                        awaitEvent { ChooseCaseUom(list) }
                    }
            } else null

            setDataState { copy(itemUomCaseWrappers = itemUomCaseWrappers) }
        }
    }

    fun setCurAuditingDetailId(id: String?) {
        id ?: return
        setDataState { copy(curAuditingDetailId = id) }
    }

    fun inputActualFullPalletQty(actualFullQty: String?) {
        //actualFullPalletQtyInput change set enabled to submit button
        if (uiState.auditingDetail?.actualFullPalletQty != actualFullQty?.toDoubleOrNull()) {
            fireEvent { EnabledToSubmitButton }
        }
        if(!actualFullQty.isNullOrEmpty() && dataState.totalScanLpOfFullPalletQty > actualFullQty?.toIntOrNull() ?: 0){
            setUiState { copy(actualFullPalletQtyInput = actualFullQty,totalScanLpOfFullPalletQty = actualFullQty?.toIntOrNull() ?: 0) }
        }else {
            setUiState { copy(actualFullPalletQtyInput = actualFullQty,totalScanLpOfFullPalletQty = dataState.totalScanLpOfFullPalletQty) }
        }
    }

    fun handleFullPalletScan(lpNo: String?) {
        lpNo ?: return
        if (uiState.actualFullPalletQtyInput.isNullOrEmpty()) {
            showToast(R.string.msg_input_actual_full_pallet_qty)
            fireEvent { MakeRedOfEmptyActualFullPalletQty }
            return
        }
        if (uiState.scannedFullLpList.safeCount() >= uiState.totalScanLpOfFullPalletQty) {
            showToast(R.string.error_scan_lp_of_full_pallet_quantity_exceed_max)
            return
        }
        val pallet = dataState.curAuditingDetail?.fullPalletDetailList?.find { it.lpId == lpNo }
        pallet ?: let {
            showToast(R.string.msg_lp_not_found)
            return
        }
        val scannedLp = uiState.scannedFullLpList?.find { it.lpId == lpNo }
        if (scannedLp != null) {
            showToast(R.string.error_found_multiple_pallet)
            return
        }
        val newScannedFullPallet = pallet.copy(itemDetails = pallet.itemDetails?.map {
            val itemInventoryUnit = dataState.curAuditingDetail?.unitsMap?.get(it.unitId)
            val isItemUomByCase = dataState.curAuditingDetail?.isItemUomByCase(it.unitId) ?: false
            val itemCaseUnits = dataState.curAuditingDetail?.itemCaseUnits(it.itemSpecId)

            val caseQty = when {
                isItemUomByCase || itemCaseUnits.safeCount() == 0 -> it.qty
                else -> {
                    val chooseCaseUnit = if (itemCaseUnits.safeCount() == 1) {
                        itemCaseUnits!![0]
                    } else {
                        dataState.itemUomCaseWrappers?.find { itemUomCaseWrapper ->
                            it.itemSpecId == itemUomCaseWrapper.itemDetailEntry.itemSpecId
                        }?.chooseCaseUnit ?: itemCaseUnits!![0]
                    }
                    if (itemInventoryUnit?.isBaseUnit == true) {
                        ArithmeticUtil.div(it.qty, chooseCaseUnit.baseQty)
                    } else {
                        ArithmeticUtil.div(ArithmeticUtil.mul(it.qty, itemInventoryUnit?.baseQty ?: 1.0), chooseCaseUnit.baseQty)
                    }
                }
            }
            it.copy(qty = caseQty)
        })
        setUiState { copy(scannedFullLpList = uiState.scannedFullLpList.addToNewList(newScannedFullPallet)) }
    }

    fun handlePartialPalletScan(lpNo: String?) {
        lpNo ?: return
        val pallet = dataState.curAuditingDetail?.partialPalletDetailList?.find { it.lpId == lpNo }
        pallet ?: let {
            showToast(R.string.msg_lp_not_found)
            return
        }
        //lp has exist select this
        val scannedLp = uiState.scannedLpList?.find { it.lpId == lpNo }
        if (scannedLp != null) {
            fireEvent { SelectLpToRequestCaseInput(scannedLp.lpId) }
            return
        }

        val newScannedPallet = pallet.copy(auditingDetails = pallet.itemDetails?.map {
            val itemInventoryUnit = dataState.curAuditingDetail?.unitsMap?.get(it.unitId)
            val isItemUomByCase = dataState.curAuditingDetail?.isItemUomByCase(it.unitId) ?: false
            val itemCaseUnits = dataState.curAuditingDetail?.itemCaseUnits(it.itemSpecId)

            //Calculate case qty
            val caseQty = when {
                isItemUomByCase || itemCaseUnits.safeCount() == 0 -> it.qty
                else -> {
                    val chooseCaseUnit = if (itemCaseUnits.safeCount() == 1) {
                        //single case billing uom
                        itemCaseUnits!![0]
                    } else {
                        //multiple case billing uom need worker selection
                        dataState.itemUomCaseWrappers?.find { itemUomCaseWrapper ->
                            it.itemSpecId == itemUomCaseWrapper.itemDetailEntry.itemSpecId
                        }?.chooseCaseUnit ?: itemCaseUnits!![0]
                    }
                    if (itemInventoryUnit?.isBaseUnit == true) {
                        ArithmeticUtil.div(it.qty, chooseCaseUnit.baseQty)
                    } else {
                        ArithmeticUtil.div(ArithmeticUtil.mul(it.qty, itemInventoryUnit?.baseQty ?: 1.0), chooseCaseUnit.baseQty)
                    }
                }
            }
            it.copy(qty = caseQty)
        })
        setUiState { copy(scannedLpList = uiState.scannedLpList.addToNewList(newScannedPallet)) }
        fireEvent { SelectLpToRequestCaseInput(newScannedPallet.lpId) }
    }

    fun removeScannedFullLp(lpId: String?) {
        setUiState {
            copy(scannedFullLpList = scannedFullLpList?.toMutableList()?.apply {
                removeIf { it.lpId == lpId }
            })
        }
    }

    fun removeScannedLp(lpId: String?) {
        setUiState {
            copy(scannedLpList = scannedLpList?.toMutableList()?.apply {
                removeIf { it.lpId == lpId }
            })
        }
    }

    fun toPrevious() {
        val curIndex = dataState.curDetailIndex ?: 0
        setCurAuditingDetailId(dataState.auditingDetailList?.getOrNull(curIndex - 1)?.id)
    }

    fun toNext() {
        val curIndex = dataState.curDetailIndex ?: 0
        setCurAuditingDetailId(dataState.auditingDetailList?.getOrNull(curIndex + 1)?.id)
    }

    fun doSubmit(onReprintConfirm: (message: String) -> Flow<Boolean?>) {
        if (dataState.curDetailIndex ?: -1 < 0) return
        if (uiState.actualFullPalletQtyInput.isNullOrEmpty()) {
            showToast(R.string.msg_missing_actual_full_pallet_qty)
            fireEvent { MakeRedOfEmptyActualFullPalletQty }
            return
        }
        if (uiState.partialPalletQty > 0 && (!uiState.isScannedAllPartialPallet || !uiState.isAllLpInputActualCaseQty)) {
            showToast(R.string.msg_missing_actual_case_qty)
            fireEvent { MakeRedOfEmptyInputLpCaseQty }
            return
        }
        if(uiState.enablePutawayAudition && uiState.totalScanLpOfFullPalletQty > 0 && uiState.totalScanLpOfFullPalletQty != uiState.scannedFullLpList.safeCount()){
            showToast(R.string.msg_missing_scan_lp_of_full_pallet)
            return
        }
        launch {
            //assemble auditing submit
            var isMatchBySubmit = true
            val auditingDetailToSubmit = dataState.curAuditingDetail!!.copy(isAudited = true,
                actualFullPalletQty = uiState.actualFullPalletQtyInput!!.toDouble(),
                partialPalletDetailList = uiState.scannedLpList?.map {
                    it.copy(actualCaseQty = it.actualQtyInputCache!!.toDouble())
                },
                updatedBy = repository.idmUserName,
                updatedWhen = TimeUtil.getLocalDateTime()).let { detail ->
                //check match state
                isMatchBySubmit = detail.run {
                    fullPalletQty == actualFullPalletQty && partialPalletDetailList?.all { it.actualCaseQty == it.allItemCaseQtyCount } ?: true
                }
                detail.copy(auditingStatus = if (isMatchBySubmit) PutAwayAuditingStatusEntry.MATCH else PutAwayAuditingStatusEntry.NOT_MATCH)
            }
            val result = requestAwait(repository.submitAuditing(auditingDetailToSubmit))
            if (result.isSuccess) {
                showToast(R.string.submit_success)
                setDataStateAwait {
                    copy(auditingDetailList = dataState.auditingDetailList?.toMutableList()?.apply {
                        set(dataState.curDetailIndex!!, auditingDetailToSubmit)
                    })
                }
                createClearedOPCycleCountRecord(auditingDetailToSubmit)
                printLabel(auditingDetailToSubmit.putAwayAuditingTaskId, auditingDetailToSubmit.locationName, auditingDetailToSubmit.receiptId, isMatchBySubmit, onReprintConfirm)
            }
            //check is all location audited to close task/step
            if (dataState.isAllAudited) {
                //close step
                val closeStepResult = requestAwait(repository.updateStepStatus(stepEntry.id, StepStatusEntry.DONE))
                if (closeStepResult.isFailure) return@launch
                parentViewModel.finish()

//                //close task
//                requestAwait(repository.closeTask(stepEntry.taskId), error = null).onSuccess {
//                    showToast(R.string.close_success)
//                    parentViewModel.finish()
//                }.onFailure {
//                    val error = it.toErrorResponse()
//                    if (error.code == ErrorCode.PUT_AWAY_AUDITING_NOT_MATCH_TO_FORCE_CLOSE) {
//                        if (awaitEvent { ConfirmToForceClose(error.errorMessage) } == true) {
//                            forceCloseTask()
//                        }
//                    } else {
//                        showToast(error.errorMessage)
//                    }
//                }
            }
        }
    }

    private fun printLabel(taskId: String?, locationName: String?, receiptId: String?, isMatchBySubmit: Boolean, onReprintConfirm: (message: String) -> Flow<Boolean?>) {
        taskId ?: return
        locationName ?: return
        receiptId ?: return
        launch {
            val printData = repository.getPrintData()
            if (!PrintUtil.hasPrinter(printData)) {
                fireEvent { PutAwayAuditingWorkUiEvent.SetupPrinter }
                return@launch
            }
            requestAwait(repository.getPutawayAuditingPrintTemplate()).onSuccess {
                it ?: return@launch
                it.zplCode ?: return@launch
                val printCommand = it.zplCode.replace("{taskId}", taskId)
                    .replace("{locationName}", locationName)
                    .replace("{receiptId}", receiptId)
                    .replace("{pass}", if (isMatchBySubmit) "QC PASSED" else "QC FAILED")
                    .replace("{user}", repository.getUserName())
                    .replace("{dateTime}", TimeUtil.getDateToString(System.currentTimeMillis(), TimeUtil.FORMAT_YYYY_MM_DD_HH_MM))
                printData.jobData = PrintData.JobData.ZPL(printCommands = printCommand)
                printLps(printData, onReprintConfirm)
            }
        }
    }

    private suspend fun printLps(
        printData: PrintData,
        onReprintConfirm: (message: String) -> Flow<Boolean?>,
    ): Result<Boolean> {
        val result = printUtil.printWithFlow(printData, onShowProgress = { showLoading(it) }).firstOrNull()
        return when (result) {
            null -> Result.success(false)
            PrintResult.NoPrinter -> {
                fireEvent { PutAwayAuditingWorkUiEvent.SetupPrinter }
                Result.success(false)
            }
            is PrintResult.Success -> Result.success(true)
            is PrintResult.Failed -> {
                val reprint = onReprintConfirm(PrintMsg.formatError(result.printerEntry, result.response.errorMessage)).firstOrNull()
                if (reprint == true) {
                    printLps(printData, onReprintConfirm)
                } else {
                    Result.success(false)
                }
            }
        }
    }

    fun forceCloseTask() {
        if (!dataState.isAllAudited) {
            //check is all audited
            dataState.auditingDetailList?.filter { !it.isAudited }?.mapNotNull { it.locationName }?.distinct()?.joinToString { it }?.let {
                showToast(R.string.msg_put_away_auditing_not_finish, it)
            }
            return
        }
        launch {
            val result = requestAwait(repository.forceCloseTask(stepEntry.taskId))
            if (result.isFailure) return@launch

            val customerId = parentViewModel.dataState.taskEntry?.customerId
            if (!customerId.isNullOrEmpty()) {
                val search = QuestionnaireResultSearch(
                    taskId = parentViewModel.dataState.taskEntry?.id,
                    customerId = customerId,
                    taskType = parentViewModel.dataState.taskEntry?.taskType
                )
                val searchResult = requestAwait(repository.searchQuestionnaireResult(search), error = null)
                if (searchResult.getOrNull().isNullOrEmpty()) {
                    requestAwait(repository.getCustomerQuestionnaire(customerId)).onSuccess {
                        it ?: return@onSuccess
                        if (!it.getTaskQuestionnaireDetails(TaskTypeEntry.PUT_AWAY_AUDITING).isNullOrEmpty()) {
                            fireEvent { StartAnswerQuestionnaire(parentViewModel.dataState.taskEntry!!, it) }
                        }
                    }
                }
            }

            showToast(R.string.close_success)
            parentViewModel.finish()
        }
    }

    private fun createClearedOPCycleCountRecord(auditingDetail: PutAwayAuditingDetailEntry) {
        if(auditingDetail.auditingStatus == PutAwayAuditingStatusEntry.NOT_MATCH){
            return
        }
        launch {
            val customerId = parentViewModel.dataState.taskEntry?.customerId?:""
            val locationIds = listOf(auditingDetail.locationId?:"")
            requestAwait(repository.createAndCleared(customerId,stepEntry.taskId,locationIds))
        }
    }
}

class PutAwayAuditingWorkRepo : BaseRepository() {

    private val TEMPLATE_NAME = "Putaway Auditing Match 2x1"

    private val putAwayAuditingTaskApi by apiServiceLazy<PutAwayAuditingTaskApi>()
    private val printApi by apiServiceLazy<PrintApi>()
    private val customerQuestionnaireApi by apiServiceLazy<CustomerQuestionnaireApi>()
    private val cycleCountApi by apiServiceLazy<CycleCountApi>()

    fun submitAuditing(auditingDetail: PutAwayAuditingDetailEntry) =
        rxRequest(putAwayAuditingTaskApi.updateAuditingDetail(auditingDetail.id, auditingDetail))

    fun closeTask(taskId: String) = rxRequest(putAwayAuditingTaskApi.close(taskId))

    fun forceCloseTask(taskId: String) = rxRequest(putAwayAuditingTaskApi.forceClose(taskId))

    fun updateStepStatus(stepId: String, statusEntry: StepStatusEntry) =
        rxRequest(putAwayAuditingTaskApi.updateStepStatus(stepId, StepStatusUpdateEntry().apply { status = statusEntry }))

    fun searchQuestionnaireResult(search: QuestionnaireResultSearch) = rxRequest(customerQuestionnaireApi.searchQuestionnaireResult(search))

    fun getCustomerQuestionnaire(customerId: String?) = rxRequest(customerQuestionnaireApi.getQuestionnaireByCustomerId(customerId))

    fun getPutawayAuditingPrintTemplate() = rxRequest(printApi.getPrintTemplate(TEMPLATE_NAME))

    fun getPrintData(): PrintData {
        val facility = facilityEntry
        val labelSize = SingleItemConfigRepository.DEFAULT_LABEL_SIZE
        return PrintData.basic(idmUserId, facility!!.getName(), labelSize)
    }

    fun getUserName(): String = idmUserName

    fun createAndCleared(customerId: String,taskId:String,locationIds:List<String>) = rxRequest(cycleCountApi.createAndCleared(
        OpCountEntry(
            customerId = customerId,
            locationIds = locationIds,
            method = "Opportunity Count",
            type = "Android",
            requestId = "OP-COUNT",
            notes = "Trigger Count By Putaway audit taskID=$taskId",
            needLockInventory = false

    )))
}