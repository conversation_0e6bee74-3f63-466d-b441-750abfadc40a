package com.lt.linc.putaway.receiveputaway.operate;

import android.text.TextUtils;
import android.view.View;

import androidx.appcompat.widget.AppCompatTextView;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.fragment.app.DialogFragment;

import com.annimon.stream.Stream;
import com.customer.widget.ListDialog;
import com.customer.widget.StateButton;
import com.customer.widget.core.LincBaseActivity;
import com.customer.widget.util.CommUtil;
import com.linc.platform.baseapp.model.LocationEntry;
import com.linc.platform.cyclecount.model.OPCountActionEntry;
import com.linc.platform.cyclecount.model.OnScreenCountResponseEntry;
import com.linc.platform.putaway.work.directputaway.DirectPutAwayPresenter;
import com.linc.platform.utils.Lists;
import com.linc.platform.utils.StringUtil;
import com.lt.linc.R;
import com.lt.linc.pick.newpick.work.ScannerLayout;
import com.lt.linc.pick.work.OpportunityCycleCountDialog;
import com.lt.linc.putaway.receiveputaway.DirectPutAwayPartView;

import java.util.List;

/**
 * <AUTHOR>
 */
public class ScanPutAwayLocation implements DirectPutAwayPartView {
    private ConstraintLayout layout;
    private AppCompatTextView locationTxt;
    private ScannerLayout scanner;
    private StateButton locFullBtn;
    private StateButton nextBtn;

    private LincBaseActivity activity;
    private DirectPutAwayPresenter presenter;

    @Override
    public void show(boolean show) {
        if (show) scanner.requestEdtFocus();
        layout.setVisibility(show ? View.VISIBLE : View.GONE);
    }

    @Override
    public boolean isVisible() {
        return layout.getVisibility() == View.VISIBLE;
    }

    @Override
    public void init(LincBaseActivity activity, DirectPutAwayPresenter presenter) {
        this.activity = activity;
        this.presenter = presenter;

        initView();
        initLocScanner();

        locFullBtn.setOnClickListener(v -> presenter.onSuggestLocationFull());
        nextBtn.setOnClickListener(v -> presenter.onScanPutAwayLocationNext());
    }

    private void initLocScanner() {
        scanner.setListener(new ScannerLayout.ScannerListener() {
            @Override
            public void onEnterDone(String data) {
                onScanDone(data);
            }

            @Override
            public void onScanDone(String data) {
                if (!TextUtils.isEmpty(data)) {
                    presenter.onLocationScanned(data);
                }
            }

            @Override
            public void onEmptyDone() {

            }
        });
    }

    private void initView() {
        layout = activity.findView(R.id.scan_location_layout);
        locationTxt = activity.findView(R.id.suggest_location_txt);
        scanner = activity.findView(R.id.location_scanner);
        locFullBtn = activity.findView(R.id.loc_full_btn);
        nextBtn = activity.findView(R.id.scan_location_next_btn);
    }

    public void resetScanner() {
        scanner.resetEdt();
    }

    public void showLocation(String location) {
        locationTxt.setText(location);
    }

    public void setScannerText(String location) {
        scanner.setText(location);
    }

    public void showLocationSelectDialog(List<LocationEntry> locations) {
        CommUtil.showListDialog(activity, -1, Stream.of(locations).map(LocationEntry::getName).toList(), (which) -> presenter.onLocationConfirmed(locations.get(which)));
    }

    public void showSelectExceptionLocationReasonDialog(String error, LocationEntry location) {
        List<String> errorList = Lists.newArrayList(activity.getString(R.string.msg_location_full),
                activity.getString(R.string.msg_could_not_find_the_location),
                activity.getString(R.string.msg_do_not_like_it));

        ListDialog.newInstance(error, errorList)
                .setOnItemClick(position -> presenter.onNotSuggestedLocationSelected(errorList.get(position), location))
                .show(activity.getSupportFragmentManager(), "selectExceptionLocationReason");
    }

    public void showOnScreenOpCountDialog(OnScreenCountResponseEntry onScreenCountResponseEntry) {
        OpportunityCycleCountDialog dialog = OpportunityCycleCountDialog.newInstance(onScreenCountResponseEntry);
        dialog.setMessageTip(StringUtil.getOnScreenOpCountTipMsg(OPCountActionEntry.SCANNING_TO_LOCATION, onScreenCountResponseEntry));
        dialog.setOnActionCallback(new OpportunityCycleCountDialog.OnActionCallback() {
            @Override
            public void onMatch(DialogFragment dialogFragment) {
                presenter.onScreenCountMatch(onScreenCountResponseEntry);
                dialogFragment.dismiss();
            }

            @Override
            public void onNotMatch(DialogFragment dialogFragment) {
                presenter.onScreenCountNotMatch(onScreenCountResponseEntry);
                dialog.dismiss();
            }

            @Override
            public void onCancelDialog(DialogFragment dialogFragment) {
                dialogFragment.dismiss();
            }
        });
        dialog.show(activity.getSupportFragmentManager(), OpportunityCycleCountDialog.class.getSimpleName());
    }
}