package com.lt.linc.replenish.collect;

import android.text.TextUtils;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.linc.platform.inventory.model.InventoryEntry;
import com.linc.platform.utils.StringUtil;
import com.lt.linc.R;
import com.lt.linc.util.Constant;

/**
 * <AUTHOR>
 */

class InventoryItemAdapter extends BaseQuickAdapter<InventoryEntry, BaseViewHolder> {
    InventoryItemAdapter() {
        super(R.layout.item_inventory_item);
    }

    @Override
    protected void convert(BaseViewHolder holder, InventoryEntry item) {
        String itemUom = StringUtil.ignorePointZero(item.qty) + item.unitEntry.name;
        holder.setText(R.id.item_name_txt, item.itemSpecName + " (" + getLocation(item) + ")")
                .setText(R.id.qty_uom_txt, itemUom)
                .setText(R.id.item_desc_txt, item.itemSpecDesc)
                .setVisible(R.id.item_desc_txt, !TextUtils.isEmpty(item.itemSpecDesc));
    }

    private String getLocation(InventoryEntry inventoryEntry) {
        return StringUtil.containIgnoreCase(inventoryEntry.lpId, Constant.PREFIX_HLP) ? inventoryEntry.locationName : inventoryEntry.lpId;
    }
}
