package com.lt.linc.home.more.lso

import com.linc.platform.home.more.lsobonding.model.LSOCollectPackageItemView
import com.lt.linc.common.mvvm.kotlin.BaseBindingDifferQuickAdapter
import com.lt.linc.common.mvvm.kotlin.BaseBindingViewHolder
import com.lt.linc.databinding.ItemLsoBondingPackageDetailBinding

class LSOBondingPackageDetailAdapter : BaseBindingDifferQuickAdapter<LSOCollectPackageItemView, ItemLsoBondingPackageDetailBinding>() {

    override fun convert(helper: BaseBindingViewHolder<ItemLsoBondingPackageDetailBinding>?, item: LSOCollectPackageItemView?) {
        helper?.apply {
            binding.apply {
                item?.let {
                    trackingTv.text = it.barcode?:""
                }
            }
        }
    }

    override fun areItemsTheSame(oldItem: LSOCollectPackageItemView, newItem: LSOCollectPackageItemView): Boolean {
        return oldItem.barcode == newItem.barcode
    }

    override fun areContentsTheSame(oldItem: LSOCollectPackageItemView, newItem: LSOCollectPackageItemView): Boolean {
        return oldItem.barcode == newItem.barcode
    }
}