package com.lt.linc.home.task.taskstepmenu.genericstep;

import android.os.Bundle;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.widget.AppCompatButton;
import androidx.appcompat.widget.AppCompatEditText;
import androidx.appcompat.widget.AppCompatImageView;
import androidx.appcompat.widget.AppCompatTextView;
import android.text.TextUtils;
import android.text.method.ScrollingMovementMethod;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.LinearLayout;

import com.annimon.stream.Collectors;
import com.annimon.stream.Stream;
import com.customer.widget.CircleProgress;
import com.customer.widget.core.LincBaseFragment;
import com.linc.platform.common.step.StepBaseEntry;
import com.linc.platform.common.step.StepStatusEntry;
import com.linc.platform.generaltask.model.GeneralTaskViewEntry;
import com.linc.platform.genericstep.model.GenericStepEntry;
import com.linc.platform.genericstep.presenter.GenericStepPresenter;
import com.linc.platform.genericstep.presenter.impl.GenericStepPresenterImpl;
import com.linc.platform.genericstep.view.GenericStepView;
import com.linc.platform.home.more.takeovermanage.TaskTakeOverCreateEntry;
import com.linc.platform.utils.CollectionUtil;
import com.linc.platform.utils.Logger;
import com.linc.platform.utils.StringUtil;
import com.linc.platform.utils.ToastUtil;
import com.lt.linc.R;

/**
 * <AUTHOR>
 */

public class GenericStepFragment extends LincBaseFragment implements GenericStepView {
    public static final String STEP_ID = "step id";
    public static final String ASSIGNEE = "assignee";
    public static final String COMPANY_ID = "Company Id";
    protected FrameLayout stepOperateLayout;
    protected LinearLayout assigneeLayout;
    protected AppCompatTextView assigneeTxt;
    protected AppCompatTextView stepDescTxt;
    protected AppCompatButton startBtn;
    protected AppCompatButton startDoneBtn;
    protected AppCompatButton takeOverBtn;
    protected AppCompatImageView stepDoneIcon;
    protected LinearLayout startPeriodLayout;
    protected LinearLayout endPeriodLayout;
    protected LinearLayout timePeriodLayout;
    protected AppCompatTextView startPeriodTxt;
    protected AppCompatTextView endPeriodTxt;

    private String stepId;
    private String companyId;
    private String assignee;
    private OnInitView onInitView;
    private CircleProgress circleProgress;
    private GenericStepPresenter presenter;
    private GenericStepEntry genericStepEntry;
    private GeneralTaskViewEntry generalTaskViewEntry;

    public static GenericStepFragment newInstance(StepBaseEntry stepBaseEntry,
                                                  GeneralTaskViewEntry generalTaskViewEntry) {
        String assigneeName = stepBaseEntry.assignees == null ? "" : Stream.of(stepBaseEntry.assignees)
                .map(assignee -> assignee.firstName + " " + assignee.lastName)
                .collect(Collectors.joining(" | "));

        GenericStepFragment genericStepFragment = new GenericStepFragment();
        Bundle bundle = new Bundle();
        bundle.putString(STEP_ID, stepBaseEntry.id);
        bundle.putString(ASSIGNEE, assigneeName);
        bundle.putString(COMPANY_ID, stepBaseEntry.companyId);
        bundle.putSerializable(GeneralTaskViewEntry.TAG, generalTaskViewEntry);
        genericStepFragment.setArguments(bundle);
        return genericStepFragment;
    }

    @Override
    protected void initView() {
        initViewLayout();
        stepDescTxt.setMovementMethod(ScrollingMovementMethod.getInstance());
        circleProgress = CircleProgress.create(getContext());

        assignee = getArguments().getString(ASSIGNEE);
        stepId = getArguments().getString(STEP_ID);
        companyId = getArguments().getString(COMPANY_ID);

        generalTaskViewEntry = (GeneralTaskViewEntry) getArguments().get(GeneralTaskViewEntry.TAG);
        presenter = new GenericStepPresenterImpl(this, companyId);

        startBtn.setOnClickListener(v -> presenter.updateStep(stepId, StepStatusEntry.IN_PROGRESS));
        startDoneBtn.setOnClickListener(v -> presenter.updateStep(stepId, StepStatusEntry.DONE));
        takeOverBtn.setOnClickListener(v -> assignOrTakeOverStep());
    }

    @Override
    public void onResume() {
        super.onResume();
        presenter.loadStep(stepId);
    }

    private void assignOrTakeOverStep() {
        if (generalTaskViewEntry != null && !TextUtils.isEmpty(generalTaskViewEntry.assigneeUserId)
                && generalTaskViewEntry.assigneeUserId.equals(getIdmUserId())) {
            presenter.assignStep(stepId, getIdmUserId(), companyId);
        } else if (CollectionUtil.isNullOrEmpty(genericStepEntry.assigneeUserIds)) {
            presenter.assignStep(stepId, getIdmUserId(), companyId);
        } else {
            showCommitDescDialog();
        }

    }

    private void showCommitDescDialog() {
        AppCompatEditText descEdt = new AppCompatEditText(getContext());
        descEdt.setHint(R.string.send_message_to_owner);
        AlertDialog.Builder builder = new AlertDialog.Builder(getContext())
                .setTitle(getString(R.string.msg_title_take_over) + "  " + genericStepEntry.name)
                .setView(descEdt)
                .setPositiveButton(R.string.text_ok, (dialog, which) -> {
                    TaskTakeOverCreateEntry createEntry = new TaskTakeOverCreateEntry();
                    createEntry.description = descEdt.getText().toString();
                    createEntry.stepId = genericStepEntry.id;
                    createEntry.taskId = genericStepEntry.taskId;
                    createEntry.applyToUserId = genericStepEntry.assigneeUserIds.get(0);
                    presenter.takeOverStep(createEntry, genericStepEntry.companyId);
                })
                .setNegativeButton(R.string.label_cancel, (dialog, which) -> dialog.dismiss());
        builder.create().show();
    }

    @Override
    public void refreshStep(GenericStepEntry genericStepEntry) {
        this.genericStepEntry = genericStepEntry;
        onInitView.initToolbar(genericStepEntry.name);

        if (CollectionUtil.isNullOrEmpty(genericStepEntry.assigneeUserIds)) {
            assigneeLayout.setVisibility(View.GONE);
        }

        initStepWorkView(genericStepEntry);

        if (genericStepEntry.startTime == null && genericStepEntry.endTime == null) {
            timePeriodLayout.setVisibility(View.GONE);
        } else if (genericStepEntry.startTime != null && genericStepEntry.endTime != null) {
            timePeriodLayout.setVisibility(View.VISIBLE);
            startPeriodLayout.setVisibility(View.VISIBLE);
            endPeriodLayout.setVisibility(View.VISIBLE);
            startPeriodTxt.setText(StringUtil.dateFormat(genericStepEntry.startTime));
            endPeriodTxt.setText(StringUtil.dateFormat(genericStepEntry.endTime));
        } else if (genericStepEntry.startTime == null) {
            timePeriodLayout.setVisibility(View.VISIBLE);
            startPeriodLayout.setVisibility(View.GONE);
            endPeriodLayout.setVisibility(View.VISIBLE);
            endPeriodTxt.setText(StringUtil.dateFormat(genericStepEntry.endTime));
        } else {
            timePeriodLayout.setVisibility(View.VISIBLE);
            endPeriodLayout.setVisibility(View.GONE);
            startPeriodLayout.setVisibility(View.VISIBLE);
            startPeriodTxt.setText(StringUtil.dateFormat(genericStepEntry.startTime));
        }

        stepDescTxt.setText(genericStepEntry.description);
        assigneeTxt.setText(assignee);
    }

    private void initStepWorkView(GenericStepEntry genericStepEntry) {
        if (StepStatusEntry.DONE.equals(genericStepEntry.status)) {
            stepDoneIcon.setVisibility(View.VISIBLE);
            stepOperateLayout.setVisibility(View.GONE);
        } else if (CollectionUtil.isNullOrEmpty(genericStepEntry.assigneeUserIds)
                || !genericStepEntry.assigneeUserIds.contains(getIdmUserId())) {
            stepDoneIcon.setVisibility(View.GONE);
            stepOperateLayout.setVisibility(View.VISIBLE);
            takeOverBtn.setVisibility(View.VISIBLE);
        } else {
            initStepOperate(genericStepEntry.status);
        }
    }

    private void initStepOperate(StepStatusEntry status) {
        switch (status) {
            case DONE:
                startBtn.setVisibility(View.GONE);
                startDoneBtn.setVisibility(View.GONE);
                stepDoneIcon.setVisibility(View.VISIBLE);
                takeOverBtn.setVisibility(View.GONE);
                break;
            case IN_PROGRESS:
                startBtn.setVisibility(View.GONE);
                startDoneBtn.setVisibility(View.VISIBLE);
                stepDoneIcon.setVisibility(View.GONE);
                takeOverBtn.setVisibility(View.GONE);
                break;
            default:
                startBtn.setVisibility(View.VISIBLE);
                startDoneBtn.setVisibility(View.GONE);
                stepDoneIcon.setVisibility(View.GONE);
                takeOverBtn.setVisibility(View.GONE);
        }
        stepOperateLayout.setVisibility(View.VISIBLE);
    }

    @Override
    public void refreshForStatusUpdate(StepStatusEntry stepStatusEntry) {
        switch (stepStatusEntry) {
            case IN_PROGRESS:
                startBtn.setVisibility(View.GONE);
                startDoneBtn.setVisibility(View.VISIBLE);
                break;
            case DONE:
                startDoneBtn.setVisibility(View.GONE);
                stepDoneIcon.setVisibility(View.VISIBLE);
                ToastUtil.showToast(getString(R.string.generic_step_done_toast));
                break;
            default:
                Logger.e("Error step status");
        }
    }

    @Override
    public void stepAssignSuccessToast() {
        initStepOperate(StepStatusEntry.NEW);
        ToastUtil.showToast(getString(R.string.msg_step_take_over_success));
    }

    @Override
    public void stepTakeOverSuccessToast() {
        takeOverBtn.setClickable(false);
        ToastUtil.showToast(getString(R.string.take_over_success_wait_confirm));
    }

    private void initViewLayout() {
        stepOperateLayout = findViewById(R.id.step_operate_layout);
        assigneeLayout = findViewById(R.id.assignee_layout);
        assigneeTxt = findViewById(R.id.assignee_txt);
        stepDescTxt = findViewById(R.id.step_desc_txt);
        startBtn = findViewById(R.id.start_btn);
        startDoneBtn = findViewById(R.id.start_done_btn);
        takeOverBtn = findViewById(R.id.take_over_btn);
        stepDoneIcon = findViewById(R.id.step_done_icon);
        timePeriodLayout = findViewById(R.id.time_period_layout);
        startPeriodLayout = findViewById(R.id.start_period_layout);
        endPeriodLayout = findViewById(R.id.end_period_layout);
        startPeriodTxt = findViewById(R.id.start_period_txt);
        endPeriodTxt = findViewById(R.id.end_period_txt);
    }

    @Override
    protected int getLayoutId() {
        return R.layout.fragment_generic_step;
    }

    @Override
    public CharSequence getTabTitle() {
        return null;
    }

    @Override
    public void showProgress(boolean show) {
        if (getActivity() == null || getActivity().isFinishing()) {
            return;
        }
        if (show) {
            circleProgress.show();
        } else {
            circleProgress.dismiss();
        }
    }

    public void setInitView(OnInitView onInitView) {
        this.onInitView = onInitView;
    }

    public interface OnInitView {
        void initToolbar(String title);
    }
}
