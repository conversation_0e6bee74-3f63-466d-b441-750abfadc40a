package com.lt.linc.home.more.picksort;

import androidx.appcompat.widget.AppCompatTextView;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.LinearLayout;

import com.chad.library.adapter.base.BaseQuickAdapter;
import com.chad.library.adapter.base.BaseViewHolder;
import com.linc.platform.home.more.picksort.SortingLPDetailEntry;
import com.linc.platform.utils.CollectionUtil;
import com.linc.platform.utils.StringUtil;
import com.lt.linc.R;

/**
 * <AUTHOR>
 */
public class SortedToteAdapter extends BaseQuickAdapter<SortingLPDetailEntry, BaseViewHolder> {
    public SortedToteAdapter() {
        super(R.layout.item_pick_sort_sorting_item);
    }

    @Override
    protected void convert(BaseViewHolder holder, SortingLPDetailEntry item) {
        initSOIDView(holder, item);
        holder.setText(R.id.item_name_txt, item.item.getName());
        holder.setText(R.id.unit_name_txt, "(" + item.unit.getName() + ")");
        holder.setText(R.id.sorting_progress_txt, StringUtil.ignorePointZero(item.qty));
    }

    private void initSOIDView(BaseViewHolder holder, SortingLPDetailEntry item) {
        if (CollectionUtil.isNullOrEmpty(item.soIdList)) return;

        LinearLayout soIdLayout = (LinearLayout) holder.itemView.findViewById(R.id.so_id_layout);
        soIdLayout.removeAllViews();
        for (String soId : item.soIdList) {
            View view = LayoutInflater.from(holder.itemView.getContext()).inflate(R.layout.view_sorting_item_so_id, null);
            ((AppCompatTextView) view.findViewById(R.id.so_id_txt)).setText(soId);
            soIdLayout.addView(view);
        }

    }
}
