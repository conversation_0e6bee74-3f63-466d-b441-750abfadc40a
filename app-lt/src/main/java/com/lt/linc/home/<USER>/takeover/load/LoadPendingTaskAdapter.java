package com.lt.linc.home.more.takeover.load;

import android.content.Context;
import android.content.res.Resources;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.annimon.stream.Collectors;
import com.annimon.stream.Stream;
import com.customer.widget.core.OnTakeOverClick;
import com.linc.platform.common.step.StepBaseEntry;
import com.linc.platform.home.more.TaskIntentEntry;
import com.linc.platform.home.more.taskassign.api.OnChildClick;
import com.linc.platform.idm.model.UserViewEntry;
import com.linc.platform.load.model.EntryTicketEntry;
import com.linc.platform.load.model.LoadDetailEntry;
import com.linc.platform.load.model.LoadTaskViewEntry;
import com.linc.platform.utils.CollectionUtil;
import com.linc.platform.utils.Logger;
import com.linc.platform.utils.StringUtil;
import com.lt.linc.R;
import com.lt.linc.home.more.takeover.TakeOverAdapter;
import com.lt.linc.home.more.takeover.TakeOverChildViewHolder;
import com.lt.linc.home.more.takeover.TakeOverPagerFragment;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */

public class LoadPendingTaskAdapter extends TakeOverAdapter<LoadPendingGroupViewHolder, TakeOverChildViewHolder> {
    private List<LoadTaskViewEntry> loadTaskEntryList = new ArrayList<>();
    private List<LoadTaskViewEntry> originEntryList = new ArrayList<>();
    private Resources resources;
    private Context context;
    private OnTakeOverClick onTakeOverClick;
    private OnChildClick onChildClick;

    public LoadPendingTaskAdapter(TakeOverPagerFragment takeOverPagerFragment) {
        resources = takeOverPagerFragment.getResources();
        this.setHasStableIds(true);
        this.context = takeOverPagerFragment.getContext();
        this.onChildClick = takeOverPagerFragment;
        this.onTakeOverClick = takeOverPagerFragment;
    }

    @Override
    public LoadPendingGroupViewHolder onCreateGroupViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_pending_task_load_group, parent, false);
        return new LoadPendingGroupViewHolder(view);
    }

    @Override
    public TakeOverChildViewHolder onCreateChildViewHolder(ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.task_assign_child, parent, false);
        return new TakeOverChildViewHolder(view);
    }

    @Override
    public void onBindGroupViewHolder(LoadPendingGroupViewHolder holder, int groupPosition, int viewType) {
        holder.takeOverBtn.setOnClickListener(v -> onTakeOverClick.onTakeOverClick(groupPosition));
        LoadTaskViewEntry entry = loadTaskEntryList.get(groupPosition);
        switch (entry.status) {
            case NEW:
                holder.statusTxt.setText(R.string.title_load_status_new);
                holder.titleLayout.setBackgroundResource(R.color.status_unlock);
                break;
            case IN_PROGRESS:
                holder.statusTxt.setText(R.string.title_load_status_progress);
                holder.titleLayout.setBackgroundResource(R.color.status_progress);
                break;
            case CLOSED:
            case FORCE_CLOSED:
                holder.statusTxt.setText(R.string.title_load_status_done);
                holder.titleLayout.setBackgroundResource(R.color.status_done);
                break;
            case PENDING:
                holder.statusTxt.setText(R.string.title_task_status_pending);
                holder.titleLayout.setBackgroundResource(R.color.gray_line);
                break;
            default:
                Logger.e("Load task status error");
        }
        holder.noteTxt.setText(entry.description);
        holder.taskTxt.setText(entry.id);
        holder.entryTxt.setText(entry.entryId);
        holder.loadListTxt.setText(getLoadIds(entry.loadList));
        holder.assignDoorTxt.setText(entry.dock == null
                ? "" : String.format(resources.getString(R.string.label_dock), entry.dock.name));
        initEntryTicketInfoTxt(holder, entry.entryTicket);
    }

    private void initEntryTicketInfoTxt(LoadPendingGroupViewHolder holder, EntryTicketEntry entryTicket) {
        if (entryTicket == null) {
            return;
        }

        if (!TextUtils.isEmpty(entryTicket.tractor)) {
            holder.licensePlateTxt.setText(entryTicket.tractor);
        }

        if (!CollectionUtil.isNullOrEmpty(entryTicket.containers)) {
            holder.equipmentTitleTxt.setText(context.getString(R.string.label_container));
            holder.equipmentTxt.setText(Stream.of(entryTicket.containers).collect(Collectors.joining("\n")));
        } else if (!CollectionUtil.isNullOrEmpty(entryTicket.trailers)) {
            holder.equipmentTitleTxt.setText(context.getString(R.string.label_trailer));
            holder.equipmentTxt.setText(Stream.of(entryTicket.trailers).collect(Collectors.joining("\n")));
        }

        holder.carrierTxt.setText(entryTicket.carrier);
    }

    private String getLoadIds(List<LoadDetailEntry> loadList) {
        if (CollectionUtil.isNullOrEmpty(loadList)) {
            return "";
        }
        return Stream.of(loadList).map(load -> load.id).collect(Collectors.joining(" | "));
    }

    @Override
    public void onBindChildViewHolder(TakeOverChildViewHolder holder, int groupPosition,
                                      int childPosition, int viewType) {
        LoadTaskViewEntry taskEntry = loadTaskEntryList.get(groupPosition);
        StepBaseEntry entry = taskEntry.stepEntries.get(childPosition);
        String assignee = StringUtil.getAssigeeString(taskEntry.assigneeDetail);
        if (!TextUtils.isEmpty(assignee)) {
            holder.assigneeLayout.setVisibility(View.VISIBLE);
            holder.operateTxt.setText(assignee);
        } else {
            holder.assigneeLayout.setVisibility(View.GONE);
        }
        holder.stepNameTxt.setText(entry.name);

        holder.taskChildLayout.setOnClickListener(v ->
                onChildClick.onChildItemClick(groupPosition, childPosition));

        switch (entry.status) {
            case NEW:
            case NOT_STARTED:
                holder.stepStatusTxt.setTextColor(resources.getColor(R.color.status_new));
                holder.stepStatusTxt.setText(R.string.status_new);
                break;
            case IN_PROGRESS:
                holder.stepStatusTxt.setTextColor(resources.getColor(R.color.status_progress));
                holder.stepStatusTxt.setText(R.string.title_load_status_progress);
                break;
            case EXCEPTION:
            case FORCE_CLOSED:
                holder.stepStatusTxt.setTextColor(resources.getColor(R.color.exception_red));
                holder.stepStatusTxt.setText(R.string.title_load_status_exception);
                break;
            case SKIPPED:
            case DONE:
                holder.stepStatusTxt.setTextColor(resources.getColor(R.color.status_done));
                holder.stepStatusTxt.setText(R.string.title_load_status_done);
                break;
            default:
                holder.stepStatusTxt.setText(R.string.text_unknown);
                holder.stepStatusTxt.setTextColor(resources.getColor(R.color.gray_line));
        }
    }

    @Override
    public boolean onCheckCanExpandOrCollapseGroup(LoadPendingGroupViewHolder holder,
                                                   int groupPosition, int x, int y, boolean expand) {
        return true;
    }

    @Override
    public int getGroupCount() {
        return loadTaskEntryList.size();
    }

    @Override
    public int getChildCount(int groupPosition) {
        return CollectionUtil.isNullOrEmpty(loadTaskEntryList.get(groupPosition).stepEntries)
                ? 0 : loadTaskEntryList.get(groupPosition).stepEntries.size();
    }

    @Override
    public long getGroupId(int groupPosition) {
        return groupPosition;
    }

    @Override
    public long getChildId(int groupPosition, int childPosition) {
        return childPosition;
    }

    @Override
    public <T> void setItems(List<T> entries) {
        List<LoadTaskViewEntry> entryList = (List<LoadTaskViewEntry>) entries;
        if (entries != null) {
            loadTaskEntryList.clear();
            originEntryList.clear();
            loadTaskEntryList.addAll(entryList);
            originEntryList.addAll(entryList);
        }
        notifyDataSetChanged();
    }

    @Override
    public void setItem(int groupPosition, int childPosition, List<UserViewEntry> idmEntries) {
        loadTaskEntryList.get(groupPosition).stepEntries.get(childPosition).assignees = idmEntries;
        notifyDataSetChanged();
    }

    @Override
    public void setFilter(String filter) {
        loadTaskEntryList.clear();
        if (TextUtils.isEmpty(filter)) {
            loadTaskEntryList.addAll(originEntryList);
        } else {
            for (LoadTaskViewEntry entry : originEntryList) {
                if ((entry.dock != null && StringUtil.containIgnoreCase(entry.dock.name, filter))
                        || StringUtil.containIgnoreCase(entry.entryId, filter)) {
                    loadTaskEntryList.add(entry);
                }
            }
        }
        notifyDataSetChanged();
    }

    @Override
    public TaskIntentEntry getPendingItemInfo(int groupPosition, int childPosition) {
        LoadTaskViewEntry loadTaskViewEntry = loadTaskEntryList.get(groupPosition);
        TaskIntentEntry itemInfo = new TaskIntentEntry();
        itemInfo.taskId = loadTaskViewEntry.id;
        itemInfo.taskStatus = loadTaskViewEntry.status;
        itemInfo.companyId = loadTaskViewEntry.companyId;
        if (childPosition >= 0) {
            itemInfo.stepId = loadTaskViewEntry.stepEntries.get(childPosition).id;
            itemInfo.stepStatus = loadTaskViewEntry.stepEntries.get(childPosition).status;
        }
        return itemInfo;
    }

    public LoadTaskViewEntry getItem(int position) {
        return loadTaskEntryList.get(position);
    }
}
