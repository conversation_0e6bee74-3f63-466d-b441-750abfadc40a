package com.lt.linc.home.more.locationsplitormerge.split

import com.linc.platform.baseapp.model.LocationEntry
import com.lt.linc.common.mvi.ReactiveDataState
import com.lt.linc.common.mvi.ReactiveUiState
import com.lt.linc.common.mvi.UiEvent

/**
 * <AUTHOR>
 * @Date 2022/9/27
 */
data class LocationSplitState(val parentLocation: LocationEntry? = null, val childLocations: List<LocationEntry>? = null) :
    ReactiveDataState


data class LocationSplitUiState(val parentLocation: LocationEntry? = null, val childLocationWrappers: List<LocationSplitWrapper>? = null) :
    ReactiveUiState

interface LocationSplitUiEvent {

    object ResetScanParent : UiEvent

    object ResetScanChild : UiEvent

    data class CheckChildLocation(val location: LocationEntry) : UiEvent
}