package com.lt.linc.home.more.folders;

import com.chad.library.adapter.base.entity.MultiItemEntity;
import com.linc.platform.core.LocalPersistence;
import com.linc.platform.core.PermissionManager;
import com.linc.platform.foundation.model.organization.common.facility.FacilityEntry;
import com.linc.platform.idm.model.PermissionEntry;
import com.linc.platform.localconfig.FacilityConfigPresenterImpl;
import com.linc.platform.utils.ResUtil;
import com.lt.linc.R;
import com.lt.linc.parcelreceive.activity.SmallParcelReceiveCreateActivity;
import com.lt.linc.toolset.lpputaway.PutAwayCreateCreateActivity;

import java.util.ArrayList;
import java.util.List;

/**
 * Author: wujf
 * Time: 2020/10/27
 * Description:
 */
public class InboundFolder extends AbstractMoreFolder {
    @Override
    public List<MultiItemEntity> create() {
        FacilityEntry facility = FacilityConfigPresenterImpl.getInstance().getFacility(LocalPersistence.getUserId(ResUtil.getContext()));
        List<MultiItemEntity> folders = new ArrayList<>();
        MoreFolder folder = new MoreFolder(new ItemData(R.string.folder_inbound, R.drawable.ic_inbound_folder));

//        addSubItem(folder, new ItemData(R.string.label_parcel_receiving, R.string.label_parcel_receiving_summary, R.drawable.ic_receipt_green, CreateEmptyTaskActivity.class));
        addSubItem(folder, new ItemData(R.string.label_small_parcel_receiving, R.string.label_small_parcel_receiving_summary, R.drawable.ic_receipt_green, SmallParcelReceiveCreateActivity.class));
        if (facility != null && !facility.forbidMovementViaPutAway) {
            addSubItem(folder, new ItemData(R.string.label_put_away, R.string.title_put_away_add, R.drawable.ic_put_away, PutAwayCreateCreateActivity.class));
        }

        folders.add(folder);
        return folders;
    }

    @Override
    boolean hasPermission() {
        return PermissionManager.getInstance().hasPermission(PermissionEntry.User_AccessGeneralFunctions);
    }
}
