package com.lt.linc.home.more.adjustment;

import android.content.Intent;
import android.os.Bundle;
import androidx.appcompat.widget.AppCompatButton;
import androidx.appcompat.widget.Toolbar;
import android.view.View;

import com.customer.widget.core.LincBaseActivity;
import com.linc.platform.core.PermissionManager;
import com.linc.platform.idm.model.PermissionEntry;
import com.lt.linc.R;
import com.lt.linc.home.more.adjustment.lpadjustment.LpAdjustmentActivity;
import com.lt.linc.home.more.adjustment.newinventory.NewInventoryActivity;

/**
 * <AUTHOR>
 */

public class AdjustmentActivity extends LincBaseActivity {

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_adjustment);
        Toolbar toolbar = findView(R.id.toolbar);
        AppCompatButton lpAdjustmentBtn = findView(R.id.lp_adjustment_btn);
        AppCompatButton newInventoryBtn = findView(R.id.new_inventory_btn);
        if (PermissionManager.getInstance().hasPermission(PermissionEntry.Inventory_Adjustment_NewInventory)) {
            newInventoryBtn.setVisibility(View.VISIBLE);
        }

        initToolBar(toolbar, getString(R.string.title_adjustment));
        lpAdjustmentBtn.setOnClickListener(view -> LpAdjustmentActivity.statActivity(this));
        newInventoryBtn.setOnClickListener(view -> startActivity(new Intent(this, NewInventoryActivity.class)));
    }
}
