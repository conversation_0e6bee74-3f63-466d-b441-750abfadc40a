package com.lt.linc.home.more.materialmanager.inventorycount;

import androidx.lifecycle.ViewModel;
import android.text.TextUtils;

import com.linc.platform.baseapp.model.LocationEntry;
import com.linc.platform.foundation.model.ItemSpecEntry;
import com.linc.platform.foundation.model.UnitEntry;
import com.linc.platform.inventory.model.InventoryEntry;

import java.util.List;

/**
 * Author: wujf
 * Time: 2020/11/30
 * Description:
 */
public class MaterialCountViewModel extends ViewModel {
    private LocationEntry mLocationEntry;

    private List<InventoryEntry> mInventoryEntries;

    private List<UnitEntry> mUnitEntries;

    private UnitEntry unitEntry;

    private ItemSpecEntry mItemSpecEntry;

    public ItemSpecEntry getItemSpecEntry() {
        return mItemSpecEntry;
    }

    public void setItemSpecEntry(ItemSpecEntry itemSpecEntry) {
        mItemSpecEntry = itemSpecEntry;
    }

    public List<UnitEntry> getUnitEntries() {
        return mUnitEntries;
    }

    public void setUnitEntries(List<UnitEntry> unitEntries) {
        mUnitEntries = unitEntries;
    }

    public UnitEntry getUnitEntry() {
        return unitEntry;
    }

    public void setUnitEntry(UnitEntry unitEntry) {
        this.unitEntry = unitEntry;
    }

    public List<InventoryEntry> getInventoryEntries() {
        return mInventoryEntries;
    }

    public void setInventoryEntries(List<InventoryEntry> inventoryEntries) {
        mInventoryEntries = inventoryEntries;
    }

    public LocationEntry getLocationEntry() {
        return mLocationEntry;
    }

    public void setLocationEntry(LocationEntry locationEntry) {
        mLocationEntry = locationEntry;
    }

    public String getLocationId() {
        return mLocationEntry == null ? "" : mLocationEntry.id;
    }

    public String getLocationName() {
        return mLocationEntry == null ? "" : mLocationEntry.name;
    }

    public String getItemSpecId() {
        return mItemSpecEntry == null ? "" : mItemSpecEntry.id;
    }

    public String getItemDetailName() {
        return mItemSpecEntry == null ? "" : mItemSpecEntry.getItemDetail();
    }

    public String getItemName() {
        return mItemSpecEntry == null ? "" : mItemSpecEntry.name;
    }


    public String getLocationHlpId() {
        return mLocationEntry == null ? "" : mLocationEntry.hlpId;
    }
}
