package com.lt.linc.home.more.tasksearch;

import static android.app.Activity.RESULT_OK;
import static com.lt.linc.home.more.assetmanagment.assettask.AssetAssignedTaskActivityKt.ACTIVITY_START_FROM_DEFAULT;

import android.content.Intent;
import android.graphics.drawable.NinePatchDrawable;
import android.text.TextUtils;
import android.view.View;
import android.widget.LinearLayout;

import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.widget.AppCompatButton;
import androidx.appcompat.widget.AppCompatEditText;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.annimon.stream.Collectors;
import com.annimon.stream.Stream;
import com.customer.widget.CircleProgress;
import com.customer.widget.QuickScanner;
import com.customer.widget.core.ActivityBundleHolder;
import com.customer.widget.core.LincBaseFragment;
import com.customer.widget.core.OnTakeOverGroupClick;
import com.h6ah4i.android.widget.advrecyclerview.animator.GeneralItemAnimator;
import com.h6ah4i.android.widget.advrecyclerview.animator.RefactoredDefaultItemAnimator;
import com.h6ah4i.android.widget.advrecyclerview.decoration.ItemShadowDecorator;
import com.h6ah4i.android.widget.advrecyclerview.decoration.SimpleListDividerDecorator;
import com.h6ah4i.android.widget.advrecyclerview.expandable.RecyclerViewExpandableItemManager;
import com.linc.platform.cctask.model.CcTaskViewEntry;
import com.linc.platform.common.step.StepBaseEntry;
import com.linc.platform.common.step.StepStatusEntry;
import com.linc.platform.common.step.StepTypeEntry;
import com.linc.platform.common.task.TaskStatusEntry;
import com.linc.platform.common.task.TaskTypeEntry;
import com.linc.platform.core.LocalPersistence;
import com.linc.platform.foundation.model.organization.common.facility.FacilityEntry;
import com.linc.platform.generaltask.model.GeneralTaskViewEntry;
import com.linc.platform.home.more.clpbondingtask.model.CLPBondingTaskEntry;
import com.linc.platform.home.more.clpbondingtask.model.CLPBondingTaskMethodEntry;
import com.linc.platform.home.more.takeovermanage.TaskTakeOverCreateEntry;
import com.linc.platform.home.more.takeovermanage.TaskTakeOverViewEntry;
import com.linc.platform.home.more.taskassign.api.OnChildClick;
import com.linc.platform.home.more.tasksearch.AdvancedSearchEntry;
import com.linc.platform.home.more.tasksearch.AdvancedSearchViewEntry;
import com.linc.platform.home.more.tasksearch.TaskSearchCenterPresenter;
import com.linc.platform.home.more.tasksearch.TaskSearchCenterPresenterImpl;
import com.linc.platform.home.more.tasksearch.TaskSearchCenterView;
import com.linc.platform.idm.model.UserViewEntry;
import com.linc.platform.load.model.LoadDetailEntry;
import com.linc.platform.load.model.LoadTaskViewEntry;
import com.linc.platform.lumper.model.LumperTaskViewEntry;
import com.linc.platform.pack.model.PackTaskEntry;
import com.linc.platform.packtopallet.model.ParcelPackTaskEntry;
import com.linc.platform.pick.model.PickTaskStartEvent;
import com.linc.platform.pick.model.PickTaskViewEntry;
import com.linc.platform.putback.model.PutBackTaskEntry;
import com.linc.platform.receive.model.ReceiveTaskEntry;
import com.linc.platform.stagetoload.model.StageToLoadTaskEntry;
import com.linc.platform.toolset.lpputaway.model.PutAwayTaskEntry;
import com.linc.platform.transload.model.TransloadTaskViewEntry;
import com.linc.platform.transload_v2.transloading.model.TransloadLoadTaskView;
import com.linc.platform.utils.CollectionUtil;
import com.linc.platform.utils.StringUtil;
import com.linc.platform.utils.ToastUtil;
import com.lt.linc.R;
import com.lt.linc.cctask.step.CcTaskStepPagerActivity;
import com.lt.linc.common.mvvm.kotlin.extensions.UniversalActivityParam;
import com.lt.linc.dock.dockcheckin.DockCheckInScanActivity;
import com.lt.linc.dock_v1.dockchechkin.DockCheckInScanV1Activity;
import com.lt.linc.home.more.assetmanagment.assettask.AssetAssignedTaskActivity;
import com.lt.linc.home.more.clpbondingtask.packageupdate.CLPPackageUpdateTaskActivity;
import com.lt.linc.home.more.clpbondingtask.task.CLPBondingTaskActivity;
import com.lt.linc.home.more.consolidatePallet.create.StartConsolidatePalletActivity;
import com.lt.linc.home.more.hospital.work.HospitalDoneActivity;
import com.lt.linc.home.more.hospital.work.HospitalWorkActivity;
import com.lt.linc.home.more.inventoryconsolidation.view.InventoryConsolidationActivity;
import com.lt.linc.home.more.lso.LSOBondingTaskActivity;
import com.lt.linc.home.more.parcelload.ParcelLoadWorkActivity;
import com.lt.linc.home.task.taskstepmenu.TaskStepMenuActivity;
import com.lt.linc.home.task.taskstepmenu.genericstep.GenericStepWorkActivity;
import com.lt.linc.inventoryreplenishment.ReplenishmentActivity;
import com.lt.linc.load.detail.TaskDetailActivity;
import com.lt.linc.load_v1.LoadActivity;
import com.lt.linc.lumper.task.LumperTaskActivity;
import com.lt.linc.packtopallet.ParcelPackActivity;
import com.lt.linc.pick.stage.StageActivity;
import com.lt.linc.putback.operate.view.PutBackViewActivity;
import com.lt.linc.receive_v1.lpsetup.LpSetupActivity;
import com.lt.linc.receive_v1.offload.OffloadActivity;
import com.lt.linc.receive_v1.snscan.SnScanActivity;
import com.lt.linc.stagetoload.StageToLoadActivity;
import com.lt.linc.step.startstep.StartStepActivity;
import com.lt.linc.step.takeover.TakeOverStepActivity;
import com.lt.linc.toolset.task.TaskRes;
import com.lt.linc.transfer.in.TransferInActivity;
import com.lt.linc.transfer.out.TransferOutActivity;
import com.lt.linc.transload.stepmenu.TransloadStepMenuActivity;
import com.lt.linc.transload.stepwork.OffloadStepActivity;
import com.lt.linc.transload.stepwork.ReceivingStepActivity;
import com.lt.linc.transload.stepwork.ShippingStepActivity;
import com.lt.linc.transload.stepwork.lpn.LpnSetupStepActivity;
import com.lt.linc.transload.stepwork.putaway.PutAwayStepActivity;
import com.lt.linc.transload_v2.loading.loadlist.TransloadLoadsActivity;
import com.lt.linc.transload_v2.receiving.TransloadReceivingStepActivity;
import com.lt.linc.util.Constant;

import org.greenrobot.eventbus.EventBus;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import kotlin.Pair;

/**
 * <AUTHOR>
 */

public class TaskSearchCenterFragment extends LincBaseFragment
        implements OnChildClick, OnTakeOverGroupClick, TaskSearchCenterView,
        RecyclerViewExpandableItemManager.OnGroupCollapseListener,
        RecyclerViewExpandableItemManager.OnGroupExpandListener {
    private static final String PREFIX_ET = "ET-";
    private static final String PREFIX_TASK = "TASK-";
    private static final int TAKE_OVER_TYPE_TASK = 0;
    private static final int TAKE_OVER_TYPE_STEP = 1;
    private static final int ADVANCED_SEARCH_REQUEST_CODE = 1;
    private static final List<StepStatusEntry> STEP_NOT_TAKE_OVER = Arrays.asList(StepStatusEntry.DONE,
            StepStatusEntry.FORCE_CLOSED, StepStatusEntry.EXCEPTION);
    private static final List<TaskStatusEntry> TASK_NOT_TAKE_OVER = Arrays.asList(TaskStatusEntry.CLOSED,
            TaskStatusEntry.FORCE_CLOSED, TaskStatusEntry.EXCEPTION);
    private LinearLayout searchLayout;
    private QuickScanner searchView;
    private RecyclerView recyclerView;
    private LinearLayout advancedSearchEntryViewLayout;
    private LinearLayout taskIdLayout;
    private AppCompatTextView taskIdTxt;
    private LinearLayout entryIdLayout;
    private AppCompatTextView entryIdTxt;
    private LinearLayout taskTypeLayout;
    private AppCompatTextView taskTypeTxt;
    private LinearLayout taskStatusLayout;
    private AppCompatTextView taskStatusTxt;
    private LinearLayout dockLayout;
    private AppCompatTextView dockNameTxt;
    private LinearLayout assigneeLayout;
    private AppCompatTextView assigneeNameTxt;
    private AppCompatButton deleteBtn;
    private LinearLayout taskSearchResultLayout;
    private AppCompatTextView taskQtyTxt;
    private CircleProgress circleProgress;

    private FacilityEntry facility;
    private TaskSearchCenterAdapter adapter;
    private TaskSearchCenterPresenter presenter;
    private StepBaseEntry stepBaseEntry;
    private AdvancedSearchViewEntry advancedSearchViewEntry;
    private List<TaskTakeOverViewEntry> taskTakeOverViewEntries = new ArrayList<>();
    private RecyclerViewExpandableItemManager recyclerViewExpandableItemManager;
    private String queryText;
    private int takeOverType;
    private boolean isFromScan;
    private AdvancedSearchEntry searchEntry;

    @Override
    protected int getLayoutId() {
        return R.layout.fragment_task_search;
    }

    @Override
    protected void initView() {
        initViewLayout();
        adapter = new TaskSearchCenterAdapter(getActivity(), this, this);
        presenter = new TaskSearchCenterPresenterImpl(this);
        circleProgress = CircleProgress.create(getContext());

        initSearchView();
        initRecyclerView();
        presenter.loadTakeOverApply(getIdmUserId());
        presenter.findFacility(getFacilityId());
        AppCompatButton advancedSearchBtn = findViewById(R.id.advanced_search_btn);
        advancedSearchBtn.setOnClickListener(view -> startAdvancedSearchActivity());
    }

    private void startAdvancedSearchActivity() {
        Intent intent = new Intent(getContext(), AdvancedSearchActivity.class);
        startActivityForResult(intent, ADVANCED_SEARCH_REQUEST_CODE);
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == ADVANCED_SEARCH_REQUEST_CODE && resultCode == RESULT_OK) {
            searchEntry = (AdvancedSearchEntry) data.getSerializableExtra(AdvancedSearchFragment.ADVANCED_SEARCH_ENTRY);
            presenter.advancedSearch(searchEntry);
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        searchLayout.setFocusable(true);
        searchLayout.setFocusableInTouchMode(true);
    }

    @Override
    public void refreshTask(List<AdvancedSearchViewEntry> taskViewEntries) {
        if (!isAdded()) {
            return;
        }

        initSearchEntryView();
        if (CollectionUtil.isNullOrEmpty(taskViewEntries)) {
            ToastUtil.showToast(getString(R.string.error_task_not_found));
            adapter.setItems(null);
        } else {
            List<AdvancedSearchViewEntry> filterEntries = Stream.of(taskViewEntries)
                    .map(e -> {
                        if (e.taskType == TaskTypeEntry.TRANSLOAD_RECEIVING) {
                            // change step type, because the type returned from server is wrong.
                            // should be TRANSLOAD_RECEIVING instead of RECEIVING.
                            for (StepBaseEntry step : e.stepEntries) {
                                step.type = StepTypeEntry.TRANSLOAD_RECEIVING;
                            }
                        }
                        return e;
                    })
                    .sorted((entry1, entry2) -> {
                        if (entry1.pendingTime == null && entry2.pendingTime == null) {
                            return entry1.id.compareTo(entry2.id);
                        }

                        if (entry1.pendingTime == null) {
                            return 1;
                        }

                        if (entry2.pendingTime == null) {
                            return -1;
                        }

                        return entry1.pendingTime.compareTo(entry2.pendingTime);
                    })
                    .collect(Collectors.toList());
            adapter.setItems(filterEntries);
            adapter.doFilter(searchEntry != null && searchEntry.searchWithNotOwnerSwitchChecked);
        }

        String taskQty = String.format(getString(R.string.title_task_qty), String.valueOf(adapter.getGroupCount()));
        taskQtyTxt.setText(taskQty);
    }

    @Override
    public void refreshTakeOverApply(List<TaskTakeOverViewEntry> taskTakeOverViewEntries) {
        if (CollectionUtil.isNullOrEmpty(taskTakeOverViewEntries)) {
            return;
        }
        this.taskTakeOverViewEntries.addAll(taskTakeOverViewEntries);
    }

    @Override
    public void onTakeOverClick(int position) {
        takeOverType = TAKE_OVER_TYPE_TASK;
        advancedSearchViewEntry = adapter.getGroupItem(position);

        if (TASK_NOT_TAKE_OVER.contains(advancedSearchViewEntry.status)) {
            ToastUtil.showToast(getString(R.string.msg_task_is_done));
            return;
        }

        if (isApplied()) {
            ToastUtil.showToast(getString(R.string.msg_the_application_has_been_submitted));
            return;
        }

        if (checkIsAlreadyYourTask(advancedSearchViewEntry)) {
            ToastUtil.showToast(getString(R.string.msg_this_task_is_already_yours));
            return;
        }

        showCommitDescDialog(advancedSearchViewEntry.id);

//        if (checkIsSuperVisor() || checkIsEntryHaveUser() || checkIsFromScan()) {
//            presenter.assignTask(advancedSearchViewEntry.id, getIdmUserId(), advancedSearchViewEntry.companyId);
//            isFromScan = false;
//        } else if (!advancedSearchViewEntry.assigneeUserId.equals(getIdmUserId())) {
//            showCommitDescDialog(advancedSearchViewEntry.id);
//        }
    }

    public boolean checkIsAlreadyYourTask(AdvancedSearchViewEntry entry) {
        if (entry == null) {
            return false;
        }
        return LocalPersistence.getIdmEntry(getContext()).idmUserId.equals(entry.assigneeUserId);
    }

    public boolean checkIsSuperVisor() {
        UserViewEntry userViewEntry = LocalPersistence.getIdmEntry(getContext());
        return userViewEntry != null && userViewEntry.isSuperVisor != null && userViewEntry.isSuperVisor;
    }

    public boolean checkIsEntryHaveUser() {
        return TextUtils.isEmpty(advancedSearchViewEntry.assigneeUserId);
    }

    public boolean checkIsFromScan() {
        return isFromScan;
    }

    @Override
    public void onChildTakeOverClick(int groupPosition, int childPosition) {
        takeOverType = TAKE_OVER_TYPE_STEP;
        advancedSearchViewEntry = adapter.getGroupItem(groupPosition);
        stepBaseEntry = adapter.getChildItem(groupPosition, childPosition);

        if (STEP_NOT_TAKE_OVER.contains(stepBaseEntry.status)) {
            ToastUtil.showToast(getString(R.string.msg_step_is_done));
        } else if (isApplied()) {
            ToastUtil.showToast(getString(R.string.msg_the_application_has_been_submitted));
        } else if (CollectionUtil.isNullOrEmpty(stepBaseEntry.assigneeUserIds)) {
            presenter.assignStep(stepBaseEntry.id, getIdmUserId(), stepBaseEntry.companyId);
        } else if (!stepBaseEntry.assigneeUserIds.contains(getIdmUserId())) {
            showCommitDescDialog(stepBaseEntry.name);
        }
    }

    @Override
    public void onChildItemClick(int groupPosition, int childPosition) {
        advancedSearchViewEntry = adapter.getGroupItem(groupPosition);
        stepBaseEntry = adapter.getChildItem(groupPosition, childPosition);
        presenter.loadTask(advancedSearchViewEntry, false);
    }

    @Override
    public void onTaskLoadSuccess(boolean toTaskStepMenu) {
        GeneralTaskViewEntry taskViewEntry = presenter.getGeneralTaskEntry();
        taskViewEntry.attach();
        if (isTaskNeedDockCheckIn(taskViewEntry)) {
            onStartDockInActivity(taskViewEntry);
            return;
        }
        if (toTaskStepMenu) {
            Intent intent = new Intent();
            switch (presenter.getGeneralTaskEntry().taskType) {
                case TRANSLOAD:
                    intent.setClass(getContext(), TransloadStepMenuActivity.class);
                    intent.putExtra(TransloadTaskViewEntry.TAG, taskViewEntry);
                    getContext().startActivity(intent);
                    break;
                case PARCEL_LOAD:
                    intent.setClass(getContext(), ParcelLoadWorkActivity.class);
                    intent.putExtra(ParcelLoadWorkActivity.TASK_ID, taskViewEntry.id);
                    getContext().startActivity(intent);
                    break;
                case TRANSLOAD_RECEIVING:
                    intent.setClass(getContext(), TransloadReceivingStepActivity.class);
                    intent.putExtra(StepBaseEntry.TAG, taskViewEntry.stepEntries.get(0));
                    getContext().startActivity(intent);
                    break;
                case TRANSLOAD_LOADING:
                    presenter.tryStartTransloadingLoadsActivity(getFacilityId(), taskViewEntry.stepEntries.get(0), presenter.getTransloadLoadTaskView());
                    break;
                case INVENTORY_CONSOLIDATION:
                    InventoryConsolidationActivity.start(getActivity(), taskViewEntry);
                    break;
                case PARCEL_PACK:
                    String lpId = Stream.ofNullable(taskViewEntry.stepEntries)
                            .map(stepBaseEntry -> CollectionUtil.isNotNullOrEmpty(stepBaseEntry.lpIds) ? stepBaseEntry.lpIds.get(0) : "")
                            .collect(Collectors.joining());
                    ParcelPackTaskEntry entry = new ParcelPackTaskEntry();
                    entry.id = presenter.getParcelPackTaskEntry().id;
                    entry.taskType = presenter.getParcelPackTaskEntry().taskType;
                    intent.setClass(getContext(), TaskStepMenuActivity.class);
                    intent.putExtra(TaskStepMenuActivity.GENERAL_TASK_ENTRY, entry);
                    intent.putExtra(TaskStepMenuActivity.PARCEL_PACK_LP, lpId);
                    getContext().startActivity(intent);
                    break;
                case HOSPITAL_TASK:
                    TaskStatusEntry taskStatus = presenter.getGeneralTaskEntry().status;
                    if (taskStatus == TaskStatusEntry.IN_PROGRESS) {
                        HospitalWorkActivity.Companion.startActivity(getActivity(), presenter.getGeneralTaskEntry().id, false);
                    } else if (taskStatus == TaskStatusEntry.CLOSED) {
                        HospitalDoneActivity.Companion.startActivity(getActivity(), presenter.getGeneralTaskEntry().id);
                    }
                    break;
                case LSO_BONDING_TASK:
                    LSOBondingTaskActivity.Companion.startActivity(getActivity(), taskViewEntry.id);
                    break;
                default:
                    TaskStepMenuActivity.startActivity(getContext(), taskViewEntry.id, taskViewEntry.taskType);
                    break;
            }

            return;
        }

        switch (stepBaseEntry.type) {
            case GENERIC:
                startGenericStep(stepBaseEntry);
                break;
            case PREDEFINED:
                break;
            case OFFLOAD:
            case LP_SETUP:
            case LP_VERIFY:
            case SN_SCAN:
            case PALLET_COUNT:
                startReceiveStepOperate(stepBaseEntry);
                break;
            case PACK:
                startPackStep(stepBaseEntry);
                break;
            case LOAD:
                startLoadStep(stepBaseEntry);
                break;
            case STAGE_TO_LOAD:
                startStageToLoadStep(stepBaseEntry);
                break;
            case COUNT_UNSHIPPED:
                startCountUnshippedStep(stepBaseEntry);
                break;
            case PUT_AWAY:
                startPutAwayTaskOperate(stepBaseEntry);
                break;
            case PUT_AWAY_AUDITING:
                startPutAwayAuditingTaskOperate(stepBaseEntry);
                break;
            case PUT_BACK:
                startPutBackStep(stepBaseEntry);
                break;
            case PICK:
            case STAGE:
            case ORDER_PICK:
            case SORT:
                startPickActivity(stepBaseEntry);
                break;
            case CC_COLLECT:
            case CC_BUILD:
            case CC_TRADITIONAL:
                startCcStep(stepBaseEntry);
                break;
            case REPLENISHMENT:
                startReplenishmentTaskOperate();
                break;
            case TRANSLOAD_OFFLOAD:
                startTransloadOffload(stepBaseEntry);
                break;
            case TRANSLOAD_LPN:
                startTransloadLpn(stepBaseEntry);
                break;
            case RECEIVING:
                startTransloadReceiving(stepBaseEntry);
                break;
            case TRANSLOAD_PUT_AWAY:
                startTransloadPutAway(stepBaseEntry);
                break;
            case SHIPPING:
                startTransloadShipping(stepBaseEntry);
                break;
            case PARCEL_LOAD:
                startParcelLoadStep(stepBaseEntry.taskId);
                break;
            case INVENTORY_RECOUNT:
                startInventoryRecountStep();
                break;
            case INTERNAL_TRANSFER_OUT:
                startTransferOutStep();
                break;
            case TRANSFER_IN:
                startTransferInStep();
                break;
            case TRANSLOAD_RECEIVING:
                startTransloadReceivingV2();
                break;
            case TRANSLOAD_LOADING:
                presenter.tryStartTransloadingLoadsActivity(getFacilityId(), taskViewEntry.stepEntries.get(0), presenter.getTransloadLoadTaskView());
                break;
            case INVENTORY_CONSOLIDATION:
                InventoryConsolidationActivity.start(getActivity(), taskViewEntry);
                break;
            case PARCEL_PACK:
                startParcelPackTask(stepBaseEntry);
                break;
            case CLP_BONDING:
                startCLPBondingStep();
                break;
            case CONSOLIDATE_PALLET:
                startConsolidateStep();
                break;
            case TIME_RECORD:
                startLumperTaskStep(stepBaseEntry);
                break;
            default:
                Intent intent = new Intent(getContext(), Constant.getHomeActivity(getContext()));
                startActivity(intent);
                break;
        }
    }

    @Override
    public void showDockOccupyed() {
        ToastUtil.showErrorToast(getString(R.string.hint_dock_occupy));
    }


    @Override
    public void showTransloadLoadingDockCheckIn(StepBaseEntry stepBaseEntry, TransloadLoadTaskView task) {
        Intent intent = new Intent(getActivity(), DockCheckInScanActivity.class);
        intent.putExtra(DockCheckInScanActivity.GENERAL_TASK_ENTRY, task);
        intent.putExtra(DockCheckInScanActivity.ENTRY_ID, task.entryId);
        intent.putExtra(DockCheckInScanActivity.COMPANY_ID, task.companyId);
        intent.putExtra(DockCheckInScanActivity.TASK_TYPE, task.taskType.name());
        intent.putExtra(DockCheckInScanActivity.DOCK_OBJECT, task.dock);
        intent.putExtra(StepBaseEntry.TAG, stepBaseEntry);
        startActivity(intent);
    }

    @Override
    public void showTransloadLoadsActivity(StepBaseEntry stepBaseEntry, TransloadLoadTaskView task) {
        TransloadLoadsActivity.start(getActivity(), advancedSearchViewEntry.companyId, presenter.getTransloadLoadTaskView(), stepBaseEntry);
    }

    private void startTransloadReceivingV2() {
        Intent intent = new Intent(getContext(), TransloadReceivingStepActivity.class);
        intent.putExtra(StepBaseEntry.TAG, stepBaseEntry);
        getActivity().startActivity(intent);
    }

    private void startTransferOutStep() {
        Intent intent = new Intent(getContext(), TransferOutActivity.class);
        intent.putExtra(StepBaseEntry.TAG, stepBaseEntry);
        getActivity().startActivity(intent);
    }

    private void startTransferInStep() {
        Intent intent = new Intent(getContext(), TransferInActivity.class);
        intent.putExtra(StepBaseEntry.TAG, stepBaseEntry);
        getActivity().startActivity(intent);
    }

    private void startInventoryRecountStep() {
        Intent intent = new Intent();
        intent.putExtra(StepBaseEntry.TAG, stepBaseEntry);

        if (!stepBaseEntry.isOwner(getIdmUserId())) {
            intent.setClass(getContext(), TakeOverStepActivity.class);
        } else if (stepBaseEntry.isNew()) {
            intent.setClass(getContext(), StartStepActivity.class);
        } else {
            intent.setClass(getContext(), TaskRes.getClass(stepBaseEntry.type, null));
        }
        startActivity(intent);
    }

    private void onStartDockInActivity(GeneralTaskViewEntry generalTaskEntry) {
        Intent intent = new Intent();
        intent.setClass(getContext(), DockCheckInScanActivity.class);
        if (generalTaskEntry.taskType == TaskTypeEntry.RECEIVE) {
            ReceiveTaskEntry item = presenter.getReceiveTaskEntry();
            boolean receiveVersionOfNew = Constant.isReceiveVersionOfNew(item.customerEntry, getFacilityId());
            if (receiveVersionOfNew) {
                DockCheckInScanV1Activity.startActivity(getContext(), item, item.entryId, item.dock);
                return;
            }
            intent.setClass(getContext(), DockCheckInScanActivity.class);
            intent.putExtra(DockCheckInScanActivity.GENERAL_TASK_ENTRY, item);
            intent.putExtra(DockCheckInScanActivity.ENTRY_ID, item.entryId);
            intent.putExtra(DockCheckInScanActivity.COMPANY_ID, item.companyId);
            intent.putExtra(DockCheckInScanActivity.TASK_TYPE, item.taskType.name());
            intent.putExtra(DockCheckInScanActivity.DOCK_OBJECT, item.dock);
            startActivity(intent);
        } else if (generalTaskEntry.taskType == TaskTypeEntry.LOAD) {
            LoadTaskViewEntry item = presenter.getLoadTaskEntry();
            if (Constant.isLoadTaskVersionOfV1(item, getFacilityId()) && getContext() != null) {
                DockCheckInScanV1Activity.startActivity(getContext(), item, item.entryId, item.dock);
            } else {
                intent.putExtra(DockCheckInScanActivity.GENERAL_TASK_ENTRY, item);
                intent.putExtra(DockCheckInScanActivity.ENTRY_ID, item.entryId);
                intent.putExtra(DockCheckInScanActivity.COMPANY_ID, item.companyId);
                intent.putExtra(DockCheckInScanActivity.TASK_TYPE, item.taskType.name());
                intent.putExtra(DockCheckInScanActivity.DOCK_OBJECT, item.dock);
                startActivity(intent);
            }
        }
    }

    private boolean isTaskNeedDockCheckIn(GeneralTaskViewEntry generalTaskEntry) {
        if (generalTaskEntry.taskType == TaskTypeEntry.RECEIVE) {
            ReceiveTaskEntry item = presenter.getReceiveTaskEntry();
            return generalTaskEntry.isNeedDockCheckInForReceive(item.entryId, item.dock);
        }

        if (generalTaskEntry.taskType == TaskTypeEntry.LOAD) {
            LoadTaskViewEntry item = presenter.getLoadTaskEntry();
            return generalTaskEntry.isNeedDockCheckIn(item.entryId, item.dock);
        }
        return false;
    }

    private void startParcelLoadStep(String taskId) {
        Intent intent = new Intent(getContext(), ParcelLoadWorkActivity.class);
        intent.putExtra(ParcelLoadWorkActivity.TASK_ID, taskId);
        startActivity(intent);
    }

    @Override
    public void onStartPickStep(FacilityEntry facility, StepBaseEntry stepBaseEntry) {
        this.facility = facility;
        startPickActivity(stepBaseEntry);
    }

    private void startCountUnshippedStep(StepBaseEntry stepBaseEntry) {
        Intent intent = new Intent(getContext(), TaskDetailActivity.class);
        intent.putExtra(LoadTaskViewEntry.TAG, presenter.getLoadTaskEntry());
        intent.putExtra(TaskDetailActivity.TASK_ID, advancedSearchViewEntry.id);
        intent.putExtra(TaskDetailActivity.COMPANY_ID, advancedSearchViewEntry.companyId);
        intent.putExtra(StepBaseEntry.TAG, stepBaseEntry);
        startActivity(intent);
    }

    private void startTransloadOffload(StepBaseEntry stepBaseEntry) {
        Intent intent = new Intent(getContext(), OffloadStepActivity.class);
        intent.putExtra(StepBaseEntry.TAG, stepBaseEntry);
        startActivity(intent);
    }

    private void startTransloadLpn(StepBaseEntry stepBaseEntry) {
        Intent intent = new Intent(getContext(), LpnSetupStepActivity.class);
        intent.putExtra(StepBaseEntry.TAG, stepBaseEntry);
        startActivity(intent);
    }

    private void startTransloadReceiving(StepBaseEntry stepBaseEntry) {
        Intent intent = new Intent(getContext(), ReceivingStepActivity.class);
        intent.putExtra(StepBaseEntry.TAG, stepBaseEntry);
        startActivity(intent);
    }

    private void startTransloadPutAway(StepBaseEntry stepBaseEntry) {
        Intent intent = new Intent(getContext(), PutAwayStepActivity.class);
        intent.putExtra(StepBaseEntry.TAG, stepBaseEntry);
        startActivity(intent);
    }

    private void startTransloadShipping(StepBaseEntry stepBaseEntry) {
        Intent intent = new Intent(getContext(), ShippingStepActivity.class);
        intent.putExtra(StepBaseEntry.TAG, stepBaseEntry);
        startActivity(intent);
    }

    private void startPutBackStep(StepBaseEntry stepBaseEntry) {
        Intent intent = new Intent(getContext(), PutBackViewActivity.class);
        intent.putExtra(StepBaseEntry.TAG, stepBaseEntry);
        intent.putExtra(PutBackTaskEntry.TAG, presenter.getPutBackTaskEntry());
        startActivity(intent);
    }

    private void startLoadStep(StepBaseEntry stepBaseEntry) {
        if (LoadDetailEntry.STEP_LOADING.equals(stepBaseEntry.name)) {
            if (Constant.isLoadTaskVersionOfV1(presenter.getLoadTaskEntry(), getFacilityId()) && getContext() != null) {
                goToSelectAssetOrLoadActivity(stepBaseEntry);
            } else {
                Intent intent = new Intent(getContext(), TaskDetailActivity.class);
                intent.putExtra(LoadTaskViewEntry.TAG, presenter.getLoadTaskEntry());
                intent.putExtra(TaskDetailActivity.TASK_ID, advancedSearchViewEntry.id);
                intent.putExtra(TaskDetailActivity.COMPANY_ID, advancedSearchViewEntry.companyId);
                intent.putExtra(StepBaseEntry.TAG, stepBaseEntry);
                startActivity(intent);
            }
        }
    }

    private void goToSelectAssetOrLoadActivity(StepBaseEntry stepBaseEntry) {
        LoadTaskViewEntry loadTaskViewEntry = presenter.getLoadTaskEntry();
//        if (loadTaskViewEntry.status == TaskStatusEntry.NEW && facility.enableScanAssetInTask) {
//            startAssetAssignedTaskActivity(loadTaskViewEntry, stepBaseEntry);
//        } else {
//            LoadActivity.startActivity(getActivity(), loadTaskViewEntry, stepBaseEntry);
//        }
        LoadActivity.startActivity(getActivity(), loadTaskViewEntry, stepBaseEntry);
    }

    private void startAssetAssignedTaskActivity(LoadTaskViewEntry loadTaskViewEntry, StepBaseEntry stepBaseEntry) {
        Class<?> clazz = AssetAssignedTaskActivity.class;
        Intent intent = new Intent(getContext(), clazz);
        AssetAssignedTaskActivity.Param param = new AssetAssignedTaskActivity.Param(loadTaskViewEntry, stepBaseEntry, ACTIVITY_START_FROM_DEFAULT);
        ActivityBundleHolder.pushSerializable(clazz, new Pair<String, Serializable>(UniversalActivityParam.TAG, param));
        startActivity(intent);
    }

    private void startStageToLoadStep(StepBaseEntry stepBaseEntry) {
        Intent intent = new Intent(getContext(), StageToLoadActivity.class);
        intent.putExtra(StageToLoadTaskEntry.Companion.getTAG(), presenter.getStageToLoadTaskEntry());
        startActivity(intent);
    }

    private void startPackStep(StepBaseEntry stepBaseEntry) {
        PackTaskEntry task = presenter.getPackTaskEntry();
        Intent intent = new Intent(getContext(), Constant.getPackClass(task));
        intent.putExtra(StepBaseEntry.TAG, stepBaseEntry);
        intent.putExtra(PackTaskEntry.TAG, task);
        startActivity(intent);
    }

    private void startReplenishmentTaskOperate() {
        ReplenishmentActivity.start(getActivity(), presenter.getReplenishmentTaskEntry());
    }

    private void startPutAwayTaskOperate(StepBaseEntry stepBaseEntry) {
        Intent intent = new Intent(getContext(), TaskRes.getClass(presenter.getPutAwayTaskEntry(), stepBaseEntry.type));
        intent.putExtra(StepBaseEntry.TAG, stepBaseEntry);
        intent.putExtra(PutAwayTaskEntry.TAG, presenter.getPutAwayTaskEntry());
        startActivity(intent);
    }

    private void startPutAwayAuditingTaskOperate(StepBaseEntry stepBaseEntry) {
        Intent intent = new Intent(getContext(), TaskRes.getClass(presenter.getPutAwayAuditingTaskEntry(), stepBaseEntry.type));
        intent.putExtra(StepBaseEntry.TAG, stepBaseEntry);
        intent.putExtra(PutAwayTaskEntry.TAG, presenter.getPutAwayAuditingTaskEntry());
        startActivity(intent);
    }

    private void startCcStep(StepBaseEntry stepBaseEntry) {
        Intent intent = new Intent(getContext(), CcTaskStepPagerActivity.class);
        intent.putExtra(StepBaseEntry.TAG, stepBaseEntry);
        intent.putExtra(CcTaskViewEntry.TAG, presenter.getCcTaskEntry());
        startActivity(intent);
    }

    private void startPickActivity(StepBaseEntry stepBaseEntry) {
        if (facility == null) {
            presenter.startPickStep(getFacilityId(), stepBaseEntry);
            return;
        }
        PickTaskViewEntry pickTaskViewEntry = presenter.getPickTaskEntry();
        pickTaskViewEntry.facilityEntry = facility;
        Class<?> clazz = Constant.getPickClass(getActivity(), facility, presenter.getPickTaskEntry(), stepBaseEntry);
        Intent intent = new Intent(getContext(), clazz);
        PickTaskStartEvent startEvent = new PickTaskStartEvent();
        startEvent.stepBaseEntry = stepBaseEntry;
        startEvent.taskViewEntry = pickTaskViewEntry;
        startEvent.startFrom = this.getClass().getName();
        EventBus.getDefault().postSticky(startEvent);
        intent.putExtra(StepBaseEntry.TAG, stepBaseEntry);
        if (clazz == StageActivity.class) {
            StageActivity.Param.RobotStage param = new StageActivity.Param.RobotStage(presenter.getPickTaskEntry().id);
            intent.putExtra(UniversalActivityParam.TAG, param);
        }
        startActivity(intent);
    }

    private void startReceiveStepOperate(StepBaseEntry stepBaseEntry) {
        ReceiveTaskEntry task = presenter.getReceiveTaskEntry();
        boolean receiveVersionOfNew = Constant.isReceiveVersionOfNew(task.customerEntry, getFacilityId());
        if (receiveVersionOfNew) {
            Class<?> clazz = null;
            Serializable param = null;
            switch (stepBaseEntry.type) {
                case LP_SETUP:
                    clazz = LpSetupActivity.class;
                    param = new LpSetupActivity.Param(task, stepBaseEntry);
                    break;
                case OFFLOAD:
                    clazz = OffloadActivity.class;
                    param = new OffloadActivity.Param(task, stepBaseEntry);
                    break;
                case SN_SCAN:
                    clazz = SnScanActivity.class;
                    param = new SnScanActivity.Param(task, stepBaseEntry);
                    break;
            }
            if (clazz != null) {
                Intent intent = new Intent(getContext(), clazz);
                ActivityBundleHolder.pushSerializable(clazz, new Pair<>(UniversalActivityParam.TAG, param));
                startActivity(intent);
                return;
            }
        }

        Intent intent = new Intent(getContext(), com.lt.linc.receive.StepOperateActivity.class);
        ActivityBundleHolder.pushSerializable(
                com.lt.linc.receive.StepOperateActivity.class,
                new Pair<>(StepBaseEntry.TAG, stepBaseEntry),
                new Pair<>(ReceiveTaskEntry.TAG, task)
        );
        startActivity(intent);
    }

    private void startGenericStep(StepBaseEntry stepBaseEntry) {
        Intent intent = new Intent(getContext(), GenericStepWorkActivity.class);
        intent.putExtra(StepBaseEntry.TAG, stepBaseEntry);
        intent.putExtra(GeneralTaskViewEntry.TAG, presenter.getGeneralTaskEntry());
        startActivity(intent);

    }

    private void startParcelPackTask(StepBaseEntry stepBaseEntry) {
        String lpId = CollectionUtil.isNotNullOrEmpty(stepBaseEntry.lpIds) ? stepBaseEntry.lpIds.get(0) : "";
        Intent intent = new Intent(getContext(), ParcelPackActivity.class);
        intent.putExtra(StepBaseEntry.TAG, stepBaseEntry);
        intent.putExtra(GeneralTaskViewEntry.TAG, presenter.getParcelPackTaskEntry());
        intent.putExtra(TaskStepMenuActivity.PARCEL_PACK_LP, lpId);
        startActivity(intent);
    }

    private boolean isApplied() {
        if (CollectionUtil.isNullOrEmpty(taskTakeOverViewEntries)) {
            return false;
        }

        long appliedQty = Stream.of(taskTakeOverViewEntries)
                .filter(viewEntry -> viewEntry.taskId.equals(advancedSearchViewEntry.id)
                        && (takeOverType == TAKE_OVER_TYPE_STEP
                        ? (viewEntry.stepId != null && viewEntry.stepId.equals(stepBaseEntry.id))
                        : (viewEntry.stepId == null)))
                .count();

        return appliedQty != 0;
    }


    private void showCommitDescDialog(String titleContent) {
        AppCompatEditText descEdt = new AppCompatEditText(getContext());
        if (checkIsSuperVisor() || checkIsFromScan() || checkIsEntryHaveUser()) {
            descEdt.setHint(R.string.text_optional);
        } else {
            descEdt.setHint(R.string.send_message_to_owner);
        }
        AlertDialog.Builder builder = new AlertDialog.Builder(getContext())
                .setTitle(getString(R.string.msg_title_take_over) + "  " + titleContent)
                .setView(descEdt)
                .setPositiveButton(R.string.text_ok, (dialog, which) -> sendTakeOverApply(descEdt))
                .setNegativeButton(R.string.label_cancel, (dialog, which) -> dialog.dismiss());
        builder.create().show();
    }

    private void sendTakeOverApply(AppCompatEditText descEdt) {
        String description = descEdt.getText().toString();

        TaskTakeOverCreateEntry createEntry = new TaskTakeOverCreateEntry();
        createEntry.taskId = advancedSearchViewEntry.id;
        createEntry.companyId = advancedSearchViewEntry.companyId;
        createEntry.description = description;
        if (!checkIsSuperVisor() && !checkIsFromScan()) {
            createEntry.needSupervisorApprove = true;
        } else {
            createEntry.needSupervisorApprove = false;
        }

        if (takeOverType == TAKE_OVER_TYPE_TASK) {
            createEntry.applyToUserId = advancedSearchViewEntry.assigneeUserId;
        } else {
            createEntry.stepId = stepBaseEntry.id;
            createEntry.applyToUserId = stepBaseEntry.assigneeUserIds.get(0);
        }

        presenter.takeOverApply(createEntry);
    }

    @Override
    public void successToastAndRefresh() {
        TaskTakeOverViewEntry taskTakeOverViewEntry = new TaskTakeOverViewEntry();
        taskTakeOverViewEntry.taskId = advancedSearchViewEntry.id;
        taskTakeOverViewEntry.stepId = stepBaseEntry == null ? null : stepBaseEntry.id;
        taskTakeOverViewEntries.add(taskTakeOverViewEntry);

        if (checkIsSuperVisor() || checkIsFromScan()) {
            isFromScan = false;
            ToastUtil.showToast(getString(R.string.take_over_success));
        } else {
            ToastUtil.showToast(getString(R.string.take_over_success_wait_confirm));
        }
    }

    @Override
    public void setFacility(FacilityEntry facility) {
        this.facility = facility;
    }

    @Override
    public void taskAssignSuccessToast() {
        presenter.advancedSearch(searchEntry);
        ToastUtil.showToast(getString(R.string.msg_task_take_over_success));
    }

    @Override
    public void stepAssignSuccessToast() {
        presenter.advancedSearch(searchEntry);
        ToastUtil.showToast(getString(R.string.msg_step_take_over_success));
    }

    @Override
    public void onStepDetailClick(int position) {
        if (recyclerViewExpandableItemManager.isGroupExpanded(position)) {
            recyclerViewExpandableItemManager.collapseGroup(position);
        } else {
            recyclerViewExpandableItemManager.expandGroup(position);
        }
    }

    @Override
    public void onTaskClick(int position) {
        advancedSearchViewEntry = adapter.getGroupItem(position);
        presenter.loadTask(advancedSearchViewEntry, true);
    }

    @Override
    public void onGroupCollapse(int groupPosition, boolean fromUser, Object payload) {
    }

    @Override
    public void onGroupExpand(int groupPosition, boolean fromUser, Object payload) {
        if (fromUser) {
            adjustScrollPositionOnGroupExpanded(groupPosition);
        }
    }

    private void adjustScrollPositionOnGroupExpanded(int groupPosition) {
        int childItemHeight = getActivity().getResources().getDimensionPixelSize(R.dimen.list_item_height);
        int topMargin = (int) (getActivity().getResources().getDisplayMetrics().density * 16);
        recyclerViewExpandableItemManager.scrollToGroup(groupPosition, childItemHeight, topMargin, topMargin);
    }

    @Override
    public void showProgress(boolean show) {
        if (getActivity() == null || getActivity().isFinishing()) {
            return;
        }
        if (show) {
            circleProgress.show();
        } else {
            circleProgress.dismiss();
        }
    }

    private void initSearchView() {
        searchView.setScanReset(false);
        searchView.setScanEvent(new QuickScanner.OnScanEvent() {
            @Override
            public void onDone(View view, String data) {
                searchEntry = new AdvancedSearchEntry();
                searchEntry.keyword = data;
                if (data.startsWith("PT-")) {
                    isFromScan = true;
                }
                presenter.advancedSearch(searchEntry);
                searchView.setText(data);
            }
        });
    }

    private void initRecyclerView() {
        RecyclerView.LayoutManager linearLayoutManager = new LinearLayoutManager(getContext());
        recyclerViewExpandableItemManager = new RecyclerViewExpandableItemManager(null);
        recyclerViewExpandableItemManager.setOnGroupExpandListener(this);
        recyclerViewExpandableItemManager.setOnGroupCollapseListener(this);

        RecyclerView.Adapter wrappedAdapter = recyclerViewExpandableItemManager.createWrappedAdapter(adapter);
        GeneralItemAnimator animator = new RefactoredDefaultItemAnimator();
        animator.setSupportsChangeAnimations(false);

        recyclerView.setLayoutManager(linearLayoutManager);
        recyclerView.setAdapter(wrappedAdapter);
        recyclerView.setItemAnimator(animator);
        recyclerView.setHasFixedSize(false);

        recyclerView.addItemDecoration(new ItemShadowDecorator(
                (NinePatchDrawable) ContextCompat.getDrawable(getContext(), R.drawable.material_shadow_z1)));
        recyclerView.addItemDecoration(new SimpleListDividerDecorator(
                ContextCompat.getDrawable(getContext(), R.drawable.list_divider_h), true));
        recyclerViewExpandableItemManager.attachRecyclerView(recyclerView);
    }

    private void initViewLayout() {
        searchLayout = findViewById(R.id.search_layout);
        searchView = findViewById(R.id.search_view);
        recyclerView = findViewById(R.id.recycler_view);
        taskIdLayout = findViewById(R.id.task_id_layout);
        taskIdTxt = findViewById(R.id.task_id_txt);
        entryIdLayout = findViewById(R.id.entry_id_layout);
        entryIdTxt = findViewById(R.id.entry_id_txt);
        taskTypeLayout = findViewById(R.id.task_type_layout);
        taskTypeTxt = findViewById(R.id.task_type_txt);
        taskStatusLayout = findViewById(R.id.task_status_layout);
        taskStatusTxt = findViewById(R.id.task_status_txt);
        dockLayout = findViewById(R.id.dock_layout);
        dockNameTxt = findViewById(R.id.dock_name_txt);
        assigneeLayout = findViewById(R.id.assignee_layout);
        assigneeNameTxt = findViewById(R.id.assignee_name_txt);
        deleteBtn = findViewById(R.id.delete_btn);
        advancedSearchEntryViewLayout = findViewById(R.id.advanced_search_entry_layout);
        taskSearchResultLayout = findViewById(R.id.search_result_layout);
        taskQtyTxt = findViewById(R.id.task_qty_txt);


        deleteBtn.setOnClickListener(view -> {
            searchEntry = null;
            adapter.setItems(null);
            advancedSearchEntryViewLayout.setVisibility(View.GONE);
        });

        advancedSearchEntryViewLayout.setOnClickListener(view -> {
            Intent intent = new Intent(getContext(), AdvancedSearchActivity.class);
            intent.putExtra(AdvancedSearchEntry.TAG, searchEntry);
            startActivityForResult(intent, ADVANCED_SEARCH_REQUEST_CODE);
        });
    }


    private void initSearchEntryView() {
        if (searchEntry == null) {
            deleteBtn.setVisibility(View.GONE);
            taskSearchResultLayout.setVisibility(View.GONE);
            advancedSearchEntryViewLayout.setVisibility(View.GONE);
            return;
        }

        if (CollectionUtil.isNullOrEmpty(searchEntry.taskIds)) {
            taskIdLayout.setVisibility(View.GONE);
        } else {
            taskIdLayout.setVisibility(View.VISIBLE);
            taskIdTxt.setText(searchEntry.taskIds.get(0));
        }

        if (TextUtils.isEmpty(searchEntry.entryId)) {
            entryIdLayout.setVisibility(View.GONE);
        } else {
            entryIdLayout.setVisibility(View.VISIBLE);
            entryIdTxt.setText(searchEntry.entryId);
        }

        if (TextUtils.isEmpty(searchEntry.taskTypeTxt)) {
            taskTypeLayout.setVisibility(View.GONE);
        } else {
            taskTypeLayout.setVisibility(View.VISIBLE);
            taskTypeTxt.setText(searchEntry.taskTypeTxt);
        }

        if (TextUtils.isEmpty(searchEntry.taskStatusTxt)) {
            taskStatusLayout.setVisibility(View.GONE);
        } else {
            taskStatusLayout.setVisibility(View.VISIBLE);
            taskStatusTxt.setText(searchEntry.taskStatusTxt);
        }

        if (searchEntry.locationEntry == null) {
            dockLayout.setVisibility(View.GONE);
        } else {
            dockLayout.setVisibility(View.VISIBLE);
            dockNameTxt.setText(searchEntry.locationEntry.name);
        }

        if (CollectionUtil.isNullOrEmpty(searchEntry.assigneeEntries)) {
            assigneeLayout.setVisibility(View.GONE);
        } else {
            assigneeLayout.setVisibility(View.VISIBLE);
            assigneeNameTxt.setText(StringUtil.getAssigeeString(searchEntry.assigneeEntries));
        }

        deleteBtn.setVisibility(View.VISIBLE);
        taskSearchResultLayout.setVisibility(View.VISIBLE);
        advancedSearchEntryViewLayout.setVisibility(View.VISIBLE);
    }


    private void startCLPBondingStep() {
        CLPBondingTaskEntry task = (CLPBondingTaskEntry) presenter.getGeneralTaskEntry();
        Class<?> clazz;
        UniversalActivityParam param;
        if (task.getMethod() == CLPBondingTaskMethodEntry.CLP_PACKAGE_UPDATE) {
            clazz = CLPPackageUpdateTaskActivity.class;
            param = new CLPPackageUpdateTaskActivity.Param(task);
        } else {
            clazz = CLPBondingTaskActivity.class;
            param = new CLPBondingTaskActivity.Param(task);
        }
        Intent intent = new Intent(getContext(), clazz);
        ActivityBundleHolder.pushSerializable(clazz, new Pair<>(UniversalActivityParam.TAG, param));
        startActivity(intent);
    }

    private void startConsolidateStep() {
        StartConsolidatePalletActivity.Companion.startActivity(getContext(), false, presenter.getGeneralTaskEntry().id);
    }

    private void startLumperTaskStep(StepBaseEntry step) {
        LumperTaskViewEntry task = presenter.getLumperTaskEntry();
        Class<?> clazz = LumperTaskActivity.class;
        UniversalActivityParam param = new LumperTaskActivity.Param(task,step);
        Intent intent = new Intent(getContext(), clazz);
        ActivityBundleHolder.pushSerializable(clazz, new Pair<>(UniversalActivityParam.TAG, param));
        startActivity(intent);
    }


}
