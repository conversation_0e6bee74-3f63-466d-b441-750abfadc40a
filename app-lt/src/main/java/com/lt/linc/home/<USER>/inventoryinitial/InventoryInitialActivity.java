package com.lt.linc.home.more.inventoryinitial;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.ArrayAdapter;
import android.widget.LinearLayout;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatButton;
import androidx.appcompat.widget.AppCompatEditText;
import androidx.appcompat.widget.AppCompatSpinner;
import androidx.appcompat.widget.AppCompatTextView;
import androidx.appcompat.widget.Toolbar;

import com.annimon.stream.Stream;
import com.customer.widget.ScanEditText;
import com.customer.widget.core.LincBaseActivity;
import com.customer.widget.datetimepicker.DatePickerDialogFragment;
import com.customer.widget.locationselector.LocationSelectorDialog;
import com.jakewharton.rxbinding.view.RxView;
import com.linc.platform.baseapp.model.LocationEntry;
import com.linc.platform.baseapp.model.LocationTypeEntry;
import com.linc.platform.foundation.model.CustomerViewEntry;
import com.linc.platform.foundation.model.ItemSpecEntry;
import com.linc.platform.foundation.model.UnitEntry;
import com.linc.platform.foundation.model.organization.common.base.OrganizationViewEntry;
import com.linc.platform.home.more.inventoryinitial.model.InventoryInitialCreateEntry;
import com.linc.platform.home.more.inventoryinitial.model.InventoryInitialType;
import com.linc.platform.home.more.inventoryinitial.presenter.InventoryInitialPresenter;
import com.linc.platform.home.more.inventoryinitial.presenter.InventoryInitialPresenterImpl;
import com.linc.platform.home.more.inventoryinitial.view.InventoryInitialView;
import com.linc.platform.http.ErrorResponse;
import com.linc.platform.print.commonprintlp.PrintData;
import com.linc.platform.print.commonprintlp.PrintMsg;
import com.linc.platform.print.model.PrinterEntry;
import com.linc.platform.utils.CollectionUtil;
import com.linc.platform.utils.Constant;
import com.linc.platform.utils.Lists;
import com.linc.platform.utils.Logger;
import com.linc.platform.utils.StringUtil;
import com.linc.platform.utils.TimeUtil;
import com.linc.platform.utils.ToastUtil;
import com.lt.linc.R;
import com.lt.linc.parcelreceive.fragment.receivecreate.ItemSpecDialogFragment;
import com.lt.linc.toolset.print.setting.PrintSettingActivity;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @Description:
 * @Author: Dennis
 * @CreateDate: 2022/6/9 17:16
 */
public class InventoryInitialActivity extends LincBaseActivity implements InventoryInitialView, ItemSpecDialogFragment.OnItemSpecSelectedListener {

    private Toolbar mToolbar;
    private ScanEditText mScannerItem;
    private LinearLayout mLlContent;
    private AppCompatTextView mTvItemName;
    private AppCompatTextView mTvCustomerName;
    private AppCompatTextView mTvItemDescription;
    private AppCompatSpinner mSpinnerTitle;
    private LinearLayout mLlGoodsType;
    private AppCompatSpinner mSpinnerGoodsType;
    private LinearLayout mLlLotNo;
    private ScanEditText mScannerLotNo;
    private LinearLayout mLlReceiveWhen;
    private AppCompatTextView mTvReceiveWhen;
    private ScanEditText mScannerLocation;
    private LinearLayout mLlLp;
    private ScanEditText mScannerLp;
    private AppCompatEditText mQtyEdit;
    private AppCompatSpinner mSpinnerUnit;
    private AppCompatSpinner mSpinnerFastAddMethod;
    private AppCompatButton mBtnSubmit;
    private AppCompatButton mBtnFastAdd;

    private LocationSelectorDialog mLocationSelectorDialog;
    private InventoryInitialPresenter mPresenter;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_inventory_initial);
        bindView();
        mPresenter = new InventoryInitialPresenterImpl(this);
        initToolBar(mToolbar, R.string.title_inventory_initial);
        initView();
    }

    private void initView() {
        initScannerItem();
        initScannerLP();
        initScannerLocation();
        mScannerLotNo.setHintText(R.string.msg_count_lot_no_error);
        mQtyEdit.setHint(R.string.hint_please_input_qty);
        String[] fastAddMethods = getResources().getStringArray(R.array.inventory_initial_fast_add_method);
        ArrayAdapter<String> spinnerAdapter = new ArrayAdapter<>(this,
                android.R.layout.simple_spinner_item, fastAddMethods);
        spinnerAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        mSpinnerFastAddMethod.setAdapter(spinnerAdapter);
        RxView.clicks(mBtnSubmit).throttleFirst(1, TimeUnit.SECONDS)
                .subscribe(aVoid -> submit(InventoryInitialType.DEFAULT));
        RxView.clicks(mBtnFastAdd).throttleFirst(1, TimeUnit.SECONDS)
                .subscribe(aVoid -> fastAdd());
    }

    private void initScannerItem() {
        mScannerItem.setHintText(R.string.hint_please_scan_or_input_item);
        mScannerItem.setOnActionListener(new ScanEditText.OnActionListener() {
            @Override
            public void onAddScanDone(String data) {
                mPresenter.searchItemSpec(data);
            }

            @Override
            public void onRemoveScanDone(String data) {

            }

            @Override
            public void onSearch(String data) {

            }

            @Override
            public void onClearText() {

            }
        });
    }

    private void initScannerLocation() {
        mScannerLocation.setHintText(R.string.msg_please_scan_or_input_location);
        mScannerLocation.setOnActionListener(new ScanEditText.OnActionListener() {
            @Override
            public void onAddScanDone(String data) {
                mPresenter.searchLocation(data);
            }

            @Override
            public void onRemoveScanDone(String data) {

            }

            @Override
            public void onSearch(String data) {

            }

            @Override
            public void onClearText() {

            }
        });
    }

    private void initScannerLP() {
        mScannerLp.setHintText(R.string.hint_please_scan_or_input_lp);
        mScannerLp.setOnActionListener(new ScanEditText.OnActionListener() {
            @Override
            public void onAddScanDone(String data) {
                setQtyEdtFocus();
            }

            @Override
            public void onRemoveScanDone(String data) {

            }

            @Override
            public void onSearch(String data) {

            }

            @Override
            public void onClearText() {

            }
        });
    }

    private void onClick(View view) {
        switch (view.getId()) {
            case R.id.btn_select_location:
                selectLocation();
                break;

            case R.id.btn_print:
                printLp();
                break;

            case R.id.tv_receive_when:
                selectReceiveWhen();
                break;
        }
    }

    private void selectLocation() {
        if (mLocationSelectorDialog == null) {
            mLocationSelectorDialog = new LocationSelectorDialog(this);
            mLocationSelectorDialog.setSelectListener(locationEntry -> {
                if (locationEntry != null) {
                    setLocationAndLpView(locationEntry);
                }
            });
        }
        mLocationSelectorDialog.showDialog(true);
    }

    private void printLp() {
        mPresenter.printLpLabel(getIdmUserId(), getFacilityName());
    }

    private void submit(InventoryInitialType inventoryInitialType) {
        InventoryInitialCreateEntry createEntry = generalInventoryInitialCreateEntry();
        mPresenter.initialInventory(createEntry, inventoryInitialType);
    }

    private InventoryInitialCreateEntry generalInventoryInitialCreateEntry() {
        InventoryInitialCreateEntry createEntry = new InventoryInitialCreateEntry();
        ItemSpecEntry selectedItemSpec = mPresenter.getSelectedItemSpec();
        LocationEntry selectedLocation = mPresenter.getSelectedLocation();
        OrganizationViewEntry itemSelectedTitle = getItemSelectedTitle();
        UnitEntry itemSelectedUnit = getItemSelectedUnit();
        createEntry.setItemSpecId(selectedItemSpec.id)
                .setDescription(selectedItemSpec.desc)
                .setCustomerId(selectedItemSpec.customerId)
                .setLocationId(selectedLocation != null ? selectedLocation.id : "")
                .setTitleId(itemSelectedTitle != null ? itemSelectedTitle.getId() : "")
                .setLpId(isPickLocation(selectedLocation) ? selectedLocation.hlpId : mScannerLp.getText())
                .setUnitId(itemSelectedUnit != null ? itemSelectedUnit.id : "");
        if (mLlLotNo.getVisibility() == View.VISIBLE) {
            createEntry.setLotNo(mScannerLotNo.getText())
                    .setNeedCaptureLotNo(true);
        }
        if (mLlReceiveWhen.getVisibility() == View.VISIBLE) {
            createEntry.setReceivedWhen(TimeUtil.local2Default(mPresenter.getReceiveWhen()))
                    .setNeedCaptureReceiveWhen(true);
        }
        if (mLlGoodsType.getVisibility() == View.VISIBLE) {
            createEntry.setGoodsType((String) mSpinnerGoodsType.getSelectedItem())
                    .setNeedCaptureGoodsType(true);
        } else {
            createEntry.setGoodsType(getDefaultGoodsTypeByCustomer(selectedItemSpec.customerViewEntry));
        }
        try {
            String qty = mQtyEdit.getText() != null ? mQtyEdit.getText().toString().trim() : "0";
            createEntry.setQty(Double.parseDouble(qty));
        } catch (Exception e) {
            Logger.e(e.getMessage());
        }
        return createEntry;
    }

    private String getDefaultGoodsTypeByCustomer(CustomerViewEntry customerViewEntry) {
        if (customerViewEntry != null && !TextUtils.isEmpty(customerViewEntry.defaultGoodsType)) {
            return customerViewEntry.defaultGoodsType;
        }
        return Constant.GOODS_TYPE_GOOD;
    }

    private OrganizationViewEntry getItemSelectedTitle() {
        List<OrganizationViewEntry> organizationViewEntries = mPresenter.getOrganizationViewEntries();
        if (CollectionUtil.isNotNullOrEmpty(organizationViewEntries) && mSpinnerTitle.getSelectedItemPosition() >= 0) {
            return organizationViewEntries.get(mSpinnerTitle.getSelectedItemPosition());
        }
        return null;
    }

    private UnitEntry getItemSelectedUnit() {
        List<UnitEntry> unitsEntry = mPresenter.getUnitsEntry();
        if (CollectionUtil.isNotNullOrEmpty(unitsEntry) && mSpinnerUnit.getSelectedItemPosition() >= 0) {
            return unitsEntry.get(mSpinnerUnit.getSelectedItemPosition());
        }
        return null;
    }

    private void fastAdd() {
        String fastAddMethod = mSpinnerFastAddMethod.getSelectedItem().toString();
        if (fastAddMethod.equals(getString(R.string.text_fast_add_by_pallet)) && mLlLp.getVisibility() == View.VISIBLE) {//For pick location, lp view gone
            submit(InventoryInitialType.BY_PALLET);
        } else {
            submit(InventoryInitialType.BY_LOCATION);
        }
    }

    private void selectReceiveWhen() {
        DatePickerDialogFragment.newInstance((date, year, month, day) -> {
            mPresenter.saveReceiveWhen(date);
            mTvReceiveWhen.setText(StringUtil.dateFormat(date));
        }, mPresenter.getReceiveWhen()).show(getSupportFragmentManager(), "");
    }

    @Override
    public void onPrinterNotSelect() {
        Intent intent = new Intent(this, PrintSettingActivity.class);
        startActivity(intent);
    }

    @Override
    public void onPrintSuccess(@NonNull PrintData data, @NonNull PrinterEntry printerEntry) {
        if (CollectionUtil.isNotNullOrEmpty(data.extra.lpIds)) {
            mScannerLp.setText(data.extra.lpIds.get(0));
        }
        setQtyEdtFocus();
        ToastUtil.showToast(R.string.toast_customized_label_print_print_success);
    }

    @Override
    public void onPrintFailed(ErrorResponse response, PrinterEntry printerEntry) {
        ToastUtil.showErrorToast(PrintMsg.formatError(printerEntry, response.getErrorMessage()));
    }

    @Override
    public void searchItemSpecSuccessful(ItemSpecEntry itemSpecEntry) {
        mPresenter.saveItemSpec(itemSpecEntry);
        mLlContent.setVisibility(View.VISIBLE);
        mScannerItem.clearText();
        mTvItemName.setText(itemSpecEntry.name);
        mTvItemDescription.setText(itemSpecEntry.desc);
        mTvCustomerName.setText(itemSpecEntry.customer != null? itemSpecEntry.customer.name : "");
        setLotNoLayoutVisibility(itemSpecEntry.customerViewEntry);
        setReceiveWhenLayoutVisibility(itemSpecEntry.customerViewEntry);
        setItemGoodsType(itemSpecEntry.customerViewEntry);
        setItemUnit(itemSpecEntry.unitsEntry);
        mPresenter.searchItemTitle(itemSpecEntry.customer.id);
    }

    private void setLotNoLayoutVisibility(CustomerViewEntry customerViewEntry) {
        mLlLotNo.setVisibility(customerViewEntry == null || customerViewEntry.showLotNoAtInventoryInitial ? View.VISIBLE : View.GONE);
    }

    private void setReceiveWhenLayoutVisibility(CustomerViewEntry customerViewEntry) {
        mLlReceiveWhen.setVisibility(customerViewEntry == null || customerViewEntry.showReceivedWhenAtInventoryInitial ? View.VISIBLE : View.GONE);
    }

    private void setItemUnit(List<UnitEntry> unitsEntry) {
        List<UnitEntry> unitEntries = Stream.of(Lists.ensureNotNull(unitsEntry)).sortBy(unitEntry -> !unitEntry.isBaseUnit).toList();
        mPresenter.setItemUnits(unitEntries);
        List<String> unitNames = Stream.of(unitEntries).map(UnitEntry::getName).toList();
        ArrayAdapter<String> spinnerAdapter = new ArrayAdapter<>(this,
                android.R.layout.simple_spinner_item, unitNames);
        spinnerAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        mSpinnerUnit.setAdapter(spinnerAdapter);
    }

    private void setItemGoodsType(CustomerViewEntry customerViewEntry) {
        if (customerViewEntry == null || customerViewEntry.captureGoodTypeOnReceivingTask) {
            ArrayAdapter<String> spinnerAdapter = new ArrayAdapter<>(this,
                    android.R.layout.simple_spinner_item, getGoodsTypes(customerViewEntry));
            spinnerAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
            mSpinnerGoodsType.setAdapter(spinnerAdapter);
            mLlGoodsType.setVisibility(View.VISIBLE);
        } else {
            mLlGoodsType.setVisibility(View.GONE);
        }
    }

    @Override
    public void searchItemSpecFailure(String errMsg) {
        mScannerItem.setText("");
        ToastUtil.showToast(errMsg);
    }

    @Override
    public void showItemSpecDialog(List<ItemSpecEntry> itemSpecEntries) {
        ItemSpecDialogFragment itemSpecDialogFragment = ItemSpecDialogFragment.getInstance();
        itemSpecDialogFragment.setItemSpecEntryList(itemSpecEntries);
        itemSpecDialogFragment.setItemSpecSelectedListener(this);
        itemSpecDialogFragment.show(getSupportFragmentManager(), ItemSpecDialogFragment.TAG);
    }

    @Override
    public void onItemSpecSelectedListener(ItemSpecEntry itemSpecEntry) {
        searchItemSpecSuccessful(itemSpecEntry);
    }

    @Override
    public void searchItemTitleSuccessful(List<OrganizationViewEntry> organizationViewEntries) {
        List<String> titleNames = Stream.of(organizationViewEntries).map(OrganizationViewEntry::getName).toList();
        ArrayAdapter<String> spinnerAdapter = new ArrayAdapter<>(this,
                android.R.layout.simple_spinner_item, titleNames);
        spinnerAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item);
        mSpinnerTitle.setAdapter(spinnerAdapter);
    }

    @Override
    public void searchLocationSuccessful(LocationEntry locationEntry) {
        setLocationAndLpView(locationEntry);
    }

    private void setLocationAndLpView(LocationEntry locationEntry) {
        mPresenter.saveLocation(locationEntry);
        mScannerLocation.setText(locationEntry.getName());
        if (isPickLocation(locationEntry)) {
            setQtyEdtFocus();
            mLlLp.setVisibility(View.GONE);
            mSpinnerFastAddMethod.setVisibility(View.GONE);
        } else {
            mScannerLp.setContentEdtFocus();
            mLlLp.setVisibility(View.VISIBLE);
            mSpinnerFastAddMethod.setVisibility(View.VISIBLE);
        }
    }

    @Override
    public void searchLocationFailure(String errMsg) {
        mScannerLocation.clearText();
        ToastUtil.showToast(errMsg);
    }

    @Override
    public void initialInventorySuccessful(InventoryInitialType inventoryInitialType) {
        if (inventoryInitialType == InventoryInitialType.BY_PALLET) {
            mScannerLp.resetEdt();
        } else if (inventoryInitialType == InventoryInitialType.BY_LOCATION) {
            mScannerLocation.resetEdt();
            mPresenter.saveLocation(null);
            mScannerLp.clearText();
        } else {
            mLlContent.setVisibility(View.GONE);
            clearView();
            mPresenter.clearData();
        }
        ToastUtil.showToast(getString(R.string.submit_success));
    }

    private boolean isPickLocation(LocationEntry locationEntry) {
        return locationEntry != null && locationEntry.type == LocationTypeEntry.PICK;
    }

    private List<String> getGoodsTypes(CustomerViewEntry customer) {
        if (customer != null && CollectionUtil.isNotNullOrEmpty(customer.allowedReceivingGoodsTypes)) {
            return customer.getAllowedReceivingGoodsTypes();
        }

        List<String> items = new ArrayList<>();
        items.add(Constant.GOODS_TYPE_GOOD);
        items.add(Constant.GOODS_TYPE_DAMAGE);
        items.add(Constant.GOODS_TYPE_NEAR_EXPIRY);
        items.add(Constant.GOODS_TYPE_EXPIRED);
        items.add(Constant.GOODS_TYPE_CONTAIN_DAMAGE);
        items.add(Constant.GOODS_TYPE_ON_HOLD);
        items.add(Constant.GOODS_TYPE_REWORK_NEEDED);
        items.add(Constant.GOODS_TYPE_QC);
        items.add(Constant.GOODS_TYPE_FDA);
        items.add(Constant.GOODS_TYPE_RETURN);
        items.add(Constant.GOODS_TYPE_B_GRADE);
        items.add(Constant.GOODS_TYPE_C_GRADE);
        return items;
    }

    private void setQtyEdtFocus() {
        mQtyEdit.setFocusable(true);
        mQtyEdit.setFocusableInTouchMode(true);
        mQtyEdit.requestFocus();
        if (mQtyEdit.getText() != null && !TextUtils.isEmpty(mQtyEdit.getText().toString())) {
            mQtyEdit.selectAll();
        }
    }

    private void clearView() {
        mTvReceiveWhen.setText("");
        mScannerLotNo.clearText();
        mScannerLp.clearText();
        mScannerLocation.clearText();
        mQtyEdit.setText("");
    }

    private void bindView() {
        mToolbar = findViewById(R.id.toolbar);
        mScannerItem = findViewById(R.id.scanner_item);
        mLlContent = findViewById(R.id.ll_content);
        mTvItemName = findViewById(R.id.tv_item_name);
        mTvCustomerName = findViewById(R.id.tv_customer_name);
        mTvItemDescription = findViewById(R.id.tv_item_description);
        mSpinnerTitle = findViewById(R.id.spinner_title);
        mLlGoodsType = findViewById(R.id.ll_goods_type);
        mSpinnerGoodsType = findViewById(R.id.spinner_goods_type);
        mLlLotNo = findViewById(R.id.ll_lot_no);
        mScannerLotNo = findViewById(R.id.scanner_lot_no);
        mLlReceiveWhen = findViewById(R.id.ll_receive_when);
        mTvReceiveWhen = findViewById(R.id.tv_receive_when);
        mScannerLocation = findViewById(R.id.scanner_location);
        mLlLp = findViewById(R.id.ll_lp);
        mScannerLp = findViewById(R.id.scanner_lp);
        mQtyEdit = findViewById(R.id.qty_edit);
        mSpinnerUnit = findViewById(R.id.spinner_unit);
        mSpinnerFastAddMethod = findViewById(R.id.spinner_fast_add_method);
        mBtnSubmit = findViewById(R.id.btn_submit);
        mBtnFastAdd = findViewById(R.id.btn_fast_add);
        findViewById(R.id.btn_select_location).setOnClickListener(this::onClick);
        findViewById(R.id.btn_print).setOnClickListener(this::onClick);
        mTvReceiveWhen.setOnClickListener(this::onClick);
    }
}
