package com.lt.linc.home.more.inventoryconsolidation.contract;

import com.linc.platform.baseapp.model.LocationEntry;
import com.linc.platform.core.ProgressView;
import com.linc.platform.foundation.model.ItemSpecEntry;
import com.linc.platform.inventory.model.InventoryEntry;

import java.util.List;

/**
 * Author: Kyle
 * Time: 2021-2-25
 * Description:Inventory Consolidation -- from lower to higher uom
 */
public interface ScanInventoryContract {
    interface View extends ProgressView {
        void showToastMessage(String message);

        void showItemInfoLayout();

        void showAdapter(List<InventoryEntry> items, boolean canLoadMore);

        void showItemInfo(String name, String desc);

        void showSelectedItemSpecIdDialog(List<ItemSpecEntry> mItems);

        void showSelectedItemBySearchLocationIdDialog(List<ItemSpecEntry> mItems, List<InventoryEntry> inventoryEntries);

        void showSelectedItemValidation(List<String> items);

        void showNotMore();

        void loadMoreSucceed(List<InventoryEntry> items);

        void loadFailed();

        void onMultiLocationFound(List<LocationEntry> locationEntries);
    }


    interface Presenter {

        void onGetInventory(String content, boolean locationSegmentedScanning);

        void onGetInventoryByItemSpecId(ItemSpecEntry item);

        void onGetInventoryByLocationId(String locationId);

        void onSelectedItemBySearchLocationId(String id, List<InventoryEntry> inventoryEntries);

        void onSelectedInventoryEntry(InventoryEntry inventoryEntry);

        void onSelectedValidateWay(String item);

        void onSearchInventoryByItemSpecId();

        void onSearchInventoryByItemSpecIdLoadMore();

        void searchLocationWithBulk(String aisle, String bay);
    }
}
