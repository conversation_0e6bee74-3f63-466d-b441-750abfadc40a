package com.lt.linc.home.more.hospital.dialog

import android.os.Bundle
import com.lt.linc.common.mvi.ReactiveDialogFragment
import com.lt.linc.common.mvi.ReactiveViewScope
import com.lt.linc.databinding.DialogLocaionActiveBinding
import com.lt.linc.home.more.hospital.model.HospitalInfoUiState

class LocationActiveDialog : ReactiveDialogFragment<HospitalInfoViewModel, HospitalInfoUiState, DialogLocaionActiveBinding>() {
    override fun createViewModel(): HospitalInfoViewModel {
        TODO("Not yet implemented")
    }

    override fun initView(savedInstanceState: Bundle?) {
        TODO("Not yet implemented")
    }

    override fun ReactiveViewScope.subscribeToUiState() {
        TODO("Not yet implemented")
    }
}