package com.lt.linc.home.timesheet

import androidx.lifecycle.MutableLiveData
import android.text.TextUtils
import com.linc.platform.common.handler.FailedHandler
import com.linc.platform.common.handler.SuccessHandler
import com.linc.platform.http.HttpService
import com.linc.platform.idm.api.IdmApi
import com.linc.platform.idm.model.*
import com.linc.platform.infoclock.model.InfoClockEmployeeViewEntry
import com.linc.platform.publicdata.PublicDataApi
import com.linc.platform.publicdata.model.PublicDataReqEntry
import com.linc.platform.utils.CollectionUtil
import com.linc.platform.utils.Lists
import com.linc.platform.utils.ToastUtil
import com.lt.linc.R
import com.lt.linc.common.mvvm.BaseVM

/**
 * @Description
 * <AUTHOR>
 * @Date 2022/4/22
 */
class PunchInViewModel : BaseVM() {

    val loginSucEvent by lazy { MutableLiveData<UserViewEntry>() }
    private val publicDataApi: PublicDataApi by lazy { HttpService.createService(PublicDataApi::class.java) }

    var loginResultEntry: LoginResultEntry? = null

    fun checkWiseFacilityToLogin(employeeViewEntry: InfoClockEmployeeViewEntry) {
        val reqEntry = PublicDataReqEntry(arrayOf("facilityCodes"))
        executeWithLoading(publicDataApi.getPublicData(reqEntry), SuccessHandler {
            if (!it.facilityCodes.isNullOrEmpty() && it.facilityCodes!!.contains(employeeViewEntry.facilityCode)) {
                loginByQrCode(employeeViewEntry)
            } else {
                showToastByResId(R.string.timesheet_not_found_facility)
            }
        }, FailedHandler { showToastByResId(R.string.timesheet_not_found_facility) })
    }

    private fun loginByQrCode(infoClockEmployeeViewEntry: InfoClockEmployeeViewEntry) {
        val idmApi = HttpService.createService(IdmApi::class.java)
        val loginByQrCodeEntry = LoginByQrCodeEntry()
        loginByQrCodeEntry.employeeResult = infoClockEmployeeViewEntry
        loginByQrCodeEntry.returnUserPermissions = Lists.newArrayList(PermissionCategory.ANDROID)

        executeWithLoading(idmApi.loginByQrCode(loginByQrCodeEntry)) {
            if (it.success) {
                val userDefaultCompanyId = getUserDefaultCompanyId(it.userViewEntry)
                if (userDefaultCompanyId.isNullOrEmpty()) {
                    showToastByResId(R.string.hint_please_assign_facility_company_for_user)
                    return@executeWithLoading
                }
                loadUserInfo(it)
            } else {
                ToastUtil.showErrorToast(it.errorMessage)
            }
        }
    }

    private fun loadUserInfo(loginResultEntry: LoginResultEntry) {
        this.loginResultEntry = loginResultEntry
        HttpService.updateToken(loginResultEntry.oAuthToken)
        val idmApi = HttpService.createService(IdmApi::class.java)
        executeWithLoading(idmApi.getUserInfo(loginResultEntry.idmUserId)) {
            it?.run {
                if (assignedCompanyFacilities.isNullOrEmpty()) {
                    showToastByResId(R.string.hint_please_assign_facility_company_for_user)
                    return@executeWithLoading
                }
                loginSucEvent.postValue(it)
            } ?: showToastByResId(R.string.msg_user_disable_please_contact_manager)
        }
    }

    private fun getUserDefaultCompanyId(userViewEntry: UserViewEntry): String? {
        val defaultCompanyId = if (userViewEntry.defaultCompanyFacility == null) "" else userViewEntry.defaultCompanyFacility.companyId
        return if (TextUtils.isEmpty(defaultCompanyId)) {
            if (CollectionUtil.isNullOrEmpty(userViewEntry.assignedCompanyFacilities)) ""
            else userViewEntry.assignedCompanyFacilities[0].companyId
        } else defaultCompanyId
    }
}