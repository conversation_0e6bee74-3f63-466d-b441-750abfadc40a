package com.lt.linc.lumper.tasklist

import com.linc.platform.common.task.TaskStatusEntry
import com.linc.platform.generaltask.PagingResultParamEntry
import com.linc.platform.load.model.LoadTaskViewEntry
import com.linc.platform.lumper.model.LumperTaskViewEntry
import com.lt.linc.common.mvi.ReactiveDataState
import com.lt.linc.common.mvi.ReactiveUiState

/**
 * <AUTHOR> on 2024/9/3
 */
data class LumperTaskListDateState(
    val userId: String,
    val currentView: TaskStatusEntry = TaskStatusEntry.NEW,
    val taskList: List<LumperTaskViewEntry> = listOf(),
    val pagingResult: PagingResultParamEntry? = null) : ReactiveDataState

data class LumperTaskListUiState(
    val lumperTaskList: List<LumperTaskViewEntry> = listOf(),
    val loadMoreEnd: Boolean = false,
    val newAmount: Long = 0L,
    val progressAmount: Long = 0L,
    val doneAmount: Long = -1L) : ReactiveUiState