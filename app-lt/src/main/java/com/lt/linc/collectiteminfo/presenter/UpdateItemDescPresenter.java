package com.lt.linc.collectiteminfo.presenter;

import android.text.TextUtils;

import com.annimon.stream.Stream;
import com.linc.platform.common.apidal.BaseApiDal;
import com.linc.platform.foundation.api.ItemSpecAPI;
import com.linc.platform.foundation.model.GroupTypeEntry;
import com.linc.platform.foundation.model.ItemSpecUpdateEntry;
import com.linc.platform.foundation.model.UpdateFromEntry;
import com.linc.platform.http.HttpService;
import com.linc.platform.utils.ResUtil;
import com.lt.linc.R;
import com.lt.linc.collectiteminfo.CollectItemInfoContract;
import com.lt.linc.collectiteminfo.model.ItemInfoViewModel;

/**
 * Author: wujf
 * Time: 2020/12/9
 * Description:
 */
public class UpdateItemDescPresenter extends BaseApiDal implements CollectItemInfoContract.UpdateItemDescPresenter {
    private ItemSpecAPI mItemSpecApi;
    private CollectItemInfoContract.UpdateItemDescView mView;
    private ItemInfoViewModel mViewModel;

    public UpdateItemDescPresenter(CollectItemInfoContract.UpdateItemDescView view, ItemInfoViewModel viewModel) {
        this.mView = view;
        this.mViewModel = viewModel;
        mItemSpecApi = HttpService.createService(ItemSpecAPI.class);
    }

    @Override
    public void onStart() {
        initItemLasterDesc();
    }

    private void initItemLasterDesc() {
        mView.showItemLasterDesc(mViewModel.getItemSpecDesc());
    }

    @Override
    public void onUpdate(String description) {
        if (TextUtils.isEmpty(description)) {
            mView.showToasMsg(ResUtil.getString(R.string.hint_collect_item_info_scan_or_enter_desc));
            return;
        }
        ItemSpecUpdateEntry updateEntry = new ItemSpecUpdateEntry();
        updateEntry.tags = Stream.of(mViewModel.getItemSpecEntry().tags)
                .map(tag -> GroupTypeEntry.valueOf(tag.toUpperCase()))
                .toList();
        updateEntry.name = mViewModel.getItemSpecEntry().name;
        updateEntry.hasSerialNumber = mViewModel.getItemSpecEntry().hasSerialNumber;
        updateEntry.updateFrom = UpdateFromEntry.ANDROID;
        updateEntry.desc = description;
        updateEntry.bundle = mViewModel.getItemSpecEntry().bundle;
        updateEntry.customerId = mViewModel.getItemSpecEntry().customerId;
        asyncExecute(mItemSpecApi.update(mViewModel.getItemSpecId(), updateEntry), mView, resoponse -> {
            mView.showUpdateSucceed(description);
            mView.showItemLasterDesc(description);
            mViewModel.getItemSpecEntry().desc = description;
            mViewModel.setNeedRefreshItemInfo(true);
        });
    }
}
