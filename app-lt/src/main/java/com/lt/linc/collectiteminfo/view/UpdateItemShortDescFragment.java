package com.lt.linc.collectiteminfo.view;

import androidx.lifecycle.ViewModelProviders;
import android.os.Bundle;
import androidx.annotation.Nullable;
import androidx.appcompat.widget.AppCompatTextView;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import com.customer.widget.QuickScanner;
import com.customer.widget.core.LincBaseFragment;
import com.linc.platform.utils.ResUtil;
import com.linc.platform.utils.ToastUtil;
import com.lt.linc.R;
import com.lt.linc.collectiteminfo.CollectItemInfoContract;
import com.lt.linc.collectiteminfo.model.ItemInfoViewModel;
import com.lt.linc.collectiteminfo.presenter.UpdateItemShortDescPresenter;

/**
 * Author: wujf
 * Time: 2020/12/9
 * Description:
 */
public class UpdateItemShortDescFragment extends LincBaseFragment implements CollectItemInfoContract.UpdateItemDescView {


    private AppCompatTextView itemDescTv;
    private QuickScanner scannerLayout;
    private AppCompatTextView itemUpdateDescTv;

    private UpdateItemShortDescPresenter mPresenter;

    @Override
    protected int getLayoutId() {
        return R.layout.fragment_collect_item_info_update_short_desc;
    }

    @Override
    protected void initView() {
        bindView();
    }

    public static UpdateItemShortDescFragment newInstance() {

        Bundle args = new Bundle();
        UpdateItemShortDescFragment fragment = new UpdateItemShortDescFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onViewCreated(View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        initListener();
        mPresenter = new UpdateItemShortDescPresenter(this, ViewModelProviders.of(getActivity()).get(ItemInfoViewModel.class));
        mPresenter.onStart();
    }

    private void initListener() {
        scannerLayout.setScanEvent((view,data) -> {
            scannerLayout.setText(data);
            mPresenter.onUpdate(data);
        });

    }

    @Override
    public void showToasMsg(String message) {
        ToastUtil.showErrorToast(message);
    }

    @Override
    public void showItemLasterDesc(String desc) {
        itemDescTv.setText(desc);
    }

    @Override
    public void showUpdateSucceed(String desc) {
        itemUpdateDescTv.setText(String.format(ResUtil.getString(R.string.label_collect_item_info_updated_short_desc_as), desc));
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container, Bundle savedInstanceState) {
        View rootView = super.onCreateView(inflater, container, savedInstanceState);
        return rootView;
    }

    private void onViewClicked() {
        mPresenter.onUpdate(scannerLayout.getText());
    }

    private void bindView() {
        itemDescTv = findViewById(R.id.item_desc_tv);
        scannerLayout = findViewById(R.id.scanner_layout);
        itemUpdateDescTv = findViewById(R.id.item_update_desc_tv);
        findViewById(R.id.update_btn).setOnClickListener(v -> onViewClicked());
    }
}
