package com.lt.linc.home_v1.profile

import com.linc.platform.foundation.model.organization.common.facility.FacilityEntry
import com.linc.platform.idm.model.CompanyFacility
import com.linc.platform.idm.model.UserCompanyEntry
import com.linc.platform.idm.model.UserViewEntry
import com.lt.linc.common.mvi.ReactiveDataState
import com.lt.linc.common.mvi.ReactiveUiState
import com.lt.linc.common.mvi.UiEvent

data class PasswordState(
    val idmUserId: String
) : ReactiveDataState


data class PasswordUiState(
    val isCurrentShow: Boolean = true, val isNewShow: Boolean = true, val isConfirmShow: Boolean = true
) : ReactiveUiState
