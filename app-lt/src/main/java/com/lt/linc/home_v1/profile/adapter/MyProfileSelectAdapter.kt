package com.lt.linc.home_v1.profile.adapter

import androidx.annotation.StringRes
import android.widget.LinearLayout
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.BaseViewHolder
import com.linc.platform.common.task.TaskTypeEntry
import com.linc.platform.taskcenter.model.TaskCenterEntry
import com.linc.platform.utils.Logger
import com.lt.linc.R

/**
 * <AUTHOR> on 2022/9/26
 */
class MyProfileSelectAdapter(private var choosePosition: Int = -1) :
    BaseQuickAdapter<String, BaseViewHolder>(R.layout.item_setting_select) {


    override fun convert(helper: BaseViewHolder, item: String) {
        helper.setText(R.id.tv_setting_select, item).setImageResource(
            R.id.iv_setting_select,
            if (helper.layoutPosition == choosePosition) R.drawable.ic_baseline_radio_button_checked_24 else R.drawable.ic_baseline_radio_button_unchecked_24)
        helper.getView<LinearLayout>(R.id.ll_select).isSelected = helper.layoutPosition == choosePosition
    }


    fun setPosition(position: Int) {
        choosePosition = position
        notifyDataSetChanged()
    }

    fun getChoosePosition() = choosePosition

}