package com.lt.linc.transload_v2.loading.loadlist;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.appcompat.widget.Toolbar;
import android.text.TextUtils;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.Button;
import android.widget.RadioButton;

import com.customer.widget.GeneralAlertDialog;
import com.customer.widget.core.LincBaseActivity;
import com.linc.platform.common.step.StepBaseEntry;
import com.linc.platform.foundation.model.questionnaire.CustomerQuestionnaireEntry;
import com.linc.platform.generaltask.model.GeneralTaskViewEntry;
import com.linc.platform.transload_v2.transloading.model.ItemTransloadLoadInfoEntry;
import com.linc.platform.transload_v2.transloading.model.TransloadLoadTaskView;
import com.linc.platform.transload_v2.transloading.model.TransloadOutboundWeight;
import com.linc.platform.utils.ConfigurationMapUtil;
import com.linc.platform.utils.ToastUtil;
import com.lt.linc.R;
import com.lt.linc.home_v1.task.taskquestionnaire.TaskQuestionnaireActivity;
import com.lt.linc.transload_v2.loading.adapter.LoadsAdapter;
import com.lt.linc.transload_v2.loading.loadorders.TransLoadOrdersActivity;
import com.lt.linc.transload_v2.loading.loadorders.muldestinations.TransloadLoadingWithMulPalletActivity;

import java.util.ArrayList;
import java.util.List;

/**
 * Author: wujf
 * Time: 2021-1-4
 * Description:load list
 */
public class TransloadLoadsActivity extends LincBaseActivity implements TransloadLoadsContract.View {
    public static final String TAG = TransloadLoadsActivity.class.getSimpleName();
    public static final String EXTRA_LOAD_TASK_VIEW = "load_task_view";
    public static final String EXTRA_COMPANY_ID = "company_Id";
    private Toolbar toolbar;
    private SwipeRefreshLayout refreshLayout;
    private RecyclerView recyclerView;
    private RadioButton progressBtn;
    private RadioButton newBtn;
    private RadioButton doneBtn;
    private Button submitBtn;
    private TransloadLoadsPresenter mPresenter;
    private LoadsAdapter mLoadsAdapter;
    private String mCompanyId = "";

    private boolean needRefreshLoads = false;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_transload_loading_list);
        bindView();
        initWidget();
        initData();
        initListener();
    }

    private void initData() {
        Intent intent = getIntent();
        mCompanyId = intent.getStringExtra(EXTRA_COMPANY_ID);
        StepBaseEntry stepBaseEntry = (StepBaseEntry) intent.getSerializableExtra(StepBaseEntry.TAG);
        TransloadLoadTaskView transloadLoadTaskView = (TransloadLoadTaskView) intent.getSerializableExtra(EXTRA_LOAD_TASK_VIEW);
        boolean isStepBaseEntryNull = stepBaseEntry == null;
        initToolBar(toolbar, isStepBaseEntryNull ? "" : stepBaseEntry.taskId);
        mPresenter = new TransloadLoadsPresenter(this, transloadLoadTaskView, stepBaseEntry,getIdmUserId());
        mPresenter.onStart();
        mPresenter.onSetSubmit();
    }

    private void initListener() {
        initSwipeRefresh();
        mLoadsAdapter.setOnItemClickListener((adapter, view, position) -> {
            ItemTransloadLoadInfoEntry item = mLoadsAdapter.getItem(position);
            mPresenter.onOpenOdersActivity(item.id);

        });
        mLoadsAdapter.setOnItemChildClickListener((adapter, view, position) -> {
            switch (view.getId()) {
                case R.id.reopen_btn:
                    mPresenter.onReOpenLoad(mLoadsAdapter.getItem(position).id);
                    break;
            }
        });
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        menu.add(R.string.action_help);
        menu.add(R.string.text_force_close_dock);
        return super.onCreateOptionsMenu(menu);
    }

    @Override
    public boolean onOptionsItemSelected(MenuItem item) {
        if (TextUtils.equals(item.getTitle(), getString(R.string.action_help))) {
            mPresenter.getAndOpenHelpPage(this, ConfigurationMapUtil.PAGE_KEY_TRANSLOAD_LOAD_TASK_LOAD_DETAIL, getFacilityId());
        }else if (TextUtils.equals(item.getTitle(), getString(R.string.text_force_close_dock))) {
            showForceReleaseDockDialog();
        }
        return super.onOptionsItemSelected(item);
    }

    @Override
    public void showSelectProgressBtn() {
        progressBtn.setChecked(true);
        mPresenter.onUpdateAdapterByLoadStatus(TransloadLoadsPresenter.LOAD_STATUS.PROGRESS_STATUS);
    }

    @Override
    public void openOrdersActivity(TransloadLoadTaskView transloadLoadTaskView, StepBaseEntry stepBaseEntry, String loadId, TransloadOutboundWeight transloadOutboundWeight) {
        if(transloadLoadTaskView.transloadAllowMultiDCInOneLoad){
            TransloadLoadingWithMulPalletActivity.start(this,transloadLoadTaskView, stepBaseEntry, loadId);
        }else {
            TransLoadOrdersActivity.start(this, transloadLoadTaskView, stepBaseEntry, loadId, transloadOutboundWeight);
        }
    }

    @Override
    public void showSubmitBtn(boolean isVisible, String content) {
        submitBtn.setVisibility(isVisible ? View.VISIBLE : View.GONE);
        submitBtn.setText(content);
    }

    private void initWidget() {
        showStatusCount("0", "0", "0");
        initRv();
    }

    private void initRv() {
        mLoadsAdapter = new LoadsAdapter(R.layout.item_transload_load_info, new ArrayList<>());
        recyclerView.setLayoutManager(new LinearLayoutManager(this));
        recyclerView.setAdapter(mLoadsAdapter);
    }

    private void initSwipeRefresh() {
        refreshLayout.setColorSchemeResources(android.R.color.holo_green_light,
                android.R.color.holo_orange_light, android.R.color.holo_red_light);
        refreshLayout.setOnRefreshListener(() -> {
            mPresenter.onRefreshLoadsByTaskId();
            mPresenter.onRefreshTaskData();
        });
    }

    @Override
    protected void onResume() {
        super.onResume();
        if (needRefreshLoads) {
            mPresenter.onRefreshLoadsWithoutProgress();
        }
        needRefreshLoads = true;
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (null != mPresenter) {
            mPresenter.onDestroy();
            mPresenter = null;
        }
    }

    @Override
    public void showStatusCount(String progressCount, String newCount, String doneCount) {
        progressBtn.setText(progressCount);
        newBtn.setText(newCount);
        doneBtn.setText(doneCount);
    }

    @Override
    public void showAdapter(List<ItemTransloadLoadInfoEntry> items) {
        mLoadsAdapter.setNewData(items);
    }

    public static void start(Context context, String companyId, TransloadLoadTaskView transloadLoadTaskView, StepBaseEntry stepBaseEntry) {
        Intent starter = new Intent(context, TransloadLoadsActivity.class);
        starter.putExtra(EXTRA_COMPANY_ID, companyId);
        starter.putExtra(EXTRA_LOAD_TASK_VIEW, transloadLoadTaskView);
        starter.putExtra(StepBaseEntry.TAG, stepBaseEntry);
        context.startActivity(starter);
    }


    @Override
    public void showToastMessage(String message) {
        ToastUtil.showErrorToast(message);
    }

    private void onViewClicked(View view) {
        switch (view.getId()) {
            case R.id.task_in_progress_status_btn:
                mPresenter.onUpdateAdapterByLoadStatus(TransloadLoadsPresenter.LOAD_STATUS.PROGRESS_STATUS);
                break;
            case R.id.task_new_status_btn:
                mPresenter.onUpdateAdapterByLoadStatus(TransloadLoadsPresenter.LOAD_STATUS.NEW_STATUS);
                break;
            case R.id.task_new_done_status_btn:
                mPresenter.onUpdateAdapterByLoadStatus(TransloadLoadsPresenter.LOAD_STATUS.DONE_STATUS);
                break;
            case R.id.submit_btn:
                mPresenter.onSubmit(mCompanyId);
                break;
        }
    }

    @Override
    public void closeStepSucceed() {
        showToastMessage(getString(R.string.text_step_closed));
        finish();
    }

    @Override
    public void showCloseTaskConfirmDialog() {
        String contentMessage = getString(R.string.msg_close_transload_task_confirm);

        GeneralAlertDialog.createAlertDialog(this,
                contentMessage,
                (dialog, which) -> dialog.dismiss(),
                (dialog, which) -> mPresenter.preCloseStep()).show();
    }

    @Override
    public void showForceCloseStepDialog(String responseErrorMessage) {
        if(isFinishing()){
            return;
        }
        GeneralAlertDialog.createYesNoAlertDialog(this,
                getString(R.string.query_force_close),
                TextUtils.isEmpty(responseErrorMessage) ? "" : responseErrorMessage,
                (dialogInterface, i) -> {

                }, (dialogInterface, i) -> {
                    mPresenter.onForceCloseStep(responseErrorMessage);
                }).show();
    }

    @Override
    public void showProgress(boolean show) {
        super.showProgress(show);
        if (isFinishing()||refreshLayout == null) return;
        if (refreshLayout.isRefreshing()) {
            refreshLayout.setRefreshing(false);
        }
    }

    @Override
    public void showConfirmDestinationDialog() {
        GeneralAlertDialog.createAlertDialog(this,
                getString(R.string.msg_close_transload_task_destination_confirm),
                (dialogInterface, i) -> {

                }, (dialogInterface, i) -> {
                    mPresenter.closeStep();
                }).show();
    }

    @Override
    public void startAnswerTaskQuestionnaire(GeneralTaskViewEntry taskViewEntry, CustomerQuestionnaireEntry customerQuestionnaire) {
        TaskQuestionnaireActivity.startActivity(this, taskViewEntry, customerQuestionnaire, TaskQuestionnaireActivity.TASK_QUESTIONNAIRE_RESULT);
    }

    private void showForceReleaseDockDialog() {
        String titleMessage = getString(R.string.label_force_close);
        String contentMessage =getString(R.string.force_release_dock);

        GeneralAlertDialog.createAlertDialog(this,
                titleMessage,
                contentMessage,
                (dialog, which) -> dialog.dismiss(),
                (dialog, which) -> mPresenter.doForceReleaseDock()).show();
    }

    private void bindView() {
        toolbar = findViewById(R.id.toolbar);
        refreshLayout = findViewById(R.id.refresh_layout);
        recyclerView = findViewById(R.id.recycler_view);
        progressBtn = findViewById(R.id.task_in_progress_status_btn);
        newBtn = findViewById(R.id.task_new_status_btn);
        doneBtn = findViewById(R.id.task_new_done_status_btn);
        submitBtn = findViewById(R.id.submit_btn);
        progressBtn.setOnClickListener(this::onViewClicked);
        newBtn.setOnClickListener(this::onViewClicked);
        doneBtn.setOnClickListener(this::onViewClicked);
        submitBtn.setOnClickListener(this::onViewClicked);
    }
}

