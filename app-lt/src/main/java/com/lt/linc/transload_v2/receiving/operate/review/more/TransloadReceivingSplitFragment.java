package com.lt.linc.transload_v2.receiving.operate.review.more;

import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;

import com.linc.platform.common.step.StepBaseEntry;
import com.linc.platform.transload_v2.receiving.model.TransloadReceivingPalletDetailEntity;
import com.lt.linc.R;
import com.lt.linc.common.mvvm.BaseVMFragment;
import com.lt.linc.transload_v2.receiving.operate.review.more.model.TransloadReceivingOperationPalletType;
import com.lt.linc.transload_v2.receiving.operate.review.more.model.TransloadReceivingSplitPalletTo;
import com.lt.linc.transload_v2.receiving.operate.review.more.view.ITransloadReceivingSplitPalletListener;
import com.lt.linc.transload_v2.receiving.operate.review.more.view.TransloadReceivingSplitToPalletView;
import com.lt.linc.transload_v2.receiving.operate.review.more.viewmodel.TransloadReceivingSplitVM;

public class TransloadReceivingSplitFragment extends BaseVMFragment<TransloadReceivingSplitVM> {

    public static final String ARG_STEP_ENTRY = "arg_step_entry";
    public static final String ARG_PALLET_DETAIL = "arg_pallet_detail";
    public static final String ARG_SHOW_OPEN_BUTTON = "arg_show_open_button";

    private TransloadReceivingSplitToPalletView splitPalletView;

    private ImageView openIv;

    StepBaseEntry stepBaseEntry;
    TransloadReceivingPalletDetailEntity palletDetailEntity;
    boolean showOpenSplitMergeButton;

    public static TransloadReceivingSplitFragment newInstance(TransloadReceivingPalletDetailEntity palletDetailEntity, StepBaseEntry stepBaseEntry,
                                                              boolean showOpenSplitMergeButton) {
        Bundle args = new Bundle();
        args.putSerializable(ARG_PALLET_DETAIL, palletDetailEntity);
        args.putSerializable(ARG_STEP_ENTRY, stepBaseEntry);
        args.putBoolean(ARG_SHOW_OPEN_BUTTON, showOpenSplitMergeButton);
        TransloadReceivingSplitFragment fragment = new TransloadReceivingSplitFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Bundle arguments = getArguments();
        stepBaseEntry = (StepBaseEntry) arguments.getSerializable(ARG_STEP_ENTRY);
        palletDetailEntity = (TransloadReceivingPalletDetailEntity) arguments.getSerializable(ARG_PALLET_DETAIL);
        showOpenSplitMergeButton = arguments.getBoolean(ARG_SHOW_OPEN_BUTTON);
    }

    @Override
    protected TransloadReceivingSplitVM createViewModel() {
        return new TransloadReceivingSplitVM(stepBaseEntry);
    }

    @Override
    protected void initView() {
        super.initView();
        bindView();
    }

    @Override
    protected int getLayoutId() {
        return R.layout.fragment_transload_receiving_split_pallet;
    }

    public void onViewCreated(View view, Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        openIv.setVisibility(showOpenSplitMergeButton?View.VISIBLE : View.GONE);
        initLiveDataEvent();
        initData();
    }

    private void initLiveDataEvent() {
        splitPalletView.setSplitPalletListener(
                new ITransloadReceivingSplitPalletListener() {
                    @Override
                    public void selectSplitTo(TransloadReceivingSplitPalletTo splitTo) {
                        viewModel.setSplitToPallet(splitTo);
                    }

                    @Override
                    public void onScanned(String palletNo) {
                        viewModel.onScannedCarton(palletNo);
                    }
                }
        );
    }

    private void initData(){
        viewModel.setSourcePalletData(palletDetailEntity);
        splitPalletView.setData(palletDetailEntity);
    }

    private void openSplitActivity(){
        TransloadReceivingSplitMergeActivity.start(this, palletDetailEntity, stepBaseEntry, TransloadReceivingOperationPalletType.SPLIT);
    }

    private void printPallet() {
        viewModel.printLabel(getIdmUserId(), getFacilityName());
    }


    private void bindView() {
        splitPalletView = findViewById(R.id.split_pallet_view);
        openIv = findViewById(R.id.open_split_merge_iv);
        findViewById(R.id.open_split_merge_iv).setOnClickListener(v -> openSplitActivity());
        findViewById(R.id.btn_print_label).setOnClickListener(v -> printPallet());
    }
}
