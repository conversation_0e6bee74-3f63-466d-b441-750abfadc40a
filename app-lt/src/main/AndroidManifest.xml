<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.lt.linc">
    <!--
         The ACCESS_COARSE/FINE_LOCATION permissions are not required to use
         Google Maps Android API v2, but you must specify either coarse or fine
         location permissions for the 'MyLocation' functionality.
    -->
    <!-- To auto-complete the email text field in the login form with the user's emails -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" /><!-- 悬浮窗权限 -->
    <uses-permission android:name="android.permission.SYSTEM_OVERLAY_WINDOW" /><!-- 悬浮窗权限 -->

    <uses-feature android:name="android.hardware.camera.autofocus" />

    <uses-permission android:name="android.permission.CAMERA" /> <!-- 连接网络权限，用于执行云端语音能力 -->
    <uses-permission android:name="android.permission.RECORD_AUDIO" /> <!-- 读取网络信息状态 -->
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" /> <!-- 获取当前wifi状态 -->
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" /> <!-- 允许程序改变网络连接状态 -->
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
    <uses-permission android:name="android.permission.CHANGE_NETWORK_STATE" /> <!-- 读取手机信息权限 -->
    <uses-permission android:name="android.permission.READ_PHONE_STATE" /> <!-- 读取联系人权限，上传联系人需要用到此权限 -->
    <uses-permission android:name="android.permission.READ_CONTACTS" /> <!-- 外存储写权限，构建语法需要用到此权限 -->
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" /> <!-- 配置权限，用来记录应用配置信息 -->
    <uses-permission android:name="android.permission.WRITE_SETTINGS" /> <!-- 手机定位信息，用来为语义等功能提供定位，提供更精准的服务 -->
    <!-- 定位信息是敏感信息，可通过Setting.setLocationEnable(false)关闭定位请求 -->
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" /> <!-- 允许解锁屏幕 -->
    <uses-permission android:name="android.permission.DISABLE_KEYGUARD" />
    <uses-permission android:name="android.permission.READ_LOGS" />
    <uses-permission android:name="android.permission.VIBRATE" /> <!-- 震动 -->

    <uses-permission android:name="android.permission.BLUETOOTH" />
    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />

    <!-- android 9.0上使用前台服务，需要添加权限 -->
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />

    <application
        android:name=".LtApplication"
        android:allowBackup="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:largeHeap="true"
        android:requestLegacyExternalStorage="true"
        android:supportsRtl="true"
        android:theme="@style/appThemeStyle"
        tools:replace="android:supportsRtl">
        <activity
            android:name=".home_v1.SplashActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <!-- 适配全面屏 -->
        <meta-data
            android:name="android.max_aspect"
            android:value="2.3" />

        <activity android:name=".common.CalendarDateSelectionActivity" />
        <activity
            android:name=".approval_rating.ApprovalActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".home.more.inventoryconsolidation.view.InventoryConsolidationActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".toolset.print.carton.PrintSSCCLabelActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".inventorymovement.batchmovement.BatchMovementActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="stateAlwaysHidden|adjustPan" />
        <activity
            android:name=".inventorymovement.choosemovement.ChooseMovementWayActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="stateAlwaysHidden|adjustPan" /> <!-- <activity -->
        <!-- android:name=".transload_v2.receiving.operate.new_pallet.add_item.TransloadReceivingOperateNewPalletAddItemActivity" -->
        <!-- android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" /> -->
        <activity
            android:name=".transload_v2.receiving.operate.TransloadReceivingOperateActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="stateAlwaysHidden|adjustPan" />
        <activity
            android:name=".transload_v2.receiving.operate.review.more.TransloadReceivingEditPalletActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="stateAlwaysHidden|adjustPan" />
        <activity
            android:name=".transload_v2.receiving.operate.review.more.TransloadReceivingAddPalletActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="stateAlwaysHidden|adjustPan" />
        <activity
            android:name=".transload_v2.receiving.operate.review.more.TransloadReceivingSplitMergeActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="stateAlwaysHidden|adjustPan" />
        <activity
            android:name=".transload_v2.receiving.operate.new_pallet_by_carton.addseal.TransloadReceivingAddSealNoActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".transload_v2.receiving.operate.existing_pallet.exception.TransloadReceivingScanExceptionActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="stateAlwaysHidden|adjustPan" />
        <activity
            android:name=".transload_v2.receiving.operate.new_pallet_by_carton.exception.TransloadReceivingScanMulPalletExceptionActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="stateAlwaysHidden|adjustPan" />
        <activity
            android:name=".transload_v2.receiving.TransloadReceivingStepActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".transload_v2.receiving.operate.last_mile.exception.TransloadReceivingLastMileExceptionActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="stateAlwaysHidden|adjustPan" />
        <!--
             The API key for Google Maps-based APIs is defined as a string resource.
             (See the file "res/values/google_maps_api.xml").
             Note that the API key is linked to the encryption key used to sign the APK.
             You need a different API key for each encryption key, including the release key that is used to
             sign the APK for publishing.
             You can define the keys for the debug and release targets in src/debug/ and src/release/.
        -->
        <meta-data
            android:name="com.google.android.geo.API_KEY"
            android:value="AIzaSyAj-yFq85Qe8u0Rs5aMw4P2HB0DHvQ8Yl4" />
        <meta-data
            android:name="com.google.android.gms.version"
            android:value="@integer/google_play_services_version" />

        <activity
            android:name=".gis.GisMapActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".gis.IntelligenceMapActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".gis.GisCameraPlayActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="${applicationId}.file.provider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/update_apk_paths" />
        </provider>

        <service android:name=".voice.service.VoiceService" />
        <service android:name=".home.more.wifilocating.AutoWifiLocatingService" />
        <service android:name=".home.more.wifilocating.ManualWifiLocatingService" />
        <service android:name=".home.usertrack.UserGatherTrackService" />

        <activity
            android:name=".login.LoginActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="stateAlwaysHidden" />
        <activity
            android:name=".login_v1.LoginActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="stateAlwaysHidden" />
        <activity
            android:name=".home.MainActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:label="@string/app_name" />
        <activity
            android:name=".inventorymovement.InventoryMovementActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:launchMode="singleTop" />
        <activity
            android:name=".inventorymovement.InventoryEquipmentActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".home.more.picksort.PickSortViewActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".home.more.picksort.PickSortHomeActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".inventory.TaskActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".voice.VoiceSettingActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".inventory.LocationTaskActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".inventory.ProgressListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".toolset.print.PrintBolActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".toolset.print.barcode.PrintBarcodeActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".toolset.print.shippinglabel.ReprintShippingLabelActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".toolset.print.equipment.PrintEquipmentLabelActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".toolset.print.location.PrintLocationLabelActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".inventory.taskassign.TaskAssignFilterActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".inventory.taskassign.TaskAssignActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <service android:name=".fcm.MessagingService">
            <intent-filter>
                <action android:name="com.google.firebase.MESSAGING_EVENT" />
            </intent-filter>
        </service>
        <service android:name=".fcm.InstanceIdService">
            <intent-filter>
                <action android:name="com.google.firebase.INSTANCE_ID_EVENT" />
            </intent-filter>
        </service>
        <service android:name=".fcm.RegistrationService" />

        <activity
            android:name=".damagegood.DamageGoodHandlingActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".damagegood.inform.DamageGoodInformActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".damagegood.list.DamageGoodListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".load.detail.TaskDetailActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".load.operate.OperateActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".load.exception.ReportExceptionActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".load.exception.ExceptionListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".load.mbol.MbolListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".load.exception.ExceptionNoteActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".dock.dockcheckin.DockCheckInScanActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".dock.dockcheckin.DockCheckResultActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".dock_v1.dockchechkin.DockCheckInScanV1Activity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".receive.tasklist.TaskListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".load.TaskListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".load_v1.LoadTaskListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".load_v1.LoadActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".load_v1.collection.addseal.AddSealV1Activity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".load_v1.work.loadlogsummary.LoadLogSummaryActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".load_v1.work.mbol.MbolListV1Activity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".pick.tasklist.PickTaskCenterActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".pick.PickTaskActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".pick.viziopick.work.PickTaskWorkActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name=".pick.newpick.work.PickTaskWorkActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name=".pick.v1.work.PickWorkBusinessActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name=".pick.newpick.returnpicked.ReturnPickedActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="adjustResize|stateHidden" />
        <activity
            android:name=".pick_v1.pick.PickTaskV1Activity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="adjustPan|stateAlwaysHidden" />
        <activity
            android:name=".pick_v1.stage.PickStageActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="stateHidden|adjustPan" />
        <activity
            android:name=".toolset.workerselect.WorkerSelectorActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".damagegood.movement.DamageGoodMovementActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".damagegood.movement.DamageGoodNewLpActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".damagegood.movement.PalletizeActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".damagegood.movement.AddNewItemConfirmActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".damagegood.movement.PutAwayActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".damagegood.movement.DamageRemoveActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".toolset.cc.TaskListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".toolset.cc.progress.TaskProgressActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".demo.WidgetDemoActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".receive.StepOperateActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="stateAlwaysHidden|adjustPan" />
        <activity
            android:name=".toolset.lpconfigure.NewLpActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name="com.customer.widget.photo.ImagePagerActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".home.more.setting.UserProfileActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".toolset.itemsetup.ItemSetupActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".toolset.itemsetup.UnitContainerSetupActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".toolset.itemsetup.UnitSetupActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".wavepick.WavePickActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".toolset.lpputaway.PutAwayCreateCreateActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".demo.WidgetSignatureActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".load.operate.AddProNoActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".toolset.itemsetup.ItemPropertySetupActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".demo.ItemSpecSelectActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".toolset.lpputaway.PutAwayTaskListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".toolset.lpputaway.PutAwayTaskOperateActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".load.operate.LoadSignatureActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".demo.DebugMenuActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".replenishment.ReplenishmentTaskListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".replenishment.ReplenishmentTaskOperateActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".replenishment.ReplenishmentGetItemActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".replenishment.ReplenishmentPutAwayActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".toolset.reportexception.ReportExceptionActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".toolset.releasedock.ReleaseDockActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name="com.customer.widget.core.permission.PermissionsActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".home.more.taskassign.assigncenter.TaskAssignCenterActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".home.more.taskassign.assigncenter.TaskAssignListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".home.more.taskassign.receipt.stepmenu.ReceiptStepMenuActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".toolset.print.lp.PrintLpActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".toolset.print.PrintHomeActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".toolset.print.PrintPalletLabelActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name="com.customer.widget.squarecamera.CameraActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".receive.setup.view.LpSetupEditActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".dock.DockMainActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".dock.dockcheckout.DockCheckOutActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".toolset.print.setting.PrintSettingActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".receive.printitem.PrintItemSpecActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".toolset.print.item.PrintItemActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".toolset.print.item.PrintDetailItemLabelActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".load.operate.AddSealOrCountingSheetActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".toolset.scanitemlabel.ScanItemLabelActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".receive.item.ItemLineActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".pick.ordergroup.OrderGroupPartialActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".dock.dockcheckin.DockReceivePhotoActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".dock.doorcheckin.LoadDockPhotoActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".pack.tasklist.PackTaskListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".pack.taskdetail.TaskDetailActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".pack.orderitemlist.OrderItemListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".pack.kittingoperate.KittingOperateActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".pack.kittingoperate.KittingPickComponentActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".toolset.barcodedetail.LPDetailActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".toolset.barcodedetail.ItemDetailActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".pick.orderallocate.OrderDetailActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".fcm.LucidActivity"
            android:theme="@style/activityAlertDialog" />

        <service android:name=".fcm.AppUpdateService" />

        <activity
            android:name=".fcm.messagecenter.MessageCenterActivity"
            android:launchMode="singleTop" />
        <activity
            android:name=".returninventory.SelectLpByOrderActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".fcm.messagecenter.takeovermessage.MessageGroupActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".fcm.messagecenter.takeovermessage.MessageWindowActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".fcm.messagecenter.taskmessage.TaskMessageActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".home.task.taskstepmenu.TaskStepMenuActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:launchMode="standard" />
        <activity
            android:name=".home.task.taskstepmenu.genericstep.GenericStepWorkActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".home.more.takeover.TakeOverPagerActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".home.more.takeovermanage.TakeOverManageActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".home.more.tasksearch.TaskSearchCenterActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".qualitycontrol.regularqc.qcsearch.QcSearchActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".qualitycontrol.trackingnoqc.TrackingNoQCActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".receive.setup.SetupLpActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".lptemplate.LpTemplateListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".pack.taskoperate.PackTaskActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".pack.taskoperate.AssignSlpLocationActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".cctask.tasklist.CcTaskListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".cctask.step.work.EfficiencyToPoolActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".cctask.step.efficiency.CcCollectStepActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="stateAlwaysHidden|adjustPan" />
        <activity
            android:name=".cctask.step.efficiency.CcBuildStepActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="stateAlwaysHidden|adjustPan" />
        <activity
            android:name=".cctask.step.CcTaskStepPagerActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".cctask.snlist.CcTaskSnListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".cctask.step.work.EfficiencyToLpActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:launchMode="singleInstance" />
        <activity
            android:name=".home.more.material.MaterialCenterActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".home.more.material.work.materialselect.MaterialItemSelectActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".home.more.material.work.diverseselect.DiverseSelectActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".receive.setup.work.singleitem.SelectedItemListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".cctask.step.work.TraditionalToLpActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".cctask.step.work.TraditionalToPoolActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".toolset.dockselect.DockSelectorActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity android:name=".home.more.tasksplitormerge.taskcenter.TaskSplitMergeCenterActivity" />
        <activity
            android:name=".home.more.tasksplitormerge.pick.tasklist.PickTaskListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".home.more.tasksplitormerge.pick.split.PickSplitActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".home.more.tasksplitormerge.pick.split.NewTaskDetailActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity android:name=".home.more.tasksplitormerge.cc.tasklist.CcTaskListActivity" />
        <activity android:name=".home.more.tasksplitormerge.cc.split.CcSplitActivity" />
        <activity android:name=".lptemplate.MultipleLpTemplateActivity" />
        <activity
            android:name=".generaltask.GeneralTaskListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".receive.snscan.SnScanWorkActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="stateAlwaysHidden|adjustPan" />
        <activity
            android:name=".receive.snscan.SnCollectDimensActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="stateAlwaysHidden|adjustPan" />
        <activity
            android:name=".home.more.material.view.MaterialLineEditActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".home.more.material.work.receiptselect.ReceiptSelectActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".putback.tasklist.PutBackTaskListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".putback.operate.view.PutBackViewActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".replenishment.ReplenishmentCreateActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".putback.operate.work.PutBackWorkActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".replenishment.ReplenishmentProcessActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".load.orderlist.LoadOrderListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="stateAlwaysHidden|adjustPan" />
        <activity
            android:name=".toolset.print.label.PrintLabelActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".load.operate.OrderOperateActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".pack.packagingtype.PackageTypeListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".home.more.tasksearch.AdvancedSearchActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".pick.batchorderpick.BatchOrderPickActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="stateAlwaysHidden|adjustPan" />
        <activity
            android:name=".toolset.itemselector.ItemSelectorActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="stateAlwaysHidden|adjustPan" />
        <activity
            android:name=".replenishment.ReplenishmentGetSuggestionActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="stateAlwaysHidden|adjustPan" />
        <activity
            android:name=".home.more.lptemplatemanage.LpTemplateManageActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".home.more.lptemplatemanage.BuildSearchEntryActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".home.more.lptemplatemanage.NewLpTemplateActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".home.more.lptemplatemanage.SelectCustomerActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".lptemplate.NewItemLpTemplateActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".transload.TransloadTaskActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".transload.stepmenu.TransloadStepMenuActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".transload.stepwork.OffloadStepActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".transload.stepwork.ReceivingStepActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".transload.stepwork.ShippingStepActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".transload.stepwork.ShippingCartonActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".transload.stepwork.AddProNoActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".transload.stepwork.AddSealNoActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".movement.MovementTaskListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".transload.stepwork.UnreceivingWorkActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".home.more.adjustment.newinventory.NewInventoryActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".home.more.adjustment.lpadjustment.LpAdjustmentActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".home.more.adjustment.AdjustmentActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".pick.PickStepOperateActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="stateAlwaysHidden|adjustPan" />
        <activity
            android:name=".toolset.itemcollector.ItemCollectorActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".pick.pickstep.PickReturnToInventoryActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity android:name=".pick.pickpull.PickPullViewActivity" />
        <activity
            android:name=".pick.pickpull.PickPullWorkActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="stateAlwaysHidden|adjustPan" />
        <activity
            android:name=".toolset.inventorycount.InventoryCountActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".toolset.inventorycount.InventoryCollectionActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".pick.pickstep.PickBindOrderActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".transload.stepwork.TransloadPreviewActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".home.more.setting.SystemFeatureSettingsActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".putback.emptytask.CreateEmptyPutBackTaskActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".putback.emptytask.EmptyPutBackTaskActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".pick.ClosePalletActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".transload.stepwork.lpn.LpnSetupStepActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".transload.stepwork.lpnsetup.BuildSetupItemActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="stateAlwaysHidden|adjustPan" />
        <activity
            android:name=".transload.stepwork.ShippingScanActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".transload.stepwork.ReceivingScanActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".transload.stepwork.lpn.DeleteLpnActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".pack.taskoperate.work.PackAllLpActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".transload.stepwork.photos.PhotoStepActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".transload.stepwork.putaway.PutAwayStepActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="stateAlwaysHidden|adjustPan" />
        <activity
            android:name=".transload.stepwork.lpnsetup.AddNewItemActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="stateAlwaysHidden|adjustPan" />
        <activity
            android:name=".transload.stepwork.ScanLpnCartonSnActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="stateAlwaysHidden|adjustPan" />
        <activity
            android:name=".replenish.replenish.ReplenishStepWorkActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="stateAlwaysHidden|adjustPan" />
        <activity
            android:name=".home.more.parcelload.ParcelLoadTaskActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="stateAlwaysHidden|adjustPan" />
        <activity
            android:name=".qualitycontrol.regularqc.qcwork.QcWorkActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".qualitycontrol.regularqc.qcdetail.QcDetailActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".transload.stepwork.trailercheckin.TrailerCheckInActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".transload.stepwork.trailercheckin.SelectEntryActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".transload.stepwork.trailercheckin.NewEntryActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".load.sncollect.view.SnCollectionStepActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".load.sncollect.work.SnCollectionWorkActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".pick.viziopick.view.PickTaskViewActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".pick.newpick.view.PickTaskViewActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".home.more.lpsncollection.LpSnCollectionViewActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".home.more.lpsncollection.SnManagerActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".home.more.lpsncollection.LpSnCollectionWorkActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".home.more.lpsncollection.OrderSNCollectionActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".home.more.lpsncollection.OrderSNCollectionWorkActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".replenish.collect.CollectStepActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".packtopallet.ParcelPackActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".packtopallet.ParcelPackHomeActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".home.more.parcelload.ParcelLoadWorkActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".parcelload.ParcelLoadTaskListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".inventorymovement.InventoryMovementCreateActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".inventorymovement.collect.InventoryMovementCollectActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".inventorymovement.drop.InventoryMovementDropActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".inventorymovement.component.InventoryMovementDetailActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".inventorymovement.taskList.InventoryMovementTaskListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".load.countunshipped.CountUnshippedActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".toolset.print.PrinterSettingActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".makepallet.MakePalletWithoutSnActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".makepallet.MakePalletActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".packtopallet.view.LpParcelPackDetailActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".pick.allocate.workviews.AllocateWorkActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="stateAlwaysHidden|adjustPan" />
        <activity
            android:name=".step.takeover.TakeOverStepActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".step.startstep.StartStepActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".pick.allocate.progress.AllocateProgressActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".replenish.replenish.print.ReplenishPrintLpActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".toolset.print.BarcodePrintActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".pick.v1.progress.PickProgressActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".autogenerationmaterial.AutoGenerationMaterialActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".autogenerationmaterial.view.MaterialEditActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".putaway.AdvancePutAwayActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="stateAlwaysHidden|adjustPan" />
        <activity
            android:name=".putaway.progress.PutAwayProgressActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".qualitycontrol.randomqc.RandomQualityControlActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".toolset.print.rework.PrintReworkLabelActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".toolset.inventorycount.newpallet.InventoryNewPalletActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity android:name=".toolset.task.TaskListActivity" />
        <activity
            android:name=".home.more.inventorycount.cyclecount.CycleCountWorkActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".home.more.inventorycount.InventoryCountCenterActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".home.more.inventorycount.cyclecount.history.CycleCountHistoryActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".putaway.receiveputaway.DirectPutAwayActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="stateAlwaysHidden|adjustResize" />
        <activity
            android:name=".receive.setup.forcecloseprocess.ForceClosedVerificationActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".receive.setup.work.receivebycarton.ReceiveByCartonActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".putaway.mulputaway.PutAwayMulItemActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="stateHidden|adjustPan" />
        <activity
            android:name=".putaway.mulputaway.progress.PutAwayMulItemProgressActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".transfer.out.TransferOutActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".transfer.in.TransferInActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity android:name=".transfer.out.TransferOutHistoryActivity" />
        <activity
            android:name=".receive.item.CollectItemDimensionActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".pick.labelprinthistory.LabelPrintHistoryActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="stateHidden|adjustPan" />
        <activity
            android:name=".pick.batchprint.PickTaskPrintLabelActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".receive.setup.work.receivetoputaway.ReceiveToPutAwayActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".home.more.createtask.CreateEmptyTaskActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".pick.picktoslp.palletmaterial.PickPalletMaterialActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".pick.picktoslp.stage.StageSLPActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".home.more.picksort.PickSortWorkActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="stateAlwaysHidden|adjustPan" />
        <activity
            android:name=".home.more.picksort.putback.PutBackPickSortingActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="stateAlwaysHidden|adjustPan" />
        <activity
            android:name=".home.more.wifilocating.CollectWifiLocatingInfoActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="stateAlwaysHidden|adjustPan" />
        <activity
            android:name=".home.more.repalletize.RePalletizeActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="stateAlwaysHidden|adjustPan" />
        <activity
            android:name=".pack.repalletize.RePalletizeWorkActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="stateAlwaysHidden|adjustPan" />
        <activity
            android:name=".home.more.epikitting.EpiKittingActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="stateAlwaysHidden|adjustPan" />
        <activity
            android:name=".generaltask.GeneralTaskActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".generaltask.GeneralTaskCreateActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".generaltask.GeneralStepWorkActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".generaltask.GeneralStepAssemblyActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="stateAlwaysHidden|adjustPan" />
        <activity
            android:name=".collectiteminfo.view.CollectItemInfoActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".transload_v2.loading.loadorders.TransLoadOrdersActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".transload_v2.loading.loadorders.muldestinations.TransloadLoadingWithMulPalletActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".transload_v2.loading.orderdetail.TransloadOrdersDetailActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".transload_v2.loading.loadlist.TransloadLoadsActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".toolset.print.customizedlabel.CustomizedLabelActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".home.more.materialmanager.receiving.view.MaterialReceivingActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".home.more.materialmanager.checkout.view.MaterialCheckOutActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="adjustNothing|stateHidden" />
        <activity
            android:name=".home.more.materialmanager.search.view.MaterialSearchActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".home.more.materialmanager.itemprint.view.MaterialItemPrintActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".home.more.materialmanager.inventorycount.view.MaterialInventoryCountActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".inventoryreplenishment.ReplenishmentCreateAndBindActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".inventoryreplenishment.ReplenishmentActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".parcelreceive.activity.SmallParcelReceiveCreateActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".parcelreceive.activity.SmallParcelReceiveTaskActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name=".load.LoadLocationActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".home.more.transload.TransloadSplitMergeActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".home.more.transload.TransloadSearchActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".home.more.transload.TransloadCartonCheckerActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".home.timesheet.PunchInActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".load.operate.AddCtnrTrailerNoActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".home.more.bluetoothlocation.BluetoothLocationActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".receive.setup.work.inventorytransfer.InventoryTransferActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".home.more.inventoryinitial.InventoryInitialActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".home.more.locationcreation.LocationManagerActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".receive_v1.lpsetup.LpSetupActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".receive_v1.lpsetup.review.LpSetupReviewActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".receive_v1.snscan.SnScanActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".receive_v1.offload.OffloadActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".receive_v1.offload.OffloadVerifyInfoActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".putaway_v1.PutAwayActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".putaway_v1.review.PutAwayReviewActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".home.more.locationsplitormerge.LocationSplitMergeCenterActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".home.more.locationsplitormerge.merge.LocationMergeActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".home.more.locationsplitormerge.split.LocationSplitActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".home_v1.more.MoreChildActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".home_v1.profile.PasswordManagementActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".home_v1.profile.MyAccountActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="stateAlwaysHidden|adjustPan" />
        <activity
            android:name=".home_v1.HomeActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:launchMode="singleTask" />
        <activity
            android:name=".putawayauditing.tasklist.PutAwayAuditingTaskListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".putawayauditing.PutAwayAuditingActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />
        <activity
            android:name=".putawayauditing.progress.PutAwayAuditingProgressActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".home_v1.more.taskassign_v1.TaskAssignActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".stagetoload.StageToLoadActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".stagetoload.taskcenter.StageToLoadTaskCenterActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".parcelreceive.CreateParcelReceiveTaskActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".equipmentinquiry.EquipmentInquiryActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".equipmentinquiry.detailinfo.EquipmentDetailInfoActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".equipmentinquiry.transfer.InventoryTransferConfirmActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".home_v1.task.taskquestionnaire.TaskQuestionnaireActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".qualitycontrol.lastmileqc.LastMileQcActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />


        <activity
            android:name=".home.more.locationinfocollection.LocationInfoCollectionActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".home.more.locationinfocollection.update.LocationInfoUpdateActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".home.more.locationinfocollection.example.LocationPatternExampleActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".home.more.locationinfocollection.locationlist.LocationListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".home.more.assetmanagment.inquiry.AssetInquiryActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".home.more.assetmanagment.assetassigned.AssetAssignedActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".home.more.assetmanagment.assettask.AssetAssignedTaskActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".transload_v2.receiving.operate.last_mile.exception.exception_hold.LSOExceptionHoldActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".inventorymovement.consolidation.ConsolidationMovementTaskActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <receiver android:name=".fcm.NotificationClickReceiver" />

        <activity
            android:name=".pick.stage.StageActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".home.more.rfidreader.RFIDReaderActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".transload_v2.receiving.operate.last_mile.work.oversizehandling.OversizeCartonHandingActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".home.more.clpbondingtask.CreateCLPBondingTaskActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".home.more.clpbondingtask.task.CLPBondingTaskActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".home.more.clpbondingtask.CLPBondingTaskDoneActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".home.more.clpbondingtask.packageupdate.CLPPackageUpdateTaskActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".pick_v1.orderpick.OrderPickActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="stateHidden|adjustPan" />

        <activity
            android:name=".pick_v1.sort.SortWallActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="stateHidden|adjustPan" />

        <activity
            android:name=".home.more.hospital.create.CreateHospitalTaskActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="stateHidden|adjustPan" />

        <activity
            android:name=".home.more.hospital.tasklist.TaskListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".home.more.hospital.work.HospitalWorkActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".home.more.hospital.work.HospitalDoneActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".pick_v1.orderpick.processingorder.ProcessingOrderListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="stateHidden|adjustPan" />

        <activity
            android:name=".home.more.consolidatePallet.create.StartConsolidatePalletActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:windowSoftInputMode="stateHidden|adjustPan" />

        <activity
            android:name=".home.more.consolidatePallet.work.ConsolidatePalletWorkActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".home.more.consolidatePallet.list.TaskListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".home.more.consolidatePallet.work.unconsolidatedboxes.UnconsolidatedBoxesActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".home.more.materialmanager.receiving.view.MaterialWorkViewActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".home.more.lpsncollection.RFIDCollectionWorkActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".asset.task.assetlist.AssetListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".asset.task.audit.AssetAuditActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".asset.task.tasklist.AssetAuditTaskListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".home.more.palletconsolidation.PalletConsolidationActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".pick_v1.stage.work.totable.unstage.UnStageSkuActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".load_v1.inspection.LoadInspectionActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".toolset.print.lso.TransitPackageLabelActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".asset.manager.receive.ReceiveAssetActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".home.more.lso.LSOBondingTaskActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".cyclecount.CycleCountActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"
            android:launchMode="singleTop" />

        <activity
            android:name=".home_v1.more.collect.CollectionDamageInfoActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".asset.manager.search.AssetSearchActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".asset.manager.detail.AssetDetailActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".cyclecount.history.CountHistoryActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".lsolocalbonding.LsoLocalBondingActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".lsolocalbonding.LsoReprintLabelActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".lumper.create.CreateLumperTaskActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".lumper.task.LumperTaskActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".lumper.tasklist.LumperTaskListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".task_pool.TaskPoolListActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity
            android:name=".task_pool.detail.TaskPoolDetailActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation" />

        <activity android:name=".home.more.lso.LSOBondingCheckActivity"
            android:configChanges="keyboardHidden|keyboard|screenSize|orientation" />

        <activity android:name=".employee.v1.LaborSettingActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"/>

        <activity android:name=".dock_v1.checkinverification.CheckInVerificationActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"/>

        <activity android:name=".pick_v1.pick.reprintshippinglabel.ReprintShippingLabelActivity"
            android:configChanges="orientation|screenSize|keyboard|keyboardHidden|navigation"/>
    </application>

</manifest>