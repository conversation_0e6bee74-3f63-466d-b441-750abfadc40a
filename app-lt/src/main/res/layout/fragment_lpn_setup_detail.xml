<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/colorBgDefaultGrey"
    android:orientation="vertical">

    <RadioGroup
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:orientation="horizontal">

        <RadioButton
            android:id="@+id/item_view_rbtn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:checked="true" />

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/labelTextMiddle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:gravity="center"
            android:text="@string/item_view" />

        <RadioButton
            android:id="@+id/lpn_view_rbtn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/labelTextMiddle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:gravity="center"
            android:text="@string/label_lpn_view" />

    </RadioGroup>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white" />

</LinearLayout>