<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/layout_scanner"
    android:layout_width="match_parent"
    android:layout_height="45dp"
    android:orientation="horizontal">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.appcompat.widget.AppCompatEditText
            android:id="@+id/edt_scan"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginLeft="10dp"
            android:layout_marginTop="1dp"
            android:layout_marginRight="10dp"
            android:layout_marginBottom="1dp"
            android:background="@null"
            android:hint="@string/text_scan_clp"
            android:inputType="textNoSuggestions"
            android:maxLines="1"
            android:paddingLeft="10dp"
            android:paddingRight="10dp"
            android:textColor="@color/black"
            android:textColorHint="#999794"
            android:textSize="16sp" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/txt_scan"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_alignEnd="@+id/edt_scan"
            android:layout_alignParentTop="true"
            android:background="?android:attr/selectableItemBackground"
            android:drawableLeft="@drawable/ic_barcode"
            android:gravity="center"
            android:paddingLeft="20dp"
            android:paddingRight="20dp" />
    </RelativeLayout>

</LinearLayout>