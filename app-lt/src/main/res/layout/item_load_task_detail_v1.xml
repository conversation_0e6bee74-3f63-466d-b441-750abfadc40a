<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingHorizontal="@dimen/page_horizontal_margin_v1"
    android:paddingVertical="@dimen/page_horizontal_margin_half_v1">

    <LinearLayout
        android:id="@+id/load_title_ll"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/rect_525252_top_4"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingHorizontal="@dimen/page_horizontal_margin_v1"
        android:paddingVertical="@dimen/page_horizontal_margin_v1">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_load_id"
            style="@style/textSubtitleV1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="0.8"
            android:textSize="@dimen/text_size_heading5_v1" />

        <FrameLayout
            android:id="@+id/fl_load_info_outline"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="horizontal">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_order_count"
                style="@style/textSubtitleV1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/load_summary_iv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="end|center_vertical"
                android:src="@drawable/ic_info_outline_v1" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_state_complete"
                style="@style/textSubtitleV1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:background="@drawable/rect_214d36_r2"
                android:paddingHorizontal="@dimen/page_horizontal_margin_v1"
                android:paddingVertical="@dimen/page_horizontal_margin_half_v1"
                android:text="@string/btn_complete"
                android:textColor="@color/accent_green_v1"
                android:visibility="gone" />

        </FrameLayout>

    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_load_add_trailer_no"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/rect_393939_bottom_4"
        android:orientation="vertical"
        android:paddingHorizontal="@dimen/page_horizontal_margin_v1">

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/color_525252" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginVertical="@dimen/chip_vertical_padding_v1"
            android:orientation="horizontal">

            <androidx.appcompat.widget.AppCompatTextView
                style="@style/textSubtitleV1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="0.8"
                android:text="@string/label_load_number" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_add_trailer_no_load_number"
                style="@style/textBodyV1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_weight="1" />

        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/color_525252" />

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/add_trailer_no_btn"
            style="@style/buttonV1"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="20dp"
            android:text="@string/title_add_ctnr_trailer_no" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_load_add_photos"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/rect_393939_bottom_4"
        android:orientation="vertical"
        android:paddingHorizontal="@dimen/page_horizontal_margin_v1">

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/color_525252" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingVertical="@dimen/chip_vertical_padding_v1">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_label_seal_number"
                style="@style/textSubtitleV1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="0.8" />

            <androidx.appcompat.widget.AppCompatEditText
                android:id="@+id/add_seal_et"
                style="@style/textSubtitleV1"
                android:layout_width="0dp"
                android:layout_height="@dimen/scanner_height_v1"
                android:layout_weight="1"
                android:background="@drawable/rect_525252_r4"
                android:hint="@string/hint_seal"
                android:imeOptions="actionDone"
                android:paddingHorizontal="@dimen/page_horizontal_margin_v1"
                android:singleLine="true"
                android:textColorHint="@color/text_hint_v1" />

        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/color_525252" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_label_seal_photo"
                style="@style/textSubtitleV1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginVertical="10dp"
                android:layout_weight="0.8"
                android:text="@string/upload_photo_of_seal" />

            <com.lt.linc.util.v1widget.UploadFileV1Widget
                android:id="@+id/upload_photo_seal"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                app:addPhotoIconRes="@drawable/ic_pallet_upload_photo_add_v1_new"
                app:deleteConfirmText="@string/text_delete_seal"
                app:photoItemRes="@layout/item_upload_photo_v1_new" />
        </LinearLayout>

        <View
            android:layout_width="wrap_content"
            android:layout_height="1dp"
            android:background="@color/color_525252" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_label_truck_photo"
                style="@style/textSubtitleV1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginVertical="10dp"
                android:layout_weight="0.8" />

            <com.lt.linc.util.v1widget.UploadFileV1Widget
                android:id="@+id/upload_photo_truck"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                app:addPhotoIconRes="@drawable/ic_pallet_upload_photo_add_v1_new"
                app:deleteConfirmText="@string/text_delete_truck"
                app:photoItemRes="@layout/item_upload_photo_v1_new" />

        </LinearLayout>

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/bol_list_btn"
            style="@style/buttonV1"
            android:background="@drawable/bg_radius4_stroke1"
            android:text="@string/title_mbol_list" />

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/complete_task_btn"
            style="@style/buttonV1"
            android:layout_marginVertical="20dp"
            android:layout_marginTop="10dp"
            android:text="@string/text_complete_task" />

    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_load_detail"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/rect_393939_bottom_4"
        android:orientation="vertical"
        android:paddingHorizontal="@dimen/page_horizontal_margin_v1">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingVertical="@dimen/chip_vertical_padding_v1">

            <androidx.appcompat.widget.AppCompatTextView
                style="@style/textSubtitleV1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="0.8"
                android:text="@string/label_load_number" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_load_number"
                style="@style/textBodyV1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_weight="1" />

        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/color_525252" />

        <LinearLayout
            android:id="@+id/ll_load_type"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingVertical="@dimen/chip_vertical_padding_v1">

            <androidx.appcompat.widget.AppCompatTextView
                style="@style/textSubtitleV1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="0.8"
                android:text="@string/label_load_type" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_load_type"
                style="@style/textBodyV1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1" />

        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/color_525252" />

        <LinearLayout
            android:id="@+id/ll_load_sequence"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingVertical="@dimen/chip_vertical_padding_v1">

            <androidx.appcompat.widget.AppCompatTextView
                style="@style/textSubtitleV1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="0.8"
                android:text="@string/label_load_seq" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_load_sequence"
                style="@style/textBodyV1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1" />

        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/color_525252" />

        <LinearLayout
            android:id="@+id/ll_create_by"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingVertical="@dimen/chip_vertical_padding_v1">

            <androidx.appcompat.widget.AppCompatTextView
                style="@style/textSubtitleV1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="0.8"
                android:text="@string/text_create_by" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_create_by"
                style="@style/textBodyV1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_weight="1" />

        </LinearLayout>

        <View
            android:id="@+id/v_line_add_pro_no"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/color_525252" />

        <LinearLayout
            android:id="@+id/ll_add_pro_number"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingVertical="@dimen/panel_vertical_margin_v1">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_label_add_pro_number"
                style="@style/textSubtitleV1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="0.8"
                android:text="@string/text_add_pro_no" />

            <com.customer.widget.QuickScanner
                android:id="@+id/scan_pro_no_scanner"
                android:layout_width="0dp"
                android:layout_height="@dimen/scanner_height_v1"
                android:layout_weight="1"
                android:background="@drawable/rect_525252_r4"
                android:focusable="false"
                android:focusableInTouchMode="false"
                app:hintText="@string/add_pro_number_hit"
                app:layout_res="@layout/view_quick_scanner_v1_blue_icon" />

            <TextView
                android:id="@+id/pro_no_tv"
                android:layout_width="0dp"
                android:layout_height="@dimen/scanner_height_v1"
                android:layout_weight="1"
                android:background="@drawable/rect_525252_r4"
                android:textColor="@color/new_grey"
                android:paddingHorizontal="16dp"
                android:gravity="center_vertical"
                android:visibility="gone"/>

        </LinearLayout>

        <View
            android:id="@+id/v_line_count_sheet"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/color_525252" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_label_count_sheet_photo"
                style="@style/textSubtitleV1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginVertical="10dp"
                android:layout_weight="0.8"
                android:text="@string/upload_photo_of_count_sheet" />

            <com.lt.linc.util.v1widget.UploadFileV1Widget
                android:id="@+id/upload_photo_count_sheet"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                app:addPhotoIconRes="@drawable/ic_pallet_upload_photo_add_v1_new"
                app:deleteConfirmText="@string/text_delete_count_sheet"
                app:photoItemRes="@layout/item_upload_photo_v1_new"
                app:takeVideoTitle="@string/text_take_video_of_count_sheet"
                app:takePhotoTitle="@string/text_take_photo_of_count_sheet" />

        </LinearLayout>

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/start_load_btn"
            style="@style/buttonV1"
            android:layout_marginVertical="20dp"
            android:text="@string/btn_start" />

    </LinearLayout>


</LinearLayout>