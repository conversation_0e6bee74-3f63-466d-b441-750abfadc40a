<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:paddingLeft="16dp"
    android:paddingRight="16dp">

    <include
        layout="@layout/fragment_base_consolidation_inventory"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <com.lt.linc.common.LocationSignsScanner
        android:id="@+id/qs_location"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        app:isShowLocationLabel="false"/>

    <LinearLayout
        android:id="@+id/ll_lp_scanner_parent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:visibility="gone"
        android:layout_marginTop="24dp">

        <com.customer.widget.QuickScanner
            android:id="@+id/qs_lp"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginRight="16dp"
            android:layout_weight="1"
            app:hintText="@string/hint_inven_consolidation_scan_lp"
            app:layout_res="@layout/view_quick_scanner_white_rectangle" />

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/new_lp_btn"
            style="@style/raisedButtonStyle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/inven_consolidation_new_lp" />
    </LinearLayout>

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/next_btn"
        style="@style/raisedButtonStyle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="48dp"
        android:text="@string/inven_consolidation_next_btn" />
</LinearLayout>
