<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/page_background_v1"
    android:orientation="vertical">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingHorizontal="@dimen/page_horizontal_margin_v1">

            <!--Header-->
            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:maxLines="1"
                android:paddingVertical="@dimen/page_vertical_margin_v1"
                android:text="@string/item_info_collect"
                android:textColor="@color/white"
                android:textSize="@dimen/text_size_heading1_v1"
                android:textStyle="bold" />

            <!--Pictures-->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/rect_393939_r4"
                android:orientation="vertical"
                android:paddingHorizontal="@dimen/page_horizontal_margin_v1"
                android:paddingVertical="@dimen/panel_body_vertical_padding_v1">

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/item_no_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/white"
                    android:textSize="@dimen/text_size_body3_v1"
                    tools:text="@string/item_no_xx" />

                <com.lt.linc.util.v1widget.UploadFileV1Widget
                    android:id="@+id/upload_photo_widget"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    app:deleteConfirmText="@string/question_remove_photo" />

            </LinearLayout>

            <!--Add Item Information-->
            <com.customer.widget.CollapsiblePanel
                android:id="@+id/item_info_collapsible_panel"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/component_vertical_padding_v1"
                app:cp_cornerRadius="4dp"
                app:cp_dividerViewId="@+id/item_info_divider"
                app:cp_hideDividerWhenCollapsed="true"
                app:cp_isCollapsed="false"
                app:cp_showDivider="true"
                app:cp_upArrowViewId="@id/ic_info_collapse_arrow">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/panel_body_v1"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingHorizontal="@dimen/page_horizontal_margin_v1"
                    android:paddingVertical="@dimen/panel_header_vertical_padding_v1">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/add_item_information"
                        android:textColor="@color/white"
                        android:textSize="@dimen/text_size_body3_v1"
                        android:textStyle="bold" />

                    <ImageView
                        android:id="@+id/ic_info_collapse_arrow"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/ic_arrow_up"
                        android:tint="@color/white" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/panel_body_v1"
                    android:orientation="vertical"
                    android:paddingHorizontal="@dimen/page_horizontal_margin_v1"
                    android:paddingBottom="@dimen/panel_body_vertical_padding_v1">

                    <View
                        android:id="@+id/item_info_divider"
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:background="@color/color_525252" />

                    <!--Short Description-->
                    <LinearLayout
                        android:id="@+id/short_description_layout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/panel_body_vertical_padding_v1"
                        android:orientation="vertical">

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/title_short_description"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/short_description"
                            android:textColor="@color/white"
                            android:textSize="@dimen/text_size_sm1_v1"
                            android:textStyle="bold" />

                        <com.customer.widget.ExtendedEditText
                            android:id="@+id/short_description_edit"
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/scanner_height_v1"
                            android:layout_marginTop="8dp"
                            android:background="@drawable/rect_525252_r4"
                            android:hint="@string/input_info"
                            android:imeOptions="actionNext"
                            android:inputType="text"
                            android:maxLines="1"
                            android:paddingHorizontal="@dimen/page_horizontal_margin_v1"
                            android:textColor="@color/white"
                            android:textColorHint="@color/text_hint_v1"
                            android:textSize="@dimen/text_size_body3_v1" />

                    </LinearLayout>

                    <!--group Spinner-->
                    <LinearLayout
                        android:id="@+id/group_layout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/panel_body_vertical_padding_v1"
                        android:orientation="vertical"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/title_group"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/label_group"
                            android:textColor="@color/white"
                            android:textSize="@dimen/text_size_sm1_v1"
                            android:textStyle="bold" />

                        <RelativeLayout
                            android:id="@+id/group_select_layout"
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/scanner_height_v1"
                            android:layout_gravity="center_vertical"
                            android:layout_marginTop="8dp"
                            android:background="@drawable/rect_525252_r4"
                            android:gravity="center_vertical"
                            android:paddingHorizontal="@dimen/page_horizontal_margin_v1">

                            <com.customer.widget.ExtendedEditText
                                android:id="@+id/group_et"
                                android:layout_width="match_parent"
                                android:layout_height="46dp"
                                android:background="@drawable/rect_525252_r4"
                                android:hint="@string/select_item_group"
                                android:textColor="@color/white"
                                android:gravity="start|center_vertical"
                                android:textColorHint="@color/text_hint_v1"
                                android:textSize="@dimen/text_size_body3_v1"
                                android:focusable="false"
                                android:focusableInTouchMode="false"
                                />

                            <ImageView
                                android:layout_width="16dp"
                                android:layout_height="16dp"
                                android:alpha="0.8"
                                android:background="@drawable/ic_white_triangle_down"
                                android:layout_alignParentEnd="true"
                                android:layout_centerVertical="true"/>

                        </RelativeLayout>

                    </LinearLayout>

                    <!--UPC Code-->
                    <LinearLayout
                        android:id="@+id/upc_layout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/panel_body_vertical_padding_v1"
                        android:orientation="vertical">

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/title_upc"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/upc_code"
                            android:textColor="@color/white"
                            android:textSize="@dimen/text_size_sm1_v1"
                            android:textStyle="bold" />

                        <com.customer.widget.QuickScanner
                            android:id="@+id/upc_scanner"
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/scanner_height_v1"
                            android:layout_marginTop="8dp"
                            android:background="@drawable/rect_525252_r4"
                            app:hintText="@string/input_info"
                            app:hintTextSize="@dimen/text_size_body3_v1"
                            app:layout_res="@layout/view_quick_scanner_v1_blue_icon" />

                    </LinearLayout>

                    <!--UPC Case Code-->
                    <LinearLayout
                        android:id="@+id/upc_case_layout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/panel_body_vertical_padding_v1"
                        android:orientation="vertical">

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/title_upc_case"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/upc_code_case"
                            android:textColor="@color/white"
                            android:textSize="@dimen/text_size_sm1_v1"
                            android:textStyle="bold" />

                        <com.customer.widget.QuickScanner
                            android:id="@+id/upc_case_scanner"
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/scanner_height_v1"
                            android:layout_marginTop="8dp"
                            android:background="@drawable/rect_525252_r4"
                            app:hintText="@string/input_info"
                            app:hintTextSize="@dimen/text_size_body3_v1"
                            app:layout_res="@layout/view_quick_scanner_v1_blue_icon" />

                    </LinearLayout>

                    <!--Small Parcel Packaging Spinner-->
                    <LinearLayout
                        android:id="@+id/small_parcel_packaging_layout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/panel_body_vertical_padding_v1"
                        android:visibility="gone"
                        android:orientation="vertical">

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/title_small_parcel_packaging"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/label_small_parcel_packaging"
                            android:textColor="@color/white"
                            android:textSize="@dimen/text_size_sm1_v1"
                            android:textStyle="bold" />

                        <LinearLayout
                            android:id="@+id/small_parcel_packaging_select_layout"
                            android:layout_width="match_parent"
                            android:layout_height="@dimen/scanner_height_v1"
                            android:layout_gravity="center_vertical"
                            android:layout_marginTop="8dp"
                            android:background="@drawable/rect_525252_r4"
                            android:gravity="center_vertical"
                            android:paddingHorizontal="@dimen/page_horizontal_margin_v1">

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/small_parcel_packaging_tv"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:hint="@string/label_select_small_parcel_packaging"
                                android:textColor="@color/white"
                                android:textColorHint="@color/text_hint_v1"
                                android:textSize="@dimen/text_size_body3_v1" />

                            <ImageView
                                android:id="@+id/iv_uom_parcel"
                                android:layout_width="16dp"
                                android:layout_height="16dp"
                                android:alpha="0.8"
                                android:background="@drawable/ic_white_triangle_down" />

                        </LinearLayout>

                    </LinearLayout>

                    <!--Description-->
                    <LinearLayout
                        android:id="@+id/description_layout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/panel_body_vertical_padding_v1"
                        android:orientation="vertical">

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/title_description"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/rb_description"
                            android:textColor="@color/white"
                            android:textSize="@dimen/text_size_sm1_v1"
                            android:textStyle="bold" />

                        <com.customer.widget.ExtendedEditText
                            android:id="@+id/description_edit"
                            android:layout_width="match_parent"
                            android:layout_height="80dp"
                            android:layout_marginTop="8dp"
                            android:background="@drawable/rect_525252_r4"
                            android:gravity="start|top"
                            android:hint="@string/input_info"
                            android:imeOptions="actionDone"
                            android:inputType="text"
                            android:paddingHorizontal="@dimen/page_horizontal_margin_v1"
                            android:paddingVertical="12dp"
                            android:textColor="@color/white"
                            android:textColorHint="@color/text_hint_v1"
                            android:textSize="@dimen/text_size_body3_v1" />

                    </LinearLayout>

                </LinearLayout>

            </com.customer.widget.CollapsiblePanel>

            <!--UOM Setup-->
            <com.customer.widget.CollapsiblePanel
                android:id="@+id/uom_collapsible_panel"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/component_vertical_padding_v1"
                app:cp_cornerRadius="4dp"
                app:cp_dividerViewId="@+id/uom_divider"
                app:cp_hideDividerWhenCollapsed="true"
                app:cp_isCollapsed="false"
                app:cp_showDivider="true"
                app:cp_upArrowViewId="@id/ic_uom_collapse_arrow">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/panel_body_v1"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingHorizontal="@dimen/page_horizontal_margin_v1"
                    android:paddingVertical="@dimen/panel_header_vertical_padding_v1">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/uom_setup"
                        android:textColor="@color/white"
                        android:textSize="@dimen/text_size_body3_v1"
                        android:textStyle="bold" />

                    <ImageView
                        android:id="@+id/ic_uom_collapse_arrow"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/ic_arrow_up"
                        android:tint="@color/white" />

                </LinearLayout>


                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/panel_body_v1"
                    android:orientation="vertical"
                    android:paddingBottom="@dimen/panel_body_vertical_padding_v1">

                    <View
                        android:id="@+id/uom_divider"
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginHorizontal="@dimen/page_horizontal_margin_v1"
                        android:background="@color/color_525252" />

                    <!--Cubit Scanner request-->
                    <LinearLayout
                        android:id="@+id/cubit_scanner_layout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/page_horizontal_margin_v1"
                        android:layout_marginTop="@dimen/panel_body_vertical_padding_v1"
                        android:orientation="horizontal">

                        <androidx.appcompat.widget.AppCompatTextView
                            android:id="@+id/title_cubit_scanner"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/label_connect_scanner"
                            android:textColor="@color/white"
                            android:textSize="@dimen/text_size_body3_v1"
                            android:textStyle="bold"
                            android:gravity="top"
                            />

                        <TextView
                            android:id="@+id/cubit_scanner_name"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            tools:text=": scanner1-scanner1-scanner1"
                            android:textColor="@color/white"
                            android:textSize="@dimen/text_size_body3_v1"
                            android:textStyle="bold"
                            android:gravity="center_vertical"
                            android:layout_marginStart="3dp"
                            android:visibility="invisible"
                            tools:visibility="visible"
                            />

                        <com.customer.widget.StateButton
                            android:id="@+id/select_cube_scanner_btn"
                            android:layout_width="wrap_content"
                            android:layout_height="@dimen/button_height_v1"
                            android:text="@string/btn_select"
                            android:textSize="@dimen/text_size_body3_v1"
                            android:visibility="gone"
                            app:btnradius="4dp"
                            app:normalBackgroundColor="@color/accent_blue_v1"
                            app:normalTextColor="@color/white"
                            app:pressedBackgroundColor="@color/extended_ocean_blue_700"
                            app:pressedTextColor="@color/text_hint_v1"
                            app:unableBackgroundColor="@color/primary_grey_g700"
                            app:unableTextColor="@color/text_hint_v1"/>

                        <ImageView
                            android:id="@+id/iv_cubit_request"
                            android:layout_width="wrap_content"
                            android:layout_height="28dp"
                            android:src="@drawable/ic_refresh_v1"
                            android:paddingStart="5dp"
                            android:paddingEnd="5dp"
                            android:layout_marginStart="5dp"
                            android:layout_gravity="center_vertical"
                            android:visibility="gone"
                            tools:visibility="visible" />

                        <ImageView
                            android:id="@+id/remove_cube_scanner_iv"
                            android:layout_width="wrap_content"
                            android:layout_height="28dp"
                            android:src="@drawable/ic_close_white_v1"
                            android:paddingStart="5dp"
                            android:paddingEnd="5dp"
                            android:layout_gravity="center_vertical"
                            android:layout_marginStart="5dp"
                            android:visibility="gone"
                            tools:visibility="visible" />

                    </LinearLayout>

                    <com.customer.widget.QuickScanner
                        android:id="@+id/cube_scanner_qs"
                        android:layout_width="match_parent"
                        android:layout_height="@dimen/scanner_height_v1"
                        android:layout_marginTop="8dp"
                        android:layout_marginHorizontal="@dimen/page_horizontal_margin_v1"
                        android:background="@drawable/rect_525252_r4"
                        app:hintText="@string/hint_input_or_scan_equipment"
                        app:hintTextSize="@dimen/text_size_body4_v1"
                        app:layout_res="@layout/view_quick_scanner_v1_blue_icon" />

                    <androidx.appcompat.widget.LinearLayoutCompat
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">
                        <!--UOM Spinner-->
                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:id="@+id/uom_layout"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginEnd="16dp"
                            android:layout_marginStart="@dimen/page_horizontal_margin_v1"
                            android:layout_marginTop="@dimen/panel_body_vertical_padding_v1"
                            android:orientation="vertical">

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/title_uom"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/label_uom"
                                android:textColor="@color/white"
                                android:textSize="@dimen/text_size_sm1_v1"
                                android:textStyle="bold"
                                app:layout_constraintTop_toTopOf="parent"
                                app:layout_constraintStart_toStartOf="parent"/>

                            <com.customer.widget.ExtendedEditText
                                android:id="@+id/uom_et"
                                android:layout_width="match_parent"
                                android:layout_height="46dp"
                                android:background="@drawable/rect_525252_r4"
                                android:hint="@string/select_uom"
                                android:textColor="@color/white"
                                android:gravity="start|center_vertical"
                                android:textColorHint="@color/text_hint_v1"
                                android:textSize="@dimen/text_size_body3_v1"
                                android:focusable="false"
                                android:focusableInTouchMode="false"
                                android:paddingStart="16dp"
                                android:layout_marginTop="8dp"
                                app:layout_constraintTop_toBottomOf="@id/title_uom"
                                app:layout_constraintStart_toStartOf="parent"/>

                            <ImageView
                                android:id="@+id/iv_uom"
                                android:layout_width="16dp"
                                android:layout_height="16dp"
                                android:alpha="0.8"
                                android:background="@drawable/ic_white_triangle_down"
                                android:layout_marginEnd="8dp"
                                app:layout_constraintTop_toTopOf="@id/uom_et"
                                app:layout_constraintEnd_toEndOf="@id/uom_et"
                                app:layout_constraintBottom_toBottomOf="@id/uom_et"/>

                        </androidx.constraintlayout.widget.ConstraintLayout>

                        <!--UOM layer-->
                        <androidx.constraintlayout.widget.ConstraintLayout
                            android:id="@+id/layer_layout"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginTop="@dimen/panel_body_vertical_padding_v1"
                            android:orientation="vertical"
                            android:layout_marginEnd="16dp"
                            android:visibility="gone"
                            tools:visibility="visible"
                            >

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/title_layer"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/text_layer"
                                android:textColor="@color/white"
                                android:textSize="@dimen/text_size_sm1_v1"
                                android:textStyle="bold"
                                app:layout_constraintTop_toTopOf="parent"
                                app:layout_constraintStart_toStartOf="parent"/>

                            <com.customer.widget.ExtendedEditText
                                android:id="@+id/et_layer"
                                android:layout_width="match_parent"
                                android:layout_height="46dp"
                                android:background="@drawable/rect_525252_r4"
                                android:textColor="@color/white"
                                android:gravity="start|center_vertical"
                                android:text="1"
                                android:inputType="number"
                                android:layout_marginTop="8dp"
                                android:paddingStart="16dp"
                                android:textColorHint="@color/text_hint_v1"
                                android:textSize="@dimen/text_size_body3_v1"
                                app:layout_constraintTop_toBottomOf="@id/title_layer"
                                app:layout_constraintStart_toStartOf="parent"/>
                        </androidx.constraintlayout.widget.ConstraintLayout>
                    </androidx.appcompat.widget.LinearLayoutCompat>

                    <!--Base Qty & Inside UOM-->
                    <LinearLayout
                        android:id="@+id/inside_qty_and_inside_uom_layout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/page_horizontal_margin_v1"
                        android:layout_marginTop="8dp"
                        android:orientation="horizontal"
                        android:visibility="gone"
                        tools:visibility="visible">

                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="@dimen/page_horizontal_margin_v1"
                            android:text="@string/inside_uom"
                            android:textColor="@color/white"
                            android:textSize="@dimen/text_size_sm1_v1" />

                        <com.customer.widget.ExtendedEditText
                            android:id="@+id/inside_qty_edit"
                            android:layout_width="0dp"
                            android:layout_height="@dimen/scanner_height_v1"
                            android:layout_weight="1.5"
                            android:background="@drawable/rect_525252_r4"
                            android:gravity="center_vertical"
                            android:hint="@string/hint_input_qty"
                            android:imeOptions="actionDone"
                            android:inputType="number"
                            android:maxLines="1"
                            android:paddingHorizontal="@dimen/page_horizontal_margin_v1"
                            android:textColor="@color/white"
                            android:textColorHint="@color/text_hint_v1"
                            android:textSize="@dimen/text_size_body3_v1" />

                        <androidx.legacy.widget.Space
                            android:layout_width="8dp"
                            android:layout_height="0dp" />

                        <LinearLayout
                            android:id="@+id/inside_uom_select_layout"
                            android:layout_width="0dp"
                            android:layout_height="@dimen/scanner_height_v1"
                            android:layout_gravity="center_vertical"
                            android:layout_weight="2"
                            android:background="@drawable/rect_525252_r4"
                            android:gravity="center_vertical"
                            android:paddingHorizontal="8dp">

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/inside_uom_tv"
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:hint="@string/select_uom"
                                android:maxLines="1"
                                android:textAlignment="center"
                                android:textColor="@color/white"
                                android:textColorHint="@color/white_o30"
                                android:textSize="14sp"
                                app:autoSizeMaxTextSize="14sp"
                                app:autoSizeMinTextSize="10sp"
                                app:autoSizeTextType="uniform" />

                            <ImageView
                                android:id="@+id/iv_uom_inside"
                                android:layout_width="16dp"
                                android:layout_height="16dp"
                                android:alpha="0.8"
                                android:background="@drawable/ic_white_triangle_down" />

                        </LinearLayout>

                    </LinearLayout>

                    <com.google.android.flexbox.FlexboxLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/page_horizontal_margin_half_v1"
                        app:flexWrap="wrap"
                        app:showDivider="middle">

                        <!--Length-->
                        <LinearLayout
                            android:id="@+id/uom_length_layout"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/panel_body_vertical_padding_v1"
                            android:orientation="vertical"
                            android:paddingHorizontal="@dimen/page_horizontal_margin_half_v1"
                            app:layout_flexBasisPercent="50%">

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/title_uom_length"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/length_inch"
                                android:textColor="@color/white"
                                android:textSize="@dimen/text_size_sm1_v1"
                                android:textStyle="bold" />

                            <com.customer.widget.ExtendedEditText
                                android:id="@+id/uom_length_edit"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/scanner_height_v1"
                                android:layout_marginTop="8dp"
                                android:background="@drawable/rect_525252_r4"
                                android:hint="@string/length_inch"
                                android:imeOptions="actionNext"
                                android:inputType="number"
                                android:paddingHorizontal="@dimen/page_horizontal_margin_v1"
                                android:textColor="@color/white"
                                android:textColorHint="@color/text_hint_v1"
                                android:textSize="@dimen/text_size_body3_v1" />

                        </LinearLayout>

                        <!--Width-->
                        <LinearLayout
                            android:id="@+id/uom_width_layout"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/panel_body_vertical_padding_v1"
                            android:orientation="vertical"
                            android:paddingHorizontal="@dimen/page_horizontal_margin_half_v1"
                            app:layout_flexBasisPercent="50%">

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/title_uom_width"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/width_inch"
                                android:textColor="@color/white"
                                android:textSize="@dimen/text_size_sm1_v1"
                                android:textStyle="bold" />

                            <com.customer.widget.ExtendedEditText
                                android:id="@+id/uom_width_edit"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/scanner_height_v1"
                                android:layout_marginTop="8dp"
                                android:background="@drawable/rect_525252_r4"
                                android:hint="@string/width_inch"
                                android:imeOptions="actionNext"
                                android:inputType="number"
                                android:paddingHorizontal="@dimen/page_horizontal_margin_v1"
                                android:textColor="@color/white"
                                android:textColorHint="@color/text_hint_v1"
                                android:textSize="@dimen/text_size_body3_v1" />

                        </LinearLayout>

                        <!--Height-->
                        <LinearLayout
                            android:id="@+id/uom_height_layout"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/panel_body_vertical_padding_v1"
                            android:orientation="vertical"
                            android:paddingHorizontal="@dimen/page_horizontal_margin_half_v1"
                            app:layout_flexBasisPercent="50%">

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/title_uom_height"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/height_inch"
                                android:textColor="@color/white"
                                android:textSize="@dimen/text_size_sm1_v1"
                                android:textStyle="bold" />

                            <com.customer.widget.ExtendedEditText
                                android:id="@+id/uom_height_edit"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/scanner_height_v1"
                                android:layout_marginTop="8dp"
                                android:background="@drawable/rect_525252_r4"
                                android:hint="@string/height_inch"
                                android:imeOptions="actionNext"
                                android:inputType="number"
                                android:paddingHorizontal="@dimen/page_horizontal_margin_v1"
                                android:textColor="@color/white"
                                android:textColorHint="@color/text_hint_v1"
                                android:textSize="@dimen/text_size_body3_v1" />

                        </LinearLayout>

                        <!--Weight-->
                        <LinearLayout
                            android:id="@+id/uom_weight_layout"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="@dimen/panel_body_vertical_padding_v1"
                            android:orientation="vertical"
                            android:paddingHorizontal="@dimen/page_horizontal_margin_half_v1"
                            app:layout_flexBasisPercent="50%">

                            <androidx.appcompat.widget.AppCompatTextView
                                android:id="@+id/title_uom_weight"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/weight_lbs"
                                android:textColor="@color/white"
                                android:textSize="@dimen/text_size_sm1_v1"
                                android:textStyle="bold" />

                            <com.customer.widget.ExtendedEditText
                                android:id="@+id/uom_weight_edit"
                                android:layout_width="match_parent"
                                android:layout_height="@dimen/scanner_height_v1"
                                android:layout_marginTop="8dp"
                                android:background="@drawable/rect_525252_r4"
                                android:hint="@string/weight_lbs"
                                android:imeOptions="actionDone"
                                android:inputType="number"
                                android:paddingHorizontal="@dimen/page_horizontal_margin_v1"
                                android:textColor="@color/white"
                                android:textColorHint="@color/text_hint_v1"
                                android:textSize="@dimen/text_size_body3_v1" />

                        </LinearLayout>

                    </com.google.android.flexbox.FlexboxLayout>

                    <androidx.appcompat.widget.AppCompatButton
                        android:id="@+id/add_uom_button"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:background="@null"
                        android:paddingVertical="12dp"
                        android:paddingStart="12dp"
                        android:paddingEnd="16dp"
                        android:layout_gravity="center_horizontal"
                        android:text="@string/create"
                        android:textAllCaps="true"
                        android:textColor="@color/accent_blue_v1"
                        android:textSize="@dimen/text_size_body4_v1" />

                </LinearLayout>

            </com.customer.widget.CollapsiblePanel>

            <com.customer.widget.CollapsiblePanel
                android:id="@+id/lp_config_collapsible_panel"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/component_vertical_padding_v1"
                android:visibility="visible"
                app:cp_cornerRadius="4dp"
                app:cp_dividerViewId="@+id/lp_config_divider"
                app:cp_hideDividerWhenCollapsed="true"
                app:cp_isCollapsed="false"
                app:cp_showDivider="true"
                app:cp_upArrowViewId="@id/ic_lp_config_collapse_arrow">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/panel_body_v1"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingHorizontal="@dimen/page_horizontal_margin_v1"
                    android:paddingVertical="@dimen/panel_header_vertical_padding_v1">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/label_collect_item_info_lp_config"
                        android:textColor="@color/white"
                        android:textSize="@dimen/text_size_body3_v1"
                        android:textStyle="bold" />

                    <ImageView
                        android:id="@+id/ic_lp_config_collapse_arrow"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/ic_arrow_up"
                        android:tint="@color/white" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/lp_ti_hi_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/panel_body_v1"
                    android:orientation="vertical">

                    <View
                        android:id="@+id/lp_config_divider"
                        android:layout_width="match_parent"
                        android:layout_height="1dp"
                        android:layout_marginHorizontal="@dimen/page_horizontal_margin_v1"
                        android:background="@color/color_525252" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/label_uom"
                        android:textColor="@color/white"
                        android:layout_marginTop="16dp"
                        android:layout_marginHorizontal="16dp"
                        android:textSize="@dimen/text_size_sm1_v1"
                        android:textStyle="bold" />

                    <com.customer.widget.RemovableGridChipView
                        android:id="@+id/uom_group"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:paddingStart="16dp"
                        android:paddingEnd="6dp"
                        app:chip_canCancelWhenSingleChooseMode="false"
                        app:chip_horizontalItemPadding="3dp"
                        app:chip_horizontalItemSpacing="4dp"
                        app:chip_itemHeight="50dp"
                        app:chip_spanCount="3"
                        app:chip_textSize="@dimen/text_size_sm1_v1"
                        app:chip_verticalItemSpacing="4dp"
                        app:chip_singleChoose="true" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/text_ti_hi"
                        android:textColor="@color/white"
                        android:layout_marginTop="16dp"
                        android:layout_marginHorizontal="16dp"
                        android:textSize="@dimen/text_size_sm1_v1"
                        android:textStyle="bold" />

                    <com.customer.widget.RemovableGridChipView
                        android:id="@+id/ti_hi_group"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:paddingStart="16dp"
                        android:paddingEnd="6dp"
                        app:chip_canCancelWhenSingleChooseMode="false"
                        app:chip_horizontalItemPadding="3dp"
                        app:chip_horizontalItemSpacing="4dp"
                        app:chip_itemHeight="50dp"
                        app:chip_spanCount="3"
                        app:chip_textSize="@dimen/text_size_sm1_v1"
                        app:chip_verticalItemSpacing="4dp"
                        app:chip_singleChoose="true" />

                    <androidx.appcompat.widget.AppCompatButton
                        android:id="@+id/add_configuration_button"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:background="@null"
                        android:paddingVertical="12dp"
                        android:layout_gravity="center_horizontal"
                        android:paddingStart="12dp"
                        android:paddingEnd="16dp"
                        android:text="@string/msg_collect_item_info_new_lp_config"
                        android:textAllCaps="true"
                        android:textColor="@color/accent_blue_v1"
                        android:textSize="@dimen/text_size_body4_v1" />
                </LinearLayout>
            </com.customer.widget.CollapsiblePanel>
        </LinearLayout>
    </ScrollView>

    <!--Buttons-->

    <com.customer.widget.StateButton
        android:id="@+id/submit_button"
        android:layout_width="match_parent"
        android:layout_height="@dimen/button_height_v1"
        android:layout_marginHorizontal="@dimen/page_horizontal_margin_v1"
        android:layout_marginTop="@dimen/page_vertical_margin_v1"
        android:text="@string/label_submit"
        android:textSize="@dimen/text_size_body3_v1"
        app:btnradius="4dp"
        app:normalBackgroundColor="@color/accent_blue_v1"
        app:normalTextColor="@color/white"
        app:pressedBackgroundColor="@color/extended_ocean_blue_700"
        app:pressedTextColor="@color/text_hint_v1"
        app:unableBackgroundColor="@color/primary_grey_g700"
        app:unableTextColor="@color/text_hint_v1" />

    <com.customer.widget.StateButton
        android:id="@+id/cancel_button"
        android:layout_width="match_parent"
        android:layout_height="@dimen/button_height_v1"
        android:layout_marginHorizontal="@dimen/page_horizontal_margin_v1"
        android:layout_marginTop="@dimen/chip_vertical_padding_v1"
        android:layout_marginBottom="@dimen/page_vertical_margin_v1"
        android:text="@string/btn_cancel"
        app:btnradius="4dp"
        app:normalStrokeColor="@color/white"
        app:normalStrokeWidth="1dp"
        app:normalTextColor="@color/white"
        app:pressedStrokeColor="@color/white_o75"
        app:pressedStrokeWidth="1dp"
        app:pressedTextColor="@color/white_o75"
        app:unableStrokeColor="@color/white_o30"
        app:unableStrokeWidth="1dp"
        app:unableTextColor="@color/white_o30" />

</LinearLayout>