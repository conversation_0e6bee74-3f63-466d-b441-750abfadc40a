<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:orientation="vertical"
    android:background="@color/page_background_v1">

    <com.google.android.material.appbar.AppBarLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/appBarOverlayStyle">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/color_2c2c2c"
            app:popupTheme="@style/popupOverlayStyle" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:cardBackgroundColor="@android:color/transparent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="8dp"
                android:background="@drawable/rect_393939_r4"
                android:orientation="vertical">

                <com.customer.widget.ScanEditText
                    android:id="@+id/tracking_no_edit"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:maxLines="1"
                    android:minHeight="40dp"
                    android:singleLine="true"
                    android:layout_margin="@dimen/margin_8"
                    android:background="@drawable/rect_525252_r4"
                    android:paddingStart="@dimen/content_padding_left"
                    android:paddingEnd="@dimen/content_padding_right"
                    app:styleModel="atta"/>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginBottom="8dp"
                    android:layout_marginEnd="4dp"
                    android:layout_marginStart="4dp"
                    android:gravity="start"
                    android:orientation="horizontal">


                    <Switch
                        android:id="@+id/fedex_switch"
                        android:layout_width="wrap_content"
                        android:layout_height="30dp"
                        android:layout_gravity="center"
                        android:layout_marginStart="4dp"
                        android:gravity="center" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginStart="4dp"
                        android:text="@string/title_fedex"
                        android:textColor="@color/white"
                        android:textSize="12sp" />

                    <LinearLayout
                        android:id="@+id/digit_layout"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:orientation="horizontal"
                        android:visibility="invisible">

                        <androidx.appcompat.widget.AppCompatEditText
                            android:id="@+id/tracking_no_length_edt"
                            style="@style/editInputStyle"
                            android:layout_width="60dp"
                            android:layout_height="24dp"
                            android:layout_gravity="center"
                            android:layout_marginStart="20dp"
                            android:background="@drawable/rect_525252_r4"
                            android:gravity="center"
                            android:inputType="number" />

                        <androidx.appcompat.widget.AppCompatTextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_gravity="center"
                            android:layout_marginEnd="16dp"
                            android:layout_marginStart="4dp"
                            android:text="@string/title_digit"
                            android:textColor="@color/white"
                            android:textSize="12sp" />

                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>


        </LinearLayout>

    </androidx.cardview.widget.CardView>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:layout_marginTop="12dp"
        android:orientation="horizontal">

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:text="@string/title_tracking_no_count_mark"
            android:textColor="@color/white"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tracking_no_count_txt"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:textColor="@color/white"/>

    </LinearLayout>

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginEnd="8dp"
        android:layout_marginStart="8dp"/>

</LinearLayout>