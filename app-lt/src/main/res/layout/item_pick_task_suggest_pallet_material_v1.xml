<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="25dp"
    android:gravity="center"
    android:orientation="horizontal">

    <HorizontalScrollView
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="0.7">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/item_txt"
            style="@style/textTaskSubBody"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center" />
    </HorizontalScrollView>

    <include
        layout="@layout/divider_white_1dp"
        android:layout_width="1dp"
        android:layout_height="12dp" />

    <HorizontalScrollView
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="0.4">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/pallet_qty_txt"
            style="@style/textTaskSubBody"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:gravity="center" />
    </HorizontalScrollView>

    <include
        layout="@layout/divider_white_1dp"
        android:layout_width="1dp"
        android:layout_height="12dp" />

    <HorizontalScrollView
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_marginStart="5dp"
        android:layout_weight="1.2">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/suggest_pallet_material_txt"
            style="@style/textTaskSubBody"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:gravity="center" />
    </HorizontalScrollView>

    <include
        layout="@layout/divider_white_1dp"
        android:layout_width="1dp"
        android:layout_height="12dp" />

    <HorizontalScrollView
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="0.3">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/stock_high_txt"
            style="@style/textTaskSubBody"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_gravity="center"
            android:layout_marginTop="3dp"
            />
    </HorizontalScrollView>
</LinearLayout>