<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:paddingLeft="20dp"
    android:paddingTop="20dp"
    android:paddingRight="20dp"
    android:paddingBottom="10dp">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="15dp"
        android:textSize="@dimen/font_size_large"
        tools:text="Name(xxxx)" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_customer"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:textSize="@dimen/font_size_middle"
        tools:text="Customer：xxxxx" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_job"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="@dimen/font_size_middle"
        android:layout_marginBottom="8dp"
        tools:text="Job Code：xxxxx" />

    <LinearLayout
        android:id="@+id/ll_estimated_time"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="start"
        android:orientation="horizontal"
        android:visibility="gone">

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxLines="1"
            android:text="@string/general_task_estimated_time"
            android:textSize="@dimen/font_size_middle" />

        <androidx.appcompat.widget.AppCompatEditText
            android:id="@+id/edt_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="@dimen/font_size_middle"
            android:inputType="number"
            android:maxLines="1"
            android:layout_weight="1"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/minutes"
            android:maxLines="1"
            android:textSize="@dimen/font_size_middle" />

    </LinearLayout>

    <com.customer.widget.autocomplete.AutoCompleteWidget
        android:id="@+id/edt_assignee"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:layout_gravity="center_vertical"
        android:layout_marginTop="15dp"
        android:visibility="gone"/>

    <LinearLayout
        android:id="@+id/ll_assign_label"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginTop="10dp"
        tools:visibility="visible"
        android:visibility="gone">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_label_assignee"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="5dp"
            android:singleLine="true"
            android:ellipsize="end"
            android:textStyle="bold"
            android:textSize="15sp"
            android:text="@string/label_assignee_text"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="10dp"
            android:textSize="15sp"
            android:textStyle="bold"
            android:textAlignment="center"
            android:text="@string/on_hand_task"/>

    </LinearLayout>


    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_view"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:layout_marginTop="10dp"
        android:visibility="gone"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:orientation="horizontal">

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/btn_cancel"
            android:layout_width="0dp"
            android:layout_height="35dp"
            android:layout_weight="1"
            android:textAllCaps="false"
            android:background="@color/gray_cccc"
            android:text="@string/btn_cancel" />


        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/btn_create"
            android:layout_width="0dp"
            android:layout_height="35dp"
            android:layout_weight="1"
            android:layout_marginStart="20dp"
            android:enabled="false"
            android:textColor="@color/selector_white_gray_enable"
            android:background="@drawable/selector_green_dark_enable"
            android:text="@string/create" />
    </LinearLayout>

</LinearLayout>