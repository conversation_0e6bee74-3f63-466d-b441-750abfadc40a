<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
android:id="@+id/location_layout"
android:layout_width="match_parent"
android:layout_height="48dp"
android:orientation="horizontal"
android:background="?attr/selectableItemBackground">

<TextView
    android:id="@+id/location_txt"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_weight="1"
    android:layout_gravity="center"
    android:paddingLeft="8dp"
    android:text="dock-1"
    android:textSize="18sp" />

<CheckBox
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:id="@+id/select_chk"
    android:layout_gravity="center"
    />

</LinearLayout>
