<?xml version="1.0" encoding="utf-8"?>

<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:id="@+id/root_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?attr/selectableItemBackground"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/indicate_layout"
            android:layout_width="match_parent"
            android:layout_height="8dp"
            android:background="@color/colorPrimary"
            android:orientation="vertical" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/item_detail_txt"
                style="@style/labelTextMiddle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginStart="4dp"
                android:layout_weight="0.3" />

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/select_btn"
                style="@style/flatButtonStyle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:layout_weight="1"
                android:gravity="center"
                android:text="@string/btn_select"
                android:visibility="gone" />
        </LinearLayout>

        <include layout="@layout/divider_gray_1dp" />

        <LinearLayout
            android:id="@+id/sn_root_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <androidx.appcompat.widget.AppCompatTextView
                    style="@style/labelTextMiddle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="4dp"
                    android:text="@string/sn_amount" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/sn_amount_txt"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:text="@string/sn_amount" />
            </LinearLayout>

            <include
                layout="@layout/divider_gray_1dp"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginStart="40dp" />


            <LinearLayout
                android:id="@+id/sn_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:layout_marginBottom="8dp"
                android:layout_marginStart="46dp"
                android:gravity="end"
                android:orientation="vertical" />
        </LinearLayout>


    </LinearLayout>
</androidx.cardview.widget.CardView>
