<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView
    android:layout_height="wrap_content"
    android:layout_width="match_parent"
    xmlns:android="http://schemas.android.com/apk/res/android">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/black"
            android:textColor="@color/white"
            android:id="@+id/item_no_txt"
            android:paddingLeft="5dp"
            android:gravity="left|center_vertical"
            android:paddingStart="@dimen/content_padding_left"
            android:text="Item"
            android:textSize="20sp"
            />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:id="@+id/no_sn_layout"
            android:paddingLeft="@dimen/content_padding_left"
            >

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/label_add_qty"
                android:textSize="18sp"
                />

            <EditText
                android:layout_width="150dp"
                android:layout_height="wrap_content"
                android:id="@+id/qty_editText"
                android:layout_marginLeft="5dp"
                />
        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:id="@+id/with_sn_layout"
            android:visibility="gone"
            android:paddingLeft="@dimen/content_padding_left"
            >

            <EditText
                android:id="@+id/search_edt"
                android:layout_width="210dp"
                android:layout_height="wrap_content"
                android:drawableRight="@drawable/ic_edittext_scan"
                android:hint="@string/hint_scan_or_input_add"
                android:textColorHint="@color/hint_color"/>

        </LinearLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingLeft="@dimen/content_padding_left"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="10dp"
            >

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/text_sn"
                android:id="@+id/label_sn"
                android:layout_alignParentLeft="true"
                android:textSize="18sp"
                android:layout_marginTop="11dp"
                />

            <RadioButton
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:id="@+id/sn_radioButton"
                android:layout_toRightOf="@+id/label_sn"
                android:textSize="18sp"
                android:layout_marginLeft="5dp"
                android:layout_marginTop="7dp"
                android:clickable="false"
                />

            <Button
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:id="@+id/confirm_btn"
                android:text="@string/btn_confirm"
                style="@style/raisedButtonStyle"
                android:layout_alignParentRight="true"
                android:layout_marginRight="5dp"
                />

        </RelativeLayout>


    </LinearLayout>

</androidx.cardview.widget.CardView>
