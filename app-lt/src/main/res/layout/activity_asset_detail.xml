<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/page_background_v1">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/toolbar_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <com.customer.widget.CenterTitleToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/color_2c2c2c"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/asset_id_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/page_horizontal_margin_v1"
                android:layout_marginVertical="@dimen/page_horizontal_margin_v1"
                android:textSize="22sp"
                android:textColor="@color/white"
                tools:text="Asset-1"/>

            <com.lt.linc.common.LabelTextInputView
                android:id="@+id/category_ltiv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/page_horizontal_margin_v1"
                app:ltiv_background_disable="@drawable/rect_525252_r4"
                app:ltiv_labelText="@string/title_progress_table_category"
                app:ltiv_enable="false"
                app:ltiv_type="input"/>

            <com.lt.linc.common.LabelTextInputView
                android:id="@+id/item_ltiv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:layout_marginBottom="16dp"
                android:layout_marginHorizontal="@dimen/page_horizontal_margin_v1"
                app:ltiv_background_disable="@drawable/rect_525252_r4"
                app:ltiv_labelText="@string/text_item"
                app:ltiv_enable="false"
                app:ltiv_type="input"/>

            <include
                android:id="@+id/asset_item_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/page_horizontal_margin_v1"
                layout="@layout/layout_asset_item_view" />

            <com.customer.widget.CollapsiblePanel
                android:id="@+id/asset_detail_collapsible_panel"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/component_vertical_padding_v1"
                android:visibility="visible"
                android:layout_marginHorizontal="@dimen/page_horizontal_margin_v1"
                app:cp_cornerRadius="4dp"
                app:cp_isCollapsed="true"
                app:cp_upArrowViewId="@id/asset_detail_collapse_arrow">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/panel_body_v1"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingHorizontal="@dimen/page_horizontal_margin_v1"
                    android:paddingVertical="@dimen/panel_header_vertical_padding_v1">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/title_asset_detail"
                        android:textColor="@color/white"
                        android:textSize="@dimen/text_size_body3_v1"
                        android:textStyle="bold" />

                    <ImageView
                        android:id="@+id/asset_detail_collapse_arrow"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/ic_arrow_up_white"
                        android:tint="@color/white" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/asset_detail_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/panel_body_v1"
                    android:visibility="visible"
                    android:orientation="vertical">

                    <com.lt.linc.common.LabelTextInputView
                        android:id="@+id/facility_ltiv"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:layout_marginHorizontal="@dimen/page_horizontal_margin_v1"
                        app:ltiv_background_disable="@drawable/rect_525252_r4"
                        app:ltiv_labelText="@string/label_facility"
                        app:ltiv_enable="false"
                        app:ltiv_type="select"/>

                    <com.lt.linc.common.LabelTextInputView
                        android:id="@+id/using_company_ltiv"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:layout_marginHorizontal="@dimen/page_horizontal_margin_v1"
                        app:ltiv_background_disable="@drawable/rect_525252_r4"
                        app:ltiv_labelText="@string/hint_using_company"
                        app:ltiv_enable="false"
                        app:ltiv_type="select"/>

                    <com.lt.linc.common.LabelTextInputView
                        android:id="@+id/ownership_type_ltiv"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:layout_marginHorizontal="@dimen/page_horizontal_margin_v1"
                        app:ltiv_background_disable="@drawable/rect_525252_r4"
                        app:ltiv_labelText="@string/text_ownership_type"
                        app:ltiv_enable="false"
                        app:ltiv_type="select"/>

                    <com.lt.linc.common.LabelTextInputView
                        android:id="@+id/using_customer_ltiv"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:layout_marginHorizontal="@dimen/page_horizontal_margin_v1"
                        app:ltiv_background_disable="@drawable/rect_525252_r4"
                        app:ltiv_labelText="@string/text_using_customer"
                        app:ltiv_enable="false"
                        app:ltiv_type="select"/>

                    <com.lt.linc.common.LabelTextInputView
                        android:id="@+id/condition_ltiv"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:layout_marginHorizontal="@dimen/page_horizontal_margin_v1"
                        app:ltiv_background_disable="@drawable/rect_525252_r4"
                        app:ltiv_labelText="@string/label_condition"
                        app:ltiv_enable="false"
                        app:ltiv_type="select"/>

                    <com.lt.linc.common.LabelTextInputView
                        android:id="@+id/location_ltiv"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:layout_marginHorizontal="@dimen/page_horizontal_margin_v1"
                        app:ltiv_background_disable="@drawable/rect_525252_r4"
                        app:ltiv_labelText="@string/label_location"
                        app:ltiv_enable="false"
                        app:ltiv_type="select"/>

                    <com.lt.linc.common.LabelTextInputView
                        android:id="@+id/vendor_ltiv"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:layout_marginHorizontal="@dimen/page_horizontal_margin_v1"
                        app:ltiv_background_disable="@drawable/rect_525252_r4"
                        app:ltiv_labelText="@string/text_vendor"
                        app:ltiv_enable="false"
                        app:ltiv_type="input"/>

                    <com.lt.linc.common.LabelTextInputView
                        android:id="@+id/purchase_date_ltiv"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:layout_marginHorizontal="@dimen/page_horizontal_margin_v1"
                        app:ltiv_background_disable="@drawable/rect_525252_r4"
                        app:ltiv_labelText="@string/text_purchase_date"
                        app:ltiv_enable="false"
                        app:ltiv_type="date"/>

                    <com.lt.linc.common.LabelTextAreaView
                        android:id="@+id/asset_description_ltarv"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:layout_marginHorizontal="@dimen/page_horizontal_margin_v1"
                        app:ltarv_background_disable="@drawable/rect_525252_r4"
                        app:ltarv_labelText="@string/text_asset_description"
                        app:ltarv_enable="false"/>

                    <com.lt.linc.common.LabelTextAreaView
                        android:id="@+id/notes_ltarv"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:layout_marginBottom="16dp"
                        android:layout_marginHorizontal="@dimen/page_horizontal_margin_v1"
                        app:ltarv_background_disable="@drawable/rect_525252_r4"
                        app:ltarv_labelText="@string/text_notes"
                        app:ltarv_enable="false"/>

                </LinearLayout>
            </com.customer.widget.CollapsiblePanel>

            <com.customer.widget.CollapsiblePanel
                android:id="@+id/accounting_information_collapsible_panel"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/component_vertical_padding_v1"
                android:layout_marginHorizontal="@dimen/page_horizontal_margin_v1"
                android:visibility="gone"
                tools:visibility="visible"
                app:cp_cornerRadius="4dp"
                app:cp_isCollapsed="true"
                app:cp_upArrowViewId="@id/accounting_information_collapse_arrow">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/panel_body_v1"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingHorizontal="@dimen/page_horizontal_margin_v1"
                    android:paddingVertical="@dimen/panel_header_vertical_padding_v1">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/text_accounting_information"
                        android:textColor="@color/white"
                        android:textSize="@dimen/text_size_body3_v1"
                        android:textStyle="bold" />

                    <ImageView
                        android:id="@+id/accounting_information_collapse_arrow"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/ic_arrow_up_white"
                        android:tint="@color/white" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/accounting_information_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/panel_body_v1"
                    android:visibility="visible"
                    android:orientation="vertical">

                    <com.lt.linc.common.LabelTextInputView
                        android:id="@+id/ownership_ltiv"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:layout_marginHorizontal="@dimen/page_horizontal_margin_v1"
                        app:ltiv_background_disable="@drawable/rect_525252_r4"
                        app:ltiv_labelText="@string/text_ownership"
                        app:ltiv_enable="false"
                        app:ltiv_type="select"/>

                    <com.lt.linc.common.LabelTextInputView
                        android:id="@+id/fixed_asset_ltiv"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:layout_marginHorizontal="@dimen/page_horizontal_margin_v1"
                        app:ltiv_background_disable="@drawable/rect_525252_r4"
                        app:ltiv_labelText="@string/text_fixed_asset"
                        app:ltiv_enable="false"
                        app:ltiv_type="select"/>

                    <com.lt.linc.common.LabelTextInputView
                        android:id="@+id/accounting_status_ltiv"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:layout_marginHorizontal="@dimen/page_horizontal_margin_v1"
                        app:ltiv_background_disable="@drawable/rect_525252_r4"
                        app:ltiv_labelText="@string/text_accounting_status"
                        app:ltiv_enable="false"
                        app:ltiv_type="select"/>

                    <com.lt.linc.common.LabelTextInputView
                        android:id="@+id/accounting_id_ltiv"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:layout_marginHorizontal="@dimen/page_horizontal_margin_v1"
                        app:ltiv_background_disable="@drawable/rect_525252_r4"
                        app:ltiv_labelText="@string/text_accounting_id"
                        app:ltiv_enable="false"
                        app:ltiv_type="input"/>

                    <com.lt.linc.common.LabelTextInputView
                        android:id="@+id/cost_ltiv"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:layout_marginHorizontal="@dimen/page_horizontal_margin_v1"
                        app:ltiv_background_disable="@drawable/rect_525252_r4"
                        app:ltiv_labelText="@string/text_cost"
                        app:ltiv_enable="false"
                        app:ltiv_type="input"/>

                    <com.lt.linc.common.LabelTextInputView
                        android:id="@+id/purchase_order_number_ltiv"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:layout_marginBottom="16dp"
                        android:layout_marginHorizontal="@dimen/page_horizontal_margin_v1"
                        app:ltiv_background_disable="@drawable/rect_525252_r4"
                        app:ltiv_labelText="@string/text_purchase_order_number"
                        app:ltiv_enable="false"
                        app:ltiv_type="input"/>

                </LinearLayout>
            </com.customer.widget.CollapsiblePanel>

            <com.customer.widget.CollapsiblePanel
                android:id="@+id/asset_photo_collapsible_panel"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/component_vertical_padding_v1"
                android:visibility="visible"
                android:layout_marginHorizontal="@dimen/page_horizontal_margin_v1"
                app:cp_cornerRadius="4dp"
                app:cp_isCollapsed="true"
                app:cp_upArrowViewId="@id/asset_photo_collapse_arrow">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/panel_body_v1"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingHorizontal="@dimen/page_horizontal_margin_v1"
                    android:paddingVertical="@dimen/panel_header_vertical_padding_v1">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/text_asset_photo"
                        android:textColor="@color/white"
                        android:textSize="@dimen/text_size_body3_v1"
                        android:textStyle="bold" />

                    <ImageView
                        android:id="@+id/asset_photo_collapse_arrow"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/ic_arrow_up_white"
                        android:tint="@color/white" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/asset_photo_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/panel_body_v1"
                    android:visibility="visible"
                    android:orientation="vertical">

                    <com.lt.linc.util.v1widget.UploadFileV1Widget
                        android:id="@+id/asset_upload_photo_widget"
                        android:layout_width="match_parent"
                        android:layout_height="100dp"
                        android:layout_marginTop="10dp"
                        android:layout_marginBottom="16dp"
                        android:layout_marginHorizontal="@dimen/page_horizontal_margin_v1"
                        app:deleteConfirmText="@string/question_remove_photo" />

                </LinearLayout>
            </com.customer.widget.CollapsiblePanel>

            <com.customer.widget.CollapsiblePanel
                android:id="@+id/additional_inventory_detail_collapsible_panel"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/component_vertical_padding_v1"
                android:visibility="gone"
                tools:visibility="visible"
                android:layout_marginHorizontal="@dimen/page_horizontal_margin_v1"
                app:cp_cornerRadius="4dp"
                app:cp_isCollapsed="true"
                app:cp_upArrowViewId="@id/additional_inventory_detail_collapse_arrow">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/panel_body_v1"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:paddingHorizontal="@dimen/page_horizontal_margin_v1"
                    android:paddingVertical="@dimen/panel_header_vertical_padding_v1">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="@string/text_additional_inventory_detail"
                        android:textColor="@color/white"
                        android:textSize="@dimen/text_size_body3_v1"
                        android:textStyle="bold" />

                    <ImageView
                        android:id="@+id/additional_inventory_detail_collapse_arrow"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:src="@drawable/ic_arrow_up_white"
                        android:tint="@color/white" />

                </LinearLayout>

                <LinearLayout
                    android:id="@+id/additional_inventory_detail_layout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/panel_body_v1"
                    android:visibility="visible"
                    android:orientation="vertical"
                    android:paddingBottom="16dp">

                </LinearLayout>
            </com.customer.widget.CollapsiblePanel>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

    <LinearLayout
        android:id="@+id/bottom_option_ll"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:padding="15dp"
        android:orientation="horizontal"
        android:background="@color/color_2c2c2c"
        android:visibility="gone"
        tools:visibility="visible">

        <com.customer.widget.StateButton
            android:id="@+id/cancel_btn"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_weight="1"
            android:maxLines="1"
            android:text="@string/text_cancel"
            android:textSize="@dimen/text_size_body3_v1"
            android:visibility="gone"
            tools:visibility="visible"
            android:layout_marginEnd="@dimen/page_horizontal_margin_v1"
            app:btnradius="4dp"
            app:normalStrokeColor="@color/white"
            app:normalStrokeWidth="1dp"
            app:normalTextColor="@color/white"
            app:pressedStrokeColor="@color/white"
            app:pressedStrokeWidth="1dp"
            app:pressedTextColor="@color/white"
            app:unableStrokeColor="@color/color_323232"
            app:unableStrokeWidth="1dp"
            app:unableTextColor="@color/color_88898B" />

        <com.customer.widget.StateButton
            android:id="@+id/edit_save_btn"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_weight="1"
            android:maxLines="1"
            android:text="@string/text_edit"
            android:textSize="@dimen/text_size_body3_v1"
            app:btnradius="4dp"
            app:normalBackgroundColor="@color/accent_blue_v1"
            app:normalTextColor="@color/white"
            app:pressedBackgroundColor="@color/accent_blue_v1_o80"
            app:pressedTextColor="@color/white"
            app:unableBackgroundColor="@color/color_323232"
            app:unableTextColor="@color/color_88898B" />

    </LinearLayout>

</LinearLayout>