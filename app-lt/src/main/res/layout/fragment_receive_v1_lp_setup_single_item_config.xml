<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/page_background_v1"
    android:orientation="vertical">

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <com.google.android.material.appbar.AppBarLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@null"
            android:paddingHorizontal="@dimen/page_horizontal_margin_v1"
            app:elevation="0dp">

            <!--Item <PERSON>lter <PERSON>-->
            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingTop="@dimen/page_vertical_margin_v1"
                android:text="@string/item_config"
                android:textColor="@color/white"
                android:textSize="@dimen/text_size_heading1_v1"
                android:textStyle="bold"
                app:layout_scrollFlags="scroll" />

            <com.lt.linc.receive_v1.lpsetup.work.singleitem.itemconfig.components.FilterScanner
                android:id="@+id/item_filter_scanner"
                android:layout_width="match_parent"
                android:layout_height="@dimen/scanner_height_v1"
                android:layout_marginTop="@dimen/component_vertical_padding_v1"
                android:layout_marginBottom="@dimen/page_vertical_margin_v1"
                android:gravity="center_vertical"
                app:hintText="@string/hint_please_scan_item_label"
                app:hintTextSize="@dimen/text_size_body3_v1"
                app:layout_res="@layout/view_filter_scanner"
                app:layout_scrollFlags="scroll|enterAlways" />

        </com.google.android.material.appbar.AppBarLayout>

        <!--Item List-->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/item_recycler_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_behavior="com.google.android.material.appbar.AppBarLayout$ScrollingViewBehavior"
            tools:itemCount="2"
            tools:listfooter="@layout/item_receive_v1_lp_setup_add_new_item"
            tools:listitem="@layout/item_receive_v1_lp_setup_single_item_config" />

        <com.customer.widget.StateButton
            android:id="@+id/collapse_all_button"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/button_height_v1"
            android:layout_gravity="end|bottom"
            android:layout_marginEnd="16dp"
            android:layout_marginBottom="32dp"
            android:clickable="true"
            android:drawableStart="@drawable/ic_collapse"
            android:drawablePadding="6dp"
            android:focusable="true"
            android:paddingHorizontal="12dp"
            android:text="@string/collapse_all"
            android:textAllCaps="false"
            android:textSize="@dimen/text_size_sm1_v1"
            app:btnradius="4dp"
            app:elevation="1dp"
            app:normalBackgroundColor="@color/accent_blue_v1"
            app:normalTextColor="@color/white"
            app:pressedBackgroundColor="@color/extended_ocean_blue_700"
            app:pressedTextColor="@color/white"
            app:unableBackgroundColor="@color/primary_grey_g700"
            app:unableTextColor="@color/white_o30" />

    </androidx.coordinatorlayout.widget.CoordinatorLayout>


    <!--Save Configuration Button-->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/color_2e2e2e"
        android:paddingVertical="@dimen/panel_body_vertical_padding_v1"
        android:orientation="horizontal">

        <com.customer.widget.StateButton
            android:id="@+id/receive_all_button"
            android:layout_width="0dp"
            android:layout_height="@dimen/button_height_v1"
            android:layout_weight="1"
            android:maxLines="1"
            android:layout_marginStart="16dp"
            android:text="@string/text_receive_all"
            android:textSize="@dimen/text_size_body3_v1"
            android:visibility="gone"
            app:btnradius="4dp"
            app:normalStrokeColor="@color/white"
            app:normalStrokeWidth="1dp"
            app:normalTextColor="@color/white"
            app:pressedStrokeColor="@color/text_hint_v1"
            app:pressedStrokeWidth="1dp"
            app:pressedTextColor="@color/white"
            app:unableStrokeColor="@color/text_hint_v1"
            app:unableStrokeWidth="1dp"
            app:unableTextColor="@color/text_hint_v1" />

        <com.customer.widget.StateButton
            android:id="@+id/complete_button"
            android:layout_width="0dp"
            android:layout_height="@dimen/button_height_v1"
            android:layout_weight="1"
            android:layout_marginHorizontal="16dp"
            android:text="@string/complete"
            android:textSize="@dimen/text_size_body3_v1"
            app:btnradius="4dp"
            app:normalBackgroundColor="@color/accent_blue_v1"
            app:normalTextColor="@color/white"
            app:pressedBackgroundColor="@color/extended_ocean_blue_700"
            app:pressedTextColor="@color/text_hint_v1"
            app:unableBackgroundColor="@color/primary_grey_g700"
            app:unableTextColor="@color/text_hint_v1" />

    </LinearLayout>

</LinearLayout>