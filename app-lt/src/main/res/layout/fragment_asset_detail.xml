<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/toolbar_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
        android:visibility="gone">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/color_2c2c2c"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/color_222222"
        android:paddingTop="10dp"
        android:paddingStart="10dp"
        android:paddingEnd="10dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingBottom="10dp"
            android:paddingTop="10dp"
            android:paddingRight="10dp"
            android:paddingLeft="10dp"
            android:background="@drawable/rect_373737_4"
            app:layout_constraintTop_toTopOf="parent">

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingTop="10dp"
                android:paddingBottom="10dp">

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/label_name"
                    android:textColor="@color/color_9e9e9e"
                    android:textSize="15sp"/>

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/name_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="end"
                    tools:text="asset name"
                    android:textColor="@color/white"
                    android:textSize="15sp"/>

            </FrameLayout>

            <include layout="@layout/divider_gray_1dp" />

            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingTop="10dp"
                android:paddingBottom="10dp">

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/label_status"
                    android:textColor="@color/color_9e9e9e"
                    android:textSize="15sp"/>

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/status_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="end"
                    android:gravity="center_vertical"
                    android:drawablePadding="5dp"
                    android:drawableEnd="@drawable/ic_info_more"
                    tools:text="status"
                    android:textColor="@color/white"
                    android:textSize="15sp"/>

            </FrameLayout>

            <include layout="@layout/divider_gray_1dp" />

            <LinearLayout
                android:id="@+id/task_id_ll"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:visibility="gone">

                <FrameLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingTop="10dp"
                    android:paddingBottom="10dp">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/label_task_id"
                        android:textColor="@color/color_9e9e9e"
                        android:textSize="15sp"/>

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/task_id_tv"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="end"
                        android:gravity="center_vertical"
                        android:drawablePadding="5dp"
                        android:drawableEnd="@drawable/ic_info_more"
                        tools:text="task id"
                        android:textColor="@color/white"
                        android:textSize="15sp"/>

                </FrameLayout>

                <include
                    android:id="@+id/task_id_divider"
                    layout="@layout/divider_gray_1dp" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingTop="10dp"
                android:paddingBottom="10dp">

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/description_label_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/label_description"
                    android:textColor="@color/color_9e9e9e"
                    android:textSize="15sp"/>

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/description_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    tools:text="description"
                    android:textColor="@color/white"
                    android:textSize="15sp"/>

            </LinearLayout>

            <View
                android:id="@+id/child_asset_divider"
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/gray_line"
                android:visibility="gone"/>

            <FrameLayout
                android:id="@+id/child_asset_fl"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingTop="10dp"
                android:paddingBottom="10dp"
                android:visibility="gone">

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/child_asset_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:text="@string/text_child_asset"
                    android:textSize="15sp"
                    android:textColor="@color/color_9e9e9e"/>

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/child_arrow_iv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical|end"
                    android:src="@drawable/ic_arrow_right_white"/>
            </FrameLayout>

        </LinearLayout>

        <com.customer.widget.StateButton
            android:id="@+id/check_out_btn"
            style="@style/raisedButtonStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="10dp"
            android:text="@string/text_check_out"
            android:textColor="@color/white"
            android:textSize="14sp"
            android:visibility="gone"
            app:btnradius="4dp"
            app:normalBackgroundColor="@color/accent_blue_v1"
            app:normalTextColor="@color/white"
            app:pressedBackgroundColor="@color/accent_blue_v1"
            app:pressedTextColor="@color/white"
            app:unableBackgroundColor="@color/color_800E7DDF"
            app:unableTextColor="@color/white_o30"
            app:layout_constraintBottom_toTopOf="@+id/assign_asset_btn"/>

        <com.customer.widget.StateButton
            android:id="@+id/assign_asset_btn"
            style="@style/raisedButtonStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="10dp"
            android:text="@string/text_assign_asset"
            android:textColor="@color/white"
            android:textSize="14sp"
            android:visibility="gone"
            app:btnradius="4dp"
            app:normalBackgroundColor="@color/accent_blue_v1"
            app:normalTextColor="@color/white"
            app:pressedBackgroundColor="@color/accent_blue_v1"
            app:pressedTextColor="@color/white"
            app:unableBackgroundColor="@color/color_800E7DDF"
            app:unableTextColor="@color/white_o30"
            app:layout_constraintBottom_toTopOf="@+id/unassign_asset_btn"/>

        <com.customer.widget.StateButton
            android:id="@+id/unassign_asset_btn"
            style="@style/raisedButtonStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="10dp"
            android:text="@string/text_unassign_asset"
            android:textColor="@color/white"
            android:textSize="14sp"
            android:visibility="gone"
            app:btnradius="4dp"
            app:normalBackgroundColor="@color/accent_blue_v1"
            app:normalTextColor="@color/white"
            app:pressedBackgroundColor="@color/accent_blue_v1"
            app:pressedTextColor="@color/white"
            app:unableBackgroundColor="@color/color_800E7DDF"
            app:unableTextColor="@color/white_o30"
            app:layout_constraintBottom_toTopOf="@+id/move_btn"/>

        <com.customer.widget.StateButton
            android:id="@+id/move_btn"
            style="@style/raisedButtonStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="10dp"
            android:text="@string/text_move"
            android:textColor="@color/white"
            android:textSize="14sp"
            android:visibility="gone"
            app:btnradius="4dp"
            app:normalBackgroundColor="@color/accent_blue_v1"
            app:normalTextColor="@color/white"
            app:pressedBackgroundColor="@color/accent_blue_v1"
            app:pressedTextColor="@color/white"
            app:unableBackgroundColor="@color/color_800E7DDF"
            app:unableTextColor="@color/white_o30"
            app:layout_constraintBottom_toTopOf="@+id/print_barcode_btn"/>

        <com.customer.widget.StateButton
            android:id="@+id/print_barcode_btn"
            style="@style/raisedButtonStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="10dp"
            android:text="@string/text_print_barcode"
            android:textColor="@color/white"
            android:textSize="14sp"
            android:visibility="gone"
            app:btnradius="4dp"
            app:normalBackgroundColor="@color/accent_blue_v1"
            app:normalTextColor="@color/white"
            app:pressedBackgroundColor="@color/accent_blue_v1"
            app:pressedTextColor="@color/white"
            app:unableBackgroundColor="@color/color_800E7DDF"
            app:unableTextColor="@color/white_o30"
            app:layout_constraintBottom_toTopOf="@+id/cancel_btn"/>

        <com.customer.widget.StateButton
            android:id="@+id/cancel_btn"
            style="@style/raisedButtonStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="10dp"
            android:text="@string/text_cancel"
            android:textColor="@color/white"
            android:textSize="14sp"
            app:btnradius="4dp"
            app:normalBackgroundColor="@color/color_373737"
            app:normalTextColor="@color/white"
            app:pressedBackgroundColor="@color/color_373737"
            app:pressedTextColor="@color/white"
            app:unableBackgroundColor="@color/color_800E7DDF"
            app:unableTextColor="@color/white_o30"
            app:layout_constraintBottom_toBottomOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

</LinearLayout>

