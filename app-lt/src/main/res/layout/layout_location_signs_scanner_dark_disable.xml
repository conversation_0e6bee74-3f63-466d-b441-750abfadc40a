<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:layout_margin="10dp"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <LinearLayout
        android:id="@+id/ll_location"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <com.customer.widget.QuickScanner
            android:id="@+id/location_scanner"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            app:isNewModel="true"
            app:hintText="@string/msg_please_scan_or_input_location"
            app:layout_res="@layout/view_quick_scanner_pick_v1_disable"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_location_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="10dp"
            android:textColor="@color/black"
            android:text="@string/text_location"/>

    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_aisle_bay"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:orientation="horizontal">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_aisle_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:textColor="@color/black"
            android:text="@string/text_aisle"/>

        <com.customer.widget.QuickScanner
            android:id="@+id/aisle_scanner"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="10dp"
            app:isNewModel="true"
            app:layout_res="@layout/view_quick_scanner_pick_v1_disable"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_bay_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="10dp"
            android:textColor="@color/black"
            android:text="@string/text_bay"/>

        <com.customer.widget.QuickScanner
            android:id="@+id/bay_scanner"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="10dp"
            app:isNewModel="true"
            app:layout_res="@layout/view_quick_scanner_pick_v1_disable"/>

    </LinearLayout>

</LinearLayout>