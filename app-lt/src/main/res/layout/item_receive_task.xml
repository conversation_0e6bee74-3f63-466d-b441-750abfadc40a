<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="10dp"
    android:background="@color/white"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_item_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_toStartOf="@+id/tv_qty"
        android:singleLine="true"
        android:ellipsize="end"
        android:textColor="@color/black"
        android:textStyle="bold"
        android:textSize="18sp"
        tools:text="Item AItem"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_sn_and_condition"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_toStartOf="@+id/tv_qty"
        android:singleLine="true"
        android:ellipsize="end"
        android:layout_marginTop="10dp"
        android:layout_below="@+id/tv_item_name"
        android:textColor="@color/black"
        android:textSize="16sp"
        tools:text="sn253u4lfdsf | GOOD"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_lp_and_location"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_toStartOf="@+id/tv_qty"
        android:singleLine="true"
        android:ellipsize="end"
        android:layout_marginTop="10dp"
        android:layout_below="@+id/tv_sn_and_condition"
        android:textColor="@color/black"
        android:textSize="16sp"
        tools:text="ILP-89697 | 23.89.67"/>

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/iv_remove"
        android:layout_width="35dp"
        android:layout_height="35dp"
        android:src="@drawable/ic_remove_circle"
        android:layout_centerVertical="true"
        android:layout_alignParentEnd="true" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_qty"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_toStartOf="@+id/iv_remove"
        android:layout_marginEnd="20dp"
        android:layout_marginStart="20dp"
        android:layout_centerVertical="true"
        android:textColor="@color/black"
        android:textSize="16sp"
        tools:text="100 qty"/>

</RelativeLayout>