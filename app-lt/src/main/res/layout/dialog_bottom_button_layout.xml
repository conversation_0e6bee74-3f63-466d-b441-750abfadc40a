<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:paddingTop="@dimen/component_vertical_padding_v1"
    tools:background="@drawable/rectangle_2e2e2e">

    <com.customer.widget.StateButton
        android:id="@+id/negative_button"
        android:layout_width="0dp"
        android:layout_height="@dimen/button_height_v1"
        android:layout_weight="1"
        android:enabled="true"
        android:text="@string/btn_cancel"
        android:textSize="@dimen/text_size_body3_v1"
        android:textStyle="bold"
        app:normalTextColor="@color/accent_blue_v1"
        app:pressedTextColor="@color/accent_blue_v1_o40"
        app:unableTextColor="@color/accent_blue_v1_o40" />

    <com.customer.widget.StateButton
        android:id="@+id/positive_button"
        android:layout_width="0dp"
        android:layout_height="@dimen/button_height_v1"
        android:layout_weight="1"
        android:layout_marginStart="8dp"
        android:enabled="true"
        android:text="@string/btn_confirm"
        android:textSize="@dimen/text_size_body3_v1"
        android:textStyle="bold"
        app:normalTextColor="@color/accent_blue_v1"
        app:pressedTextColor="@color/accent_blue_v1_o40"
        app:unableTextColor="@color/accent_blue_v1_o40" />

</LinearLayout>