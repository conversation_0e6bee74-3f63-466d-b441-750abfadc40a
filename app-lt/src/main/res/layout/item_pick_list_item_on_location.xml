<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/txt_item_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:textColor="@color/pick_text_normal_color"
        android:layout_marginRight="10dp"
        android:textSize="@dimen/pick_item_on_location_list_size" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/txt_item_desc"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center_vertical"
        android:textColor="@color/pick_text_normal_color"
        android:textSize="@dimen/pick_item_on_location_list_size" />

</LinearLayout>