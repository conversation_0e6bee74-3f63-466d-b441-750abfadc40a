<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/rect_434444_4"
    android:backgroundTint="@color/page_background_v1"
    android:orientation="vertical"
    android:padding="24dp">

    <TextView
        android:id="@+id/warning_title_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/warring"
        android:textColor="@color/white"
        android:textSize="20sp"
        android:textStyle="bold" />

    <TextView
        android:id="@+id/warning_message_tv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:textColor="@color/white"
        android:textSize="16sp"
        android:text="@string/msg_not_match_shipping_rule" />

    <TextView
        android:id="@+id/suggestion_tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:textColor="@color/white"
        android:textSize="16sp"
        tools:text="Suggestion: ILP-123456 (001.002.003)" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="24dp"
        android:orientation="horizontal">

        <com.customer.widget.StateButton
            style="@style/stateButtonStyle"
            android:id="@+id/suggest_btn"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginEnd="8dp"
            android:paddingStart="24dp"
            android:paddingEnd="24dp"
            android:text="@string/text_suggest" />

        <com.customer.widget.StateButton
            style="@style/stateStrokeButtonStyle"
            android:id="@+id/close_btn"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:layout_marginStart="8dp"
            android:paddingStart="24dp"
            android:paddingEnd="24dp"
            android:text="@string/text_cancel" />

    </LinearLayout>

</LinearLayout> 