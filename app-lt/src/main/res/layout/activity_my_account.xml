<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_262626"
    android:orientation="vertical"
    tools:context=".home_v1.profile.MyAccountActivity">

    <androidx.appcompat.widget.Toolbar
        android:id="@+id/toolbar"
        android:layout_width="match_parent"
        android:layout_height="56dp"
        android:minHeight="56dp"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
        app:popupTheme="@style/popupOverlayStyle" />


    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="20dp"
                android:text="@string/system_feature_management"
                android:textColor="@color/white"
                android:textSize="16sp"
                android:textStyle="bold" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingLeft="20dp"
                android:paddingRight="20dp"
                android:paddingTop="10dp"
                android:paddingBottom="10dp">

                <TextView
                    android:id="@+id/tv_uom"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/replenishment_uom_downgrade"
                    android:textColor="@color/white"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/replenishment_same_lp_unit_downgrade_summary"
                    android:textColor="@color/color_9e9e9e"
                    android:textSize="12sp"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_uom" />

                <com.customer.widget.SwitchButton
                    android:id="@+id/sb_uom"
                    android:layout_width="60dp"
                    android:layout_height="35dp"
                    app:layout_constraintBottom_toTopOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="parent"
                    app:sb_background="@color/color_6f6f6f"
                    app:sb_checked_color="@color/accent_blue_v1"
                    app:sb_uncheckcircle_color="@color/white"
                    app:sb_uncheck_color="@color/color_6f6f6f"
                    app:sb_text_size="11sp" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingLeft="20dp"
                android:paddingRight="20dp"
                android:paddingTop="10dp"
                android:paddingBottom="10dp">

                <TextView
                    android:id="@+id/tv_inventory"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/inventory_adjustment"
                    android:textColor="@color/white"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/inventory_adjustment_summary"
                    android:textColor="@color/color_9e9e9e"
                    android:textSize="12sp"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_inventory" />

                <com.customer.widget.SwitchButton
                    android:id="@+id/sb_inventory"
                    android:layout_width="60dp"
                    android:layout_height="35dp"
                    app:layout_constraintBottom_toTopOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="parent"
                    app:sb_background="@color/color_6f6f6f"
                    app:sb_checked_color="@color/accent_blue_v1"
                    app:sb_uncheckcircle_color="@color/white"
                    app:sb_uncheck_color="@color/color_6f6f6f"
                    app:sb_text_size="11sp" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingLeft="20dp"
                android:paddingRight="20dp"
                android:paddingTop="10dp"
                android:paddingBottom="10dp">

                <TextView
                    android:id="@+id/tv_trace"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/back_end_trace_log"
                    android:textColor="@color/white"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/enable_back_end_trace_log"
                    android:textColor="@color/color_9e9e9e"
                    android:textSize="12sp"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_trace" />

                <com.customer.widget.SwitchButton
                    android:id="@+id/sb_trace"
                    android:layout_width="60dp"
                    android:layout_height="35dp"
                    app:layout_constraintBottom_toTopOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="parent"
                    app:sb_background="@color/color_6f6f6f"
                    app:sb_checked_color="@color/accent_blue_v1"
                    app:sb_uncheckcircle_color="@color/white"
                    app:sb_uncheck_color="@color/color_6f6f6f"
                    app:sb_text_size="11sp" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingLeft="20dp"
                android:paddingRight="20dp"
                android:paddingTop="10dp"
                android:paddingBottom="10dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:layout_centerVertical="true"
                    android:text="@string/label_speak_language"
                    android:textColor="@color/white"
                    android:textSize="14sp"
                    android:textStyle="bold" />

                <androidx.cardview.widget.CardView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    app:cardCornerRadius="20dp"
                    app:cardElevation="0dp">

                    <com.google.android.material.tabs.TabLayout
                        android:id="@+id/tab_language"
                        android:layout_width="wrap_content"
                        android:layout_height="38dp"
                        android:background="@color/color_6f6f6f"
                        app:tabIndicatorColor="@color/color_f4f4f4"
                        app:tabIndicatorGravity="center"
                        app:tabIndicatorHeight="36dp"
                        app:tabIndicator="@drawable/rect_f4f4f4_20"
                        app:tabMode="scrollable"
                        app:tabRippleColor="@android:color/transparent"
                        app:tabSelectedTextColor="@color/color_6f6f6f"
                        app:tabTextColor="@color/white"
                        app:tabIndicatorFullWidth="true"
                        app:tabTextAppearance="@style/tab_language"
                        app:tabPadding="0dp"
                        app:tabMinWidth="45dp"
                        app:tabMaxWidth="50dp">

                        <com.google.android.material.tabs.TabItem
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:text="EN" />

                        <com.google.android.material.tabs.TabItem
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:text="中文" />
                    </com.google.android.material.tabs.TabLayout>
                </androidx.cardview.widget.CardView>
            </RelativeLayout>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="20dp"
                android:visibility="gone">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentLeft="true"
                    android:layout_centerVertical="true"
                    android:text="@string/home_my_profile_appearance"
                    android:textColor="@color/white"
                    android:textSize="14sp"
                    android:textStyle="bold" />

                <com.customer.widget.SwitchButton
                    android:layout_width="60dp"
                    android:layout_height="wrap_content"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true"
                    app:sb_background="@color/color_C4C4C4"
                    app:sb_checked_color="@color/accent_blue_v1"
                    app:sb_uncheckcircle_color="@color/white" />
            </RelativeLayout>

            <androidx.appcompat.widget.LinearLayoutCompat
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:paddingLeft="20dp"
                android:paddingRight="20dp"
                android:paddingTop="10dp"
                android:paddingBottom="10dp"
                android:focusable="true"
                android:focusableInTouchMode="true">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="@string/device_id"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <EditText
                    android:id="@+id/edt_device"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_weight="1"
                    android:background="@drawable/rect_2a2a2a_4"
                    android:imeOptions="actionDone"
                    android:inputType="text"
                    android:maxLines="1"
                    android:paddingLeft="20dp"
                    android:textColor="@color/color_9e9e9e"
                    android:textSize="14sp" />
            </androidx.appcompat.widget.LinearLayoutCompat>


            <androidx.appcompat.widget.LinearLayoutCompat
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="10dp"
                android:orientation="vertical"
                android:paddingLeft="20dp"
                android:paddingRight="20dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/text_check_is_tablet"
                    android:textColor="@color/white"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tv_device_type"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/color_9e9e9e"
                    android:textSize="12sp" />
            </androidx.appcompat.widget.LinearLayoutCompat>


            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btn_new_version"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_marginLeft="20dp"
                android:layout_marginTop="40dp"
                android:layout_marginRight="20dp"
                android:background="@drawable/rect_storke_ffffff_1"
                android:text="@string/label_update"
                android:textColor="@color/white"
                android:textSize="14sp"
                android:textStyle="bold" />
        </androidx.appcompat.widget.LinearLayoutCompat>
    </androidx.core.widget.NestedScrollView>

</androidx.appcompat.widget.LinearLayoutCompat>