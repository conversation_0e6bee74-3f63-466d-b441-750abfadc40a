<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="8dp"
        android:orientation="horizontal">

        <TextView
            android:layout_width="35dp"
            android:layout_height="35dp"
            android:layout_gravity="center"
            android:layout_marginEnd="8dp"
            android:background="@drawable/ic_order_blue" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <TextView
                android:id="@+id/title_txt"
                style="@style/textItemLabelStyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />

            <include
                layout="@layout/divider_gray_v1_1dp"
                android:layout_width="wrap_content"
                android:layout_height="1dp"
                android:layout_marginBottom="2dp"
                android:layout_marginTop="2dp" />

            <TextView
                android:id="@+id/content_txt"
                style="@style/textItemContentStyle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"/>
        </LinearLayout>
    </LinearLayout>
</LinearLayout>
