<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/color_222222"
    android:orientation="vertical"
    android:layout_marginHorizontal="8dp"
    android:paddingHorizontal="14dp"
    android:paddingVertical="12dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tracking_no_tv"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@color/white"
            android:textSize="18sp"
            tools:text="trackingNo1" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/is_print_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:textColor="@color/white"
            android:textSize="18sp"
            tools:text="IsPrinted: No" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:orientation="horizontal">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/item_name_tv"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@color/white"
            tools:text="Item1 (1EA)" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/order_id_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:textColor="@color/white"
            tools:text="DN-12927327" />
    </LinearLayout>

    <com.customer.widget.StateButton
        android:id="@+id/print_btn"
        android:layout_width="wrap_content"
        android:layout_height="42dp"
        android:layout_marginTop="8dp"
        android:layout_gravity="end"
        android:paddingHorizontal="10dp"
        android:text="@string/print"
        android:textAllCaps="false"
        android:textColor="@color/white"
        android:textSize="16sp"
        android:textStyle="bold"
        app:btnradius="@dimen/dp_4"
        app:normalBackgroundColor="@color/color_1f8bea"
        app:pressedBackgroundColor="@color/color_265c8b" />

</LinearLayout>