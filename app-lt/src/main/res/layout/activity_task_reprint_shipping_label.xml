<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/container"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/page_background_v1">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/toolbar_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar">

        <com.customer.widget.CenterTitleToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/color_2c2c2c"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light" />

    </com.google.android.material.appbar.AppBarLayout>

    <RadioGroup
        android:id="@+id/view_radio_group"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:layout_marginHorizontal="10dp"
        android:orientation="horizontal">

        <RadioButton
            android:id="@+id/not_printed_rb"
            style="@style/tabRadioButtonStyle"
            android:layout_width="match_parent"
            android:layout_height="30dp"
            android:layout_weight="1"
            android:button="@null"
            android:textSize="16sp"
            android:text="@string/text_not_printed" />

        <RadioButton
            android:id="@+id/printed_rb"
            style="@style/tabRadioButtonStyle"
            android:layout_width="match_parent"
            android:layout_height="30dp"
            android:layout_weight="1"
            android:button="@null"
            android:textSize="16sp"
            android:text="@string/text_printed" />

    </RadioGroup>


    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipe_refresh_layout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:paddingBottom="10dp">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/shipping_label_rv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"/>

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

</LinearLayout>