<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:paddingHorizontal="16dp"
    android:paddingTop="10dp"
    android:orientation="vertical">

    <TextView
        android:id="@+id/network_error_tv"
        android:layout_width="match_parent"
        android:layout_height="30dp"
        android:background="@color/red"
        android:gravity="center"
        android:text="@string/network_unavailable"
        android:textColor="@color/white"
        android:textSize="16sp"
        android:visibility="gone" />

    <LinearLayout
        android:id="@+id/ll_info"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/label_rn_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/label_rn_no"
                app:layout_constraintBottom_toBottomOf="@id/receipt_number_tv"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/receipt_number_tv" />

            <TextView
                android:id="@+id/receipt_number_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="6dp"
                android:textColor="@color/black"
                app:layout_constraintStart_toEndOf="@id/label_rn_tv"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="RN-1121" />

            <TextView
                android:id="@+id/label_container_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/label_container_number"
                app:layout_constraintBottom_toBottomOf="@id/container_number_tv"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/container_number_tv" />

            <TextView
                android:id="@+id/container_number_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="6dp"
                android:layout_marginTop="6dp"
                android:textColor="@color/black"
                app:layout_constraintStart_toEndOf="@id/label_container_tv"
                app:layout_constraintTop_toBottomOf="@id/receipt_number_tv"
                tools:text="ABCD121312" />

            <TextView
                android:id="@+id/label_scanned_expected_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/label_scanned_expected"
                app:layout_constraintBottom_toBottomOf="@id/scanned_expected_tv"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/scanned_expected_tv" />

            <TextView
                android:id="@+id/scanned_expected_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="6dp"
                android:layout_marginTop="6dp"
                android:textColor="@color/black"
                app:layout_constraintStart_toEndOf="@id/label_scanned_expected_tv"
                app:layout_constraintTop_toBottomOf="@id/container_number_tv"
                tools:text="1000 / 2000" />
        </androidx.constraintlayout.widget.ConstraintLayout>>

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="right"
            android:orientation="vertical"
            android:paddingTop="10dp"
            android:paddingBottom="10dp"
            app:layout_constraintBottom_toBottomOf="@id/container_number_tv"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/oversize_handing_btn"
                style="@style/raisedButtonStyle"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:gravity="center"
                android:textSize="10sp"
                android:text="@string/btn_oversize_handling"
                android:textAllCaps="false" />

            <TextView
                android:id="@+id/receipt_scan_type_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/black"
                android:textSize="24sp"
                android:textStyle="bold"
                tools:text="A Scan" />
        </LinearLayout>
    </LinearLayout>

    <com.customer.widget.QuickScanner
        android:id="@+id/scan_parcel_label_qs"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginVertical="12dp"
        app:hintText="@string/scan_parcel_label"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/ll_info" />

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycleView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:layout_marginBottom="10dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/scan_parcel_label_qs"
            tools:listitem="@layout/item_package_info_layout"
            android:visibility="gone"/>

        <LinearLayout
            android:id="@+id/ll_package_info_a_scan"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="12dp"
            android:background="@drawable/rect_storke_a4a4a4_2"
            android:orientation="vertical"
            android:padding="16dp"
            android:visibility="gone"
            tools:visibility="visible">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/label_tracking_number"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tracking_no_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="6dp"
                android:textColor="@color/black"
                android:textSize="20sp"
                tools:text="ABCD12345678912345" />

            <LinearLayout
                android:id="@+id/package_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                tools:visibility="visible">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:text="@string/text_type"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/delivery_type_tv"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/bg_is_assignee_sequence"
                        android:textSize="20sp"
                        android:textStyle="bold"
                        tools:text="Local Delivery" />

                    <ImageView
                        android:id="@+id/delivery_type_iv"
                        android:layout_width="30dp"
                        android:layout_height="30dp"
                        android:layout_marginStart="6dp"
                        android:src="@drawable/ic_directions_car" />
                </LinearLayout>

                <TextView
                    android:id="@+id/label_route_name_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:text="@string/route_name"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/route_name_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/black"
                    android:textSize="50sp"
                    android:textStyle="bold"
                    tools:text="100" />
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/label_condition"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <RadioGroup
                    android:id="@+id/goods_type_rg"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:orientation="horizontal">

                    <RadioButton
                        android:id="@+id/good_rb"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:checked="true"
                        android:text="@string/text_good"
                        android:textSize="@dimen/sp_16" />

                    <RadioButton
                        android:id="@+id/damaged_rb"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="20dp"
                        android:text="@string/label_damaged"
                        android:textSize="@dimen/sp_16" />
                </RadioGroup>

            </LinearLayout>

        </LinearLayout>

    </FrameLayout>

</LinearLayout>
