<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_location_label"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginTop="20dp"
        android:gravity="end"
        android:text=">>> Location"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_location_name"
        style="@style/textContentSmall"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        tools:text="E06W050"
        android:textSize="12sp"
        app:layout_constraintBottom_toBottomOf="@+id/tv_location_label"
        app:layout_constraintStart_toEndOf="@+id/tv_location_label"
        app:layout_constraintTop_toTopOf="@+id/tv_location_label" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_item_label"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginTop="8dp"
        android:gravity="end"
        android:text=">>> Item"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_location_label" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tv_item_name"
        style="@style/textContentSmall"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="8dp"
        tools:text="949 | Expensive liquid 64ML "
        android:textSize="12sp"
        app:layout_constraintBaseline_toBaselineOf="@id/tv_item_label"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0"
        app:layout_constraintStart_toEndOf="@+id/tv_item_label"
        app:layout_constraintTop_toTopOf="@+id/tv_item_label" />

    <LinearLayout
        android:id="@+id/ll_qty_input"
        android:layout_width="match_parent"
        android:layout_height="52dp"
        android:layout_marginStart="10dp"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="10dp"
        android:background="@drawable/shape_gray_round_rectangle"
        android:orientation="horizontal"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/guideline_horizontal">

        <androidx.appcompat.widget.AppCompatEditText
            android:id="@+id/edt_count_qty"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginStart="8dp"
            android:layout_weight="1"
            android:background="@null"
            android:inputType="numberDecimal"
            android:hint="@string/input_lp_pallet_qty"
            android:textSize="16sp" />

    </LinearLayout>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/label_sn_count"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp"
        android:text="@string/input_lp_pallet_qty"
        app:layout_constraintBottom_toTopOf="@+id/ll_qty_input"
        app:layout_constraintStart_toStartOf="@+id/ll_qty_input" />

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/btn_enter_qty"
        style="@style/raisedButtonStyle"
        android:layout_width="0dp"
        android:layout_height="50dp"
        android:layout_marginStart="8dp"
        android:layout_marginEnd="8dp"
        android:layout_weight="1"
        android:text="@string/text_enter"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="@+id/guideline_vertical"
        app:layout_constraintTop_toBottomOf="@+id/ll_qty_input" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_horizontal"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.5" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_vertical"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.5" />

</androidx.constraintlayout.widget.ConstraintLayout>