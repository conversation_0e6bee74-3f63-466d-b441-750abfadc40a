<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:paddingHorizontal="@dimen/page_horizontal_margin_v1">

    <LinearLayout
        android:id="@+id/order_title_ll"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/rect_525252_top_4"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:paddingHorizontal="@dimen/page_horizontal_margin_v1"
        android:paddingVertical="@dimen/page_horizontal_margin_v1">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_order_id"
            style="@style/textSubtitleV1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="0.8"
            android:textSize="@dimen/text_size_heading5_v1" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_lp_count"
                style="@style/textSubtitleV1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/order_summary_iv"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/order_summary_iv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="end|center_vertical"
                android:src="@drawable/ic_info_outline_v1"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/tv_lp_count"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_state_complete"
                style="@style/textSubtitleV1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="end"
                android:background="@drawable/rect_214d36_r2"
                android:paddingHorizontal="@dimen/page_horizontal_margin_v1"
                android:paddingVertical="@dimen/page_horizontal_margin_half_v1"
                android:text="@string/btn_complete"
                android:textColor="@color/accent_green_v1"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </LinearLayout>

    <LinearLayout
        android:id="@+id/ll_order_detail"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/rect_393939_bottom_4"
        android:orientation="vertical"
        android:paddingHorizontal="@dimen/page_horizontal_margin_v1">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingVertical="@dimen/chip_vertical_padding_v1">

            <androidx.appcompat.widget.AppCompatTextView
                style="@style/textSubtitleV1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="0.8"
                android:text="@string/label_load_seq" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_load_sequence"
                style="@style/textBodyV1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_weight="1" />

        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/color_525252" />


        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingVertical="@dimen/chip_vertical_padding_v1">

            <androidx.appcompat.widget.AppCompatTextView
                style="@style/textSubtitleV1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="0.8"
                android:text="@string/text_clp_count" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_clp_count"
                style="@style/textBodyV1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:layout_weight="1" />

        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/color_525252" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingVertical="@dimen/panel_vertical_margin_v1">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_label_add_pro_number"
                style="@style/textSubtitleV1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="0.8"
                tools:text="@string/text_add_pro_no" />

            <LinearLayout
                android:id="@+id/ll_add_pro_number"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <com.customer.widget.QuickScanner
                    android:id="@+id/scan_pro_no_scanner"
                    android:layout_width="0dp"
                    android:layout_height="@dimen/scanner_height_v1"
                    android:layout_weight="1"
                    android:background="@drawable/rect_525252_r4"
                    android:focusable="false"
                    android:focusableInTouchMode="false"
                    app:hintText="@string/add_pro_number_hit"
                    app:layout_res="@layout/view_quick_scanner_v1_blue_icon" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/no_pro_btn"
                    style="@style/textSubtitleV1"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/scanner_height_v1"
                    android:layout_marginStart="6dp"
                    android:background="@drawable/bg_btn_blue_gray_v1"
                    android:gravity="center"
                    android:paddingHorizontal="@dimen/radio_button_horizontal_margin_v1"
                    android:text="@string/text_no_pro" />

            </LinearLayout>

            <TextView
                android:id="@+id/pro_no_tv"
                android:layout_width="0dp"
                android:layout_height="@dimen/scanner_height_v1"
                android:layout_weight="1"
                android:gravity="center_vertical"
                android:background="@drawable/rect_525252_r4"
                android:textColor="@color/new_grey"
                android:paddingHorizontal="16dp"
                android:visibility="gone"
                tools:text="123213"/>

        </LinearLayout>

        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/color_525252" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tv_label_order_photo"
                style="@style/textSubtitleV1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginVertical="10dp"
                android:layout_weight="0.8"
                android:text="@string/upload_photo_of_order" />

            <com.lt.linc.util.v1widget.UploadFileV1Widget
                android:id="@+id/upload_photo_order"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                app:addPhotoIconRes="@drawable/ic_pallet_upload_photo_add_v1_new"
                app:deleteConfirmText="@string/text_delete_seal"
                app:photoItemRes="@layout/item_upload_photo_v1_new"
                app:takeVideoTitle="@string/text_take_video_of_order"
                app:takePhotoTitle="@string/text_take_photo_of_order" />
        </LinearLayout>

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/start_order_btn"
            style="@style/buttonV1"
            android:layout_marginVertical="20dp"
            android:text="@string/btn_start" />

    </LinearLayout>
</LinearLayout>