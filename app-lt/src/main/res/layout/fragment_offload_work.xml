<?xml version="1.0" encoding="utf-8"?>
<androidx.appcompat.widget.LinearLayoutCompat xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/color_222222"
    android:descendantFocusability="blocksDescendants"
    android:orientation="vertical">

    <androidx.core.widget.NestedScrollView
        android:id="@+id/offload_sv"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:focusable="true"
        android:focusableInTouchMode="true"
        android:visibility="visible">

        <androidx.appcompat.widget.LinearLayoutCompat
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingBottom="10dp"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_offload_select_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="20dp"
                android:layout_marginTop="20dp"
                android:layout_marginBottom="20dp"
                android:text="@string/text_off_load_type"
                android:textAllCaps="false"
                android:textColor="@color/white"
                android:textSize="20sp"
                android:textStyle="bold" />


            <androidx.appcompat.widget.LinearLayoutCompat
                android:id="@+id/offload_type_equipment_title_ll"
                android:layout_width="match_parent"
                android:layout_height="52dp"
                android:layout_marginLeft="18dp"
                android:layout_marginTop="12dp"
                android:layout_marginRight="18dp"
                android:background="@drawable/ripple_offload_tablet_title"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingLeft="10dp"
                android:paddingRight="10dp">

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:paddingLeft="10dp"
                    android:paddingRight="10dp"
                    android:text="@string/text_offload_type_equipment"
                    android:textColor="@color/white"
                    android:textSize="14sp"
                    android:textStyle="bold" />

                <ImageView
                    android:id="@+id/iv_offload_type_equipment_title"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:src="@drawable/ic_baseline_keyboard_arrow_down_24" />
            </androidx.appcompat.widget.LinearLayoutCompat>

            <androidx.appcompat.widget.LinearLayoutCompat
                android:id="@+id/ll_offload_type_equipment_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="18dp"
                android:layout_marginRight="18dp"
                android:background="@drawable/rect_373737_bottom_4"
                android:orientation="vertical"
                android:visibility="gone">

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/color_434444" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_offload_type_equipment"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@drawable/rect_373737_bottom_4"
                    android:focusable="false"
                    android:focusableInTouchMode="false"
                    android:paddingTop="8dp"
                    android:paddingBottom="20dp" />
            </androidx.appcompat.widget.LinearLayoutCompat>


            <androidx.appcompat.widget.LinearLayoutCompat
                android:id="@+id/offload_type_ship_title_ll"
                android:layout_width="match_parent"
                android:layout_height="52dp"
                android:layout_marginLeft="18dp"
                android:layout_marginTop="12dp"
                android:layout_marginRight="18dp"
                android:background="@drawable/ripple_offload_tablet_title"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingLeft="10dp"
                android:paddingRight="10dp">

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:paddingLeft="10dp"
                    android:paddingRight="10dp"
                    android:text="@string/text_off_load_type_seletct_ship"
                    android:textColor="@color/white"
                    android:textSize="14sp"
                    android:textStyle="bold" />

                <ImageView
                    android:id="@+id/iv_offload_type_ship_title"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:src="@drawable/ic_baseline_keyboard_arrow_down_24" />
            </androidx.appcompat.widget.LinearLayoutCompat>

            <androidx.appcompat.widget.LinearLayoutCompat
                android:id="@+id/ll_offload_type_ship_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="18dp"
                android:layout_marginRight="18dp"
                android:background="@drawable/rect_373737_bottom_4"
                android:orientation="vertical"
                android:visibility="gone">

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/color_434444" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_offload_type_ship"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:layout_marginBottom="20dp"
                    android:focusable="false"
                    android:focusableInTouchMode="false" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_equipment_size"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="16dp"
                    android:layout_marginRight="8dp"
                    android:layout_marginBottom="14dp"
                    android:focusable="false"
                    android:focusableInTouchMode="false" />


            </androidx.appcompat.widget.LinearLayoutCompat>

            <androidx.appcompat.widget.LinearLayoutCompat
                android:id="@+id/offload_type_note_title_ll"
                android:layout_width="match_parent"
                android:layout_height="52dp"
                android:layout_marginLeft="18dp"
                android:layout_marginTop="12dp"
                android:layout_marginRight="18dp"
                android:background="@drawable/ripple_offload_tablet_title"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingLeft="10dp"
                android:paddingRight="10dp">

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:paddingLeft="10dp"
                    android:paddingRight="10dp"
                    android:text="@string/label_note"
                    android:textColor="@color/white"
                    android:textSize="14sp"
                    android:textStyle="bold" />

                <ImageView
                    android:id="@+id/iv_offload_type_note_title"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:src="@drawable/ic_baseline_keyboard_arrow_down_24" />
            </androidx.appcompat.widget.LinearLayoutCompat>

            <androidx.appcompat.widget.LinearLayoutCompat
                android:id="@+id/ll_offload_type_note_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="18dp"
                android:layout_marginRight="18dp"
                android:background="@drawable/rect_373737_bottom_4"
                android:orientation="vertical"
                android:visibility="gone">

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/color_434444" />

                <TextView
                    android:id="@+id/tv_offload_type_note_content"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingLeft="20dp"
                    android:paddingTop="10dp"
                    android:paddingRight="20dp"
                    android:paddingBottom="20dp"
                    android:text="11111"
                    android:textColor="@color/white"
                    android:textSize="14sp" />
            </androidx.appcompat.widget.LinearLayoutCompat>

            <androidx.appcompat.widget.LinearLayoutCompat
                android:id="@+id/offload_type_slip_sheets_amount_ll"
                android:layout_width="match_parent"
                android:layout_height="52dp"
                android:layout_marginLeft="18dp"
                android:layout_marginTop="12dp"
                android:layout_marginRight="18dp"
                android:background="@drawable/ripple_offload_tablet_title"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingLeft="10dp"
                android:paddingRight="10dp"
                android:visibility="gone">

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:paddingLeft="10dp"
                    android:paddingRight="10dp"
                    android:text="@string/label_slip_sheets_quantity"
                    android:textColor="@color/white"
                    android:textSize="14sp"
                    android:textStyle="bold" />

                <ImageView
                    android:id="@+id/iv_offload_type_slip_sheets_amount"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:src="@drawable/ic_baseline_keyboard_arrow_down_24" />
            </androidx.appcompat.widget.LinearLayoutCompat>

            <androidx.appcompat.widget.LinearLayoutCompat
                android:id="@+id/ll_offload_type_slip_sheets_amount_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="18dp"
                android:layout_marginRight="18dp"
                android:background="@drawable/rect_373737_bottom_4"
                android:orientation="vertical"
                android:visibility="gone">

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/color_434444" />

                <androidx.appcompat.widget.LinearLayoutCompat
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal" >
                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tv_offload_type_slip_sheets_amount"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:paddingLeft="20dp"
                        android:paddingTop="10dp"
                        android:paddingRight="20dp"
                        android:paddingBottom="20dp"
                        android:textColor="@color/white"
                        android:textSize="14sp" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tv_offload_type_slip_sheets_amount_edit"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:paddingLeft="20dp"
                        android:paddingTop="10dp"
                        android:paddingRight="20dp"
                        android:paddingBottom="20dp"
                        android:text="@string/label_add"
                        android:textColor="@color/accent_blue_v1"
                        android:textSize="14sp" />

                </androidx.appcompat.widget.LinearLayoutCompat>

            </androidx.appcompat.widget.LinearLayoutCompat>

            <androidx.appcompat.widget.LinearLayoutCompat
                android:id="@+id/offload_type_carton_quality_ll"
                android:layout_width="match_parent"
                android:layout_height="52dp"
                android:layout_marginLeft="18dp"
                android:layout_marginTop="12dp"
                android:layout_marginRight="18dp"
                android:background="@drawable/ripple_offload_tablet_title"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingLeft="10dp"
                android:paddingRight="10dp"
                android:visibility="gone">

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:paddingLeft="10dp"
                    android:paddingRight="10dp"
                    android:text="@string/label_carton_quality"
                    android:textColor="@color/white"
                    android:textSize="14sp"
                    android:textStyle="bold" />

                <ImageView
                    android:id="@+id/iv_offload_type_carton_quality"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:src="@drawable/ic_baseline_keyboard_arrow_down_24" />
            </androidx.appcompat.widget.LinearLayoutCompat>

            <androidx.appcompat.widget.LinearLayoutCompat
                android:id="@+id/ll_offload_type_carton_quality_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="18dp"
                android:layout_marginRight="18dp"
                android:background="@drawable/rect_373737_bottom_4"
                android:orientation="vertical"
                android:visibility="gone">

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/color_434444" />

                <androidx.appcompat.widget.LinearLayoutCompat
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal" >
                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tv_offload_type_carton_quality_amount"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:paddingLeft="20dp"
                        android:paddingTop="10dp"
                        android:paddingRight="20dp"
                        android:paddingBottom="20dp"
                        android:textColor="@color/white"
                        android:textSize="14sp" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tv_offload_type_carton_quality_edit"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:paddingLeft="20dp"
                        android:paddingTop="10dp"
                        android:paddingRight="20dp"
                        android:paddingBottom="20dp"
                        android:text="@string/label_add"
                        android:textColor="@color/accent_blue_v1"
                        android:textSize="14sp" />

                </androidx.appcompat.widget.LinearLayoutCompat>

            </androidx.appcompat.widget.LinearLayoutCompat>

            <androidx.appcompat.widget.LinearLayoutCompat
                android:id="@+id/offload_type_material_title_ll"
                android:layout_width="match_parent"
                android:layout_height="52dp"
                android:layout_marginLeft="18dp"
                android:layout_marginTop="12dp"
                android:layout_marginRight="18dp"
                android:background="@drawable/ripple_offload_tablet_title"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingLeft="10dp"
                android:paddingRight="10dp"
                android:visibility="gone">

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:paddingLeft="10dp"
                    android:paddingRight="10dp"
                    android:text="@string/text_offload_type_materials"
                    android:textColor="@color/white"
                    android:textSize="14sp"
                    android:textStyle="bold" />

                <ImageView
                    android:id="@+id/iv_offload_type_material_title"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:src="@drawable/ic_baseline_keyboard_arrow_down_24" />
            </androidx.appcompat.widget.LinearLayoutCompat>

            <androidx.appcompat.widget.LinearLayoutCompat
                android:id="@+id/ll_offload_type_material_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="18dp"
                android:layout_marginRight="18dp"
                android:layout_marginBottom="10dp"
                android:background="@drawable/rect_373737_bottom_4"
                android:orientation="vertical"
                android:visibility="gone">

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/color_434444" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_offload_type_materials"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/more_materials_offload_tv"
                    android:layout_width="match_parent"
                    android:layout_height="50dp"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginLeft="16dp"
                    android:layout_marginTop="20dp"
                    android:layout_marginRight="16dp"
                    android:layout_marginBottom="20dp"
                    android:background="@drawable/ripple_offload_type_add_material"
                    android:gravity="center"
                    android:text="@string/text_offload_type_add_materials_more"
                    android:textAllCaps="true"
                    android:textColor="@color/white"
                    android:textSize="14sp" />

            </androidx.appcompat.widget.LinearLayoutCompat>

            <androidx.appcompat.widget.LinearLayoutCompat
                android:id="@+id/offload_type_photo_title_ll"
                android:layout_width="match_parent"
                android:layout_height="52dp"
                android:layout_marginLeft="18dp"
                android:layout_marginTop="12dp"
                android:layout_marginRight="18dp"
                android:background="@drawable/ripple_offload_tablet_title"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:paddingLeft="10dp"
                android:paddingRight="10dp">

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tv_offload_type_photo_title"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="center_vertical"
                    android:paddingLeft="10dp"
                    android:paddingRight="10dp"
                    android:text="@string/text_offload_photos"
                    android:textColor="@color/white"
                    android:textSize="14sp"
                    android:textStyle="bold" />

                <ImageView
                    android:id="@+id/iv_offload_type_photo_title"
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:scaleType="fitCenter"
                    android:src="@drawable/ic_baseline_keyboard_arrow_down_24" />
            </androidx.appcompat.widget.LinearLayoutCompat>

            <androidx.appcompat.widget.LinearLayoutCompat
                android:id="@+id/ll_offload_type_photo_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="18dp"
                android:layout_marginRight="18dp"
                android:background="@drawable/rect_373737_bottom_4"
                android:orientation="vertical"
                android:visibility="gone">

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="@color/color_434444" />

                <com.lt.linc.util.v1widget.UploadFileV1Widget
                    android:id="@+id/rv_offload_type_photo"
                    android:layout_width="match_parent"
                    android:layout_height="100dp"
                    android:paddingLeft="10dp"
                    android:paddingRight="10dp" />
            </androidx.appcompat.widget.LinearLayoutCompat>

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/add_material_offload_btn"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_marginLeft="18dp"
                android:layout_marginTop="30dp"
                android:layout_marginRight="18dp"
                android:layout_weight="1"
                android:background="@drawable/rect_storke_000000_1"
                android:enabled="true"
                android:gravity="center"
                android:text="@string/btn_add_material"
                android:textColor="@color/white"
                android:textSize="14sp" />

        </androidx.appcompat.widget.LinearLayoutCompat>
    </androidx.core.widget.NestedScrollView>

    <androidx.appcompat.widget.LinearLayoutCompat
        android:id="@+id/ll_btn"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/color_2e2e2e"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        android:padding="18dp"
        android:visibility="visible">

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/offload_finish_btn"
            style="@style/raisedButtonStyle"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_weight="1"
            android:background="@drawable/ripple_offload"
            android:enabled="false"
            android:gravity="center"
            android:text="@string/btn_done"
            android:textColor="@color/white"
            android:textSize="14sp" />
    </androidx.appcompat.widget.LinearLayoutCompat>

</androidx.appcompat.widget.LinearLayoutCompat>