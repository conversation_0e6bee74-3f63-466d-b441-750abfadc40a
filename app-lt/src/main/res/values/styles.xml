<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="appThemeStyle" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/color_262626</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="android:windowBackground">@color/colorBackground</item>

        <!-- ##################2018-01-08 集成QMUI必需要的Style ####Start##############-->
        <!--**********************************************
        *                qmui common color               *
        **********************************************-->
        <item name="qmui_content_padding_horizontal">@dimen/qmui_content_padding_horizontal
        </item> <!-- 已废弃 -->
        <item name="qmui_content_spacing_horizontal">@dimen/qmui_content_spacing_horizontal</item>

        <item name="qmui_config_color_blue">@color/colorAccent</item>
        <item name="qmui_config_color_red">@color/qmui_config_color_red</item>
        <item name="qmui_config_color_separator">@color/qmui_config_color_separator</item>
        <item name="qmui_config_color_separator_darken">@color/qmui_config_color_separator_darken
        </item>
        <item name="qmui_config_color_background">@color/qmui_config_color_background</item>
        <item name="qmui_config_color_background_pressed">
            @color/qmui_config_color_background_pressed
        </item>
        <item name="qmui_config_color_black">@color/qmui_config_color_black</item>
        <item name="qmui_config_color_link">@color/qmui_config_color_link</item>
        <item name="qmui_config_color_pressed">@color/qmui_config_color_pressed</item>

        <item name="qmui_config_color_gray_1">@color/qmui_config_color_gray_1</item>
        <item name="qmui_config_color_gray_2">@color/qmui_config_color_gray_2</item>
        <item name="qmui_config_color_gray_3">@color/qmui_config_color_gray_3</item>
        <item name="qmui_config_color_gray_4">@color/qmui_config_color_gray_4</item>
        <item name="qmui_config_color_gray_5">@color/qmui_config_color_gray_5</item>
        <item name="qmui_config_color_gray_6">@color/qmui_config_color_gray_6</item>
        <item name="qmui_config_color_gray_7">@color/qmui_config_color_gray_7</item>
        <item name="qmui_config_color_gray_8">@color/qmui_config_color_gray_8</item>
        <item name="qmui_config_color_gray_9">@color/qmui_config_color_gray_9</item>

        <item name="qmui_alpha_pressed">0.5</item>
        <item name="qmui_alpha_disabled">0.5</item>

        <!--**********************************************
        *                qmui dialog                     *
        **********************************************-->
        <item name="qmui_dialog_margin_horizontal">40dp</item>
        <item name="qmui_dialog_min_width">280dp</item>
        <item name="qmui_dialog_bg">@drawable/qmui_dialog_bg</item>
        <item name="qmui_dialog_margin_vertical">20dp</item>
        <item name="qmui_dialog_padding_horizontal">24dp</item>

        <!-- title -->
        <item name="qmui_dialog_title_text_color">?attr/qmui_config_color_black</item>
        <item name="qmui_dialog_title_margin_top">24dp</item>
        <item name="qmui_dialog_title_text_size">17sp</item>

        <!-- menu -->
        <item name="qmui_dialog_menu_item_check_icon_margin_horizontal">6dp</item>

        <!-- content -->
        <item name="qmui_dialog_content_padding_top">14dp</item>
        <item name="qmui_dialog_content_padding_top_when_no_title">27dp</item>
        <item name="qmui_dialog_content_padding_top_when_list">8dp</item>
        <item name="qmui_dialog_content_padding_bottom">28dp</item>
        <item name="qmui_dialog_content_padding_bottom_when_no_action">8dp</item>
        <item name="qmui_dialog_content_padding_bottom_when_action_block">16dp</item>
        <item name="qmui_dialog_content_message_text_size">16sp</item>
        <item name="qmui_dialog_content_list_item_height">48dp</item>
        <item name="qmui_dialog_content_list_item_text_size">15sp</item>
        <item name="qmui_dialog_content_list_item_bg">?attr/qmui_s_list_item_bg_with_border_none
        </item>
        <item name="qmui_dialog_confirm_content_padding_top">28dp</item>
        <item name="qmui_dialog_confirm_content_padding_bottom">22dp</item>
        <item name="qmui_dialog_edit_content_padding_top">28dp</item>
        <item name="qmui_dialog_edit_content_padding_bottom">31dp</item>
        <item name="qmui_dialog_block_content_text_size">14sp</item>
        <item name="qmui_dialog_menu_item_text_color">?attr/qmui_config_color_black</item>

        <!-- action -->
        <item name="qmui_dialog_action_container_margin_bottom">12dp</item>
        <item name="qmui_dialog_action_container_margin_horizontal">12dp</item>
        <item name="qmui_dialog_action_button_min_width">64dp</item>
        <item name="qmui_dialog_action_button_height">36dp</item>
        <item name="qmui_dialog_action_button_margin_left">8dp</item>
        <item name="qmui_dialog_action_drawable_padding">6dp</item>
        <item name="qmui_dialog_action_button_text_size">14sp</item>
        <item name="qmui_dialog_action_button_padding_horizontal">12dp</item>

        <item name="qmui_dialog_action_block_container_margin_bottom">12dp</item>
        <item name="qmui_dialog_action_block_btn_height">36dp</item>

        <item name="qmui_dialog_action_text_negative_color">
            @color/qmui_dialog_action_text_negative_color
        </item>
        <item name="qmui_dialog_action_text_color">@color/color_primary_text</item>
        <item name="qmui_dialog_action_btn_bg">@drawable/qmui_dialog_action_btn_bg</item>
        <item name="qmui_dialog_action_block_btn_bg">?attr/qmui_s_list_item_bg_with_border_none
        </item>

        <!-- tip -->
        <item name="qmui_tip_dialog_bg">@drawable/qmui_tip_dialog_bg</item>
        <item name="qmui_tip_dialog_min_width">120dp</item>
        <item name="qmui_tip_dialog_min_height">56dp</item>
        <item name="qmui_tip_dialog_margin_horizontal">?attr/qmui_content_spacing_horizontal</item>
        <item name="qmui_tip_dialog_padding_vertical">12dp</item>
        <item name="qmui_tip_dialog_padding_horizontal">?attr/qmui_content_padding_horizontal</item>
        <!--**********************************************
        *             qmui list item bg                  *
        **********************************************-->
        <item name="qmui_list_item_height">@dimen/qmui_list_item_height</item>
        <item name="qmui_list_item_height_higher">@dimen/qmui_list_item_height_higher</item>

        <item name="qmui_list_item_bg_with_border_bottom">
            @drawable/qmui_list_item_bg_with_border_bottom
        </item>
        <item name="qmui_list_item_bg_with_border_bottom_pressed">
            @drawable/qmui_list_item_bg_with_border_bottom_pressed
        </item>
        <item name="qmui_list_item_bg_with_border_bottom_inset_left">
            @drawable/qmui_list_item_bg_with_border_bottom_inset_left
        </item>
        <item name="qmui_list_item_bg_with_border_bottom_inset_left_pressed">
            @drawable/qmui_list_item_bg_with_border_bottom_inset_left_pressed
        </item>
        <item name="qmui_list_item_bg_with_border_top">@drawable/qmui_list_item_bg_with_border_top
        </item>
        <item name="qmui_list_item_bg_with_border_top_pressed">
            @drawable/qmui_list_item_bg_with_border_top_pressed
        </item>
        <item name="qmui_list_item_bg_with_border_top_inset_left">
            @drawable/qmui_list_item_bg_with_border_top_inset_left
        </item>
        <item name="qmui_list_item_bg_with_border_top_inset_left_pressed">
            @drawable/qmui_list_item_bg_with_border_top_inset_left_pressed
        </item>
        <item name="qmui_list_item_bg_with_border_double">
            @drawable/qmui_list_item_bg_with_double_border
        </item>
        <item name="qmui_list_item_bg_with_border_double_pressed">
            @drawable/qmui_list_item_bg_with_double_border_pressed
        </item>

        <item name="qmui_s_list_item_bg_with_border_bottom">
            @drawable/qmui_s_list_item_bg_with_border_bottom
        </item>
        <item name="qmui_s_list_item_bg_with_border_bottom_inset">
            @drawable/qmui_s_list_item_bg_with_border_bottom_inset
        </item>
        <item name="qmui_s_list_item_bg_with_border_bottom_inset_left">
            @drawable/qmui_s_list_item_bg_with_border_bottom_inset_left
        </item>
        <item name="qmui_s_list_item_bg_with_border_top">
            @drawable/qmui_s_list_item_bg_with_border_top
        </item>
        <item name="qmui_s_list_item_bg_with_border_top_inset_left">
            @drawable/qmui_s_list_item_bg_with_border_top_inset_left
        </item>
        <item name="qmui_s_list_item_bg_with_border_double">
            @drawable/qmui_s_list_item_bg_with_border_double
        </item>
        <item name="qmui_s_list_item_bg_with_border_none">
            @drawable/qmui_s_list_item_bg_with_border_none
        </item>

        <!--**********************************************
        *             qmui drawable                      *
        ***********************************************-->
        <item name="qmui_s_checkbox">@drawable/qmui_s_checkbox</item>
        <item name="qmui_icon_check_mark">@drawable/qmui_icon_checkmark</item>

        <!--**********************************************
        *                   qmui btn                     *
        ***********************************************-->
        <item name="qmui_round_btn_text_size">@dimen/qmui_btn_text_size</item>
        <item name="qmui_round_btn_border_width">@dimen/qmui_btn_border_width</item>
        <item name="qmui_round_btn_bg_color">@color/qmui_btn_blue_bg</item>
        <item name="qmui_round_btn_border_color">@color/qmui_btn_blue_border</item>
        <item name="qmui_round_btn_text_color">@color/qmui_btn_blue_text</item>

        <!--**********************************************
        *                  qmui topbar                   *
        ***********************************************-->
        <item name="qmui_topbar_height">56dp</item>
        <item name="qmui_topbar_title_color">@color/qmui_config_color_gray_1</item>
        <item name="qmui_topbar_title_text_size">17sp</item>
        <item name="qmui_topbar_title_text_size_with_subtitle">16sp</item>
        <item name="qmui_topbar_title_margin_horizontal_when_no_btn_aside">
            @dimen/qmui_content_padding_horizontal
        </item>
        <item name="qmui_topbar_subtitle_text_size">11sp</item>
        <item name="qmui_topbar_subtitle_color">@color/qmui_config_color_gray_1</item>
        <item name="qmui_topbar_image_btn_width">48dp</item>
        <item name="qmui_topbar_image_btn_height">48dp</item>
        <item name="qmui_topbar_text_btn_padding_horizontal">12dp</item>
        <item name="qmui_topbar_text_btn_color_state_list">@color/qmui_topbar_text_color</item>
        <item name="qmui_topbar_text_btn_text_size">16sp</item>

        <!--**********************************************
        *                 qmui bottom sheet              *
        ***********************************************-->
        <item name="qmui_bottom_sheet_title_height">56dp</item>
        <item name="qmui_bottom_sheet_title_appearance">@style/QMUITextAppearance.Title.Gray</item>
        <item name="qmui_bottom_sheet_title_bg">?attr/qmui_list_item_bg_with_border_bottom</item>

        <item name="qmui_bottom_sheet_list_item_padding_horizontal">
            ?attr/qmui_content_padding_horizontal
        </item>
        <item name="qmui_bottom_sheet_list_item_height">56dp</item>
        <item name="qmui_bottom_sheet_list_item_mark_margin_left">12dp</item>
        <item name="qmui_bottom_sheet_list_item_tip_point_margin_left">4dp</item>
        <item name="qmui_bottom_sheet_list_item_text_appearance">
            @style/QMUITextAppearance.ListItem
        </item>
        <item name="qmui_bottom_sheet_list_item_bg">?attr/qmui_s_list_item_bg_with_border_bottom
        </item>
        <item name="qmui_bottom_sheet_list_item_icon_size">22dp</item>
        <item name="qmui_bottom_sheet_list_item_icon_margin_right">12dp</item>

        <item name="qmui_bottom_sheet_grid_padding_vertical">12dp</item>
        <item name="qmui_bottom_sheet_grid_line_padding_horizontal">12dp</item>
        <item name="qmui_bottom_sheet_grid_line_vertical_space">0dp</item>
        <item name="qmui_bottom_sheet_grid_item_mini_width">84dp</item>
        <item name="qmui_bottom_sheet_grid_item_icon_size">56dp</item>
        <item name="qmui_bottom_sheet_grid_item_text_appearance">
            @style/QMUITextAppearance.GridItem.Small
        </item>

        <item name="qmui_bottom_sheet_button_height">56dp</item>
        <item name="qmui_bottom_sheet_button_text_size">15sp</item>
        <item name="qmui_bottom_sheet_button_text_color">@color/qmui_config_color_gray_2</item>
        <item name="qmui_bottom_sheet_button_text_background">
            @drawable/qmui_s_list_item_bg_with_border_top
        </item>

        <!--**********************************************
        *                 qmui common list item          *
        ***********************************************-->
        <item name="qmui_common_list_item_chevron">@drawable/qmui_icon_chevron</item>

        <!--**********************************************
        *               qmui loading view                *
        ***********************************************-->
        <item name="qmui_loading_size">20dp</item>
        <item name="qmui_loading_color">@color/qmui_config_color_gray_5</item>

        <!--**********************************************
        *                   qmui popup                   *
        ***********************************************-->
        <item name="qmui_popup_bg">@drawable/qmui_popup_bg</item>
        <item name="qmui_popup_arrow_down">@drawable/ic_white_triangle_down</item>
        <item name="qmui_popup_arrow_up">@drawable/qmui_popup_arrow_up</item>
        <item name="qmui_popup_arrow_up_margin_top">13dp</item>
        <item name="qmui_popup_arrow_down_margin_bottom">1dp</item>

        <!--**********************************************
        *                  qmui tabSegment               *
        ***********************************************-->
        <item name="qmui_tab_sign_count_view">@style/qmui_tab_sign_count_view</item>
        <item name="qmui_tab_sign_count_view_minSize">@dimen/qmui_tab_sign_count_view_minSize</item>
        <item name="qmui_tab_sign_count_view_minSize_with_text">
            @dimen/qmui_tab_sign_count_view_minSize_with_text
        </item>
        <item name="qmui_tab_sign_count_view_bg">@drawable/qmui_sign_count_view_bg</item>
        <item name="qmui_tab_sign_count_view_padding_horizontal">4dp</item>

        <!--**********************************************
        *               qmui 提供的控件样式                *
        ***********************************************-->
        <item name="QMUIButtonStyle">@style/QMUI.RoundButton</item>
        <item name="QMUITabSegmentStyle">@style/QMUI.TabSegment</item>
        <item name="QMUICommonListItemViewStyle">@style/QMUI.CommonListItemView</item>
        <item name="QMUIGroupListSectionViewStyle">@style/QMUI.GroupListSectionView</item>
        <item name="QMUITopBarStyle">@style/QMUI.TopBar</item>

        <item name="QMUITipPointStyle">@style/QMUI.TipPoint</item>
        <item name="QMUITipNewStyle">@style/QMUI.TipNew</item>

        <item name="QMUILoadingStyle">@style/QMUI.Loading</item>
        <item name="QMUIPullRefreshLayoutStyle">@style/QMUI.PullRefreshLayout</item>
        <item name="QMUIRadiusImageViewStyle">@style/QMUI.RadiusImageView</item>
        <item name="QMUIQQFaceStyle">@style/QMUI.QQFaceView</item>
        <!-- ################## 2018-01-08 集成QMUI必需要的Style ####End##############  -->
    </style>

    <style name="launcherStyle" parent="appThemeStyle">
        <item name="android:windowBackground">@color/color_2e2e2e</item>
    </style>

    <!-- ################## edit text style begin ################## -->
    <style name="editTextStyle" parent="Widget.AppCompat.EditText">
        <item name="android:background">@drawable/bg_edt</item>
    </style>

    <style name="checkInEtStyle">
        <item name="android:singleLine">true</item>
        <item name="android:layout_marginTop">4dp</item>
        <item name="android:layout_marginBottom">4dp</item>
    </style>
    <!--  edit text style end -->

    <!-- ################## dialog style begin ################## -->
    <style name="itemSpecDialogAnim" parent="android:Animation">
        <item name="@android:windowEnterAnimation">@anim/dialog_item_spec_in</item>
        <item name="@android:windowExitAnimation">@anim/dialog_item_spec_out</item>
    </style>

    <style name="itemSpecDialog">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowAnimationStyle">@style/itemSpecDialogAnimStyle</item>
        <item name="android:backgroundDimEnabled">false</item>
    </style>

    <style name="fullScreenDialogStyle">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowAnimationStyle">@style/itemSpecDialogAnimStyle</item>
    </style>

    <style name="fullGrayScreenDialogStyle">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowFullscreen">true</item>
        <item name="android:background">@color/bg_replenish_task</item>
        <item name="android:windowAnimationStyle">@style/itemSpecDialogAnimStyle</item>
    </style>

    <style name="AlertDialogStyle" parent="Theme.AppCompat.Light.Dialog">
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowTitleStyle">@style/dialogWindowTitleStyle</item>
    </style>

    <style name="DialogWindowTitle">
        <item name="android:maxLines">1</item>
        <item name="android:scrollHorizontally">true</item>
        <item name="android:textAppearance">@style/DialogWindowTitleAppearance</item>
    </style>

    <style name="DialogWindowTitleAppearance" parent="@android:style/TextAppearance.Holo.DialogWindowTitle">
        <item name="android:textColor">@color/colorAccent</item>
    </style>

    <style name="barcodeDialog">
        <item name="android:windowNoTitle">true</item>
        <item name="android:backgroundDimEnabled">false</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
    </style>

    <style name="locationSelectorDialog" parent="appThemeStyle">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowAnimationStyle">@style/itemSpecDialogAnimStyle</item>
        <item name="android:backgroundDimEnabled">false</item>
    </style>

    <style name="activityAlertDialog" parent="Theme.AppCompat">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:backgroundDimEnabled">true</item>
    </style>
    <!-- dialog style end -->


    <!-- ################## card view style begin ################## -->
    <style name="mainCardStyle" parent="Theme.AppCompat.Light">
        <item name="cardCornerRadius">0dp</item>
        <item name="cardElevation">4dp</item>
    </style>

    <style name="checkInCardStyle" parent="Theme.AppCompat.Light">
        <item name="cardCornerRadius">2dp</item>
        <item name="cardElevation">4dp</item>
        <item name="android:layout_marginLeft">4dp</item>
        <item name="android:layout_marginRight">4dp</item>
    </style>

    <style name="loadTaskCardStyle" parent="Theme.AppCompat.Light">
        <item name="cardCornerRadius">2dp</item>
        <item name="cardElevation">4dp</item>
    </style>
    <!-- card view style end -->


    <!-- ################## progress style begin ################## -->
    <style name="circleProgressTheme" parent="android:style/Theme.Dialog">
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:backgroundDimEnabled">true</item>
    </style>

    <style name="circleProgressStyle" parent="android:Widget.ProgressBar" />

    <style name="horizontalProgressStyle" parent="Base.Widget.AppCompat.ProgressBar.Horizontal">
        <item name="android:progressDrawable">@drawable/widget_horizontal_progress</item>
    </style>

    <style name="SyncProgressBarTheme" parent="android:Widget.Holo.ProgressBar" />
    <!-- progress style end -->

    <!-- ################## define button style begin  ##################-->
    <style name="raisedButtonStyle" parent="Widget.AppCompat.Button.Colored" />

    <style name="flatButtonStyle" parent="Widget.AppCompat.Button.Borderless.Colored" />

    <style name="flatButtonStyleV1" parent="flatButtonStyle">
        <item name="android:textColor">@color/accent_blue_v1</item>
    </style>

    <style name="filterButtonStyle">
        <item name="android:minWidth">@dimen/btn_filter_min_width</item>
        <item name="android:minHeight">@dimen/btn_filter_height</item>
        <item name="android:background">@drawable/bg_filter_btn</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:clickable">true</item>
    </style>

    <style name="filterRadioButtonStyle" parent="Widget.AppCompat.CompoundButton.RadioButton">
        <item name="android:paddingBottom">4dp</item>
        <item name="android:paddingTop">4dp</item>
        <item name="android:paddingLeft">4dp</item>
        <item name="android:paddingRight">4dp</item>
        <item name="android:drawablePadding">4dp</item>
        <item name="android:button">@null</item>
        <item name="android:gravity">center</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:minHeight">36dp</item>
    </style>

    <style name="tabRadioButtonStyle" parent="Widget.AppCompat.CompoundButton.RadioButton">
        <item name="android:paddingBottom">4dp</item>
        <item name="android:paddingTop">4dp</item>
        <item name="android:paddingLeft">8dp</item>
        <item name="android:paddingRight">8dp</item>
        <item name="android:button">@null</item>
        <item name="android:gravity">center</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:minHeight">36dp</item>
        <item name="android:background">@drawable/bg_tab_rbtn_selector</item>
    </style>

    <style name="tabRadioButtonStyleV1" parent="tabRadioButtonStyle">
        <item name="android:background">@drawable/bg_tab_rbtn_selector_v1</item>
    </style>

    <!-- define button style end -->

    <!-- ################## define spinner style begin ################## -->
    <style name="spinnerStyle" parent="Base.Widget.AppCompat.Spinner.Underlined">
        <item name="android:minHeight">32dp</item>
        <item name="android:dropDownListViewStyle">@style/spinnerItemStyle</item>
    </style>

    <style name="spinnerItemStyle" parent="Base.Widget.AppCompat.DropDownItem.Spinner">
        <item name="android:divider">@color/gray_line</item>
        <item name="android:dividerHeight">2dp</item>
    </style>

    <style name="spinnerV1Style" parent="Widget.AppCompat.DropDownItem.Spinner">
        <item name="android:textColor">@color/white</item>
        <item name="android:textColorHint">@color/white</item>
        <item name="android:backgroundTint">@color/white</item>
        <item name="android:spinnerMode">dropdown</item>
        <item name="android:dropDownItemStyle">@style/itemSpinnerStyle</item>
    </style>

    <style name="itemSpinnerStyle" parent="@android:style/Widget.TextView.SpinnerItem">
        <item name="android:textColor">@color/white</item>
    </style>

    <!-- define spinner style end -->

    <!-- ################## define text view style begin ################## -->
    <style name="checkInTitleTxtStyle">
        <item name="android:gravity">right</item>
        <item name="android:textColor">@color/color_primary_text</item>
        <item name="android:textSize">17sp</item>
        <item name="android:paddingRight">5dp</item>
    </style>

    <style name="checkInLabelTxtStyle">
        <item name="android:gravity">right</item>
        <item name="android:textSize">15sp</item>
        <item name="android:paddingRight">5dp</item>
    </style>

    <style name="labelTextSmall" parent="TextAppearance.AppCompat.Small">
        <item name="android:textStyle">bold</item>
        <item name="android:textSize">14sp</item>
    </style>

    <style name="labelTextMiddle" parent="TextAppearance.AppCompat.Medium">
        <item name="android:textStyle">bold</item>
        <item name="android:textSize">16sp</item>
    </style>

    <style name="labelTextLarge" parent="TextAppearance.AppCompat.Large">
        <item name="android:textStyle">bold</item>
        <item name="android:textSize">18sp</item>
    </style>

    <style name="TextViewTitle">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textSize">16sp</item>
        <item name="android:layout_marginRight">5dp</item>
        <item name="android:gravity">center</item>
    </style>

    <style name="txtTitle">
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="txtLabel">
        <item name="android:textSize">14sp</item>
        <item name="android:paddingRight">5dp</item>
    </style>

    <style name="txtBlackLabel">
        <item name="android:textSize">15sp</item>
        <item name="android:paddingRight">5dp</item>
        <item name="android:textColor">@color/color_primary_text</item>
    </style>

    <style name="txtHint">
        <item name="android:textSize">14sp</item>
        <item name="android:paddingRight">5dp</item>
        <item name="android:textColor">#9E9E9E</item>
    </style>

    <style name="txtBody">
        <item name="android:textSize">12sp</item>
    </style>

    <style name="AppTabTextAppearance" parent="TextAppearance.Design.Tab">
        <item name="android:textSize">14sp</item>
        <item name="android:textColor">@color/white</item>
        <item name="textAllCaps">false</item>
    </style>

    <style name="clickTxtStyle">
        <item name="android:clickable">true</item>
        <item name="android:textColor">@color/colorAccent</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textSize">18sp</item>
        <item name="android:background">?android:attr/selectableItemBackground</item>
    </style>

    <style name="textContentSmall">
        <item name="android:textColor">@color/colorAccent</item>
        <item name="android:textSize">14sp</item>
    </style>

    <style name="textContentMiddle">
        <item name="android:textColor">@color/colorAccent</item>
        <item name="android:textSize">16sp</item>
    </style>

    <style name="textContentLarge">
        <item name="android:textColor">@color/colorAccent</item>
        <item name="android:textSize">18sp</item>
    </style>

    <style name="textBody1">
        <item name="android:textColor">@color/primary_grey_g700</item>
        <item name="android:textSize">16sp</item>
    </style>

    <style name="textBody2">
        <item name="android:textColor">@color/primary_grey_g900</item>
        <item name="android:textSize">14sp</item>
    </style>

    <style name="textBody3">
        <item name="android:textColor">@color/primary_grey_g900</item>
        <item name="android:textSize">12sp</item>
    </style>

    <style name="text_white_14sp">
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">14sp</item>
    </style>

    <style name="text_blue_14sp">
        <item name="android:textColor">@color/accent_blue_v1</item>
        <item name="android:textSize">14sp</item>
    </style>

    <style name="text_white_16sp">
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">16sp</item>
    </style>

    <style name="text_blue_16sp">
        <item name="android:textColor">@color/accent_blue_v1</item>
        <item name="android:textSize">16sp</item>
    </style>

    <style name="text_white_12sp">
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">12sp</item>
    </style>

    <style name="smallText">
        <item name="android:textColor">@color/primary_grey_g700</item>
        <item name="android:textSize">10sp</item>
    </style>

    <style name="caption">
        <item name="android:textColor">@color/primary_grey_g900</item>
        <item name="android:textSize">13sp</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="textSubtitleV1">
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">@dimen/text_size_sm1_v1</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="textBodyV1">
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">@dimen/text_size_body4_v1</item>
    </style>

    <style name="buttonV1">
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">@dimen/text_size_body3_v1</item>
        <item name="android:textStyle">bold</item>
        <item name="android:paddingVertical">@dimen/panel_header_vertical_padding_v1</item>
        <item name="android:background">@drawable/bg_btn_blue_gray_v1</item>
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">wrap_content</item>
    </style>

    <style name="textSubtitle1G700">
        <item name="android:textColor">@color/primary_grey_g700</item>
        <item name="android:textSize">13sp</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="textSubtitle1G900">
        <item name="android:textColor">@color/primary_grey_g900</item>
        <item name="android:textSize">13sp</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="textSubtitle2G900">
        <item name="android:textColor">@color/primary_grey_g900</item>
        <item name="android:textSize">12sp</item>
        <item name="android:textStyle">normal</item>
    </style>

    <style name="textHeading1">
        <item name="android:textColor">@color/primary_grey_g900</item>
        <item name="android:textSize">32sp</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="textHeading2">
        <item name="android:textColor">@color/primary_grey_g900</item>
        <item name="android:textSize">24sp</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="textHeading3">
        <item name="android:textColor">@color/primary_grey_g900</item>
        <item name="android:textSize">18sp</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="textHeading4">
        <item name="android:textColor">@color/primary_grey_g900</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="textHeading5">
        <item name="android:textColor">@color/primary_grey_g900</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="display2">
        <item name="android:textColor">@color/black</item>
        <item name="android:textSize">32sp</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="textCalendarDate" parent="TextAppearance.MaterialCalendarWidget.Date">
        <item name="android:textColor">@color/calendar_date_text_color</item>
        <item name="android:textSize">13sp</item>
        <item name="android:textStyle">bold</item>
    </style>

    <!-- define text view style end -->

    <!--  ##################  others style define begin  ##################  -->
    <style name="appBarOverlayStyle" parent="ThemeOverlay.AppCompat.Dark.ActionBar" />

    <style name="popupOverlayStyle" parent="ThemeOverlay.AppCompat.Light" />

    <style name="loadTabBarStyle">
        <item name="android:minWidth">70dp</item>
        <item name="android:minHeight">@dimen/btn_filter_height</item>
        <item name="android:background">@drawable/bg_load_work_tab</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:gravity">center</item>
        <item name="android:button">@android:color/transparent</item>
    </style>

    <style name="AppTabLayout" parent="Widget.Design.TabLayout">
        <item name="tabIndicatorColor">?attr/colorAccent</item>
        <item name="tabIndicatorHeight">4dp</item>
        <item name="tabPaddingStart">6dp</item>
        <item name="tabPaddingEnd">6dp</item>
        <item name="tabBackground">?attr/selectableItemBackground</item>
        <item name="tabMode">fixed</item>
        <item name="tabTextAppearance">@style/AppTabTextAppearance</item>
        <item name="tabSelectedTextColor">@color/colorAccent</item>
    </style>

    <style name="subListRecyclerViewStyle">
        <item name="android:clipChildren">true</item>
        <item name="android:clipToPadding">false</item>
        <item name="android:paddingBottom">@dimen/activity_vertical_margin</item>
        <item name="android:paddingTop">@dimen/activity_vertical_margin</item>
        <item name="android:scrollbarStyle">outsideOverlay</item>
        <item name="android:scrollbars">vertical</item>
    </style>

    <style name="blackRadioButtonStyle">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:layout_margin">10dp</item>
        <item name="android:background">@null</item>
        <item name="android:button">@null</item>
        <item name="android:drawableLeft">@drawable/radio_btn_selector</item>
        <item name="android:drawablePadding">19dp</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">18sp</item>
    </style>

    <style name="whiteBackButtonToolbar" parent="android:Theme.Holo">
        <item name="android:homeAsUpIndicator">@drawable/ic_back_white</item>
    </style>

    <style name="redRadioButtonStyle" parent="Widget.AppCompat.CompoundButton.RadioButton">
        <item name="android:colorControlNormal">#FFFFFF</item>
        <item name="android:colorControlActivated">#ff0000</item>
        <item name="android:textSize">14sp</item>
    </style>

    <style name="blueRadioButtonStyle" parent="Widget.AppCompat.CompoundButton.RadioButton">
        <item name="android:colorControlNormal">#FFFFFF</item>
        <item name="android:colorControlActivated">@color/accent_blue_v1</item>
        <item name="android:textSize">14sp</item>
    </style>

    <style name="radioButtonV1Style" parent="Widget.AppCompat.CompoundButton.RadioButton">
        <item name="android:gravity">center</item>
        <item name="android:textColor">@color/tab_item_text_v1</item>
        <item name="android:background">@drawable/bg_tab_item_v1</item>
    </style>

    <style name="filterTitleText">
        <item name="android:gravity">center</item>
        <item name="android:textColor">@color/status_filter_text_v1</item>
        <item name="android:textSize">@dimen/text_size_sm1_v1</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="filterAmountText">
        <item name="android:gravity">center</item>
        <item name="android:textColor">@color/status_filter_amount_text_v1</item>
        <item name="android:background">@drawable/bg_status_filter_num_v1</item>
        <item name="android:textSize">12sp</item>
        <item name="android:layout_height">20dp</item>
        <item name="android:layout_width">wrap_content</item>
        <item name="android:minWidth">20dp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:paddingHorizontal">4dp</item>
    </style>

    <style name="dialogNoBg">
        <item name="android:background">#********</item>
    </style>

    <style name="MyAccountPswEditText" parent="Theme.AppCompat.Light">
        <item name="colorControlNormal">@android:color/darker_gray</item>
        <item name="colorControlActivated">@color/accent_blue_v1</item>
    </style>

    <style name="tab_language">
        <item name="android:textSize">11sp</item>
    </style>

    <style name="SplashTheme" parent="appThemeStyle">
        <item name="windowNoTitle">true</item><!--无标题-->
        <item name="android:windowFullscreen">true</item><!--全屏-->
        <item name="android:windowIsTranslucent">true</item><!--半透明-->
        <item name="android:windowBackground">@drawable/ic_background</item>
    </style>


    <style name="MyRadioButton_tablet" parent="Theme.AppCompat.Light">
        <item name="colorControlNormal">#ffffff</item>
        <item name="colorControlActivated">#ffffff</item>
    </style>

    <style name="MyRadioButton" parent="Theme.AppCompat.Light">
        <item name="colorControlNormal">#ffffff</item>
        <item name="colorControlActivated">#0e7ddf</item>
    </style>

    <style name="tab_offload_type">
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">bold</item>
    </style>


    <style name="textTaskTitle">
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">@dimen/text_size_body3_v1</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="textTaskSubtitle">
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">@dimen/text_size_sm1_v1</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="textTaskSubBody">
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">@dimen/text_size_body4_v1</item>
    </style>

    <style name="textNormalStyle">
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">@dimen/text_size_body3_v1</item>
    </style>

    <style name="textItemLabelStyle">
        <item name="android:textColor">@color/color_9e9e9e</item>
        <item name="android:textSize">@dimen/text_size_heading5_v1</item>
    </style>

    <style name="textItemContentStyle">
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">@dimen/text_size_sm1_v1</item>
    </style>

    <style name="textItemContentBigStyle" parent="textItemContentStyle">
        <item name="android:textSize">@dimen/text_size_heading4_v1</item>
    </style>

    <style name="editInputStyle">
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">@dimen/text_size_body3_v1</item>
        <item name="android:textColorHint">@color/text_hint_v1</item>
        <item name="android:padding">@dimen/content_padding_right</item>
        <item name="android:background">@drawable/rect_393939_r4</item>
    </style>

    <style name="stateButtonStyle">
        <item name="android:textSize">@dimen/text_size_body3_v1</item>
        <item name="btnradius">4dp</item>
        <item name="normalBackgroundColor">@color/accent_blue_v1</item>
        <item name="normalTextColor">@color/white</item>
        <item name="pressedBackgroundColor">@color/accent_blue_v1_o80</item>
        <item name="pressedTextColor">@color/white_o30</item>
        <item name="unableBackgroundColor">@color/accent_blue_v1_o40</item>
        <item name="unableTextColor">@color/white_o30</item>
    </style>

    <style name="stateStrokeButtonStyle">
        <item name="android:textSize">@dimen/text_size_body3_v1</item>
        <item name="btnradius">4dp</item>
        <item name="normalStrokeWidth">1dp</item>
        <item name="pressedStrokeWidth">1dp</item>
        <item name="unableStrokeWidth">1dp</item>
        <item name="normalStrokeColor">@color/white</item>
        <item name="pressedStrokeColor">@color/white_o75</item>
        <item name="unableStrokeColor">@color/white_o30</item>
        <item name="normalTextColor">@color/white</item>
        <item name="pressedTextColor">@color/white_o75</item>
        <item name="unableTextColor">@color/white_o30</item>
    </style>

    <style name="printItemStyle">
        <item name="android:textSize">@dimen/text_size_heading4_v1</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:background">@drawable/rect_373737_4</item>
    </style>

    <style name="DarkAlertDialog" parent="Base.Theme.AppCompat.Dialog" >
        <item name="android:textColor">@color/accent_blue_v1</item>
        <item name="colorPrimary">@color/accent_blue_v1</item>
        <item name="colorPrimaryDark">@color/accent_blue_v1</item>
        <item name="colorAccent">@color/accent_blue_v1</item>
        <item name="android:windowBackground">@color/color_373737</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowTitleStyle">@style/dialogWindowTitleStyle</item>
        <item name="android:textAppearance">@style/dialogTextAppearance</item>
    </style>

    <style name="dialogTextAppearance">
        <item name="android:textColor">@color/white</item>
    </style>

    <style name="radioButtonStyle">
        <item name="android:button">@null</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">@dimen/text_size_body3_v1</item>
        <item name="android:drawablePadding">@dimen/content_padding_left</item>
        <item name="android:drawableStart">@drawable/bg_radio_button_blue</item>
    </style>

    <style name="editTextDarkTheme">
        <item name="colorAccent">@color/accent_blue_v1</item>
    </style>

    <style name="editInputStyleV1">
        <item name="android:textColorHint">@color/gray_line</item>
        <item name="android:textColor">@color/white</item>
        <item name="theme">@style/editTextDarkTheme</item>
        <item name="android:background">@drawable/rect_393939_r4</item>
        <item name="android:padding">12dp</item>
    </style>

    <style name="textLayoutHintAppearance" parent="TextAppearance.AppCompat">
        <item name="android:textColor">@color/color_ff8d8d8d</item>
        <item name="android:textSize">14sp</item>
    </style>

</resources>