package com.lt.linc.SNScan;

import com.linc.platform.foundation.model.CustomerViewEntry;
import com.linc.platform.utils.StringUtil;

import org.junit.Test;

import java.util.ArrayList;

import static org.junit.Assert.assertEquals;

/**
 * Created by Gavin
 */
public class SNScanTest {

    @Test
    public void getScannedSNListTest() throws Exception {
        CustomerViewEntry customerViewEntry = new CustomerViewEntry();
        customerViewEntry.receiveSNPrefixes = new ArrayList<>();
        customerViewEntry.receiveSNPrefixes.add("SN:");
        customerViewEntry.receiveSNPrefixes.add("S/N:");
        customerViewEntry.receiveSNSeparators = new ArrayList<>();
        customerViewEntry.receiveSNSeparators.add(",");
        customerViewEntry.receiveSNSeparators.add("+");
        customerViewEntry.receiveSNSeparators.add(" ");

        String data = "L2408I3003826";
        assertEquals("L2408I3003826", StringUtil.getSNListByCustomerConfig(data, customerViewEntry).get(0));

        data = "L2408I3003826\nL2408I3003867";
        assertEquals("L2408I3003826", StringUtil.getSNListByCustomerConfig(data, customerViewEntry).get(0));
        assertEquals("L2408I3003867", StringUtil.getSNListByCustomerConfig(data, customerViewEntry).get(1));

        data = "SN:L2408I3003826";
        assertEquals("SN:L2408I3003826", StringUtil.getSNListByCustomerConfig(data, customerViewEntry).get(0));

        data = "SN:L2408I3003826,";
        assertEquals("SN:L2408I3003826", StringUtil.getSNListByCustomerConfig(data, customerViewEntry).get(0));

        data = "SN:L2408I3003826, ";
        assertEquals("SN:L2408I3003826", StringUtil.getSNListByCustomerConfig(data, customerViewEntry).get(0));

        data = "L2408I3003826,";
        assertEquals("L2408I3003826", StringUtil.getSNListByCustomerConfig(data, customerViewEntry).get(0));

        data = "L2408I3003826,\r\n";
        assertEquals("L2408I3003826", StringUtil.getSNListByCustomerConfig(data, customerViewEntry).get(0));

        data = "L2408I3003826, L2408I3003867, \rL2408I3003886";
        assertEquals("L2408I3003826", StringUtil.getSNListByCustomerConfig(data, customerViewEntry).get(0));
        assertEquals("L2408I3003867", StringUtil.getSNListByCustomerConfig(data, customerViewEntry).get(1));
        assertEquals("L2408I3003886", StringUtil.getSNListByCustomerConfig(data, customerViewEntry).get(2));

        data = "S/N:L2408I3003826, S/N:L2408I3003867";
        assertEquals("S/N:L2408I3003826", StringUtil.getSNListByCustomerConfig(data, customerViewEntry).get(0));
        assertEquals("S/N:L2408I3003867", StringUtil.getSNListByCustomerConfig(data, customerViewEntry).get(1));

        data = "S/N:L2408I3003826S/N:L2408I3003867S/N:L2408I3003886";
        assertEquals("S/N:L2408I3003826", StringUtil.getSNListByCustomerConfig(data, customerViewEntry).get(0));
        assertEquals("S/N:L2408I3003867", StringUtil.getSNListByCustomerConfig(data, customerViewEntry).get(1));
        assertEquals("S/N:L2408I3003886", StringUtil.getSNListByCustomerConfig(data, customerViewEntry).get(2));

        data = "SN:L2408I3003826SN:L2408I3003867,SN:L2408I3003886";
        assertEquals("SN:L2408I3003826", StringUtil.getSNListByCustomerConfig(data, customerViewEntry).get(0));
        assertEquals("SN:L2408I3003867", StringUtil.getSNListByCustomerConfig(data, customerViewEntry).get(1));
        assertEquals("SN:L2408I3003886", StringUtil.getSNListByCustomerConfig(data, customerViewEntry).get(2));

        data = "SN:L2408I3003826+SN:L2408I3003867,SN:L2408I3003886";
        assertEquals("SN:L2408I3003826", StringUtil.getSNListByCustomerConfig(data, customerViewEntry).get(0));
        assertEquals("SN:L2408I3003867", StringUtil.getSNListByCustomerConfig(data, customerViewEntry).get(1));
        assertEquals("SN:L2408I3003886", StringUtil.getSNListByCustomerConfig(data, customerViewEntry).get(2));
    }
}
