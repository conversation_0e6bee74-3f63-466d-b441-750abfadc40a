package com.linc.platform.asset.model

import com.google.gson.annotations.SerializedName
import java.io.Serializable

data class AddressEntry(
    @SerializedName("accountingCode")
    val accountingCode: String?,
    
    @SerializedName("state")
    val state: String?,
    
    @SerializedName("address1")
    val address1: String?,
    
    @SerializedName("name")
    val name: String?,
    
    @SerializedName("id")
    val id: String
) : Serializable {
    
    fun showName(): String {
        val parts = listOf(accountingCode, state, address1)
            .filter { !it.isNullOrEmpty() }
        return if (parts.isEmpty()) "" else parts.joinToString("/")
    }
}