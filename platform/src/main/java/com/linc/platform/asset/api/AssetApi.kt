package com.linc.platform.asset.api

import com.linc.platform.asset.model.AddressEntry
import com.linc.platform.asset.model.AddressSearchEntry
import com.linc.platform.asset.model.AssetCategoryViewEntry
import com.linc.platform.asset.model.AssetItemViewEntry
import com.linc.platform.asset.model.AssetViewEntry
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Path
import rx.Observable

interface AssetApi {

    @GET("bam/asset/{id}")
    fun getAsset(@Path("id") id: String): Observable<Response<AssetViewEntry>>

    @GET("bam/asset/category/{id}")
    fun getCategory(@Path("id") id: String): Observable<Response<AssetCategoryViewEntry>>

    @GET("bam/asset/item/{id}")
    fun getAssetItem(@Path("id") id: String): Observable<Response<AssetItemViewEntry>>
    
    @POST("bam/fd-app/asset-facility/search")
    fun searchAddresses(@Body searchEntry: AddressSearchEntry): Observable<Response<List<AddressEntry>>>

}