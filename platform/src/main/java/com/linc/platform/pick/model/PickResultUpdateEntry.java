package com.linc.platform.pick.model;

import com.annimon.stream.Stream;
import com.google.gson.annotations.SerializedName;
import com.linc.platform.common.SNType;
import com.linc.platform.home.more.adjustment.model.InventoryCreateEntry;
import com.linc.platform.utils.CollectionUtil;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class PickResultUpdateEntry implements Serializable, Cloneable {

    @SerializedName("fromLPId")
    public String fromLPId;

    @SerializedName("toLPId")
    public String toLPId;

    @SerializedName("isEntireLPPick")
    public Boolean isEntireLPPick;

    @SerializedName("pickedQtyByLPUnit")
    public double pickedQtyByLPUnit;

    @SerializedName("snList")
    public List<String> snList = new ArrayList<>();

    @SerializedName("itemSpecName")
    public String itemSpecName;

    @SerializedName("lpTotalBaseQty")
    public double lpTotalBaseQty;

    @SerializedName("locationId")
    public String locationId;

    @SerializedName("orderId")
    public String orderId;

    @SerializedName("itemSpecId")
    public String itemSpecId;

    @SerializedName("pickRoundNo")
    public Integer pickRoundNo;

    @SerializedName("isPickByQTY")
    public boolean isPickByQTY;

    @SerializedName("soIds")
    public List<String> soIds;

    @SerializedName("allowExceedMaximumPartialPallet")
    public Boolean allowExceedMaximumPartialPallet;

    @SerializedName("allowSkipForcePickingByPickType")
    public Boolean allowSkipForcePickingByPickType;

    @SerializedName("unitId")
    public String unitId;

    /**
     * Indicate that this request has multi orders, otherwise the server will throw 'double submission' exception.
     */
    @SerializedName("batch")
    public boolean batch = false;

    @SerializedName("lotNo")
    public String lotNo;

    @SerializedName("type")
    public SNType snType;

    @SerializedName("pickTaskId")
    public String pickTaskId;

    public boolean isBatchOrderPick = false;

    public List<InventoryCreateEntry> pickInventoryList = new ArrayList<>();

    public PickResultUpdateEntry withLotNo(String lotNo, double qty) {
        try {
            PickResultUpdateEntry result = (PickResultUpdateEntry) PickResultUpdateEntry.this.clone();
            result.lotNo = lotNo;
            result.pickedQtyByLPUnit = qty;
            result.batch = true;
            return result;
        } catch (CloneNotSupportedException e) {
            e.printStackTrace();
        }
        return PickResultUpdateEntry.this;
    }

}
