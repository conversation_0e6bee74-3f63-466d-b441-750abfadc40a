package com.linc.platform.pick.v1.presenter.processor;

import com.linc.platform.http.ErrorCodeSubscriber;
import com.linc.platform.http.ErrorResponse;
import com.linc.platform.http.HttpService;
import com.linc.platform.http.IdResponse;
import com.linc.platform.pick.v1.api.PickWorkAPI;
import com.linc.platform.pick.v1.presenter.APIProcessor;
import com.linc.platform.pick.v1.presenter.SuccessHandler;
import com.linc.platform.pick.v1.presenter.action.PickAction;
import com.linc.platform.pick.v1.view.PickWorkView;
import com.linc.platform.utils.RxUtil;

import retrofit2.Response;

public class UpdatePickResultAPI implements APIProcessor<PickAction, PickWorkView, IdResponse> {
    private static UpdatePickResultAPI api;

    private UpdatePickResultAPI() {
    }

    public static UpdatePickResultAPI instance() {
        if (api == null) api = new UpdatePickResultAPI();
        return api;
    }

    @Override
    public void execute(PickAction input, PickWorkView pickWorkView, SuccessHandler<IdResponse> successHandler) {
        pickWorkView.showProgress(true, true);
        ErrorCodeSubscriber<Response<IdResponse>> subscriber = new ErrorCodeSubscriber<Response<IdResponse>>() {
            @Override
            public void onSuccess(Response<IdResponse> response) {
                pickWorkView.onPickSubmitSuccess();
                if (successHandler != null) {
                    successHandler.onSuccess(response.body());
                }
            }

            @Override
            public void onFailed(ErrorResponse errorResponse) {
                pickWorkView.showErrorDialog(errorResponse.error);
                pickWorkView.onSubmitPickResultFail(errorResponse.error);
            }

            @Override
            public void onDone() {
                pickWorkView.showProgress(false, false);
            }
        };

        getAPI().updatePickResult(input.getTask().id, input.orderPickAction().getPickResult()).compose(RxUtil.asyncSchedulers()).subscribe(subscriber);
    }

    private PickWorkAPI getAPI() {
        return HttpService.createService(PickWorkAPI.class);
    }
}
