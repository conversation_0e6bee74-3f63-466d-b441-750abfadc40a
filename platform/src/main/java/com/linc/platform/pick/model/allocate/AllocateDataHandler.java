package com.linc.platform.pick.model.allocate;

import com.annimon.stream.Stream;
import com.linc.platform.utils.CollectionUtil;
import com.linc.platform.utils.Lists;
import com.linc.platform.utils.StringUtil;

import java.util.List;

/**
 * <AUTHOR>
 */

public class AllocateDataHandler {
    public static String buildOrderProgressTxt(List<AllocateDetailEntry> allocateDetails) {
        int allocatedOrderCount = Stream.of(Lists.ensureNotNull(allocateDetails)).filter(AllocateDataHandler::orderAllocateCompleted).toList().size();
        return allocatedOrderCount + "/" + Lists.ensureNotNull(allocateDetails).size();
    }

    public static String buildSnProgressTxt(List<AllocateDetailEntry> allocateDetails) {
        return StringUtil.ignorePointZero(getAllocatedSNQty(allocateDetails)) + "/" + StringUtil.ignorePointZero(getTotalSNQty(allocateDetails));
    }

    private static double getTotalSNQty(List<AllocateDetailEntry> allocateDetails) {
        long totalSNQty = 0;
        for (AllocateDetailEntry allocateDetail : Lists.ensureNotNull(allocateDetails)) {
            totalSNQty += allocateDetail.totalQty;
        }
        return totalSNQty;
    }

    private static double getAllocatedSNQty(List<AllocateDetailEntry> allocateDetails) {
        long allocatedSNQty = 0;
        for (AllocateDetailEntry allocateDetail : Lists.ensureNotNull(allocateDetails)) {
            allocatedSNQty += allocateDetail.allocatedSNList.size();
        }
        return allocatedSNQty;
    }

    public static double getAllocatedSNPercent(List<AllocateDetailEntry> allocateDetails) {
        double allocatedSNQty = getAllocatedSNQty(allocateDetails);
        return allocatedSNQty == 0L ? 0L : allocatedSNQty / getTotalSNQty(allocateDetails);
    }

    public static AllocateStepEntry sortOrderByProgress(AllocateStepEntry allocateStep) {
        if (CollectionUtil.isNotNullOrEmpty(allocateStep.allocateDetails)) {
            allocateStep.allocateDetails = Stream.of(allocateStep.allocateDetails).sorted((v1, v2) -> (int) v2.totalQty - v2.allocatedSNList.size() - ((int) v1.totalQty - v1.allocatedSNList.size())).toList();
        }

        return allocateStep;
    }

    public static String buildTitle(AllocateDetailEntry allocateDetail) {
        return allocateDetail.orderId + (orderAllocateCompleted(allocateDetail) ? " √" : "");
    }

    private static boolean orderAllocateCompleted(AllocateDetailEntry allocateDetail) {
        return allocateDetail.totalQty != 0 && allocateDetail.allocatedSNList.size() >= allocateDetail.totalQty;
    }

    public static List<String> getAllocatedSNList(AllocateStepEntry allocateStep) {
        List<String> allocatedSNList = Lists.newArrayList();
        for (AllocateDetailEntry allocateDetail : Lists.ensureNotNull(allocateStep.allocateDetails)) {
            allocatedSNList.addAll(allocateDetail.allocatedSNList);
        }

        return allocatedSNList;
    }
}
