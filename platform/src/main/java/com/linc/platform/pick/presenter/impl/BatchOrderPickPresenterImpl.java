package com.linc.platform.pick.presenter.impl;

import android.text.TextUtils;

import com.annimon.stream.Collectors;
import com.annimon.stream.Stream;
import com.linc.platform.R;
import com.linc.platform.baseapp.api.FacilityEquipmentApi;
import com.linc.platform.baseapp.api.LocationApi;
import com.linc.platform.baseapp.model.FacilityEquipmentSearch;
import com.linc.platform.baseapp.model.FacilityEquipmentType;
import com.linc.platform.baseapp.model.LocationEntry;
import com.linc.platform.baseapp.model.LocationSearchEntry;
import com.linc.platform.common.apidal.BaseApiDal;
import com.linc.platform.common.customer.ManualInputScenario;
import com.linc.platform.common.handler.DoneHandler;
import com.linc.platform.common.handler.SuccessHandler;
import com.linc.platform.common.lp.LpApi;
import com.linc.platform.common.lp.LpTypeEntry;
import com.linc.platform.common.task.TaskTypeEntry;
import com.linc.platform.cyclecount.api.CycleCountApi;
import com.linc.platform.cyclecount.model.OPCountActionEntry;
import com.linc.platform.cyclecount.model.OPCountOperatorEntry;
import com.linc.platform.cyclecount.model.OnScreenCountEntry;
import com.linc.platform.cyclecount.model.OnScreenCountResponseEntry;
import com.linc.platform.cyclecount.model.OnScreenCountSearchEntry;
import com.linc.platform.foundation.api.ItemSpecAPI;
import com.linc.platform.foundation.api.OrderApi;
import com.linc.platform.foundation.model.CustomerViewEntry;
import com.linc.platform.foundation.model.CustomerViewEntryEx;
import com.linc.platform.foundation.model.ItemSNValidateRuleEntry;
import com.linc.platform.foundation.model.ItemSpecEntry;
import com.linc.platform.foundation.model.ItemSpecSearchEntry;
import com.linc.platform.foundation.model.ItemSpecWithAkaEntry;
import com.linc.platform.foundation.model.OrderSearchEntry;
import com.linc.platform.http.ErrorCode;
import com.linc.platform.http.ErrorCodeSubscriber;
import com.linc.platform.http.ErrorResponse;
import com.linc.platform.http.HttpService;
import com.linc.platform.inventory.api.InventoryApi;
import com.linc.platform.inventory.model.InventoryEntry;
import com.linc.platform.inventory.model.InventorySearchEntry;
import com.linc.platform.inventory.model.InventoryStatusEntry;
import com.linc.platform.inventory.model.LpBatchCreateEntry;
import com.linc.platform.inventory.model.LpDetail;
import com.linc.platform.pick.api.OperateWorkItemApi;
import com.linc.platform.pick.api.PickTaskApi;
import com.linc.platform.pick.api.PickTaskCenterApi;
import com.linc.platform.pick.model.InventoryIssueParameter;
import com.linc.platform.pick.model.InventoryPickIssueCreateEntry;
import com.linc.platform.pick.model.PickStrategy;
import com.linc.platform.pick.model.PickStrategyRebuildEntry;
import com.linc.platform.pick.model.PickTaskSearchEntry;
import com.linc.platform.pick.model.PickTaskViewEntry;
import com.linc.platform.pick.model.batchorderpick.BatchOrderPickData;
import com.linc.platform.pick.model.batchorderpick.BatchOrderPickIntentData;
import com.linc.platform.pick.model.batchorderpick.SoIdObject;
import com.linc.platform.pick.model.batchprint.PickSoIdSearchEntry;
import com.linc.platform.pick.model.ordergroup.OrderEntry;
import com.linc.platform.pick.model.pickbyoreder.OrdersEntry;
import com.linc.platform.pick.model.pickbyoreder.PickSuggestEntry;
import com.linc.platform.pick.presenter.BatchOrderPickPresenter;
import com.linc.platform.pick.presenter.util.PickingRestrictUtil;
import com.linc.platform.pick.reportissue.ReportPartialPalletIssueHandle;
import com.linc.platform.pick.validator.OrderPickValidator;
import com.linc.platform.pick.view.BatchOrderPickView;
import com.linc.platform.print.api.LabelApi;
import com.linc.platform.print.commonprintlp.PrintData;
import com.linc.platform.print.commonprintlp.PrintDataKt;
import com.linc.platform.print.commonprintlp.PrintJobCreate;
import com.linc.platform.print.model.LabelSizeEntry;
import com.linc.platform.print.model.PrintJobType;
import com.linc.platform.print.model.SoIdLabelBatchPrintRequestEntry;
import com.linc.platform.receive.api.ReceivePreAlertSnApi;
import com.linc.platform.receive.model.PreAlertSnSearchEntry;
import com.linc.platform.toolset.inventorycount.validator.SNValidator;
import com.linc.platform.utils.CollectionUtil;
import com.linc.platform.utils.CustomerConfigUtil;
import com.linc.platform.utils.EquipmentUtil;
import com.linc.platform.utils.LPUtil;
import com.linc.platform.utils.Lists;
import com.linc.platform.utils.PrintUtil;
import com.linc.platform.utils.ResUtil;
import com.linc.platform.utils.RxUtil;
import com.linc.platform.utils.SNCheckUtil;
import com.linc.platform.utils.StringUtil;
import com.linc.platform.utils.ToastUtil;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.linc.platform.utils.SNCheckUtil.SN_VALIDATE_AKA_KEY_PRE_FIX;

import retrofit2.Response;

/**
 * <AUTHOR>
 */

public class BatchOrderPickPresenterImpl extends BaseApiDal implements BatchOrderPickPresenter {
    private static final String INVALID_TOTE_ERROR_CODE = "44036";

    private BatchOrderPickView view;
    private BatchOrderPickData data;

    private LpApi lpApi;
    private LabelApi labelApi;
    private OrderApi orderApi;
    private OperateWorkItemApi operateWorkItemApi;
    private ItemSpecAPI itemSpecAPI;
    private InventoryApi inventoryApi;
    private ReceivePreAlertSnApi masterSNApi;
    private LocationApi locationApi;
    private FacilityEquipmentApi equipmentApi;
    private PickTaskCenterApi pickTaskApi;
    private CycleCountApi cycleCountApi;

    private PrintData printData;
    private PrintUtil printUtil;

    private ItemSpecWithAkaEntry suggestItem;
    private String itemValidateRegex;

    public BatchOrderPickPresenterImpl(BatchOrderPickView view, BatchOrderPickIntentData intentData) {
        this.view = view;
        this.data = new BatchOrderPickData(intentData);
        this.printUtil = PrintUtil.newInstance(view);

        lpApi = api(LpApi.class);
        labelApi = api(LabelApi.class);
        orderApi = api(OrderApi.class);
        operateWorkItemApi = api(OperateWorkItemApi.class);
        itemSpecAPI = HttpService.createService(ItemSpecAPI.class);
        masterSNApi = api(ReceivePreAlertSnApi.class);
        inventoryApi = api(InventoryApi.class);
        locationApi = api(LocationApi.class);
        equipmentApi = api(FacilityEquipmentApi.class);
        pickTaskApi = api(PickTaskCenterApi.class);
        cycleCountApi = api(CycleCountApi.class);
    }

    @Override
    public boolean isNeedPrintSoId() {
        // No need to print SoId in Batch Order Pick if requireCollectSOIDSubOrderTypes contains subOrderType
        return (CustomerConfigUtil.isNeedPrintSoId(data.getCustomer(), data.getOrder()) &&
                !CustomerConfigUtil.isNeedCollectSoId(data.getCustomer(), data.getOrder())) && !data.getTask().requireScanSoldAtPackWorkstation;
    }

    @Override
    public boolean isNeedCollectSoId() {
        return (CustomerConfigUtil.isNeedCollectSoId(data.getCustomer(), data.getOrder()) ||
                CustomerConfigUtil.isNeedPrintSoId(data.getCustomer(), data.getOrder())) && !data.getTask().printSoIdAtPackWorkstation;
    }

    @Override
    public boolean isPrintSoId() {
        return printData != null && PrintDataKt.getZplJobType(printData) == PrintJobType.SOID_LABEL_JOB;
    }

    @Override
    public boolean isRePrintSoId() {
        return printData != null && printData.extra.isReprint;
    }

    @Override
    public boolean isAllowManualEntry() {
        return data.isAllowManualEntry();
    }

    @Override
    public boolean isAllowUserPrintLP() {
        return data.isAllowUserPrintLP();
    }

    @Override
    public List<SoIdObject> getPrintedSoIdList() {
        return data.getPrintedSoIdList();
    }

    @Override
    public BatchOrderPickIntentData intentData() {
        return data.getIntentData();
    }

    @Override
    public BatchOrderPickData businessData() {
        return data;
    }

    @Override
    public void onViewCreated() {
        if (data.getIntentData().taskId != null) {
            startLoadOrder();
        } else {
            view.onErrorToast(ResUtil.getString(R.string.error_task_not_found));
        }
    }

    private void startLoadOrder() {
        loadOrder(data.getIntentData().orderId, () ->
        {
            PickSuggestEntry pickSuggest = data.getIntentData().pickSuggestEntry;
            if (pickSuggest == null) {
                return;
            }

            if (pickSuggest.item != null) {
                getSuggestItemDetail(pickSuggest.item.itemSpecId);
            }

            getPickAssignLpByConsolidation();
            getSuggestStageLocations();

            BatchOrderPickIntentData intentData = data.getIntentData();
            List<OrdersEntry> suggestOrders = pickSuggest.getSuggestOrders(intentData.orderId, intentData.lpUnitId);
            if (suggestOrders.size() != 1) {
                data.setPickSuggest(null);
                view.finish(false);
                return;
            }

            data.setPickSuggest(pickSuggest);

            if (isNeedPrintSoId() && !PrintUtil.hasPrinter(view.getIdmUserId(), view.getFacilityName(), LabelSizeEntry.TWO_ONE)) {
                view.onPrinterNotSelect();
                return;
            }

            if (!PickingRestrictUtil.shouldRestrictInputQty(getCustomer(), ManualInputScenario.BATCH_ORDER_PICK)) {
                view.speakMsg(ResUtil.getString(R.string.message_confirm_quantity));
            } else if (isNeedPrintSoId() && !data.hasPrintSoID) {
                // auto print soid for no permission user
                onPickQtyEnter("1");
                data.hasPrintSoID = true;
                data.hasGetSoId = true;// will auto get soId when printing soId
            }

            if (isNeedCollectSoId() && !data.hasGetSoId) {
                getSoId();
                data.hasGetSoId = true;
            }

            view.onGetSuggestSuccess(pickSuggest);

        });
    }

    private void getSoId() {
        String taskId = data.getTask().id;
        List<String> orderIds = Lists.newArrayList(data.getIntentData().orderId);
        List<String> itemSpecIds = Lists.newArrayList(data.getIntentData().itemSpecId);
        PickSoIdSearchEntry search = new PickSoIdSearchEntry(taskId, orderIds, itemSpecIds);
        asyncExecute(orderApi.getUnPickedSoId(search), null, list -> {
            if (CollectionUtil.isNotNullOrEmpty(list)) {
                refreshSoIdList(list.get(0).soIds);
            }
        });
    }

    public void getSuggestItemDetail(String itemSpecId) {
        asyncExecute(itemSpecAPI.getWithAka(itemSpecId), null, item -> {
            this.suggestItem = item;
            if (isNeedCollectSN()) {
                view.showCollectSNLayout();
                loadItemValidateRegex();
            }
        });
    }

    @Override
    public boolean isNeedCollectSN() {
        return isHasSN() || isNeedCollectSNForShipping();
    }

    private boolean isHasSN() {
        return businessData().getPickSuggest() != null && businessData().getPickSuggest().hasSN;
    }

    private boolean isNeedCollectSNForShipping() {
        OrderEntry order = data.getOrder();
        if (order == null) {
            return false;
        }
        if (suggestItem == null) {
            return false;
        }
        return CustomerViewEntryEx.isNeedCollectSNForShipping(data.getCustomer(), suggestItem.itemSpec, order.orderType, order.skipCollectSn);
    }

    private void getPickAssignLpByConsolidation() {
        String consolidationNo = data.getIntentData().consolidationNo;
        if (!TextUtils.isEmpty(consolidationNo)) {
            executeWithoutShowProgress(operateWorkItemApi.getLpByConsolidationNo(data.getTask().id, consolidationNo), lps -> {
                if (CollectionUtil.isNotNullOrEmpty(lps)) {
                    view.setPickAssignLp(lps);
                } else {
                    suggestOrderBindingLp();
                }
            });
        } else {
            suggestOrderBindingLp();
        }
    }

    private void suggestOrderBindingLp() {
        if (!data.isPickToToteCart()) {
            return;
        }
        String bindingLPIds = getOrderBindingLPIds();
        if (TextUtils.isEmpty(bindingLPIds)) {
            String suggestTote = data.getPickToToteEquipment();
            if (!TextUtils.isEmpty(suggestTote)) {
                view.setPickAssignLp(Collections.singletonList(data.getPickToToteEquipment()));
            }
        } else {
            view.setPickAssignLp(Collections.singletonList(bindingLPIds));
        }
    }


    private void getSuggestStageLocations() {
        asyncExecute(locationApi.getRetailerSuggestStageLocation(data.getTask().id), null, locationPagingResultEntry -> {
            view.showSuggestStageLocations(locationPagingResultEntry.locations);
        });
    }

    @Override
    public void onPickQtyEnter(String qty) {
        if (TextUtils.isEmpty(qty)) {
            return;
        }

        if (!qty.matches("^[0-9]+$")) {
            view.onEnterQtyFailed(ResUtil.getString(R.string.msg_invalid_qty_format));
            return;
        }

        if (Double.valueOf(qty) > suggestPickQty()) {
            data.setPickQty(0);
            view.onEnterQtyFailed(ResUtil.getString(R.string.label_qty_more_than_suggest_qty));
            view.resetFocusView(data);
            return;
        }

        data.setPickQty(Integer.parseInt(qty));
        view.resetFocusView(data);

        String speakMsg;
        if (isNeedCollectSoId()) {
            if (isNeedPrintSoId()) {
                printSoIdLabel(data.getPickQty());
            }
            speakMsg = ResUtil.getString(R.string.message_scan_soid);
        } else if (isNeedCollectSN()) {
            speakMsg = ResUtil.getString(R.string.scan_sn);
        } else {
            speakMsg = ResUtil.getString(R.string.message_scan_to_lp);
        }
        view.speakMsg(speakMsg);
    }

    @Override
    public void refreshSoIdList(List<String> soIds) {
        data.setPrintedSoIdList(soIds);
        view.refreshSoIdListView(data.getPrintedSoIdList());
    }

    public void printSoIdLabel(int qty) {
        printData = new PrintData();
        printData.idmUserId = view.getIdmUserId();
        printData.facilityName = view.getFacilityName();
        printData.paperSize = LabelSizeEntry.TWO_ONE;

        PrintJobCreate.SOIDLabel jobCreate = new PrintJobCreate.SOIDLabel();
        jobCreate.qty = qty;
        BatchOrderPickIntentData intentData = data.getIntentData();
        jobCreate.itemSpecId = intentData.itemSpecId;
        jobCreate.orderId = intentData.orderId;
        jobCreate.pickTaskId = intentData.task.id;
        printData.jobData = new PrintData.JobData.ZPL(null, jobCreate);

        printUtil.print(printData);
    }

    @Override
    public void onSoIdScanned(String soId) {
        if (data.scannedSOIDMatch(soId)) {
            data.updatePrintSoIdStatus(soId);
            view.refreshSoIdListView(data.getPrintedSoIdList());
        } else {
            view.onSOIDNotMatch(soId, data);
        }
    }

    @Override
    public void checkSN(String sn, List<String> scannedSNList, boolean isMasterSN) {
        if (isMasterSN) {
            loadMasterSN(sn);
            return;
        }

        String error = OrderPickValidator.validatorCombinationSN(sn, scannedSNList, getCustomer(), data.getTask());
        if (!TextUtils.isEmpty(error)) {
            ToastUtil.showToast(error);
            return;
        }

        List<String> snList = StringUtil.getSNListByCustomerConfig(sn.toUpperCase(), getCustomer());

        if (!checkSNLength(snList, scannedSNList)) {
            view.onSNValidateFailed(ResUtil.getString(R.string.text_invalid_sn_length));
            return;
        }

        if (hasDuplicateSN(snList, scannedSNList)) {
            view.onSNValidateFailed(ResUtil.getString(R.string.text_scan_sn_duplicate));
            return;
        }

        if (isNeedCollectSNForShipping()) {
            if (Stream.of(snList).anyMatch(it -> SNValidator.isSNFormatNotMatchItemOrCustomer(it, data.getCustomer(),itemValidateRegex))) {
                view.onSNValidateFailed(ResUtil.getString(R.string.text_invalid_sn_format));
                return;
            }

            SNCheckUtil snCheckUtil = new SNCheckUtil(new SNCheckUtil.OnCheckResultListener() {
                @Override
                public void onCheckSuccess(List<String> snList) {
                    view.addSnToList(snList);
                }

                @Override
                public void onSNItemNotMatch() {
                    view.onSNValidateFailed(ResUtil.getString(R.string.item_no_match));
                }

                @Override
                public void onDuplicateShippingSn(List<String> shippingSns) {
                    view.showDuplicateShippingSn(shippingSns);
                }
            });
            String itemSpecName = snCheckUtil.getItemSpecNameWithoutDesc(suggestItem.itemSpec.name);
            List<String> validateAkaValues = Stream.of(suggestItem.akas)
                    .filter(itemAkaViewEntry -> itemAkaViewEntry.key.startsWith(SN_VALIDATE_AKA_KEY_PRE_FIX))
                    .map(itemAkaViewEntry -> itemAkaViewEntry.value).collect(Collectors.toList());
            snCheckUtil.checkSNItemMatchAndAvailable(snList, itemSpecName, validateAkaValues, data.getOrder().getOrderId(), new ArrayList<>());
        } else {
            searchInventoryToCheckSN(snList);
        }
    }

    private boolean checkSNLength(List<String> snList, List<String> scannedSNList) {
        if (CollectionUtil.isNullOrEmpty(scannedSNList)) {
            return true;
        }
        return Stream.of(snList).allMatch(sn -> scannedSNList.get(0).length() == sn.length());
    }

    private boolean hasDuplicateSN(List<String> snList, List<String> scannedSNList) {
        return CollectionUtil.isNotNullOrEmpty(scannedSNList) && Stream.of(scannedSNList).anyMatch(snList::contains);
    }

    private void loadMasterSN(String masterSN) {
        PreAlertSnSearchEntry searchEntry = new PreAlertSnSearchEntry();
        searchEntry.customerId = getCustomer().orgId;
        searchEntry.palletId = masterSN;
        asyncExecute(masterSNApi.searchWithInventory(searchEntry), view, preAlertSnViewEntries -> {
            if (!preAlertSnViewEntries.isEmpty() && !preAlertSnViewEntries.get(0).snList.isEmpty()) {
                view.addSnToList(preAlertSnViewEntries.get(0).snList);
            } else {
                ToastUtil.showToast(R.string.msg_invalid_sn);
            }
        });
    }

    @Override
    public double getSuggestPickBaseQty() {
        if (data.getPickSuggest() == null && data.getPickSuggest().suggestPickBaseQty != 0)
            return 0;
        return suggestPickQty();
    }

    private void searchInventoryToCheckSN(List<String> snList) {
        InventorySearchEntry searchEntry = new InventorySearchEntry();
        searchEntry.snList = snList;
        searchEntry.excludeStatuses = Collections.singletonList(InventoryStatusEntry.SHIPPED);
        execute(inventoryApi.searchInventory(searchEntry), inventories -> {
            if (CollectionUtil.isNullOrEmpty(inventories)) {
                view.inventoryNotFound();
            } else {
                List<InventoryEntry> entries = Stream.of(inventories)
                        .distinctBy(inventoryEntry -> inventoryEntry.itemSpecId)
                        .toList();
                if (entries.size() == 1) {
                    view.onFoundInventory(inventories.get(0), searchEntry.snList);
                } else {
                    view.foundMultipleInventory();
                }
            }
        });
    }

    private void loadOrder(String orderId, DoneHandler doneHandler) {
        if (data.getOrder() != null) {
            doneHandler.onDone();
            return;
        }

        OrderSearchEntry searchEntry = new OrderSearchEntry();
        searchEntry.orderIds = Lists.newArrayList(orderId);

        asyncExecute(orderApi.searchOrderWithCustomerFilled(searchEntry), null,
                errorResponse -> view.showReloadOrderConfirmDialog(aBoolean -> loadOrder(orderId, doneHandler)),
                orderEntries ->
                {
                    if (CollectionUtil.isNullOrEmpty(orderEntries)) {
                        view.showReloadOrderConfirmDialog(aBoolean -> loadOrder(orderId, doneHandler));
                        return;
                    }

                    data.setOrder(orderEntries.get(0));
                    doneHandler.onDone();
                });
    }

    @Override
    public void printLP(LpTypeEntry lpType) {
        String bindingLPIds = getOrderBindingLPIds();
        if (data.getOrder() == null) {
            view.showReloadOrderConfirmDialog(aBoolean -> startLoadOrder());
            return;
        }
        if (needConfirmForPrintLP(bindingLPIds)) {
            view.showPrintNewLpConfirmDialog(bindingLPIds, lpType);
            return;
        }

        generateNewLp(lpType);
    }

    private boolean needConfirmForPrintLP(String bindingLPIds) {
        return CustomerConfigUtil.allowPrintMultipleLPsForOrderWhenPicking(data.getCustomer()) && !data.getOrder().isSingleQTY && !TextUtils.isEmpty(bindingLPIds);
    }

    private String getOrderBindingLPIds() {
        PickSuggestEntry pickSuggest = data.getIntentData().pickSuggestEntry;
        if (pickSuggest == null) return "";

        List<OrdersEntry> ordersEntries = pickSuggest.getSuggestOrders(intentData().orderId, intentData().lpUnitId);
        OrdersEntry orderEntry = CollectionUtil.isNullOrEmpty(ordersEntries) ? null : ordersEntries.get(0);

        return orderEntry == null ? "" : orderEntry.getBindingLPIds(pickSuggest.lpToteMap);
    }

    @Override
    public void onToLPScanned(String barcode) {
        if (TextUtils.isEmpty(barcode)) {
            return;
        }
        if (LPUtil.isLP(barcode)) {
            processScannedLP(barcode);
            return;
        }
        if (data.isPickToToteCart()) {
            asyncExecute(equipmentApi.search(new FacilityEquipmentSearch(barcode)), view, error -> {
                ToastUtil.showErrorToast(error.error);
            }, equipmentList -> {
                if (CollectionUtil.isNullOrEmpty(equipmentList)) {
                    ToastUtil.showToast(R.string.msg_scanned_entered_is_not_an_equipment_please_scan_enter_again);
                    return;
                }
                //如果是pick to tote,挡下User采集TOTECART ID为Container的操作
                boolean isTote = Stream.of(equipmentList).anyMatch(equipment -> equipment.type == FacilityEquipmentType.TOTE);
                if (!isTote) {
                    ToastUtil.showToast(R.string.msg_please_scan_tote);
                    return;
                }
                //挡下User采集其他Tote Cart的Tote ID
                if (!data.isToteInToteCart(barcode)) {
                    ToastUtil.showToast(String.format(ResUtil.getString(R.string.msg_tote_is_not_in_tote_cart), barcode, data.getToteCartBarcode()));
                    return;
                }
                if (data.isToteUsedByOrder(barcode)) {
                    ToastUtil.showToast(String.format(ResUtil.getString(R.string.msg_tote_used), barcode, data.getOccupiedOrderWithTote(barcode)));
                    return;
                }
                onGetLPDetail(barcode, equipmentList.get(0).hlpId);
            });
        } else {
            asyncExecute(operateWorkItemApi.getEquipmentHLPForPickTask(barcode, data.getTask().id), view, error -> {
                if (INVALID_TOTE_ERROR_CODE.equals(error.code)) {
                    ToastUtil.showErrorToast(error.error);
                } else {
                    autoCompleteLPId(barcode, this::processScannedLP);
                }
            }, tote -> {
                if (EquipmentUtil.isTote(tote)) {
                    onGetLPDetail(barcode, tote.hlpId);
                } else {
                    autoCompleteLPId(barcode, this::processScannedLP);
                }
            });
        }

    }

    private void autoCompleteLPId(String barcode, SuccessHandler<String> handler) {
        asyncExecute(lpApi.idAutoComplete(Collections.singletonList(barcode)), view, lpIds ->
        {
            String lpId = lpIds.get(0);
            if (LPUtil.isLP(lpId)) {
                handler.onSuccess(lpId);
            } else {
                view.resetFocusView(data);
                view.onErrorToast(ResUtil.getString(R.string.msg_tote_lp_not_found));
            }
        });
    }

    private void processScannedLP(String lpId) {
        if (TextUtils.isEmpty(lpId)) return;

        if (LPUtil.isHLP(lpId)) {
            view.resetFocusView(data);
            view.onErrorToast(ResUtil.getString(R.string.msg_tote_lp_not_found));
        } else {
            onGetLPDetail(lpId, lpId);
        }
    }

    private String checkToLp(String barcode, LpDetail lpDetail) {
        String orderBindingLPIds = getOrderBindingLPIds();
        if (needUsePreviousLP(orderBindingLPIds, barcode)) {
            return String.format(ResUtil.getString(R.string.message_batch_order_pick_to_lp_validate), orderBindingLPIds);

        }
        if (isLP(lpDetail.id) && lpDetail.isBindedOrderShipped) {
            return String.format(ResUtil.getString(R.string.message_lp_order_shipped), lpDetail.id, lpDetail.orderId);
        }
        return "";
    }

    private boolean isLP(String lpId) {
        return LPUtil.isCLP(lpId) || LPUtil.isSaasLP(lpId) || LPUtil.isUnisLP(lpId);
    }

    private boolean needUsePreviousLP(String orderBindingLPIds, String barcode) {
        return CustomerConfigUtil.allowPrintMultipleLPsForOrderWhenPicking(data.getCustomer())
                && !data.getOrder().isSingleQTY
                && !TextUtils.isEmpty(orderBindingLPIds)
                && !data.getPrintedNewLps().contains(barcode)
                && !orderBindingLPIds.contains(barcode);
    }

    private void onGetLPDetail(String barcode, String lpId) {
        execute(lpApi.getLpStatus(lpId), lpDetail -> onGetToteLPSuccess(barcode, lpId, lpDetail));
    }

    private void onGetToteLPSuccess(String barcode, String lpId, LpDetail lpDetail) {
        String errorMessage = checkToLp(barcode, lpDetail);
        if (!TextUtils.isEmpty(errorMessage)) {
            ToastUtil.showErrorToast(errorMessage);
            return;
        }

        view.refreshToLPId(barcode);
        data.savePickToLPId(barcode, lpId);

        if (!isAllowManualEntry()) {
            view.resetFocusView(data);
            view.submitPick();
        } else if (data.allowPickAutoSubmit()) {
            view.submitPick();
        }
    }

    @Override
    public String getPickToLP(String toLpEdtTxt) {
        return data.getToLPId(toLpEdtTxt);
    }

    @Override
    public void onSubmitClick(String toLPId) {
        int pickQty = data.getPickQty();

        if (pickQty <= 0) {
            view.onSubmitFailed(ResUtil.getString(R.string.msg_please_input_qty));
            return;
        }

        if (pickQty > suggestPickQty()) {
            view.onSubmitFailed(ResUtil.getString(R.string.label_qty_more_than_suggest_qty));
            return;
        }

        if (isNeedCollectSoId() && pickQty != data.getScannedSoIdList().size()) {
            view.onSubmitFailed(ResUtil.getString(R.string.msg_so_id_qty_not_match));
            return;
        }

        if (TextUtils.isEmpty(getPickToLP(toLPId))) {
            view.onSubmitFailed(ResUtil.getString(R.string.msg_please_scan_lp));
            return;
        }

        checkConsolidationNoForLp(toLPId);
    }

    private void checkConsolidationNoForLp(String toLPId) {
        asyncExecute(operateWorkItemApi.getConsolidationNosByLp(toLPId), view,
                errorResponse -> ToastUtil.showToast(errorResponse.getErrorMessage()),
                lpConsolidationNos -> {
                    String orderConsolidationNo = data.getIntentData().consolidationNo;
                    if (canPickToLP(orderConsolidationNo, lpConsolidationNos)) {
                        toSubmitPick(toLPId);
                    } else {
                        ToastUtil.showToast(ResUtil.getString(R.string.msg_order_consolidation_key_and_lp_consolidation_key_is_inconsistent));
                    }
                });
    }

    private boolean canPickToLP(String orderConsolidationNo, List<String> lpConsolidationNos) {
        if (CollectionUtil.isNullOrEmpty(lpConsolidationNos)) {
            return true;
        }
        return Stream.of(lpConsolidationNos).allMatch(value -> value.equals(orderConsolidationNo));
    }

    private void toSubmitPick(String toLPId) {
        //2021.1.12，Andy Lee要求先submit picked后再report inventory issue
        submitPick(toLPId, () -> {
            checkToteCart();
            view.onSubmitSuccess();
            checkOnScreenOpCount(getCurrentLocation());
        });
    }

    private void submitPick(String toLPId, DoneHandler doneHandler) {
        asyncExecute(operateWorkItemApi.updateTaskPickResult(data.getTask().id, data.buildUpdateEntry(toLPId)), view,
                error -> {
                    if (ErrorCode.EXCEED_MAXIMUM_ALLOWED_PARTIAL_PALLET.equals(error.code)) {
                        view.showPriorityToPickDialog(error.getErrorMessage(), getPriorityLp(error.getErrorMessage()));
                        return;
                    }
                    view.onPickFailed(error.getErrorMessage());
                },
                success -> doneHandler.onDone());
    }

    private String getPriorityLp(String errorMsg) {
        if (TextUtils.isEmpty(errorMsg)) return "";
        Pattern pattern = Pattern.compile("(\\w+LP)-(\\d+)");
        Matcher matcher = pattern.matcher(errorMsg);
        if (matcher.find()) {
            return matcher.group(0);
        }
        return "";
    }

    private void checkOnScreenOpCount(LocationEntry location) {
        String customerId = getCustomer().orgId;
        OnScreenCountSearchEntry searchEntry = new OnScreenCountSearchEntry(TaskTypeEntry.PICK, OPCountOperatorEntry.IS_AFTER, OPCountActionEntry.SUBMITTING, customerId, location.id);
        execute(cycleCountApi.onScreenCountSearch(searchEntry),
                onScreenCountResponseEntry -> {
                    if (onScreenCountResponseEntry.getOnScreenCount()) {
                        view.showOnScreenOpCountDialog(onScreenCountResponseEntry);
                    } else {
                        actionAfterPickSubmitSuccess();
                    }
                },
                errorResponse -> {
                    ToastUtil.showErrorToast(errorResponse.getErrorMessage());
                    actionAfterPickSubmitSuccess();
                });
    }

    private void actionAfterPickSubmitSuccess() {
        view.finish(false);
    }

    @Override
    public void afterOnScreenOpCount() {
        actionAfterPickSubmitSuccess();
    }

    @Override
    public void onScreenCountMatch(OnScreenCountResponseEntry onScreenCountResponseEntry) {
        OnScreenCountEntry entry = new OnScreenCountEntry(
                getCustomerIdForOnScreenCount(onScreenCountResponseEntry),
                onScreenCountResponseEntry.getLocationId(), getItemSpecIdForOnScreenCount(onScreenCountResponseEntry),
                data.getTask().id, data.getTask().taskType);
        asyncExecute(cycleCountApi.onScreenCountMatch(entry), view, Void -> ToastUtil.showToast(ResUtil.getString(R.string.on_screen_count_complete)));
    }

    @Override
    public void onScreenCountNotMatch(OnScreenCountResponseEntry onScreenCountResponseEntry) {
        OnScreenCountEntry entry = new OnScreenCountEntry(
                getCustomerIdForOnScreenCount(onScreenCountResponseEntry),
                onScreenCountResponseEntry.getLocationId(), getItemSpecIdForOnScreenCount(onScreenCountResponseEntry),
                data.getTask().id, data.getTask().taskType);
        asyncExecute(cycleCountApi.onScreenCountNotMatch(entry), view, Void -> ToastUtil.showToast(ResUtil.getString(R.string.on_screen_count_complete)));
    }



    private String getCustomerIdForOnScreenCount(OnScreenCountResponseEntry onScreenCountResponseEntry) {
        if (CollectionUtil.isNotNullOrEmpty(onScreenCountResponseEntry.getInventories())
                && onScreenCountResponseEntry.getInventories().size() == 1) {
            String customerId = onScreenCountResponseEntry.getInventories().get(0).getCustomerId();
            if (!TextUtils.isEmpty(customerId)) {
                return customerId;
            }
        }
        return getCustomer().orgId;
    }

    private String getItemSpecIdForOnScreenCount(OnScreenCountResponseEntry onScreenCountResponseEntry) {
        if (CollectionUtil.isNotNullOrEmpty(onScreenCountResponseEntry.getInventories())
                && onScreenCountResponseEntry.getInventories().size() == 1) {
            String itemSpecId = onScreenCountResponseEntry.getInventories().get(0).getItemSpecId();
            if (!TextUtils.isEmpty(itemSpecId)) {
                return itemSpecId;
            }
        }
        return data.getIntentData().itemSpecId;
    }

    @Override
    public double suggestPickQty() {
        BatchOrderPickIntentData intentData = data.getIntentData();
        List<OrdersEntry> suggestOrders = data.getSuggestOrders(intentData.orderId, intentData.lpUnitId);
        return CollectionUtil.isNotNullOrEmpty(suggestOrders) ? suggestOrders.get(0).suggestPickQty : 0D;
    }

    @Override
    public void onPrintSoIdClick(String soId) {
        SoIdLabelBatchPrintRequestEntry entry = new SoIdLabelBatchPrintRequestEntry();
        entry.pickTaskId = data.getTask().id;
        entry.qty = 1;
        entry.orderId = data.getIntentData().orderId;
        entry.itemSpecId = data.getIntentData().itemSpecId;
        entry.soIds = Collections.singletonList(soId);

        asyncExecute(labelApi.createSoIdLabelPrintJob(entry), view, responseEntry ->
        {
            if (responseEntry == null || CollectionUtil.isNullOrEmpty(responseEntry.jobIds)) {
                return;
            }

            printData = new PrintData();
            printData.idmUserId = view.getIdmUserId();
            printData.facilityName = view.getFacilityName();
            printData.paperSize = LabelSizeEntry.TWO_ONE;

            printData.jobData = new PrintData.JobData.ZPL(responseEntry.jobIds, null, PrintJobType.SOID_LABEL_JOB);
            printData.extra.isReprint = true;
            printData.extra.soIds = responseEntry.getSoIds(soId);

            printUtil.print(printData);
        });
    }

    @Override
    public void rePrintSoId(PrintData printData) {
        this.printData = printData;
        printUtil.print(printData);
    }

    @Override
    public void generateNewLp(LpTypeEntry lpType) {
        printData = new PrintData();
        printData.paperSize = LabelSizeEntry.TWO_ONE;
        printData.idmUserId = view.getIdmUserId();
        printData.facilityName = view.getFacilityName();
        PrintJobCreate.LpLabel jobCreate = new PrintJobCreate.LpLabel(null,1,lpType,data.getIntentData().orderId);
        printData.jobData = new PrintData.JobData.ZPL(null, jobCreate);

        LpBatchCreateEntry createEntry = new LpBatchCreateEntry();
        createEntry.type = lpType;
        createEntry.count = (int) printData.printQty;
        createEntry.orderId = jobCreate.orderId;

        execute(lpApi.batchCreate(createEntry), lpCreateResponseEntry -> {
            jobCreate.lpIds = lpCreateResponseEntry.lpIds;
            printData.extra.lpIds = lpCreateResponseEntry.lpIds;
            data.setPrintedNewLp(lpCreateResponseEntry.lpIds);
            printUtil.print(printData);
        });
    }

    @Override
    public InventoryIssueParameter getPickIssueParameter() {
        InventoryIssueParameter parameter = new InventoryIssueParameter();
        parameter.taskId = data.getTask().id;
        parameter.taskType = TaskTypeEntry.PICK;
        parameter.codeType = InventoryIssueParameter.CODE_TYPE_ITEM;
        parameter.locationId = data.getIntentData().location.id;
        parameter.locationType = data.getIntentData().location.type;
        parameter.inputtedItemSpecId = data.getIntentData().itemSpecId;
        parameter.inputtedLpId = data.getIntentData().fromLpId;

        return parameter;
    }

    /**
     * rebuild pick strategy
     *
     * @param issueEntries
     * @param successHandler
     */
    private void rebuildPickStrategy(List<InventoryPickIssueCreateEntry> issueEntries, SuccessHandler<List<PickStrategy>> successHandler) {
        PickStrategyRebuildEntry pickStrategyRebuildEntry = new PickStrategyRebuildEntry();
        InventoryPickIssueCreateEntry issueCreateEntry = issueEntries.get(0);
        pickStrategyRebuildEntry.itemSpecId = issueCreateEntry.itemSpecId;
        pickStrategyRebuildEntry.taskId = data.getTask().id;
        pickStrategyRebuildEntry.locationId = data.getIntentData().location.id;
        asyncExecute(orderApi.rebuildTaskPickStrategies(pickStrategyRebuildEntry), view, successHandler);
    }

    @Override
    public void onReportIssue(String functionPwd, String lp, String facilityId) {
        ReportPartialPalletIssueHandle reportIssueHandle = new ReportPartialPalletIssueHandle(() -> ToastUtil.showErrorToast(ResUtil.getString(R.string.toast_report_partial_pallet_issue_succeed)));
        reportIssueHandle.onCheckPwdThenToReportIssue(functionPwd, facilityId, data.getTask().id, lp, view);
    }

    @Override
    public CustomerViewEntry getCustomer() {
        return this.data.getCustomer();
    }

    @Override
    public void verifyItem(String item) {
        if (suggestItem == null || isItemSpecOrAkaMatch(item)) {
            view.verifyPickItemSuccess();
        } else {
            view.verifyPickItemFail();
        }
    }

    private boolean isItemSpecOrAkaMatch(String item) {
        // check ItemSpec
        ItemSpecEntry itemSpec = suggestItem.itemSpec;
        if (itemSpec != null &&
                (item.equals(itemSpec.name) || item.equals(itemSpec.upcCode) ||
                        item.equals(itemSpec.upcCodeCase) || item.equals(itemSpec.eanCode) || item.equals(itemSpec.abbreviation)
                || item.equalsIgnoreCase(itemSpec.getItemSpecId()))
        ) {
            return true;
        }
        // check aka
        if (CollectionUtil.isNotNullOrEmpty(suggestItem.akas) && Stream.of(suggestItem.akas).anyMatch(e -> item.equals(e.value))) {
            return true;
        }
        return false;
    }

    @Override
    public PrintData getPrintData() {
        return printData;
    }

    @Override
    public void searchLocation(String data) {
        if (TextUtils.isEmpty(data)) {
            return;
        }
        LocationSearchEntry searchEntry = new LocationSearchEntry();
        searchEntry.name = data;
        asyncExecute(locationApi.search(searchEntry), view, locations -> {
            if (CollectionUtil.isNullOrEmpty(locations)) {
                view.onErrorToast(ResUtil.format(R.string.msg_location_not_found_by_xx, data));
            } else if (locations.size() == 1) {
                view.onGetLocationSuccess(locations.get(0));
            } else {
                view.onMultipleLocationFound(locations);
            }
        });
    }

    @Override
    public void saveValidationLocation(LocationEntry locationEntry) {
        data.setValidationLocation(locationEntry);
    }

    @Override
    public boolean isValidatedLocation() {
        return data.getValidationLocation() != null;
    }

    @Override
    public LocationEntry getCurrentLocation() {
        return data.getIntentData().location;
    }

    @Override
    public boolean isScanLpToPick() {
        return data.getIntentData().isScanLpToPick;
    }

    @Override
    public String getConsolidation() {
        return data.getIntentData().consolidationNo;
    }

    private void checkToteCart() {
        if (data.isPickToToteCart()) {
            String toteCart = data.getToteCartBarcode();
            asyncExecute(operateWorkItemApi.getToteCartDetail(toteCart), view, toteCartDetailEntry -> {
                if (toteCartDetailEntry == null || toteCartDetailEntry.toteCart == null || CollectionUtil.isNullOrEmpty(toteCartDetailEntry.toteDetails)) {
                    return;
                }
                data.setPickToteCartDetailEntry(toteCartDetailEntry);
            });
        }
    }

    private void loadItemValidateRegex() {
        itemValidateRegex = null;
        ItemSpecSearchEntry searchEntry = new ItemSpecSearchEntry();
        searchEntry.itemSpecId = suggestItem.itemSpec.id;
        execute(itemSpecAPI.getItemRule(searchEntry), rules -> {
            ItemSNValidateRuleEntry itemSNValidateRuleEntry = Stream.of(rules).filter(rule -> TextUtils.equals(rule.itemSpecId, suggestItem.itemSpec.id)).findSingle().orElse(null);
            if (null != itemSNValidateRuleEntry) {
                itemValidateRegex = itemSNValidateRuleEntry.validateRegex;
            }

        }, errorResponse -> ToastUtil.showErrorToast(errorResponse.getErrorMessage()));
    }
}
