package com.linc.platform.pick.v1.presenter;

import com.linc.platform.pick.model.newpick.BarcodeTypeEntry;
import com.linc.platform.pick.v1.presenter.action.PickAction;
import com.linc.platform.pick.v1.view.PickWorkView;

/**
 * <AUTHOR>
 */
public interface CheckItemMatchedHandler {
    CheckItemMatchedHandler init(PickAction pickAction, PickWorkView pickWorkView);

    void printILP(BarcodeTypeEntry barcodeType, SuccessHandlerWithoutParamter successHandler);
}
