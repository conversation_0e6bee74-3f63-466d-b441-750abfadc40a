package com.linc.platform.pick.presenter;

import android.content.Context;
import android.net.wifi.ScanResult;

import com.linc.platform.baseapp.model.LocationEntry;
import com.linc.platform.baseapp.model.LocationPagingResultEntry;
import com.linc.platform.baseapp.model.LocationSearchEntry;
import com.linc.platform.baseapp.model.LocationTypeEntry;
import com.linc.platform.bluetooth.model.Bluetooth;
import com.linc.platform.bluetooth.presenter.BluetoothLocationDataDao;
import com.linc.platform.cctask.model.CcTaskCheckLpEntry;
import com.linc.platform.common.BarcodeTypeSearchEnum;
import com.linc.platform.common.handler.DoneHandler;
import com.linc.platform.common.handler.SuccessHandler;
import com.linc.platform.common.step.StepTypeEntry;
import com.linc.platform.core.ProgressView;
import com.linc.platform.cyclecount.model.OnScreenCountResponseEntry;
import com.linc.platform.foundation.model.CustomerViewEntry;
import com.linc.platform.home.more.takeovermanage.TaskTakeOverCreateEntry;
import com.linc.platform.inventory.model.ReturnLpDetailEntry;
import com.linc.platform.pick.model.InventoryIssueParameter;
import com.linc.platform.pick.model.InventoryIssueReportSuccessEvent;
import com.linc.platform.pick.model.InventoryMovement;
import com.linc.platform.pick.model.InventoryPickIssueCreateEntry;
import com.linc.platform.pick.model.ItemReturnToInventoryEntry;
import com.linc.platform.pick.model.ItemViewEntry;
import com.linc.platform.pick.model.LPBatchUpdateEntry;
import com.linc.platform.pick.model.LpItemLotNoEntry;
import com.linc.platform.pick.model.PickLocationViewEntry;
import com.linc.platform.pick.model.PickResultUpdateEntry;
import com.linc.platform.pick.model.PickStrategy;
import com.linc.platform.pick.model.PickStrategyRebuildEntry;
import com.linc.platform.pick.model.PickTaskViewEntry;
import com.linc.platform.pick.model.SpecItemEntry;
import com.linc.platform.pick.model.newpick.BarcodeTypeEntry;
import com.linc.platform.pick.model.ordergroup.SuggestBodyEntry;
import com.linc.platform.pick.model.partial.LoadDateEntry;
import com.linc.platform.pick.model.picktote.PickModeData;
import com.linc.platform.pick.view.BatchOrderAllocateView;
import com.linc.platform.pick.view.OperateWorkItemView;
import com.linc.platform.pick.view.OrderGroupView;
import com.linc.platform.pick.view.PickPartialByOrderView;
import com.linc.platform.pick.view.PickPartialLpView;
import com.linc.platform.pick.view.PickTaskView;
import com.linc.platform.pick.view.PresetView;
import com.linc.platform.pick.view.ReturnInventoryItemView;
import com.linc.platform.pick.view.ReturnPickedToInventoryView;
import com.linc.platform.pick.view.ScanLocationToPickView;
import com.linc.platform.pick.view.ScanLpToPickView;
import com.linc.platform.pick.view.ScanSnToPickView;
import com.linc.platform.pick.view.StageView;

import java.util.List;


/**
 * <AUTHOR>
 */
public interface OperateWorkItemPresenter {
    void takeOverStep(TaskTakeOverCreateEntry createEntry, String companyId);

    void checkIfRequireReplenishBeforePick(String stepId, String companyId);

    void startStep(String stepId, String companyId);

    void getLocationId(LocationSearchEntry locationSearchEntry);

    void getLocationId(LocationSearchEntry locationSearchEntry, String aisle, String bay);

    PickLocationViewEntry getSuggestLocation();

    void loadLpData(String locationId, String locationName);

    void loadItemView();

    void loadSnData();

    void loadPartialData();

    void loadLocationData();

    void loadStageData();

    void addScanLpDone(String fromLpId, String toLpId, LocationEntry stageLocationEntry, SpecItemEntry pickItem, boolean mixItemOnFormLp, ProgressView progressView);

    void setFromLpByOrder(String fromLpId);

    void removeScanSnDone(String sn);

    void doPartialSubmit(PickResultUpdateEntry entry);

    void doBatchSubmit(List<PickResultUpdateEntry> entries);

    void submitPartial(PickResultUpdateEntry entry);

    void submitScanSn(PickResultUpdateEntry entry);

    void setPickTaskView(PickTaskView pickTaskView);

    void setOperateWorkItemView(OperateWorkItemView operateWorkItemView);

    void setScanLocationView(ScanLocationToPickView scanLocationToPickView);

    void setScanLpToPickView(ScanLpToPickView scanLpToPickView);

    void setBatchOrderView(BatchOrderAllocateView batchOrderView);

    void setPickPartialLpView(PickPartialLpView pickPartialLpView);

    void setOrderGroupView(OrderGroupView orderGroupView);

    void setScanSnToPickView(ScanSnToPickView scanSnToPickView);

    void setStageView(StageView stageView);

    void setLocationId(String locationId);

    void completePickTask();

    void completeStep(String reason, StepTypeEntry stepType);

    void closeStep(StepTypeEntry stepTypeEntry, String userId);

    void forceCloseStep(StepTypeEntry stepTypeEntry, String userId);

    void movement(InventoryMovement movement);

    void reopenSubTask();

    void forceCloseTask();

    PickTaskViewEntry getSubTask();

    void doStage(LPBatchUpdateEntry entry);

    void filterLpByOrder(String orderId);

    void setDoneStatus();

    void getLpStatusOfPartial(String lpId);

    int getTaskCompleteCount(List<ItemViewEntry> itemViewEntries);

    void getLpStatusAndSubmit(PickResultUpdateEntry entry);

    void getLpDetail(String lpId, String itemSpecId);

    void getLpDetailForScanLpToPick(String lpId, String itemSpecId, boolean isScanLp);

    void getLpMsg(String lpId, LocationEntry selectedLocation);

//    void jumpToStage();

    void jumpToOrderAllocate();

    /***********
     * order gruoup
     ************************/

    void loadOrderIds();

    void updateOrderIds();

    // void getOrderItemLines(String orderId);

    void getLowLevelItemLines(String orderId);

    void updateLowLevelItemLines(String orderId);

    void getQualifiedLPs(String orderId, SuggestBodyEntry entry);

    String getSubTaskId();
    /********* order gruoup ************************/

    /*********
     * return to inventory
     *****************/

    void getDetailOfLp(String lpId);

    void setReturnPickedToInventoryView(ReturnPickedToInventoryView view);

    void returnItemToInventory(List<ItemReturnToInventoryEntry> entry);

    void setReturnInventoryItemView(ReturnInventoryItemView view);

    void getPickedLP();

    /********* return to inventory *****************/

    /***************
     * preset
     **********************/
    void setPresetView(PresetView presetView);

    void setPresetData(String tolp, LocationEntry selectedLocation);

    void loadPresetData();

    /***************
     * preset
     **********************/

    //pick by order
    void setPickPartialByOrderView(PickPartialByOrderView view);

    void loadPickDataByOrder();

    void submitPartialByOrder(PickResultUpdateEntry entry);

    //batch order pick
    void loadPickSuggestByOrder();

    void addBatchOrderAllocate(String fromLpId, String itemSpecId);

    void bindLpToOrder(String lpId, String orderId, int position);

    void assignStep(String userId, String stepId, String companyId);

    void loadStageOrder();

    void getSuggestLocation(LocationEntry lastPickLocation);

    void findNearestPickTask(String idmUserId);

    void findTaskLocation(String taskId);

    boolean isStepDone(StepTypeEntry stepTypeEntry);

    LocationEntry getCurrentLocation();

    String getDirectPickLocation();

    void setDirectPickLocation(String location);

    void getHiddenLp(LocationEntry location);

    void findItemByKey(String data, List<ItemViewEntry> itemViewEntryList, BarcodeTypeEntry barcodeTypeEntry);

    void getHiddenLpToPick(String itemSpecId);

    void loadFacility(String facilityId, PickLocationViewEntry lastViewEntry);

    void loadFacility(boolean isInit, List<InventoryPickIssueCreateEntry> issues);

    boolean isReLoadFacility();

    boolean needLocationSuggestionByCurrentLocation();

    String getPickItemSpecId();

    void queryPendingPackLPIds(boolean isForceClose, String dialogString);

    void getSuggestItemDetail();

    void checkIsBottleUpc(String data, String itemSpecId);

    boolean allowPickByClickItem();

    void createCcTask(List<CcTaskCheckLpEntry> list);

    void loadLpSn(String lpId);

    boolean isCloseLocationSuggestion();

    void searchInventoryToCheckSN(List<String> snList);

    void taskOverCcTask(String taskId, String userId);

    void searchCCTaskById(String taskId);

    void getCcTaskById(String taskId);

    void getBarcodeType(String barcode, String customerId, List<BarcodeTypeSearchEnum> scope, SuccessHandler<List<BarcodeTypeEntry>> successHandler);

    void getInventoryBySn(String sn);

    void setScannedSn(String sn);

    void loadLocationSuggestion(List<String> submitLps, String stepId);

    void autoCompleteLP(String lpStr, SuccessHandler<List<String>> successHandler);

    void getLPByToteId(String data, String taskId);

    String getPickToLP(String toLpEdtTxt);

    CustomerViewEntry getCustomer();

    boolean isWavePickByItem(PickTaskViewEntry pickTaskViewEntry);

    boolean allowPickByQTYOnWavePickOfSNItem(CustomerViewEntry customerEntry);

    void loadCustomerByItem(String itemSpecId);

    void loadLPShippingSN(String lpId);

    List<String> getLPShippingSNList();

    void getReturnInventoryLP(String data, String taskId);

    void checkSN(String data, List<String> scannedSNList, LoadDateEntry loadDateEntry, String orderId, boolean isMasterSN);

    void loadSuggestVLG();

    void searchStageLocation(String data);

    void verifyItem(String item);

    boolean isPickToLoadTask();

    boolean haveOpenSLP();

    void loadTaskAssociatedLPList();

    String getTaskUnStageSLP();

    void checkPickLP(String data);

    boolean noMaterialSelected();

    void checkStageLP(String data);

    List<ItemViewEntry> getItemViewList();

    boolean pickEnough(List<ItemViewEntry> itemViewEntries);

    void getLPInventories(String fromLp);

    void reloadPickSuggestByItem(List<ReturnLpDetailEntry> lpDetailEntries, String itemSpecName);

    void loadTaskMaterial(String pickTaskId);

    String getItemSuggestPalletMaterial(ItemViewEntry itemViewEntry);

    void rebuildStrategyAndGetNextSuggestLocation(PickLocationViewEntry location, PickStrategyRebuildEntry rebuildEntry);

    void rebuildStrategyAndGetNextSuggestLocation(LocationEntry locationEntry, PickStrategyRebuildEntry rebuildEntry);

    void getSuggestLocationAfterRebuildStrategy(PickLocationViewEntry locationViewEntry);

    void getSuggestLocationAfterRebuildStrategy(LocationEntry locationEntry);

    void setInventoryIssueCodeType(String codeType);

    InventoryIssueParameter getPickIssueParameter();

    void setPickIssueDetail(String lpId, String itemSpecId, String description);

    void setPickIssueLocation(String locationId, LocationTypeEntry type);

    void clearPickIssueParameter();

    void setPickIssueDescription(String description);

    void searchSuggestStageLocation(String stagingLocationId);

    void upLoadLocationWifiSignal(String wifiSignalLocationName, List<ScanResult> results);

    void addReportedPickIssue(InventoryIssueReportSuccessEvent event);

    List<InventoryPickIssueCreateEntry> getHaveReportedPickedIssues();

    void searchLotNoInventoryToPick(String lotNo, List<ItemViewEntry> locationUnfinishedItems, List<ItemViewEntry> itemViewEntryList);

    void onPickLotNoFound(LocationEntry locationEntry, String pickItemSpecId, List<ItemViewEntry> itemViewEntryList);

    void setAllowPickOtherVLG(boolean allowPickOtherVLG);

    void attemptToAssignCrossVLGTaskToOther();

    void assignPickTask(String userId);

    PickStrategyRebuildEntry buildPickStrategyRebuildEntryWithInventoryIssue(List<InventoryPickIssueCreateEntry> issueEntries);

    void rebuildPickStrategy(PickStrategyRebuildEntry rebuildEntry, SuccessHandler<List<PickStrategy>> successHandler);

    void doZonePickVLGValidationForLocation(PickLocationViewEntry location, DoneHandler nextHandler);

    void onCreateTaskStepProcessTimeEntry();

    void onCommitTaskStepProcessTime();

    void onGetLocationIdByLpThenReportPartialPalletIssue(String lp);

    void onReportIssue(String functionPwd, String lp, String facilityId, ProgressView progressView);

    void checkOverrideNextLocation(DoneHandler nextHandler);

    void autoPrintItemLabel(String itemSpecId, String userId);

    void getNextSuggestLocation();

    void getPickStageBarcodeType(String lpStr, SuccessHandler<List<BarcodeTypeEntry>> successHandler);

    void getAndOpenHelpPage(Context context, String helpPageKey, String facilityId);

    boolean isNotAllowOverrideStageSuggestedLocation();

    void getSuggestStageLocation();

    void getSuggestStageLocationOfRetailer(SuccessHandler<LocationPagingResultEntry> successHandler);

    void getSuggestLocationAndItemView(LocationEntry lastPickLocation);

    void getUnStageSLP();

    boolean isAllowUserPrintLP();

    boolean isPickToTracking();

    void loadPickToTrackingNos();

    void checkLocationOrTracking(String barcode, SuccessHandler<BarcodeTypeEntry> resultHandler);

    void scanTrackingToGetLocation(String trackingNumber);

    void searchLocation(String data, String aisle, String bay, boolean locationSegmentedScanning);

    void searchLocation(LocationEntry locationEntry);

    void saveValidationLocation(LocationEntry locationEntry);

    boolean isValidatedLocation();

    boolean isScanLpToPick();

    boolean isAllowAutoLocationArrivedDetection();

    void locateSuggestLocationByBlueTooth(List<Bluetooth> bluetoothList, BluetoothLocationDataDao bluetoothLocationDataDao);

    boolean isRequireScanLotNoOnPicking();

    LpItemLotNoEntry matchingLotNo(String lotNo);

    void setPickModeData(PickModeData pickModeData);

    PickModeData getPickModeData();

    void syncToteCart();

    void afterOnScreenOpCount(PickResultUpdateEntry pickResultUpdateEntry, boolean isPickToTracking);

    void onScreenCountMatch(OnScreenCountResponseEntry onScreenCountResponseEntry);

    void onScreenCountNotMatch(OnScreenCountResponseEntry onScreenCountResponseEntry);

    void loadItemValidateRegex();
}
