package com.linc.platform.pick.view;

import java.util.List;

/**
 * Created by devinc on 2018/5/11.
 */
public interface CountUnshippedView {
    void removeSlp(String slp);

    void showProcess(boolean isShow);

    void showUnshippedSlp(List<String> unshippedSlpIds);

    void onAddSlpSuccess();

    void onRemoveSlpSuccess(String slp);

    void onCompleteLoadSuccess();

    void onCheckIsNeedScanSn(String lp, boolean isNeed);

    void confirmForceCompleteLoad(String loadId, String errorMessage);
}
