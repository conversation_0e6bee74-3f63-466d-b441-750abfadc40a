package com.linc.platform.pick.model;

import android.text.TextUtils;

import com.annimon.stream.Stream;
import com.google.gson.annotations.SerializedName;
import com.linc.platform.baseapp.model.VirtualLocationGroupViewEntry;
import com.linc.platform.common.lp.LocationGroupEntry;
import com.linc.platform.common.lp.LocationTagEntry;
import com.linc.platform.common.lp.LpTypeEntry;
import com.linc.platform.common.step.StepBaseEntry;
import com.linc.platform.common.step.StepStatusEntry;
import com.linc.platform.common.step.StepTypeEntry;
import com.linc.platform.foundation.model.CustomerViewEntry;
import com.linc.platform.foundation.model.OrderTypeEntry;
import com.linc.platform.foundation.model.organization.common.facility.FacilityEntry;
import com.linc.platform.generaltask.model.GeneralTaskViewEntry;
import com.linc.platform.pick.model.newpick.PickRoundEntry;
import com.linc.platform.pick.model.ordergroup.OrderEntry;
import com.linc.platform.utils.CollectionUtil;
import com.linc.platform.utils.Lists;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.linc.platform.pick.model.PickWayEntry.WAVE_PICK_BY_ITEM;
import static com.linc.platform.utils.CustomerConfigUtil.allowManualEntry;

/**
 * <AUTHOR>
 */
public class PickTaskViewEntry extends GeneralTaskViewEntry implements Serializable {
    public static final String TAG = PickTaskViewEntry.class.getSimpleName();

    @SerializedName("orderIds")
    public List<String> orderIds;

    @SerializedName("orders")
    public List<OrderEntry> orders;

    @SerializedName("orderPlanId")
    public String orderPlanId;

    @SerializedName("customer")
    public CustomerViewEntry customerEntry;

    @SerializedName("pickType")
    public PickTypeEntry pickType;

    @SerializedName("pickWay")
    public PickWayEntry pickWay;

    @SerializedName("planMethod")
    public PlanMethodEntry planMethod;

    @SerializedName("isRush")
    public boolean isRush;

    @SerializedName("plannedStartTime")
    public Date plannedStartTime;

    @SerializedName("plannedEndTime")
    public Date plannedEndTime;

    @SerializedName("pickItemLines")
    public List<PickItemLineEntry> pickItemLines;

    @SerializedName("pickHistories")
    public List<PickHistoryEntry> pickHistories;

    @SerializedName("returnHistories")
    public List<ReturnHistoryEntry> returnHistories;

    @SerializedName("longHaulIds")
    public List<String> longHaulIds = new ArrayList<>();

    @SerializedName("pickRounds")
    public List<PickRoundEntry> pickRounds = new ArrayList<>();

    @SerializedName("isPickToOrder")
    public boolean isPickToOrder;

    @SerializedName("skipCLP")
    public boolean skipCLP = false;

    @SerializedName("isForSingleQty")
    public boolean isForSingleQty = false;

    @SerializedName("isClosePickSuggest")
    public boolean isClosePickSuggest;

    @SerializedName("taskHLPId")
    public String taskHLPId;

    @SerializedName("stagingLocation")
    public String stagingLocationId;

    @SerializedName("pickedQty")
    public int pickedQty = 0;

    @SerializedName("pickedBaseQty")
    public int pickedBaseQty = 0;

    @SerializedName("pickedItem")
    public int pickedItem = 0;

    @SerializedName("totalItem")
    public int totalItem = 0;

    @SerializedName("pickedUnitName")
    public String pickedUnitName;

    @SerializedName("truckPickUps")
    public List<String> truckPickUps;

    @SerializedName("pickStrategies")
    public List<PickStrategy> pickStrategies = new ArrayList<>();

    @SerializedName("enforceSelectSuggestStageLocation")
    public boolean enforceSelectSuggestStageLocation;

    @SerializedName("forbidGenerateLPLabelAtPickTask")
    public boolean forbidGenerateLPLabelAtPickTask;

    @SerializedName("printSoIdAtPackWorkstation")
    public boolean printSoIdAtPackWorkstation;

    @SerializedName("requireScanSoldAtPackWorkstation")
    public boolean requireScanSoldAtPackWorkstation;
    
    public SpecItemEntry pickItem;

    public int taskPriority;

    @SerializedName("zonePickVirtualLocationGroupId")
    public String zonePickVirtualLocationGroupId;

    @SerializedName("zonePickLocationTags")
    public List<LocationTagEntry> zonePickLocationTags;

    @SerializedName("facility")
    public FacilityEntry facilityEntry;

    @SerializedName("equipmentBarcode")
    public String equipmentBarcode;

    @SerializedName("pickToLPType")
    public String pickToLPType;

    @SerializedName("pickMode")
    public PickModelEntry pickMode;

    @SerializedName("customerNames")
    public List<String> customerNames;

    @SerializedName("customerPrintNames")
    public List<String> customerPrintNames;

    @SerializedName("retailerNames")
    public List<String> retailerNames;

    @SerializedName("retailerPrintNames")
    public List<String> retailerPrintNames;

    @SerializedName("requiredEquipments")
    public List<String> requiredEquipments;

    /**
     * Check if this task a zone-pick task.
     * It's determined by 'virtualLocationGroupId' field and 'restrictCrossZoneOnZonePickTask' tag of customer.
     *
     * @return boolean isZonePick
     */
    public boolean isZonePick() {
        return zonePickVirtualLocationGroupId != null && !zonePickVirtualLocationGroupId.isEmpty()
                && customerEntry.restrictCrossZoneOnZonePickTask;
    }

    public boolean isNeedForceClose() {
        boolean hasForceClose = false;
        for (StepBaseEntry entry : stepEntries) {
            if (entry.status != StepStatusEntry.DONE
                    && entry.status != StepStatusEntry.FORCE_CLOSED) {
                return false;
            }

            if (entry.status == StepStatusEntry.FORCE_CLOSED) {
                hasForceClose = true;
            }
        }

        return hasForceClose;
    }

    public boolean isNeedClose() {
        for (StepBaseEntry entry : stepEntries) {
            if (entry.status != StepStatusEntry.DONE && entry.status != StepStatusEntry.FORCE_CLOSED) {
                return false;
            }
        }
        return true;
    }

    public boolean isNeedAutoForceClose() {
        if (stepEntries != null && stepEntries.size() == 1
                && stepEntries.get(0).type.equals(StepTypeEntry.PICK)
                && stepEntries.get(0).status == StepStatusEntry.FORCE_CLOSED) {
            return true;
        }
        return false;
    }

    public boolean isTaskRush() {
        return isRush;
    }

    public boolean isOrderNeedSuggestLocation() {
        for (OrderEntry order : orders) {
            if (order.orderType != null
                    && (order.orderType == OrderTypeEntry.MT || order.orderType == OrderTypeEntry.TT)) {
                return true;
            }
        }
        return false;
    }

    public double getTotalQty() {
        return Stream.of(pickItemLines).mapToDouble(i -> i.qty).sum();
    }

    public int getPickedQty() {
        return pickedQty;
    }

    public boolean isWavePick() {
        return WAVE_PICK_BY_ITEM.equals(this.pickWay);
    }

    public boolean isAllowManualEntry() {
        return allowManualEntry(customerEntry);
    }

    public boolean isDNForSingleItem() {
        for (OrderEntry orderEntry : Lists.ensureNotNull(orders)) {
            if (!orderEntry.isSingleItem()) return false;
        }
        return true;
    }

    public boolean isDNForConsolidation() {
        for (OrderEntry orderEntry : Lists.ensureNotNull(orders)) {
            if (!orderEntry.isConsolidation()) return false;
        }
        return true;
    }

    public boolean isForbidForceCloseStepsFromPickTask() {
        if (customerEntry == null) {
            return false;
        }
        return customerEntry.forbidForceCloseStepsFromPickTask;
    }

    public String getPickToLPType() {
        return !TextUtils.isEmpty(pickToLPType) ? pickToLPType : LpTypeEntry.CLP.name();
    }

    public boolean hasStageStep() {
        return getStep(StepTypeEntry.STAGE) != null;
    }
}
