package com.linc.platform.pick.api;

import com.linc.platform.http.IdResponse;
import com.linc.platform.load.model.FileEntry;
import com.linc.platform.pick.model.FileIdResponse;
import com.linc.platform.pick.model.LabelPrintStatus;
import com.linc.platform.pick.model.UCCLabelPrint;
import com.linc.platform.pick.model.batchprint.CreateSmallParcelShipmentRequest;
import com.linc.platform.pick.model.batchprint.OrderItemLineSearch;
import com.linc.platform.pick.model.batchprint.OrderItemLineSearchResult;
import com.linc.platform.pick.model.batchprint.SmallParcelShipmentCreateResult;
import com.linc.platform.print.model.LabelPrintEntry;

import java.util.List;

import retrofit2.Response;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.PUT;
import retrofit2.http.Path;
import rx.Observable;

/**
 * Created by Gavin
 */
public interface PickTaskBatchPrintAPI {
    @POST("bam/outbound/batch-order/order-item-line/search")
    Observable<Response<OrderItemLineSearchResult>> searchOrderItemLine(@Body OrderItemLineSearch search);

    @POST("wms-app/pallet-label/orders")
    Observable<Response<FileIdResponse>> printPalletLabelByOrders(@Body List<String> orderIds);

    @POST("wms-app/ucc-label/{orderId}")
    Observable<Response<FileIdResponse>> printUCCLabel(@Path("orderId") String orderId, @Body UCCLabelPrint uccLabelPrint);

    @GET("wms-app/outbound/order/{orderId}/packing-list/print")
    Observable<Response<FileEntry>> createPackingListFileIdByOrder(@Path("orderId") String orderId);

    @GET("wms-app/pick-ticket-label/task/{taskId}")
    Observable<Response<IdResponse>> printPickTicket(@Path("taskId") String taskId);

    @POST("bam/wms-app/outbound/small-parcel-shipment")
    Observable<Response<SmallParcelShipmentCreateResult>> createSmallParcelShipment(@Body CreateSmallParcelShipmentRequest request);

    @PUT("wms-app/small-parcel-shipment/mark-as-printed/{trackingNo}")
    Observable<Response<Void>> markTrackingNoAsPrinted(@Path("trackingNo") String trackingNo);

    @GET("wms-app/outbound/order/packing-list/{trackingNo}/print")
    Observable<Response<FileEntry>> createPackingListFileIdByTrackingNo(@Path("trackingNo") String trackingNo);

    @GET("wms-app/packaging-ticket-label/tracking-no/{trackingNo}")
    Observable<Response<IdResponse>> createPackingListJobIdByTrackingNo(@Path("trackingNo") String trackingNo);

    @POST("bam/label/can-print")
    Observable<Response<List<LabelPrintEntry>>> getCanPrintLabelType(@Body OrderItemLineSearch search);

    @PUT("wms-app/outbound/order/{orderId}/printLabelSign/update")
    Observable<Response<IdResponse>> makeLabelAsPrinted(@Path("orderId") String orderId, @Body LabelPrintStatus printStatusUpdate);
}
