package com.linc.platform.pick.model;

import com.google.gson.annotations.SerializedName;
import com.linc.platform.foundation.model.DiversePropertyEntry;
import com.linc.platform.foundation.model.ItemAkaViewEntry;
import com.linc.platform.foundation.model.UnitEntry;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class SpecItemEntry implements Serializable {

    @SerializedName("itemSpecName")
    public String itemSpecName;

    @SerializedName("productId")
    public String productId;

    @SerializedName("itemSpecId")
    public String itemSpecId;

    @SerializedName("baseUnitName")
    public String baseUnitName;

    @SerializedName("unitName")
    public String unitName;

    @SerializedName("originalItemSpecName")
    public String originalItemSpecName;

    @SerializedName("upcCode")
    public String upcCode;

    @SerializedName("upcCodeCase")
    public String upcCodeCase;

    @SerializedName("diverseProperties")
    public List<DiversePropertyEntry> diverseProperties = new ArrayList<>();

    @SerializedName("itemAkas")
    public List<ItemAkaViewEntry> itemAkaList = new ArrayList<>();

    @SerializedName("itemUnits")
    public List<UnitEntry> itemUnits;

}
