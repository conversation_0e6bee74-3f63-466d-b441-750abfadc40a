package com.linc.platform.cctask.model;

import android.text.TextUtils;

import com.annimon.stream.Collectors;
import com.annimon.stream.Stream;
import com.google.gson.annotations.SerializedName;
import com.linc.platform.foundation.model.ItemDiverseViewEntry;
import com.linc.platform.generaltask.model.GeneralTaskViewEntry;

import java.io.Serializable;

/**
 * <AUTHOR>
 */

public class CcTaskViewEntry extends GeneralTaskViewEntry implements Serializable {
    public static final String TAG = CcTaskViewEntry.class.getSimpleName();

    @SerializedName("itemSpecId")
    public String itemSpecId;

    @SerializedName("productId")
    public String productId;

    @SerializedName("unitId")
    public String unitId;

    @SerializedName("taskWay")
    public CcTaskWayEntry taskWay;

    @SerializedName("singleLPTemplateId")
    public String singleLPTemplateId;

    @SerializedName("itemCount")
    public Double itemCount;

    @SerializedName("packagingTypeSpecId")
    public String packagingTypeSpecId;

    @SerializedName("packagingTypeProductId")
    public String packagingTypeProductId;

    @SerializedName("product")
    public ItemDiverseViewEntry product;

    @SerializedName("itemSpecName")
    public String itemSpecName;

    @SerializedName("unitName")
    public String unitName;

    @SerializedName("packagingTypeSpecName")
    public String packagingTypeSpecName;

    @SerializedName("packagingTypeProduct")
    public ItemDiverseViewEntry packagingTypeProduct;

    @SerializedName("lpTemplate")
    public LpTemplateEntry lpTemplate;

    @SerializedName("pickTaskId")
    public String pickTaskId;

    public String getPackingProduct() {
        if (packagingTypeProduct != null
                && packagingTypeProduct.diverseProperties != null
                && packagingTypeProduct.diverseProperties.size() > 0) {
            return Stream.of(packagingTypeProduct.diverseProperties)
                    .filter(entry -> !TextUtils.isEmpty(entry.name) && !TextUtils.isEmpty(entry.value))
                    .map(entry -> entry.name + ": " + entry.value)
                    .collect(Collectors.joining(" | "));
        }

        return "";
    }

    public double getTotalQty() {
        return itemCount;
    }
}
