package com.linc.platform.yms.checkin.presenter.impl;

import android.text.TextUtils;

import com.linc.platform.common.EntryTicketCheckEntry;
import com.linc.platform.core.ServiceFactory;
import com.linc.platform.foundation.api.FileEntryApi;
import com.linc.platform.foundation.model.EquipmentTypeEntry;
import com.linc.platform.http.ErrorCodeSubscriber;
import com.linc.platform.http.ErrorResponse;
import com.linc.platform.utils.RxUtil;
import com.linc.platform.yms.checkin.api.CheckInRejectEntry;
import com.linc.platform.yms.checkin.api.GateCheckInApi;
import com.linc.platform.yms.checkin.model.BlacklistEntry;
import com.linc.platform.yms.checkin.model.BlacklistSearchEntry;
import com.linc.platform.yms.checkin.model.BlacklistValidateEntry;
import com.linc.platform.yms.checkin.model.EntryTicketUpdateEntry;
import com.linc.platform.yms.checkin.model.GateCheckInEntry;
import com.linc.platform.yms.checkin.model.RejectBlackListEntry;
import com.linc.platform.yms.checkin.presenter.TruckInfoPresenter;
import com.linc.platform.yms.checkin.view.TruckInfoView;
import com.linc.platform.yms.model.YmsFileEntryBatchCreateEntry;

import java.util.ArrayList;
import java.util.List;

import retrofit2.Response;
import rx.android.schedulers.AndroidSchedulers;
import rx.schedulers.Schedulers;

/**
 * Created by devinc on 2016/12/7.
 */

public class TruckInfoPresenterImpl implements TruckInfoPresenter {
    private TruckInfoView truckInfoView;
    private GateCheckInApi checkInApi;

    public TruckInfoPresenterImpl(TruckInfoView truckInfoView) {
        this.truckInfoView = truckInfoView;
        checkInApi = ServiceFactory.createRetrofitService(GateCheckInApi.class);
    }

    @Override
    public void checkBlackList(String lp, String carrierName, String mcdot) {
        truckInfoView.showProgress(true);
        List<BlacklistSearchEntry> searchEntryList = new ArrayList<>();
        BlacklistSearchEntry lpEntry = new BlacklistSearchEntry();
        lpEntry.type = BlacklistSearchEntry.BLACKLIST_TYPE_LICENSE_PLATE;
        lpEntry.value = lp;
        searchEntryList.add(lpEntry);

        if (!TextUtils.isEmpty(carrierName)) {
            BlacklistSearchEntry carrierNameEntry = new BlacklistSearchEntry();
            carrierNameEntry.type = BlacklistSearchEntry.BLACKLIST_TYPE_CARRIER;
            carrierNameEntry.value = carrierName;
            searchEntryList.add(carrierNameEntry);
        }

        if (!TextUtils.isEmpty(mcdot)) {
            BlacklistSearchEntry mcdotEntry = new BlacklistSearchEntry();
            mcdotEntry.type = BlacklistSearchEntry.BLACKLIST_TYPE_MCDOT;
            mcdotEntry.value = mcdot;
            searchEntryList.add(mcdotEntry);
        }

        BlacklistValidateEntry validateEntry = new BlacklistValidateEntry();
        validateEntry.blacklistValidateDatas = searchEntryList;

        checkInApi.validateBlackList(ServiceFactory.getYard(), validateEntry)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeOn(Schedulers.io())
                .subscribe(new ErrorCodeSubscriber<Response<List<BlacklistEntry>>>() {
                    @Override
                    public void onSuccess(Response<List<BlacklistEntry>> listResponse) {
                        if (listResponse != null
                                && listResponse.body() != null) {
                            List<BlacklistEntry> result = listResponse.body();
                            if (result.size() > 0) {
                                truckInfoView.inBlackList(result);
                            } else {
                                truckInfoView.notInBlackList();
                            }
                        }
                    }

                    @Override
                    public void onFailed(ErrorResponse errorResponse) {
                        truckInfoView.showFail(errorResponse.error);
                    }

                    @Override
                    public void onDone() {
                        truckInfoView.showProgress(false);
                    }
                });
    }

    @Override
    public void uploadDailyPickUpPhotos(List<String> photos, String entryId) {
        FileEntryApi fileEntryApi = ServiceFactory
                .createRetrofitService(FileEntryApi.class);

        YmsFileEntryBatchCreateEntry createEntry = new YmsFileEntryBatchCreateEntry();
        createEntry.fileIds = photos;
        createEntry.fileType = YmsFileEntryBatchCreateEntry.FILE_TYPE_PHOTO;
        createEntry.fileScenario = YmsFileEntryBatchCreateEntry.FILE_SCENARIO_GATE_CHECK_IN;
        createEntry.fileCategory = YmsFileEntryBatchCreateEntry.FILE_CATEGORY_DAILY_PICKUP;
        createEntry.tags.add(entryId);
        fileEntryApi.ymsBatchCreate(ServiceFactory.getYard(), createEntry)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeOn(Schedulers.io())
                .subscribe(new ErrorCodeSubscriber<Response<Void>>() {
                    @Override
                    public void onSuccess(Response<Void> voidResponse) {
                    }

                    @Override
                    public void onFailed(ErrorResponse errorResponse) {
                        truckInfoView.showFail(errorResponse.error);
                    }

                    @Override
                    public void onDone() {

                    }
                });
    }

    @Override
    public void reject(String entryId, List<BlacklistEntry> result) {
        CheckInRejectEntry rejectEntry = new CheckInRejectEntry();
        rejectEntry.entryId = entryId;
        for (BlacklistEntry blacklistEntry : result) {
            switch (blacklistEntry.type) {
                case RejectBlackListEntry.TYPE_DRIVER_NAME:
                    rejectEntry.driverName = blacklistEntry.value;
                    break;
                case RejectBlackListEntry.TYPE_CARRIER:
                    rejectEntry.carrierName = blacklistEntry.value;
                    break;
                case RejectBlackListEntry.TYPE_DIRVER_LICENSE:
                    rejectEntry.driverLicense = blacklistEntry.value;
                    break;
                case RejectBlackListEntry.TYPE_LICENSE_PLATE:
                    rejectEntry.licensePlate = blacklistEntry.value;
                    break;
                case RejectBlackListEntry.TYPE_MCDOT:
                    rejectEntry.mcDot = blacklistEntry.value;
                    break;
                default:
                    break;
            }

            RejectBlackListEntry entry = new RejectBlackListEntry();
            entry.id = blacklistEntry.id;
            entry.type = blacklistEntry.type;
            entry.value = blacklistEntry.value;
            entry.expiredDate = blacklistEntry.expiredDate;
            entry.effectiveDate = blacklistEntry.effectiveDate;
            rejectEntry.rejectBlackLists.add(entry);
        }
        checkInApi.reject(ServiceFactory.getYard(), entryId, rejectEntry)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeOn(Schedulers.io())
                .subscribe(new ErrorCodeSubscriber<Response<Void>>() {
                    @Override
                    public void onSuccess(Response<Void> voidResponse) {

                    }

                    @Override
                    public void onFailed(ErrorResponse errorResponse) {
                        truckInfoView.showFail(errorResponse.error);
                    }

                    @Override
                    public void onDone() {
                        truckInfoView.afterReject();
                    }
                });
    }

    @Override
    public void doCheckIn(String entryId, String truckType) {
        truckInfoView.showProgress(true);
        GateCheckInEntry checkInEntry = new GateCheckInEntry();
        EntryTicketCheckEntry checkEntry = new EntryTicketCheckEntry();
        checkEntry.equipmentType = truckType;
        checkInEntry.entryCheckin = checkEntry;
        checkInApi.gateCheckIn(ServiceFactory.getYard(), entryId, checkInEntry)
                .observeOn(AndroidSchedulers.mainThread())
                .subscribeOn(Schedulers.io())
                .subscribe(new ErrorCodeSubscriber<Response<Void>>() {
                    @Override
                    public void onSuccess(Response<Void> voidResponse) {
                        truckInfoView.dailyCheckInSuccess();
                        updateEntryTicketAsDaily(entryId);
                    }

                    @Override
                    public void onFailed(ErrorResponse errorResponse) {
                        truckInfoView.showFail(errorResponse.error);
                    }

                    @Override
                    public void onDone() {
                        truckInfoView.showProgress(false);
                    }
                });
    }

    private void updateEntryTicketAsDaily(String entryId) {
        EntryTicketUpdateEntry entry = new EntryTicketUpdateEntry();
        entry.checkInTypes.add(EquipmentTypeEntry.TRACTOR_DAILY_PICKUP);
        checkInApi.updateEntryTicket(ServiceFactory.getYard(), entryId, entry)
                .compose(RxUtil.asyncSchedulers())
                .subscribe(new ErrorCodeSubscriber<Response<Void>>() {
                    @Override
                    public void onSuccess(Response<Void> voidResponse) {

                    }

                    @Override
                    public void onFailed(ErrorResponse errorResponse) {

                    }

                    @Override
                    public void onDone() {

                    }
                });
    }

}