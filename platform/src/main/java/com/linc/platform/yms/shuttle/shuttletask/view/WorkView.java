package com.linc.platform.yms.shuttle.shuttletask.view;

import com.linc.platform.yms.model.YardEquipmentEntry;
import com.linc.platform.yms.shuttle.shuttletask.model.ShuttleTaskProcessEntry;

import java.util.List;

/**
 * Created by devinc on 2017/2/14.
 */

public interface WorkView {
    void showProgress(boolean show);

    void showFail(int resId);

    void showFail(String error);

    void onStartTask();

    void showNotFound();

    void closeSuccess();

    void toVerifyEquipment(YardEquipmentEntry entry);

    void startShuttle(YardEquipmentEntry entry);

    void endShuttle(YardEquipmentEntry entry);

    void startShuttleSuccess(YardEquipmentEntry entry);

    void addFoundEquipment(YardEquipmentEntry yardEquipmentEntry);

    void onEndShuttle(YardEquipmentEntry entry);

    void setShuttleItems(List<ShuttleTaskProcessEntry> inCompleteShuttleList);

    void refreshData(YardEquipmentEntry entry);

    void updateVerifiedEquipment(YardEquipmentEntry entry);

    void onSelectEquipment(List<YardEquipmentEntry> body);
}
