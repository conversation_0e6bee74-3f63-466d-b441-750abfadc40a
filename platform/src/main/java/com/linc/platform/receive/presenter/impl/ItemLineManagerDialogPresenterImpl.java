package com.linc.platform.receive.presenter.impl;

import android.text.TextUtils;

import com.annimon.stream.Stream;
import com.linc.platform.core.BaseAdapterData;
import com.linc.platform.foundation.api.DiverseItemSpecApi;
import com.linc.platform.foundation.api.ItemSpecAPI;
import com.linc.platform.foundation.api.OrganizationApi;
import com.linc.platform.foundation.api.UnitApi;
import com.linc.platform.foundation.model.CustomerViewEntry;
import com.linc.platform.foundation.model.DiverseItemSpecEntry;
import com.linc.platform.foundation.model.DiverseItemSpecSearchEntry;
import com.linc.platform.foundation.model.DiversePropertyEntry;
import com.linc.platform.foundation.model.ItemDiverseViewEntry;
import com.linc.platform.foundation.model.ItemFieldEntry;
import com.linc.platform.foundation.model.ItemSpecEntry;
import com.linc.platform.foundation.model.ItemSpecSearchEntry;
import com.linc.platform.foundation.model.ItemUnitSearchEntry;
import com.linc.platform.foundation.model.UnitEntry;
import com.linc.platform.foundation.model.organization.common.base.OrganizationSearchEntry;
import com.linc.platform.http.ErrorCodeSubscriber;
import com.linc.platform.http.ErrorResponse;
import com.linc.platform.http.HttpService;
import com.linc.platform.http.IdResponse;
import com.linc.platform.receive.api.ReceiptApi;
import com.linc.platform.receive.api.ReceiveTaskApi;
import com.linc.platform.receive.model.ReceiptEntry;
import com.linc.platform.receive.model.ReceiptItemLineEntry;
import com.linc.platform.receive.presenter.ItemLineManagerDialogPresenter;
import com.linc.platform.receive.view.ItemLineManagerDialogView;
import com.linc.platform.utils.CollectionUtil;
import com.linc.platform.utils.Logger;
import com.linc.platform.utils.RxUtil;
import com.linc.platform.utils.ToastUtil;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

import retrofit2.Call;
import retrofit2.Response;

/**
 * <AUTHOR>
 */

public class ItemLineManagerDialogPresenterImpl implements ItemLineManagerDialogPresenter {
    private ItemSpecEntry itemSpecEntry;
    private ItemLineManagerDialogView itemLineManagerDialogView;
    private ItemSpecAPI itemSpecAPI;
    private OrganizationApi organizationApi;
    private List<ItemSpecEntry> customerItemSpecEntries;
    private UnitApi unitApi;
    private ReceiveTaskApi receiptTaskApi;
    private ReceiptApi receiptApi;
    private DiverseItemSpecApi diverseItemSpecApi;
    private List<ReceiptEntry> receiptEntries;

    private String receiveTaskId;
    private CustomerViewEntry customer;
    private List<ReceiptItemLineEntry> itemLineEntries;

    public ItemLineManagerDialogPresenterImpl(ItemLineManagerDialogView itemLineManagerDialogView,
                                              String receiveTaskId,
                                              String companyId) {
        this.receiveTaskId = receiveTaskId;
        this.itemLineManagerDialogView = itemLineManagerDialogView;
        itemSpecAPI = HttpService.createService(ItemSpecAPI.class);
        receiptTaskApi = HttpService.createService(ReceiveTaskApi.class);
        organizationApi = HttpService.createService(OrganizationApi.class);
        unitApi = HttpService.createService(UnitApi.class);
        receiptApi = HttpService.createService(ReceiptApi.class);
        diverseItemSpecApi = HttpService.createService(DiverseItemSpecApi.class);

    }

    @Override
    public void loadItemDetail(String itemSpecId, String diverseId) {
        itemLineManagerDialogView.showProgress(true);
        itemSpecAPI.getItemFieldsById(itemSpecId)
                .compose(RxUtil.asyncSchedulers())
                .subscribe(new ErrorCodeSubscriber<Response<ItemSpecEntry>>() {
                    @Override
                    public void onSuccess(Response<ItemSpecEntry> itemSpecEntryResponse) {
                        itemSpecEntry = itemSpecEntryResponse.body();
                        itemLineManagerDialogView.updateItemDetail(itemSpecEntry);
                    }

                    @Override
                    public void onFailed(ErrorResponse errorResponse) {
                        ToastUtil.showErrorToast(errorResponse.getErrorMessage());
                    }

                    @Override
                    public void onDone() {
                        itemLineManagerDialogView.showProgress(false);
                    }
                });
    }

    @Override
    public List<BaseAdapterData> getCustomer(String filter) {
        OrganizationSearchEntry searchEntry = new OrganizationSearchEntry();
//        searchEntry.tags.add(OrganizationEntry.TAG_CUSTOMER);
        searchEntry.name = filter;
        List organizationEntries = null;

        Call call = organizationApi.getOrganization(searchEntry);
        try {
            Response<List<BaseAdapterData>> datas = call.execute();
            organizationEntries = datas.body();
        } catch (IOException e) {
            Logger.handleException(e);
        }

        return organizationEntries;
    }

    @Override
    public void loadCustomerItemSpec(String customerId) {
        itemLineManagerDialogView.showProgress(true);
        ItemSpecSearchEntry specSearchEntry = new ItemSpecSearchEntry();
        specSearchEntry.customerIds = new ArrayList<>();
        specSearchEntry.customerIds.add(customerId);
        itemSpecAPI.search(specSearchEntry)
                .compose(RxUtil.asyncSchedulers())
                .subscribe(new ErrorCodeSubscriber<Response<List<ItemSpecEntry>>>() {
                    @Override
                    public void onSuccess(Response<List<ItemSpecEntry>> listResponse) {
                        customerItemSpecEntries = listResponse.body();
                        itemLineManagerDialogView.updateCustomerItemSpec(customerItemSpecEntries);
                    }

                    @Override
                    public void onFailed(ErrorResponse errorResponse) {
                        ToastUtil.showErrorToast(errorResponse.getErrorMessage());
                    }

                    @Override
                    public void onDone() {
                        itemLineManagerDialogView.showProgress(false);
                    }
                });
    }

    @Override
    public void loadItemDetail(String itemSpecId) {
        itemLineManagerDialogView.showProgress(true);
        ItemSpecSearchEntry specSearchEntry = new ItemSpecSearchEntry();
        specSearchEntry.itemSpecId = itemSpecId;
        itemSpecAPI.getItemFields(specSearchEntry)
                .compose(RxUtil.asyncSchedulers())
                .subscribe(new ErrorCodeSubscriber<Response<ItemSpecEntry>>() {
                    @Override
                    public void onSuccess(Response<ItemSpecEntry> itemSpecEntryResponse) {
                        itemSpecEntry = itemSpecEntryResponse.body();
                        itemLineManagerDialogView.updateItemDetail(itemSpecEntry);
                    }

                    @Override
                    public void onFailed(ErrorResponse errorResponse) {
                        ToastUtil.showErrorToast(errorResponse.getErrorMessage());
                    }

                    @Override
                    public void onDone() {
                        itemLineManagerDialogView.showProgress(false);
                    }
                });
    }

    @Override
    public void loadDiverseDetail(String itemSpedId) {
        itemLineManagerDialogView.showProgress(true);
        DiverseItemSpecSearchEntry specSearchEntry = new DiverseItemSpecSearchEntry();
        specSearchEntry.itemSpecId = itemSpedId;
        itemSpecAPI.diverseSearch(specSearchEntry)
                .compose(RxUtil.asyncSchedulers())
                .subscribe(new ErrorCodeSubscriber<Response<List<DiverseItemSpecEntry>>>() {
                    @Override
                    public void onSuccess(Response<List<DiverseItemSpecEntry>> listResponse) {
                        itemLineManagerDialogView.updateDiverseSpec(listResponse.body());
                    }

                    @Override
                    public void onFailed(ErrorResponse errorResponse) {
                        ToastUtil.showErrorToast(errorResponse.getErrorMessage());
                    }

                    @Override
                    public void onDone() {
                        itemLineManagerDialogView.showProgress(false);
                    }
                });
    }

    @Override
    public List<ItemSpecEntry> getItemSpec(String name) {
        if (TextUtils.isEmpty(name)) {
            return customerItemSpecEntries;
        }

        List<ItemSpecEntry> result = new ArrayList<>();
        for (ItemSpecEntry entry : customerItemSpecEntries) {
            if (!TextUtils.isEmpty(name)
                    && entry.name.toLowerCase().contains(name.toLowerCase())) {
                result.add(entry);
            }
        }
        return result;
    }

    @Override
    public void searchCustomerItemSpec(String customerId, String data) {
        itemLineManagerDialogView.showProgress(true);
        ItemSpecSearchEntry specSearchEntry = new ItemSpecSearchEntry();
        specSearchEntry.keyword = data;
        specSearchEntry.customerIds = new ArrayList<>();
        specSearchEntry.customerIds.add(customerId);
        itemSpecAPI.searchCustomerItemSpec(specSearchEntry)
                .compose(RxUtil.asyncSchedulers())
                .subscribe(new ErrorCodeSubscriber<Response<List<ItemSpecEntry>>>() {
                    @Override
                    public void onSuccess(Response<List<ItemSpecEntry>> listResponse) {
                        List<ItemSpecEntry> body = listResponse.body();
                        if (body != null) {
                            itemLineManagerDialogView.onFindCustomerItem(body);
                        }
                    }

                    @Override
                    public void onFailed(ErrorResponse errorResponse) {
                        ToastUtil.showErrorToast(errorResponse.error);
                    }

                    @Override
                    public void onDone() {
                        itemLineManagerDialogView.showProgress(false);
                    }
                });

    }

    @Override
    public void searchDiverse(ItemSpecEntry itemSpecEntry, List<ItemFieldEntry> itemFieldEntries) {
        itemLineManagerDialogView.showProgress(true);
        DiverseItemSpecSearchEntry specSearchEntry = new DiverseItemSpecSearchEntry();
        specSearchEntry.itemSpecId = itemSpecEntry.id;
        specSearchEntry.diverseProperties = new ArrayList<>();
        for (ItemFieldEntry fieldEntry : itemFieldEntries) {
            DiversePropertyEntry diversePropertyEntry = new DiversePropertyEntry();
            diversePropertyEntry.value = fieldEntry.selectedDiverseValue.value;
            diversePropertyEntry.propertyId = fieldEntry.selectedDiverseValue.propertyId;
            diversePropertyEntry.unit = fieldEntry.selectedDiverseValue.unit;
            specSearchEntry.diverseProperties.add(diversePropertyEntry);
        }

        itemSpecAPI.diverseSearch(specSearchEntry)
                .compose(RxUtil.asyncSchedulers())
                .subscribe(new ErrorCodeSubscriber<Response<List<DiverseItemSpecEntry>>>() {
                    @Override
                    public void onSuccess(Response<List<DiverseItemSpecEntry>> listResponse) {
                        itemLineManagerDialogView.updateDiverseSpec(listResponse.body());
                    }

                    @Override
                    public void onFailed(ErrorResponse errorResponse) {
                        ToastUtil.showErrorToast(errorResponse.getErrorMessage());
                    }

                    @Override
                    public void onDone() {
                        itemLineManagerDialogView.showProgress(false);
                    }
                });
    }

    @Override
    public void addItemLine(String receiveId, String itemSpecId, String productId,
                            String unitId, String qty, String lotNo) {
        itemLineManagerDialogView.showProgress(true);
        ReceiptItemLineEntry itemLineEntry = new ReceiptItemLineEntry();
        if (!TextUtils.isEmpty(productId)) {
            itemLineEntry.productId = productId;
        }
        itemLineEntry.source = ReceiptItemLineEntry.SOURCE_ANDROID;
        itemLineEntry.itemSpecId = itemSpecId;
        itemLineEntry.unitId = unitId;
        itemLineEntry.lotNo = lotNo;
        itemLineEntry.qty = Double.parseDouble(qty);
        receiptApi.addItemLine(receiveId, itemLineEntry)
                .compose(RxUtil.asyncSchedulers())
                .subscribe(new ErrorCodeSubscriber<Response<Void>>() {
                    @Override
                    public void onSuccess(Response<Void> voidResponse) {
                        if (!TextUtils.isEmpty(receiveTaskId)) {
                            loadTaskDetail(receiveTaskId, itemSpecId);
                        }
                    }

                    @Override
                    public void onFailed(ErrorResponse errorResponse) {
                        ToastUtil.showErrorToast(errorResponse.getErrorMessage());
                    }

                    @Override
                    public void onDone() {
                        itemLineManagerDialogView.showProgress(false);
                    }
                });
    }

    private void loadTaskDetail(String taskId, String itemSpecId) {
        itemLineManagerDialogView.showProgress(true);
        receiptTaskApi.loadTaskDetail(taskId)
                .compose(RxUtil.asyncSchedulers())
                .subscribe(new ErrorCodeSubscriber<Response<List<ReceiptEntry>>>() {
                    @Override
                    public void onSuccess(Response<List<ReceiptEntry>> listResponse) {
                        if (itemLineEntries != null) {
                            itemLineEntries.clear();
                        } else {
                            itemLineEntries = new ArrayList<>();
                        }
                        for (ReceiptEntry entry : listResponse.body()) {
                            for (ReceiptItemLineEntry itemLineEntry : entry.itemLines) {
                                itemLineEntry.customerId = entry.customerId;
                                itemLineEntry.referenceNo = entry.referenceNo;
                                itemLineEntry.customerName = entry.customerName;
                                itemLineEntry.receiptId = entry.id;
                                itemLineEntry.receiptEntry = entry;
                                for (UnitEntry uomEntry : itemLineEntry.itemSpecInfo.itemUoms) {
                                    if (uomEntry.id.equals(itemLineEntry.unitId)) {
                                        itemLineEntry.itemSpecInfo.selectUom = uomEntry;
                                    }
                                }
                                itemLineEntries.add(itemLineEntry);
                            }
                        }

                        loadItemLine(taskId, itemSpecId);
                    }

                    @Override
                    public void onFailed(ErrorResponse errorResponse) {
                        ToastUtil.showErrorToast(errorResponse.getErrorMessage());
                    }

                    @Override
                    public void onDone() {
                        itemLineManagerDialogView.showProgress(false);
                    }
                });
    }

    private void loadItemLine(String receiveTaskId, String itemSpecId) {
        itemLineManagerDialogView.showProgress(true);
        receiptTaskApi.getTaskItemLine(receiveTaskId)
                .compose(RxUtil.asyncSchedulers())
                .subscribe(new ErrorCodeSubscriber<Response<List<ReceiptItemLineEntry>>>() {
                    @Override
                    public void onSuccess(Response<List<ReceiptItemLineEntry>> listResponse) {
                        List<ReceiptItemLineEntry> lineEntries = listResponse.body();
                        for (ReceiptItemLineEntry entry : lineEntries) {
                            for (ReceiptItemLineEntry lineEntry : itemLineEntries) {
                                if (entry.id.equals(lineEntry.id)) {
                                    lineEntry.baseQty = entry.getBaseQty();
                                    lineEntry.receivedBaseUnit = entry.receivedBaseUnit;
                                    lineEntry.unitName = entry.unitName;
                                    lineEntry.diversePropertyEntries = entry.diversePropertyEntries;
                                    lineEntry.receivedQty = entry.receivedQty;
                                    lineEntry.defaultSingleLpTemplateEntry = entry.defaultSingleLpTemplateEntry;
                                    lineEntry.defaultLPTemplateUnitEntry = entry.defaultLPTemplateUnitEntry;
                                    lineEntry.defaultSingleItemLpTemplateEntry = entry.defaultSingleItemLpTemplateEntry;
                                    lineEntry.requireCollectSeasonalPack = entry.requireCollectSeasonalPack;
                                    lineEntry.itemSpec = entry.itemSpec;
                                    lineEntry.itemCodes = entry.itemCodes;
                                    lineEntry.lotNo = entry.lotNo;
                                }
                            }
                        }
                       List<ReceiptItemLineEntry> filterItemLineList = Stream.of(itemLineEntries).filter((itemLineEntry) ->itemLineEntry.itemSpecId.equals(itemSpecId))
                               .toList();
                        if(CollectionUtil.isNotNullOrEmpty(filterItemLineList)){
                            itemLineManagerDialogView.onAddItemLineSuccess(filterItemLineList.get(filterItemLineList.size() - 1));
                        }
                    }

                    @Override
                    public void onFailed(ErrorResponse errorResponse) {
                        ToastUtil.showErrorToast(errorResponse.getErrorMessage());
                    }

                    @Override
                    public void onDone() {
                        itemLineManagerDialogView.showProgress(false);
                    }
                });
    }

    @Override
    public void addItemLine(String receiveId, String itemSpecId,
                            List<ItemFieldEntry> itemFieldEntries, String uintId, String qty, String lotNo) {
        itemLineManagerDialogView.showProgress(true);

        ItemSpecEntry itemSpecEntry = new ItemSpecEntry();
        itemSpecEntry.itemSpecId = itemSpecId;
        itemSpecEntry.diverseProperties = itemFieldEntries;
        diverseItemSpecApi.searchUpdate(itemSpecEntry)
                .compose(RxUtil.asyncSchedulers())
                .subscribe(new ErrorCodeSubscriber<Response<IdResponse>>() {
                    @Override
                    public void onSuccess(Response<IdResponse> idResponseResponse) {
                        addItemLine(receiveId, itemSpecId, idResponseResponse.body().id, uintId, qty, lotNo);
                    }

                    @Override
                    public void onFailed(ErrorResponse errorResponse) {
                        ToastUtil.showErrorToast(errorResponse.getErrorMessage());
                    }

                    @Override
                    public void onDone() {
                        itemLineManagerDialogView.showProgress(false);
                    }
                });
    }

    @Override
    public void loadUom(String itemSpecId) {
        itemLineManagerDialogView.showProgress(true);
        ItemUnitSearchEntry searchEntry = new ItemUnitSearchEntry();
        searchEntry.itemSpecId = itemSpecId;
        unitApi.search(searchEntry)
                .compose(RxUtil.asyncSchedulers())
                .subscribe(new ErrorCodeSubscriber<Response<List<UnitEntry>>>() {
                    @Override
                    public void onSuccess(Response<List<UnitEntry>> listResponse) {
                        itemLineManagerDialogView.updateUom(listResponse.body());
                    }

                    @Override
                    public void onFailed(ErrorResponse errorResponse) {
                        ToastUtil.showErrorToast(errorResponse.getErrorMessage());
                    }

                    @Override
                    public void onDone() {
                        itemLineManagerDialogView.showProgress(false);
                    }
                });
    }

    @Override
    public void loadItemDiverseView(String itemDiverseId) {
        itemLineManagerDialogView.showProgress(true);
        itemSpecAPI.loadItemDetail(itemDiverseId)
                .compose(RxUtil.asyncSchedulers())
                .subscribe(new ErrorCodeSubscriber<Response<ItemDiverseViewEntry>>() {
                    @Override
                    public void onSuccess(Response<ItemDiverseViewEntry> itemDiverseViewEntryResponse) {

                    }

                    @Override
                    public void onFailed(ErrorResponse errorResponse) {
                        ToastUtil.showErrorToast(errorResponse.getErrorMessage());
                    }

                    @Override
                    public void onDone() {
                        itemLineManagerDialogView.showProgress(false);
                    }
                });
    }

    @Override
    public boolean tryValidateLotRegexMatch(String lotNo) {
        if (customer != null && !TextUtils.isEmpty(customer.defaultLotNoValidateRegex)) {
            String regex = customer.defaultLotNoValidateRegex;
            boolean matches = lotNo.matches(regex);
            return matches;
        } else {
            return true;
        }
    }

    @Override
    public void setCustomer(CustomerViewEntry customerViewEntry) {
        this.customer = customerViewEntry;
    }

    @Override
    public void setReceiptEntryList(List<ReceiptEntry> receiptEntryList) {
        this.receiptEntries = receiptEntryList;
    }

    @Override
    public boolean isSameLotNoInItemLines(String itemSpecId, String lotNo) {
        if (CollectionUtil.isNullOrEmpty(receiptEntries)) {
            return false;
        }
        for(ReceiptEntry receiptEntry : receiptEntries){
            if (CollectionUtil.isNullOrEmpty(receiptEntry.itemLines)) {
                return false;
            }
            List<ReceiptItemLineEntry> itemLines = receiptEntry.itemLines;
            for(ReceiptItemLineEntry itemLine : itemLines){
                if (itemLine.itemSpecId.equals(itemSpecId) && lotNo.equals(itemLine.lotNo)) {
                     return true;
                }
            }
        }
        return false;
    }


}
