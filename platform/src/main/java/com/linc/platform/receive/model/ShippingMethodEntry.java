package com.linc.platform.receive.model;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

/**
 * Created by dexter on 18/5/24.
 */

public enum ShippingMethodEntry implements Serializable {
    @SerializedName("Truckload")
    TRUCKLOAD("Truckload"),

    @SerializedName("LTL")
    LTL("LTL"),

    @SerializedName("Small Parcel")
    SMALL_PARCEL("Small Parcel"),

    @SerializedName("LCL")
    LCL("Less Container Load"),

    @SerializedName("Will Call")
    WILL_CALL("Will Call");

    private String name;

    ShippingMethodEntry(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }
}
