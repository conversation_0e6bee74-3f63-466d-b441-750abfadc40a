package com.linc.platform.receive;

import android.text.TextUtils;

import com.linc.platform.foundation.model.CustomerViewEntry;
import com.linc.platform.receive.model.LpItemEntry;
import com.linc.platform.receive.model.ReceiptItemLineEntry;
import com.linc.platform.utils.Constant;
import com.linc.platform.utils.Lists;

import java.util.List;

/**
 * @Description:
 * @Author: Dennis
 * @CreateDate: 2022/5/19 17:36
 */
public class ReceiveCommonHelper {

    public static String PHOTO_UPLOAD_PARAM_APP = "wms";
    public static String PHOTO_UPLOAD_PARAM_MODULE = "receive";
    public static String PHOTO_UPLOAD_PARAM_SERVICE = "takephoto";

    public static String getDefaultGoodsType(ReceiptItemLineEntry receiptItemLineEntry, CustomerViewEntry customerViewEntry) {
        if (customerViewEntry == null || !customerViewEntry.specialGoodsType) {
            return Constant.GOODS_TYPE_GOOD;
        }
        if (customerViewEntry.captureGoodTypeOnReceivingTask && !TextUtils.isEmpty(customerViewEntry.defaultGoodsType)) {
            return customerViewEntry.defaultGoodsType;
        }
        if (receiptItemLineEntry != null && !TextUtils.isEmpty(receiptItemLineEntry.goodsType)) {
            return receiptItemLineEntry.goodsType;
        }
        return "";
    }

    public static String getDefaultGoodsType(LpItemEntry lpItemEntry, CustomerViewEntry customerViewEntry) {
        if (customerViewEntry == null || !customerViewEntry.specialGoodsType) {
            return Constant.GOODS_TYPE_GOOD;
        }
        if (customerViewEntry.captureGoodTypeOnReceivingTask && !TextUtils.isEmpty(customerViewEntry.defaultGoodsType)) {
            return customerViewEntry.defaultGoodsType;
        }
        if (lpItemEntry != null && !TextUtils.isEmpty(lpItemEntry.goodsType)) {
            return lpItemEntry.goodsType;
        }
        return "";
    }

    public static boolean enableShowGoodsTypeView(CustomerViewEntry customerViewEntry) {
        // specialGoodsType is open and captureGoodTypeOnReceivingTask is off, then hide goodsType
        return customerViewEntry == null
                || !customerViewEntry.specialGoodsType
                || customerViewEntry.captureGoodTypeOnReceivingTask;
    }

    public static List<String> defaultGoodsTypes = Lists.newArrayList(
            Constant.GOODS_TYPE_GOOD, Constant.GOODS_TYPE_DAMAGE, Constant.GOODS_TYPE_NEAR_EXPIRY, Constant.GOODS_TYPE_EXPIRED, Constant.GOODS_TYPE_CONTAIN_DAMAGE, Constant.GOODS_TYPE_ON_HOLD,
            Constant.GOODS_TYPE_REWORK_NEEDED, Constant.GOODS_TYPE_QC, Constant.GOODS_TYPE_FDA, Constant.GOODS_TYPE_RETURN,
            Constant.GOODS_TYPE_B_GRADE, Constant.GOODS_TYPE_C_GRADE
    );

    public static List<String> needCollectPhotoGoodsTypes =
            Lists.newArrayList(Constant.GOODS_TYPE_DAMAGE, Constant.GOODS_TYPE_CONTAIN_DAMAGE, Constant.GOODS_TYPE_ON_HOLD, Constant.GOODS_TYPE_REWORK_NEEDED);

}
