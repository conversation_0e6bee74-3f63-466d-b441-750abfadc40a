package com.linc.platform.foundation.dal;

import static com.linc.platform.utils.ConfigurationMapUtil.KEY_DISABLE_RFID_FACILITY;
import static com.linc.platform.utils.ConfigurationMapUtil.KEY_DISABLE_RFID_TASK;
import static com.linc.platform.utils.ConfigurationMapUtil.KEY_RFID_COUNT;
import static com.linc.platform.utils.ConfigurationMapUtil.KEY_RFID_EQUIPMENT;
import static com.linc.platform.utils.ConfigurationMapUtil.KEY_RFID_RSSI;
import static com.linc.platform.utils.ConfigurationMapUtil.KEY_RFID_TASK;

import android.text.TextUtils;

import com.annimon.stream.Stream;
import com.linc.platform.common.apidal.BaseApiDal;
import com.linc.platform.common.handler.SuccessHandler;
import com.linc.platform.foundation.api.configurationmap.ConfigurationApi;
import com.linc.platform.foundation.model.ConfigurationMapSearchEntry;
import com.linc.platform.http.HttpService;
import com.linc.platform.utils.CollectionUtil;
import com.linc.platform.utils.ConfigurationMapUtil;

import java.util.Map;

public class RFIDConfigDal extends BaseApiDal {
    private ConfigurationApi configurationApi;

    private String count;
    private String rssi;

    public RFIDConfigDal() {
        configurationApi = HttpService.createService(ConfigurationApi.class);
    }

    public void getRFIDConfig(String task, String equipment, RFIDConfigResult result) {
        ConfigurationMapSearchEntry searchEntry = new ConfigurationMapSearchEntry();
        searchEntry.tableName = ConfigurationMapUtil.TABLE_RFID_CONFIG;
        execute(configurationApi.search(searchEntry), configs -> {
            if (configs == null || configs.isEmpty()) return;
            Map<String, String> regexMap = Stream.of(configs).filter(config -> config.valueMapping.get(KEY_RFID_TASK).equals(task) &&
                    config.valueMapping.get(KEY_RFID_EQUIPMENT).equals(equipment)).findFirst().orElse(null).valueMapping;
            if (regexMap == null || regexMap.isEmpty()) {
                result.onResult(getCount(), getRssi());
                return;
            }
            count = regexMap.get(KEY_RFID_COUNT);
            rssi = regexMap.get(KEY_RFID_RSSI);
            result.onResult(getCount(), getRssi());
        }, error -> {
            result.onResult(getCount(), getRssi());
        });
    }

    public void getDisableRFIDCollectConfig(String customerId, String rfidConfigTask, String facility, SuccessHandler<Boolean> successHandler) {
        ConfigurationMapSearchEntry searchEntry = new ConfigurationMapSearchEntry();
        searchEntry.tableName = ConfigurationMapUtil.TABLE_DISABLE_RFID_CONFIG;
        execute(configurationApi.search(searchEntry), configs -> {
            if (configs == null || configs.isEmpty()) {
                successHandler.onSuccess(false);
                return;
            }
            String currentFacilityName = facility.toUpperCase();
            String currentTask = rfidConfigTask.toUpperCase();
            boolean isDisableRFID = (Stream.of(configs).anyMatch(e -> customerId.equals(e.customerId) && CollectionUtil.valueContainsValue(e.valueMapping.values(), currentFacilityName) && CollectionUtil.valueContainsValue(e.valueMapping.values(), currentTask)))
                    || (Stream.of(configs).anyMatch(e -> customerId.equals(e.customerId) && CollectionUtil.valueContainsValue(e.valueMapping.values(), currentFacilityName) && !CollectionUtil.valueContainsValue(e.valueMapping.keySet(), KEY_DISABLE_RFID_TASK)))
                    || (Stream.of(configs).anyMatch(e -> customerId.equals(e.customerId) && CollectionUtil.valueContainsValue(e.valueMapping.values(), currentTask) && !CollectionUtil.valueContainsValue(e.valueMapping.keySet(), KEY_DISABLE_RFID_FACILITY)))
                    || (Stream.of(configs).anyMatch(e -> CollectionUtil.valueContainsValue(e.valueMapping.values(), currentFacilityName) && CollectionUtil.valueContainsValue(e.valueMapping.values(), currentTask) && TextUtils.isEmpty(e.customerId)))
                    || (Stream.of(configs).anyMatch(e -> CollectionUtil.valueContainsValue(e.valueMapping.values(), currentFacilityName) && !CollectionUtil.valueContainsValue(e.valueMapping.keySet(), KEY_DISABLE_RFID_TASK) && TextUtils.isEmpty(e.customerId)))
                    || (Stream.of(configs).anyMatch(e -> customerId.equals(e.customerId) && !CollectionUtil.valueContainsValue(e.valueMapping.keySet(), KEY_DISABLE_RFID_FACILITY) && !CollectionUtil.valueContainsValue(e.valueMapping.keySet(), KEY_DISABLE_RFID_TASK)));
            successHandler.onSuccess(isDisableRFID);
        }, error -> {
            successHandler.onSuccess(false);
        });
    }

    public int getCount() {
        if (TextUtils.isEmpty(count)) {
            return 0;
        }
        return Integer.parseInt(count);
    }

    public int getRssi() {
        if (TextUtils.isEmpty(rssi)) {
            return -10000;
        }
        return Integer.parseInt(rssi);
    }

    public interface RFIDConfigResult {
        public void onResult(int count, int rssi);
    }

}
