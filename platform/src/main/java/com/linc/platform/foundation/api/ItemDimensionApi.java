package com.linc.platform.foundation.api;

import com.linc.platform.baseapp.model.FacilityEquipmentView;
import com.linc.platform.foundation.model.ItemDimensionCollectEntry;
import com.linc.platform.foundation.model.ItemDimensionCollectPagingResultEntry;
import com.linc.platform.foundation.model.ItemDimensionCollectSearchEntry;

import java.util.List;

import retrofit2.Response;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Path;
import rx.Observable;

public interface ItemDimensionApi {

    @POST("fd-app/item-dimension-collect/search")
    Observable<Response<List<ItemDimensionCollectEntry>>> search(@Body ItemDimensionCollectSearchEntry searchEntry);

    @POST("fd-app/item-dimension-collect/search-by-paging")
    Observable<Response<ItemDimensionCollectPagingResultEntry>> searchByPaging(@Body ItemDimensionCollectSearchEntry searchEntry);

    @POST("shared/bam/item-dimension-collect/latest-collect")
    Observable<Response<ItemDimensionCollectEntry>> getLatestCollect(@Body ItemDimensionCollectSearchEntry searchEntry);

    @GET("bam/item-dimension-collect/cube-scanner/{barcode}")
    Observable<Response<FacilityEquipmentView>> getCubeScannerByBarcode(@Path("barcode") String barcode);
}
