package com.linc.platform.foundation.model;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public enum ReceiveMethodEntry implements Serializable {
    @SerializedName("Receive By Single Item")
    RECEIVE_BY_SINGLE_ITEM,

    @SerializedName("Receive By MIX Item")
    RECEIVE_BY_MIX_ITEM,

    @SerializedName("Receive By Carton")
    RECEIVE_BY_CARTON,

    @SerializedName("Receive By Pallet")
    RECEIVE_BY_PALLET,

    @SerializedName("Receive To Pick Location")
    RECEIVE_TO_PICK_LOCATION,

    @SerializedName("Receive To Put Away")
    RECEIVE_TO_PUT_AWAY,

    @SerializedName("Receive By Inventory Transfer")
    RECEIVE_BY_INVENTORY_TRANSFER
}
