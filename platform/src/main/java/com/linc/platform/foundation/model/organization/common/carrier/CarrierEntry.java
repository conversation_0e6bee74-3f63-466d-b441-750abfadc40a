package com.linc.platform.foundation.model.organization.common.carrier;

import com.google.gson.annotations.SerializedName;
import com.linc.platform.core.BaseAdapterData;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class CarrierEntry implements Serializable, BaseAdapterData {
    @SerializedName("id")
    public String id;

    @SerializedName("carrierId")
    public String carrierId;

    @SerializedName("carrierName")
    public String carrierName;

    @SerializedName("scac")
    public String scac;

    @SerializedName("mcDot")
    public String mcDot;

    @SerializedName("name")
    public String name;
    
    @SerializedName("isAutoCreateProNo")
    public Boolean isAutoCreateProNo;

    @Override
    public String getName() {
        return name;
    }
}
