package com.linc.platform.foundation.model;

import com.google.gson.annotations.SerializedName;
import com.linc.platform.baseapp.model.LocationEntry;
import com.linc.platform.load.model.LoadItemEntry;
import com.linc.platform.load.model.LoadOrderPickedItemLineEntry;
import com.linc.platform.load.model.ShipAddressEntry;
import com.linc.platform.load.v1.model.LpViewEntry;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class OrderEntry implements Serializable {
    public static final String LOAD_TASK_STATUS_EXCEPTION = "Exception";
    public static final String LOAD_TASK_STATUS_DONE = "Loaded";
    public static final String LOAD_PARTIAL_SHIPPED = "Partial Shipped";
    public static final String LOAD_TASK_STATUS_PROGRESS = "Loading";
    public static final String LOAD_TASK_STATUS_AVAILABLE = "Unlock";
    public static final String LOAD_TASK_STATUS_UNAVILABLE = "Locked";

    public static final String LOAD_TYPE_LOAD_LEVEL = "LP Level";

    public static final String TAG = "OrderEntry";

    @SerializedName("id")
    public String id;

    @SerializedName("loadNote")
    public String note;

    @SerializedName("items")
    public List<LoadItemEntry> items = new ArrayList<>();

    @SerializedName("status")
    public OrderStatusEntry status;

    public int lpAmount;
    public int lpDoneAmount;

    @SerializedName("shipToAddress")
    public ShipAddressEntry shipTo;

    @SerializedName("lpIds")
    public List<String> lps = new ArrayList<>();

    @SerializedName("locationIds")
    public List<LocationEntry> locations = new ArrayList<>();

    @SerializedName("carrierSignature")
    public String carrierSignature;

    @SerializedName("shipperSignature")
    public String shipperSignature;

    @SerializedName("packNote")
    public String packNote;

    @SerializedName("shipmentTrackingType")
    public String shipmentTrackingType;

    @SerializedName("shipTo")
    public String shipToName;

    @SerializedName("poNo")
    public String poNo;

    @SerializedName("proNo")
    public String proNo;

    @SerializedName("customerName")
    public String customer;

    @SerializedName("customerId")
    public String customerId;

    public String companyId;
    public String loadId;
    public String stepId;
    public String taskId;

    public boolean printable = false;

    @SerializedName("sequence")
    public String sequence;

    @SerializedName("carrierName")
    public String carrier;

    @SerializedName("retailerId")
    public String retailerId;

    @SerializedName("retailerName")
    public String retailerName;

    @SerializedName("loadedLpQty")
    public int loadedQty = 0;

    @SerializedName("requireLoadLpQty")
    public int requestQty = 0;

    @SerializedName("totalWeight")
    public Double totalWeight;

    @SerializedName("itemlinesView")
    public List<LoadOrderPickedItemLineEntry> itemlinesView;

    @SerializedName("orderType")
    public OrderTypeEntry orderType;

    @SerializedName("palletLabels")
    public List<String> palletLabels;

    @SerializedName("pickToLoad")
    public boolean pickToLoad;

    @SerializedName("lps")
    public List<LpViewEntry> orderLps;

    @SerializedName("orderItemSpecNames")
    public String orderItemSpecNames;

    @SerializedName("carrierId")
    public String carrierId;

    public boolean isNewForLoad(OrderStatusEntry status) {
        return status != null && (OrderStatusEntry.PACKED.equals(status)
                || OrderStatusEntry.PICKED.equals(status)
                || OrderStatusEntry.PARTIAL_SHIPPED.equals(status)
                || OrderStatusEntry.STAGED.equals(status));
    }
}
