package com.linc.platform.foundation.model;

import com.google.gson.annotations.SerializedName;
import com.linc.platform.core.BaseAdapterData;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
public class ItemGroupEntry implements Serializable, BaseAdapterData {

    @SerializedName("id")
    public String id;

    @SerializedName("name")
    public String name;

    @SerializedName("properties")
    public List<ItemGroupPropertyEntry> properties;

    @SerializedName("parentId")
    public String parentId;

    @SerializedName("type")
    public String type;

    @Override
    public String getName() {
        return this.name;
    }
}
