package com.linc.platform.foundation.api;

import com.linc.platform.foundation.model.organization.common.base.OrganizationViewEntry;
import com.linc.platform.foundation.model.organization.common.carrier.CarrierEntry;
import com.linc.platform.foundation.model.organization.common.carrier.CarrierPagingResultEntry;
import com.linc.platform.foundation.model.organization.common.carrier.CarrierRelationshipSearchEntry;
import com.linc.platform.foundation.model.organization.common.carrier.CarrierSearchEntry;

import java.util.List;

import retrofit2.Response;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Path;
import rx.Observable;

/**
 * <AUTHOR>
 */

public interface CarrierApi {

    @POST("fd-app/carrier/search")
    Observable<Response<List<CarrierEntry>>> search(@Body CarrierSearchEntry search);

    @POST("fd-app/carrier/search-by-paging")
    Observable<Response<CarrierPagingResultEntry>> searchByPaging(@Body CarrierSearchEntry search);

    @POST("bam/carrier/search-around-customerId")
    Observable<Response<List<OrganizationViewEntry>>> search(@Body CarrierRelationshipSearchEntry search);
    
    @GET("fd-app/carrier/{carrierId}")
    Observable<Response<CarrierEntry>> getCarrierById(@Path("carrierId") String carrierId);
}
