package com.linc.platform.foundation.model;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

/**
 * Created by Gavin
 */
public class ItemUnitUpdate implements Serializable {
    @SerializedName("id")
    public String id;

    @SerializedName("name")
    public String name;

    @SerializedName("itemSpecId")
    public String itemSpecId;

    @SerializedName("length")
    public Double length;

    @SerializedName("width")
    public Double width;

    @SerializedName("height")
    public Double height;

    @SerializedName("linearUnit")
    public ItemLinearUnit linearUnit;

    @SerializedName("weight")
    public Double weight;

    @SerializedName("weightUnit")
    public ItemWeightUnit weightUnit;

    @SerializedName("isBaseUnit")
    public Boolean isBaseUnit;
}
