package com.linc.platform.foundation.model;

import android.text.TextUtils;

import com.google.gson.annotations.SerializedName;
import com.linc.platform.baseapp.model.AutoUpgradeType;
import com.linc.platform.common.CustomerBarcodeFieldEntry;
import com.linc.platform.common.DimensionEntry;
import com.linc.platform.common.customer.ManualInputRestrictionConfig;
import com.linc.platform.core.BaseAdapterData;
import com.linc.platform.foundation.PickStrategyLevel;
import com.linc.platform.foundation.view.PutAwaySuggestOption;
import com.linc.platform.load.model.RetailerPalletTypeSetting;
import com.linc.platform.load.v1.model.LoadVersionEntry;
import com.linc.platform.load.v1.model.LoadVersionEnum;
import com.linc.platform.pick.model.PickWayEntry;
import com.linc.platform.pick.v1.model.PickVersionEntry;
import com.linc.platform.pick.v1.model.PickVersionEnum;
import com.linc.platform.putaway.work.directputaway.PutAwayVersionEntry;
import com.linc.platform.qualitycontrol.trackingnoqc.model.CarrierPickUpVerifyItemEntry;
import com.linc.platform.qualitycontrol.trackingnoqc.model.QCSubmitTypeEntry;
import com.linc.platform.receive.v1.model.ReceiceVersionEntry;
import com.linc.platform.transload_v2.receiving.model.TransloadReceiveMode;
import com.linc.platform.utils.Constant;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.LinkedHashSet;
import java.util.List;

/**
 * Created by devinc on 2017/11/27.
 */

public class CustomerViewEntry implements Serializable, BaseAdapterData {
    public static final String TAG = CustomerViewEntry.class.getSimpleName();
    public static final String LOAD_COLLECT_FIELD_CTNR_TRAILER_NO = "CTNR / Trailer NO.";
    public static final String LOAD_COLLECT_FIELD_PRO_NO = "ProNo";
    public static final String LOAD_COLLECT_FIELD_COUNTING_SHEET = "Counting Sheet Picture";
    public static final String LOAD_COLLECT_FIELD_SEAL_NO = "SealNo";

    @SerializedName("id")
    public String id;

    @SerializedName("orgId")
    public String orgId;

    @SerializedName("orgName")
    public String orgName;

    @SerializedName("customerCode")
    public String customerCode;

    @SerializedName("activatedFacilityIds")
    public List<String> activatedFacilityIds;

    @SerializedName("isAutoCreateProNo")
    public boolean isAutoCreateProNo;

    @SerializedName("isForceReceiveByUPC")
    public boolean isForceReceiveByUPC;

    @SerializedName("forceDefaultUOMReceiving")
    public boolean forceDefaultUOMReceiving;

    @SerializedName("requireCollectLotNoOnTransloadLPN")
    public Boolean collectLotNo = false;

    @SerializedName("requireCollectLotNoOnReceive")
    public Boolean requireCollectLotNoOnReceive = false;

    @SerializedName("requireCollectItemPoOnReceive")
    public Boolean requireCollectItemPoOnReceive = false;

    @SerializedName("requireCollectExpirationDateOnTransloadLPN")
    public Boolean collectExpirationDate = false;

    @SerializedName("requireCollectPalletNoOnTransloadLPN")
    public Boolean collectPalletNo = false;

    @SerializedName("allowAddingNewItemForReceiving")
    public Boolean allowAddingNewItem = false;

    @SerializedName("requireScanSerialNumberOnTransloadLPN")
    public Boolean scanSerialNumber = false;

    @SerializedName("skipCLPInWavePick")
    public Boolean skipCLPInWavePick = false;

    @SerializedName("skipCLPInOrderPick")
    public Boolean skipCLPInOrderPick = false;

    @SerializedName("allowAutoClosePickTask")
    public Boolean allowAutoClosePickTask = true;

    @SerializedName("skipPrintCLPInPick")
    public Boolean skipPrintCLPInPick = false;

    @SerializedName("facilityLPLabelSizes")
    public List<FacilityLPLabelSizeEntry> facilityLPLabelSizes;

    @SerializedName("forceScanCaseUPCForCasePick")
    public Boolean forceScanCaseUPCForCasePick = false;

    @SerializedName("allowShortReceive")
    public Boolean allowShortReceive = false;

    @SerializedName("requireCollectPalletNoOnReceive")
    public Boolean requireCollectPalletNoOnReceive = false;

    @SerializedName("allowPickByClickItem")
    public boolean allowPickByClickItem = false;

    @SerializedName("isPackNeedCheckPackagingType")
    public Boolean isPackNeedCheckPackagingType = false;

    @SerializedName("allowAutoCC")
    public Boolean allowAutoCc = false;

    @SerializedName("allowMakePalletAdjustInventory")
    public boolean allowMakePalletAdjustInventory = false;

    @SerializedName("defaultItemSNValidateRegex")
    public String defaultItemSNValidateRegex;

    @SerializedName("isAllowShortShip")
    public boolean
            isAllowShortShip;

    @SerializedName("allowPartialShipForDropShip")
    public boolean allowPartialShipForDropShip;

    @SerializedName("allowPartialShipForRegular")
    public boolean allowPartialShipForRegular;

    @SerializedName("disablePickByPallet")
    public Boolean disablePickByPallet = false;

    @SerializedName("maxOrderPercentage")
    public Double maxOrderPercentage = 1.0;

    @SerializedName("minOrderPercentage")
    public Double minOrderPercentage = 1.0;

    @SerializedName("skipPutAwayOnReceive")
    public Boolean skipPutAwayOnReceive = false;

    @SerializedName("enableSelectLPTemplateByTiHiOnReceive")
    public Boolean enableSelectLPTemplateByTiHiOnReceive = false;

    @SerializedName("requireCollectItemDimensionOnReceive")
    public Boolean requireCollectItemDimensionOnReceive = false;

    @SerializedName("requireCheckItemLocationOnReceive")
    public Boolean requireCheckItemLocationOnReceive = false;

    @SerializedName("requireCollectWeightForSnOnReceive")
    public Boolean requireCollectWeightForSnOnReceive = false;

    @SerializedName("pickSuggestLevel")
    public PickStrategyLevel pickSuggestLevel = PickStrategyLevel.LP_LEVEL;

    @SerializedName("putAwayPreferredLocations")
    public List<PutAwaySuggestOption> putAwayPreferredLocations;

    @SerializedName("enablePickByLotNo")
    public Boolean enablePickByLotNo = false;

    @SerializedName("enablePickBySn")
    public Boolean enablePickBySn = false;

    @SerializedName("allowPickFromSLP")
    public boolean allowPickFromSLP;

    @SerializedName("enableSnCheckByPreProvidedSnFile")
    public Boolean enableSnCheckByPreProvidedSnFile = false;

    @SerializedName("enableSNCheckWithoutSupplierAndFacility")
    public boolean enableSNCheckWithoutSupplierAndFacility;

    @SerializedName("enableSNCheckWithoutContainer")
    public boolean enableSNCheckWithoutContainer;

    @SerializedName("needReuseOtherFacilityLP")
    public boolean needReuseOtherFacilityLP;

    @SerializedName("preProvidedSnCheckSupplierFacilities")
    public List<SupplierFacilityEntry> preProvidedSnCheckSupplierFacilities = new ArrayList<>();

    @SerializedName("allowReceiveInvalidSN")
    public Boolean allowReceiveInvalidSN = false;

    @SerializedName("needAlertHighValueItem")
    public boolean needAlertHighValueItem;

    @SerializedName("pickVersion")
    public PickVersionEnum pickVersion;

    @SerializedName("pickVersions")
    public List<PickVersionEntry> pickVersions;

    @SerializedName("requireProNoWhenCloseOrder")
    public RequireProNoWhenCloseOrder requireProNoWhenCloseOrder;

    @SerializedName("allowAutoCloseLoadTask")
    public Boolean allowAutoCloseLoadTask = true;

    @SerializedName("allowPickAutoSubmit")
    public Boolean allowPickAutoSubmit = false;

    @SerializedName("requiredCountingSheetFacilities")
    public List<String> requiredCountingSheetFacilities = new ArrayList<>();

    @SerializedName("applyProNoToOrdersOnSameLoad") // except ds order
    public Boolean applyProNoToOrdersOnSameLoad = false;

    @SerializedName("enforceSelectSuggestStageLocation")
    public Boolean enforceSelectSuggestStageLocation = false;

    @SerializedName("pickSeasonMakePallet")
    public Boolean pickSeasonMakePallet = false;

    @SerializedName("receiveSnPrefixes")
    public List<String> receiveSNPrefixes;

    @SerializedName("receiveSnSeparators")
    public List<String> receiveSNSeparators;

    @SerializedName("allowPickByQTYOnWavePickOfSnItem")
    public Boolean allowPickByQTYOnWavePickOfSNItem = false;

    @SerializedName("forbidDockCheckinIfContainerNoNotMatch")
    public Boolean forbidDockCheckinIfContainerNoNotMatch = false;

    @SerializedName("putAwayVersion")
    public PutAwayVersionEntry putAwayVersion;

    @SerializedName("requirePermissionCheckOnReceiveForceClose")
    public Boolean requirePermissionCheckOnReceiveForceClose = false;

    @SerializedName("requireVerificationOnReceiveTaskForceClose")
    public Boolean requireVerificationOnReceiveTaskForceClose = false;

    @SerializedName("receiveOneQtySubmit")
    public Boolean receiveOneQtySubmit = false;

    @SerializedName("interfaceManualEntryRestriction")
    public Boolean interfaceManualEntryRestriction = false;

    @SerializedName("requirePrintSOIDSubOrderTypes")
    public List<String> requirePrintSOIDSubOrderTypes = new ArrayList<>();

    @SerializedName("needCompatibleWithThirdPartyLP")// Mapping to Compatible With Third Party LP in web page
    public Boolean needCompatibleWithThirdPartyLP = false;

    @SerializedName("useThirdPartyLPN")// Mapping to Compatible With Third Party LP V2 in web page
    public CompatibleWithThirdPartyLPSetting useThirdPartyLPN;

    @SerializedName("allowPrintMultipleLPsForOrderWhenPicking")
    public Boolean allowPrintMultipleLPsForOrderWhenPicking = false;

    @SerializedName("needCheckStageToConveyorLine")
    public Boolean needCheckStageToConveyorLine = false;

    @SerializedName("allowRequestPutAway")
    public Boolean allowRequestPutAway = false;

    @SerializedName("useCustomerBarcode")
    public boolean useCustomerBarcode;

    @SerializedName("customerBarcodeSeparator")
    public String customerBarcodeSeparator;

    @SerializedName("customerBarcodeFields")
    public List<CustomerBarcodeFieldEntry> customerBarcodeFields = new ArrayList<>();

    @SerializedName("receiveMethods")
    public List<ReceiveMethodEntry> receiveMethods = Arrays.asList(ReceiveMethodEntry.RECEIVE_BY_SINGLE_ITEM, ReceiveMethodEntry.RECEIVE_BY_MIX_ITEM);

    @SerializedName("forceItemCheckOnLpPicking")
    public boolean forceItemCheckOnLpPicking;

    @SerializedName("requireItemCheckOnPickSorting")
    public boolean requireItemCheckOnPickSorting;

    @SerializedName("scanLotNoToPick")
    public boolean scanLotNoToPick;

    @SerializedName("manualInputRestrictionConfigs")
    public List<ManualInputRestrictionConfig> manualInputRestrictionConfigs = new ArrayList<>();

    @SerializedName("lpItemQTYCalcMethodOnReceive")
    public LPItemQTYCalcMethodEntry lpItemQTYCalcMethodOnReceive;

    @SerializedName("allowedReceiveLocationTypes")
    public List<StageLocationTypeEntry> allowedReceiveLocationTypes = Collections.singletonList(StageLocationTypeEntry.STAGE_LOCATION);

    @SerializedName("sendGRBeforePutAway")
    public boolean sendGRBeforePutAway;

    @SerializedName("displaySuggestSnOnPick")
    public boolean displaySuggestSnOnPick;

    @SerializedName("forceScanLotNumberInReceiving")
    public boolean isForceScanLotNo;

    @SerializedName("defaultLotNoValidateRegex")
    public String defaultLotNoValidateRegex;

    @SerializedName("forbidForceCloseStepsFromPickTask")
    public boolean forbidForceCloseStepsFromPickTask;

    @SerializedName("specialGoodsType")
    public boolean specialGoodsType;

    @SerializedName("allowedReceivingGoodsTypes")
    public List<String> allowedReceivingGoodsTypes;

    @SerializedName("defaultGoodsType")
    public String defaultGoodsType;

    @SerializedName("captureGoodTypeOnReceivingTask")
    public boolean captureGoodTypeOnReceivingTask;

    @SerializedName("collectItemInfoAtReceiving")
    public boolean collectItemInfoAtReceiving;

    @SerializedName("collectItemSmallParcelPackagingAtReceiving")
    public boolean collectItemSmallParcelPackagingAtReceiving;

    @SerializedName("forcePickingByPickType")
    public boolean forcePickingByPickType;

    @SerializedName("displayTiHiUOMOnReceiving")
    public boolean displayTiHiUOMOnReceiving;

    @SerializedName("displayBillingUOMOnReceiving")
    public boolean displayBillingUOMOnReceiving;

    /**
     * 控制zone-pick，为true时强制要求picker先將這個zone的所有東西先Pick和stage, 才允許到下一個pick zone
     */
    @SerializedName("restrictCrossZoneOnZonePickTask")
    public boolean restrictCrossZoneOnZonePickTask;

    @SerializedName("requireCollectItemDimensionBySn")
    public boolean requireCollectItemDimensionBySn;

    @SerializedName("searchSnFromOpenReceipts")
    public boolean searchSnFromOpenReceipts;

    @SerializedName("snDimensionValues")
    public ArrayList<DimensionEntry> snDimensionValues = new ArrayList<>();

    @SerializedName("transloadReworkLocationIds")
    public ArrayList<String> transloadReworkLocationIds = new ArrayList<>();

    @SerializedName("displayStackHighAtPutAwayTask")
    public boolean displayStackHighAtPutAwayTask;

    @SerializedName("scanItemToAddPickingQty")
    public boolean scanItemToAddPickingQty = false;

    @SerializedName("masterSNImport")
    public boolean masterSNImport = false;

    @SerializedName("transloadTreatCrossMatchedCartonASRegularCarton")
    public boolean transloadTreatCrossMatchedCartonASRegularCarton = false;

    @SerializedName("transloadExceptionCartonFormat")
    public String transloadExceptionCartonFormat;

    @SerializedName("allowDirectLoad")
    public boolean allowDirectLoad;

    @SerializedName("transloadReceiveModes")
    public ArrayList<TransloadReceiveMode> transloadReceiveModes = new ArrayList<>();

    @SerializedName("allowPartialReceive")
    public boolean allowPartialReceive = false;

    @SerializedName("packagingMaterialGroupsForLTLOrders")
    public ArrayList<String> packagingMaterialGroupsForLTLOrders;

    @SerializedName("transloadMaxWeightOfOutboundTrailer")
    public Double transloadMaxWeightOfOutboundTrailer;

    @SerializedName("transloadAllowMultiDCInOneLoad")
    public Boolean transloadAllowMultiDCInOneLoad;

    @SerializedName("transloadCartonNoFormat")
    public String transloadCartonNoFormat;

    @SerializedName("supportSuggestLPByItemOnPackTask")
    public boolean supportSuggestLPByItemOnPackTask = false;

    @SerializedName("itemBarcodePrefix")
    public String itemBarcodePrefix;

    @SerializedName("itemBarcodeSuffix")
    public String itemBarcodeSuffix;

    @SerializedName("requireCollectSOIDSubOrderTypes")
    public List<String> requireCollectSOIDSubOrderTypes = new ArrayList<>();

    @SerializedName("collectSNDimensionDefaultUnit")
    public String collectSNDimensionDefaultUnit;

    @SerializedName("collectSNDimensionDefaultWeightUnit")
    public String collectSNDimensionDefaultWeightUnit;

    @SerializedName("allowCountByLPQty")
    public boolean allowCountByLPQty;

    @SerializedName("transloadRequireCollectContainerSizeOnReceiving")
    public boolean transloadRequireCollectContainerSizeOnReceiving;

    @SerializedName("forbidCreateLPConfigAtReceiveTask")
    public boolean forbidCreateLPConfigAtReceiveTask;

    @SerializedName("forbidMultipleLPConfigAtReceiveTask")
    public boolean forbidMultipleLPConfigAtReceiveTask;

    @SerializedName("validateUCCAtLoading")
    public boolean validateUCCAtLoading;

    @SerializedName("validateUCCAtLoadingRetailerIds")
    public List<String> validateUCCAtLoadingRetailerIds;

    @SerializedName("validatePalletCountAtLoadTask")
    public boolean validatePalletCountAtLoadTask;

    @SerializedName("pickToTrackingNo")
    public boolean pickToTrackingNo;

    @SerializedName("pickToTrackingNoPickWays")
    public List<PickWayEntry> pickToTrackingNoPickWays;

    @SerializedName("autoPrintUCCWhenOrderIsPicked")
    public boolean autoPrintUCCWhenOrderIsPicked;

    @SerializedName("enableCheckAndRebuildTaskSuggestion")
    public boolean enableCheckAndRebuildTaskSuggestion;

    @SerializedName("enforcePickTaskLocationValidation")
    public boolean enforcePickTaskLocationValidation;

    @SerializedName("loadCollectFields")
    public List<String> loadCollectFields;

    @SerializedName("allowAutoLocationArrivedDetection")
    public boolean allowAutoLocationArrivedDetection;

    @SerializedName("retailerPalletTypeSetting")
    public RetailerPalletTypeSetting retailerPalletTypeSetting;

    @SerializedName("skipCollectSnOnPicking")
    public boolean skipCollectSnOnPicking;

    @SerializedName("skipCollectSnOnPickingOrderTypes")
    public List<OrderTypeEntry> skipCollectSnOnPickingOrderTypes;

    @SerializedName("showLotNoAtInventoryInitial")
    public boolean showLotNoAtInventoryInitial;

    @SerializedName("showReceivedWhenAtInventoryInitial")
    public boolean showReceivedWhenAtInventoryInitial;

    @SerializedName("askForCapturingMaterial")
    public boolean askForCapturingMaterial;

    @SerializedName("loadVersion")
    public LoadVersionEnum loadVersion;

    @SerializedName("loadVersions")
    public List<LoadVersionEntry> loadVersions;

    @SerializedName("requireTakePhotoForEachPalletInLoading")
    public boolean requireTakePhotoForEachPalletInLoading;

    @SerializedName("receiveVersions")
    public List<ReceiceVersionEntry> receiveVersions;

    @SerializedName("allowedOrderStatuses")
    public List<String> qcAllowedOrderStatuses;

    @SerializedName("qcScanCodeType")
    public QCSubmitTypeEntry qcSubmitType = QCSubmitTypeEntry.SCANNING_CARRIER_PICK_UP;

    @SerializedName("verifyItemOfSingleQty")
    public CarrierPickUpVerifyItemEntry qcVerifyItemOfSingleQty;

    @SerializedName("verifyItemOfMultipleQty")
    public CarrierPickUpVerifyItemEntry qcVerifyItemOfMultipleQty;

    @SerializedName("closeOrderAfterScanningTrackingNo")
    public Boolean qcCloseOrderAfterQCPassed;

    @SerializedName("forbidScanningCsUpcWhilePiecePicking")
    public boolean forbidScanningCsUpcWhilePiecePicking;

    @SerializedName("forbidScanningCsBarcodeWhilePiecePicking")
    public String forbidScanningCsBarcodeWhilePiecePicking;

    @SerializedName("autoUpgradeUOMForPickStrategy")
    public boolean autoUpgradeUOMForPickStrategy;

    @SerializedName("enableCollectMaterialAtPickingSetting")
    public CollectMaterialAtPickingSetting enableCollectMaterialAtPickingSetting;

    @SerializedName("autoUpgradeType")
    public AutoUpgradeType autoUpgradeType;

    @SerializedName("maxTaskVideoLengthInSeconds")
    public int maxTaskVideoLengthInSeconds;

    @SerializedName("allowTakeVideo")
    public boolean allowTakeVideo;

    @SerializedName("allowManualPutAwayByRN")
    public boolean allowManualPutawayByRN;

    @SerializedName("enablePutAwayAuditing")
    public boolean enablePutawayAudition;

    @SerializedName("scanPercentLPWhenPutAwayAuditing")
    public int scanPercentLPWhenPutAwayAuditing;

    @SerializedName("isLastMileTransload")
    public boolean isLastMileTransload;

    @SerializedName("ignoreCollectForCycleCount")
    public List<ItemRequiredDataTypeEntry> ignoreCollectForCycleCount;

    @SerializedName("shippingRule")
    public ShippingRuleEntry shippingRule;

    @SerializedName("timeWindow")
    public TimeWindowEntry timeWindow;

    @SerializedName("brandAsTitle")
    public boolean brandAsTitle;

    @SerializedName("collectTotalHours")
    public boolean collectTotalHours;

    @SerializedName("collectSlipSheets")
    public boolean collectSlipSheets;

    @SerializedName("allowNewItemInReceiving")
    public boolean allowNewItemInReceiving;

    @SerializedName("allowNewUomInReceiving")
    public boolean allowNewUomInReceiving;

    @SerializedName("collectItemGroupAtReceiving")
    public boolean collectItemGroupAtReceiving;

    @SerializedName("inventoryAvailableAfterPutaway")
    public boolean inventoryAvailableAfterPutaway;

    @SerializedName("confirmCapacityAfterPutAwayByLP")
    public boolean confirmCapacityAfterPutAwayByLP;

    @SerializedName("skipCollectingRFIDForDropshipOrder")
    public boolean skipCollectingRFIDForDropshipOrder;

    @SerializedName("collectCartonCount")
    public boolean collectCartonCount;

    @SerializedName("outboundInspection")
    public boolean outboundInspection;

    @SerializedName("inspectionMandatory")
    public boolean inspectionMandatory;

    @SerializedName("allowDuplicateInLPSNCollection")
    public boolean allowDuplicateInLPSNCollection;

    @SerializedName("printSoIdAtPackWorkstation")
    public boolean printSoIdAtPackWorkstation;

    @SerializedName("printShippingLabelOnSubmit")
    public boolean printShippingLabelOnSubmit;

    @Override
    public String getName() {
        return TextUtils.isEmpty(orgName) ? "" : orgName;
    }

    /**
     * Get all allowed receiving goods types including standard types and custom types.
     * Standard types include: GOOD, DAMAGE, NEAR EXPIRY, EXPIRED
     * @return List of allowed receiving goods types with duplicates removed
     */
    public List<String> getAllowedReceivingGoodsTypes() {
        List<String> standardTypes = Arrays.asList(
                Constant.GOODS_TYPE_GOOD, Constant.GOODS_TYPE_DAMAGE, Constant.GOODS_TYPE_NEAR_EXPIRY, Constant.GOODS_TYPE_EXPIRED
        );

        // Create a LinkedHashSet to maintain order and remove duplicates
        LinkedHashSet<String> result = new LinkedHashSet<>(standardTypes);

        // Add custom types if available
        if (allowedReceivingGoodsTypes != null) {
            result.addAll(allowedReceivingGoodsTypes);
        }

        return new ArrayList<>(result);
    }
}
