package com.linc.platform.foundation.model;

import com.google.gson.annotations.SerializedName;
import com.linc.platform.baseapp.model.BaseItemDimension;

import java.io.Serializable;

public class ItemDimensionCollectEntry extends BaseItemDimension implements Serializable {

    @SerializedName("facilityId")
    public String facilityId;

    @SerializedName("customerId")
    public String customerId;

    @SerializedName("itemSpecId")
    public String itemSpecId;

    @SerializedName("equipmentId")
    public String equipmentId;

    @SerializedName("length")
    public double length;

    @SerializedName("width")
    public double width;

    @SerializedName("height")
    public double height;

    @SerializedName("weight")
    public double weight;

    @SerializedName("shape")
    public String shape;

    @SerializedName("device")
    public String device;

    @Override
    public double getBaseLength() {
        return length;
    }

    @Override
    public double getBaseWidth() {
        return width;
    }

    @Override
    public double getBaseHeight() {
        return height;
    }

    @Override
    public double getBaseWeight() {
        // Return 0.01 as default value if weight is 0 or negative
        return weight <= 0 ? 0.01 : weight;
    }
}
