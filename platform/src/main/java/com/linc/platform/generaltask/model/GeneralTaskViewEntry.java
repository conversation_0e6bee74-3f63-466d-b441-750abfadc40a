package com.linc.platform.generaltask.model;

import android.text.TextUtils;

import com.annimon.stream.Collectors;
import com.annimon.stream.Stream;
import com.chad.library.adapter.base.entity.MultiItemEntity;
import com.google.gson.annotations.SerializedName;
import com.linc.platform.baseapp.model.LocationEntry;
import com.linc.platform.common.ChannelTypeEntry;
import com.linc.platform.common.DockStatusEntry;
import com.linc.platform.common.step.StepBaseEntry;
import com.linc.platform.common.step.StepTypeEntry;
import com.linc.platform.common.task.PriorityEntry;
import com.linc.platform.common.task.TaskStatusEntry;
import com.linc.platform.common.task.TaskTypeEntry;
import com.linc.platform.common.task.worktime.TaskUserTimeRecordEntry;
import com.linc.platform.idm.model.UserViewEntry;
import com.linc.platform.transload_v2.receiving.model.TransloadReceivingLastMileScanType;
import com.linc.platform.utils.CollectionUtil;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */

public class GeneralTaskViewEntry implements Serializable, MultiItemEntity {
    public static final String TAG = GeneralTaskViewEntry.class.getSimpleName();
    private static final String UOM_DAY = "Day";
    private static final String UOM_HOUR = "Hour";
    private static final int A_DAY = 86400;
    private static final int AN_HOUR = 3600;
    //用作埋点获取当前运行时的task实例
    private static volatile GeneralTaskViewEntry sCurrentGeneralTaskViewEntry;

    @SerializedName("id")
    public String id;

    @SerializedName("customerId")
    public String customerId;

    @SerializedName("customerIds")
    public List<String> customerIds;

    @SerializedName("customerName")
    public String customerName;

    @SerializedName("description")
    public String description;

    @SerializedName("assigneeUserId")
    public String assigneeUserId;

    @SerializedName("status")
    public TaskStatusEntry status;

    @SerializedName("taskType")
    public TaskTypeEntry taskType;

    @SerializedName("priority")
    public PriorityEntry priority;

    @SerializedName("startTime")
    public Date startTime;

    @SerializedName("endTime")
    public Date endTime;

    @SerializedName("createdBy")
    public String createdBy;

    @SerializedName("createdWhen")
    public Date createdWhen;

    @SerializedName("updatedBy")
    public String updatedBy;

    @SerializedName("updatedWhen")
    public Date updatedWhen;

    @SerializedName("steps")
    public List<StepBaseEntry> stepEntries;

    @SerializedName("companyId")
    public String companyId;

    @SerializedName("assignee")
    public UserViewEntry assignee;

    @SerializedName("lastAssignedWhen")
    public Date lastAssignedWhen;

    @SerializedName("pendingTime")
    public Long pendingTime;

    @SerializedName("isReopen")
    public boolean isReopen;

    @SerializedName("tags")
    public List<String> tags;

    @SerializedName("upStreamTaskId")
    public String upStreamTaskId;

    @SerializedName("channel")
    public ChannelTypeEntry channelType;

    @SerializedName("needsApproval")
    public boolean needsApproval;

    @SerializedName("userTimeRecords")
    public List<TaskUserTimeRecordEntry> userTimeRecords;

    @SerializedName("scanType")
    public TransloadReceivingLastMileScanType scanType;

    @SerializedName("isRobotForklift")
    public boolean isPickedByRobot;

    @SerializedName("taskPoolType")
    public TaskTypeEntry taskPoolType;

    public boolean isNew() {
        return TaskStatusEntry.NEW == status;
    }

    public boolean isTaskDone() {
        return TaskStatusEntry.CLOSED == status || TaskStatusEntry.FORCE_CLOSED == status;

    }

    public boolean isNeedDockCheckIn(String taskEntryId, LocationEntry dock) {
        return !isTaskDone() && dock != null && !TextUtils.isEmpty(dock.id) && !TextUtils.isEmpty(taskEntryId)
                && (((DockStatusEntry.RESERVED == dock.dockStatus || DockStatusEntry.AVAILABLE == dock.dockStatus) && taskEntryId.equals(dock.entryId))
                || (DockStatusEntry.AVAILABLE == dock.dockStatus && TextUtils.isEmpty(dock.entryId)));
    }

    public boolean isNeedDockCheckInForReceive(String taskEntryId, LocationEntry dock) {
         return !isTaskDone() && dock != null && !TextUtils.isEmpty(dock.id) && !TextUtils.isEmpty(taskEntryId)
                && (DockStatusEntry.RESERVED == dock.dockStatus || DockStatusEntry.PARKED == dock.dockStatus) && taskEntryId.equals(dock.entryId);
    }

    public boolean isPROGRESS() {
        return TaskStatusEntry.IN_PROGRESS == status;
    }

    public List<StepBaseEntry> getShippingStep() {
        if (stepEntries == null || stepEntries.size() == 0) {
            return null;
        }

        return Stream.of(stepEntries)
                .filter(entry -> entry.type == StepTypeEntry.SHIPPING)
                .collect(Collectors.toList());
    }

    public StepBaseEntry getStep(StepTypeEntry typeEntry) {
        if (stepEntries == null || stepEntries.size() == 0 || !stepExist(typeEntry)) {
            return null;
        }

        return Stream.of(stepEntries).filter(entry -> entry.type == typeEntry).findSingle().orElse(null);
    }

    public List<StepBaseEntry> getStepsOfSpecificTaskType(TaskTypeEntry typeEntry) {
        if (stepEntries == null || stepEntries.size() == 0) {
            return null;
        }

        return Stream.of(stepEntries).filter(entry -> entry.taskType == typeEntry).toList();
    }

    private boolean stepExist(StepTypeEntry stepTypeEntry) {
        return Stream.of(stepEntries).filter(entry -> entry.type == stepTypeEntry).count() > 0L;
    }

    public String getPendingTime() {
        if (pendingTime == null) {
            return "";
        }

        int day = (int) (pendingTime / A_DAY);
        int hours = (int) (pendingTime % A_DAY / AN_HOUR);

        return day == 0
                ? hours == 0 ? "" : hours + UOM_HOUR
                : hours == 0 ? day + UOM_DAY : day + UOM_DAY + " " + hours + UOM_HOUR;
    }

    public StepBaseEntry getStepById(String stepId) {
        if (TextUtils.isEmpty(stepId) || CollectionUtil.isNullOrEmpty(stepEntries)) {
            return null;
        }
        return Stream.of(stepEntries).filter(stepEntry -> stepEntry.id.equals(stepId)).findSingle().orElse(null);
    }

    public boolean isCreateBy(String userName) {
        return createdBy.equals(userName);
    }

    public boolean isCreateByPhone() {
        return channelType != null && channelType == ChannelTypeEntry.ANDROID;
    }

    public void attach() {
        sCurrentGeneralTaskViewEntry = this;
    }

    @Override
    public int getItemType() {
        return TaskTypeEntry.PICK == taskType ? 1 : TaskTypeEntry.PUT_BACK == taskType ? 2 : 0;
    }

    public String getCustomerId() {
        return CollectionUtil.isNotNullOrEmpty(customerIds) ? customerIds.get(0) : customerId;
    }
}
