package com.linc.platform.baseapp.model;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;
import java.util.List;

public class VirtualLocationGroupViewEntry implements Serializable {
    @SerializedName("id")
    public String id;

    @SerializedName("customerId")
    public String customerId;

    @SerializedName("name")
    public String name;

    @SerializedName("pickStrategyWeight")
    public Double pickStrategyWeight;

    @SerializedName("tagIds")
    public List<String> tagIds;

    @SerializedName("virtualLocationGroupType")
    public VirtualLocationGroupType virtualLocationGroupType;

    @SerializedName("allowAutoAssign")
    public boolean allowAutoAssign;

    @SerializedName("forbidIgnoringLocationSuggestion")
    public boolean forbidIgnoringLocationSuggestion;

    @SerializedName("userIds")
    public List<String> userIds;
}
