package com.linc.platform.baseapp.model

import com.linc.platform.utils.StringUtil

abstract class BaseItemDimension {
    open fun convertToIN(data: Double): String? {
        return StringUtil.twoDecimalPointKeep(data)
    }

    open fun convertToLB(data: Double): String? {
        return StringUtil.twoDecimalPointKeep(data)
    }

    abstract fun getBaseLength(): Double
    abstract fun getBaseWidth(): Double
    abstract fun getBaseHeight(): Double
    abstract fun getBaseWeight(): Double
}