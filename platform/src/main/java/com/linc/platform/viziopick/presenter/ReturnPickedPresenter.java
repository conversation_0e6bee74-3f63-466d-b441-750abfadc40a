package com.linc.platform.viziopick.presenter;


import com.linc.platform.pick.model.ItemReturnToInventoryEntry;

import java.util.List;

/**
 * Created by Gavin
 */

public interface ReturnPickedPresenter {
    void getReturnItemDetail(String itemSpecId);

    void onLpAutoComplete(String data, int scannerType);

    void getReturnToLocation(String data);

    void getOriginalLocation(String originalReceiveLPId);

    void returnPickedToInventory(List<ItemReturnToInventoryEntry> returnData);

    void getPickTask();
}
