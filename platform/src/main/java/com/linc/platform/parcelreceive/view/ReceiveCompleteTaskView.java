package com.linc.platform.parcelreceive.view;

import com.linc.platform.core.ProgressView;
import com.linc.platform.foundation.model.questionnaire.CustomerQuestionnaireEntry;
import com.linc.platform.generaltask.model.GeneralTaskViewEntry;

/**
 * @Description:
 * @Author: Dennis
 * @CreateDate: 2021/1/26 15:31
 */
public interface ReceiveCompleteTaskView extends ProgressView {

    void completeTask();

    void showForceCloseTaskDialog(String taskId, String errMsg);

    void startAnswerTaskQuestionnaire(GeneralTaskViewEntry taskViewEntry, CustomerQuestionnaireEntry customerQuestionnaire);
}
