package com.linc.platform.toolset.inventorycount.makepallet;

import com.linc.platform.common.help.FunctionHelpPresenter;
import com.linc.platform.foundation.model.ItemSpecEntry;
import com.linc.platform.foundation.model.UnitEntry;
import com.linc.platform.foundation.model.organization.common.base.OrganizationViewEntry;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface InventoryNewPalletPresenter extends FunctionHelpPresenter {
    InventoryNewPalletData data();

    void onViewCreated();

    void loadTitles();

    void onTitleSelected(OrganizationViewEntry title);

    void newLP();

    void loadItem(String barcode);

    void onItemSelected(ItemSpecEntry item);

    void onClearItem();

    void onClickToSelectQty();

    void onQtySelected(String qty, UnitEntry unit, String lotNO, Date expDate, Date mfgDate, Date shelfLifeDate);

    void onSubmit(List<String> snList);

    void reprintLP();

    void onSNScanned(String sn);

    void removeSN(String removedSn);
}
