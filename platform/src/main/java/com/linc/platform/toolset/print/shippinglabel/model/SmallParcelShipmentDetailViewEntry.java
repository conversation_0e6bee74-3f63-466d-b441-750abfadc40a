package com.linc.platform.toolset.print.shippinglabel.model;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;
import java.util.List;

public class SmallParcelShipmentDetailViewEntry implements Serializable {
    @SerializedName( "orderId")
    public String orderId;

    @SerializedName("itemSpecId")
    public String itemSpecId;

    @SerializedName("itemSpecName")
    public String itemSpecName;

    @SerializedName("unitId")
    public String unitId;

    @SerializedName("uomName")
    public String uomName;

    @SerializedName("qty")
    public Double qty;

    @SerializedName("trackingNo")
    public String trackingNo;

    @SerializedName("masterTrackingNo")
    public String masterTrackingNo;

    @SerializedName("soId")
    public String soId;

    @SerializedName("sns")
    public List<String> sns;

    @SerializedName("coo")
    public String coo;

    @SerializedName("pickTaskId")
    public String pickTaskId;

    @SerializedName("suggestedPickLocationId")
    public String suggestedPickLocationId;

    @SerializedName("shipmentInfoId")
    public String shipmentInfoId;

    @SerializedName("spShipmentId")
    public String spShipmentId;

    @SerializedName("shipmentLabel")
    public String shipmentLabel;

    @SerializedName("isShipmentDeleted")
    public Boolean isShipmentDeleted = false;

    @SerializedName("isTrackingNoDeleted")
    public Boolean isTrackingNoDeleted = false;

    @SerializedName("isShippingLabelPrinted")
    public Boolean isShippingLabelPrinted = false;

    @SerializedName("labelType")
    public String labelType;
}
