package com.linc.platform.toolset.itemcollector.presenter;

import com.linc.platform.foundation.model.ItemSpecUpdateEntry;
import com.linc.platform.foundation.model.ItemUpcCollectViewEntry;


/**
 * Created by devinc on 2017/9/27.
 */

public interface ItemCollectorPresenter {
    void loadItemUpcCollection(String itemSpecId);

    void removeUpc(String itemSpecId, ItemUpcCollectViewEntry entry);

    void addUpcCode(String id, String data);

    void getItemPhotos(String itemSpecId);

    void createItemPhoto(String itemSpecId, String fileId);

    void deleteItemPhoto(String id, String fileId);

    void updateItemSpec(String id, ItemSpecUpdateEntry updateEntry);
}
