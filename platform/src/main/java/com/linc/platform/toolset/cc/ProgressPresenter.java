package com.linc.platform.toolset.cc;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface ProgressPresenter {
    void loadLocationData();

    void loadLpData();

    void onMenuSubmit(int action, String lpConfiguration);

    void onLpScanComplete(String fromLp, String toLp);

    void onQtySubmit(String qty);

    void onSnSubmit(List<String> snList);

    void onLpConfigurationSubmit(boolean checked);

    void setProgressView(ProgressView progressView);

    void setWorkView(WorkView workView);
}
