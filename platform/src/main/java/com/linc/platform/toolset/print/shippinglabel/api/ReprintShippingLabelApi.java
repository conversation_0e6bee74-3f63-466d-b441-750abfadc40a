package com.linc.platform.toolset.print.shippinglabel.api;

import com.linc.platform.http.IdResponse;
import com.linc.platform.toolset.print.shippinglabel.model.SmallParcelShipmentDetailSearchEntry;
import com.linc.platform.toolset.print.shippinglabel.model.SmallParcelShipmentDetailViewEntry;
import com.linc.platform.toolset.print.shippinglabel.model.SmallParcelShipmentSearchEntry;
import com.linc.platform.toolset.print.shippinglabel.model.SmallParcelShipmentViewEntry;

import java.util.List;

import retrofit2.Response;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.Path;
import rx.Observable;

/**
 * Created by Gavin
 */

public interface ReprintShippingLabelApi {

    @POST("wms-app/small-parcel-shipment/search")
    Observable<Response<List<SmallParcelShipmentViewEntry>>> search(@Body SmallParcelShipmentSearchEntry search);

    @GET("wms-app/outbound/order/packing-list/{trackingNo}/print")
    Observable<Response<IdResponse>> getFileId(@Path("trackingNo") String trackingNo);

    @POST("bam/wms-app/small-parcel-shipment/shipment-detail/search")
    Observable<Response<List<SmallParcelShipmentDetailViewEntry>>> searchDetails(@Body SmallParcelShipmentDetailSearchEntry search);
}
