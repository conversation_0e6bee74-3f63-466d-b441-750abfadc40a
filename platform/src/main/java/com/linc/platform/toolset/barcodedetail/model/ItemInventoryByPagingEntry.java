package com.linc.platform.toolset.barcodedetail.model;

import com.google.gson.annotations.SerializedName;
import com.linc.platform.generaltask.PagingResultParamEntry;
import com.linc.platform.inventory.model.InventoryEntry;

import java.io.Serializable;
import java.util.List;

/**
 * Author:Arnold
 * Date:2019/12/30 17:03
 */
public class ItemInventoryByPagingEntry implements Serializable {

    @SerializedName("inventories")
    public List<InventoryEntry> inventories;

    @SerializedName("paging")
    public PagingResultParamEntry paging;

    public String locationId;
}
