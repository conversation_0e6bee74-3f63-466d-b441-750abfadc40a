package com.linc.platform.home.more.inventorycount.cyclecount.model;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class CycleCountScanLPData {
    public String lpId;
    public List<CycleCountProcessItem> processItems = new ArrayList<>();

    public boolean isLPFinished() {
        for (CycleCountProcessItem processItem : processItems) {
            if (!processItem.isCounted) {
                return false;
            }
        }
        return true;
    }

    public boolean isLPHasCounted() {
        for (CycleCountProcessItem processItem : processItems) {
            if (processItem.isCounted) {
                return true;
            }
        }
        return false;
    }
}
