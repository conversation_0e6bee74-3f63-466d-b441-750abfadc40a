package com.linc.platform.home.more.lpsncollection.view;

import com.linc.platform.foundation.model.rfid.RFIDRelatedItemEntry;

import java.util.List;
import java.util.Map;

/**
 * Created by Gavin
 */
public interface OrderSNCollectionWorkView {
    void onSuccessToast();

    void setSubmitBtnEnabled();

    void onSNLengthNotMatch();

    void onSNDuplicate();

    void toastSNItemNotMatch();

    void addSnToList(List<String> snList);

    void addSnToListDuplicate(List<String> snList);

    void showDuplicateShippingSn(List<String> shippingSns);

    void onRemoveAllSuccess();

    void showRFIDNoMatchDialog(Map<String, List<RFIDRelatedItemEntry>> itemWithRFIDs);
}
