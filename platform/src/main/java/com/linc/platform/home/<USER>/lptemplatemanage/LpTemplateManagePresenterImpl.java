package com.linc.platform.home.more.lptemplatemanage;

import android.content.Context;

import com.linc.platform.common.help.FunctionHelpPresenterImpl;
import com.linc.platform.foundation.api.SingleLpTemplateApi;
import com.linc.platform.foundation.model.lptemplate.SingleLpTemplateEntry;
import com.linc.platform.foundation.model.lptemplate.SingleLpTemplateSearchEntry;
import com.linc.platform.http.ErrorCodeSubscriber;
import com.linc.platform.http.ErrorResponse;
import com.linc.platform.http.HttpService;
import com.linc.platform.utils.RxUtil;
import com.linc.platform.utils.ToastUtil;

import java.util.List;

import retrofit2.Response;

/**
 * <AUTHOR>
 */

public class LpTemplateManagePresenterImpl implements LpTemplateManagePresenter {
    private LpTemplateManageView lpTemplateManageView;
    private SingleLpTemplateApi singleLpTemplateApi;
    private final FunctionHelpPresenterImpl mFunctionHelpPresenter;
    private final String customerId;

    public LpTemplateManagePresenterImpl(LpTemplateManageView lpTemplateManageView, String customerId) {
        this.lpTemplateManageView = lpTemplateManageView;
        this.customerId = customerId;
        singleLpTemplateApi = HttpService.createService(SingleLpTemplateApi.class);
        mFunctionHelpPresenter = new FunctionHelpPresenterImpl();
    }

    @Override
    public void loadLpTemplate(SingleLpTemplateSearchEntry searchEntry) {
        if (searchEntry == null) {
            return;
        }
        lpTemplateManageView.showProgress(true);
        singleLpTemplateApi.search(searchEntry)
                .compose(RxUtil.asyncSchedulers())
                .subscribe(new ErrorCodeSubscriber<Response<List<SingleLpTemplateEntry>>>() {
                    @Override
                    public void onSuccess(Response<List<SingleLpTemplateEntry>> listResponse) {
                        lpTemplateManageView.refreshLpTemplates(listResponse.body());
                    }

                    @Override
                    public void onFailed(ErrorResponse errorResponse) {
                        ToastUtil.showErrorToast(errorResponse.getErrorMessage());
                    }

                    @Override
                    public void onDone() {
                        lpTemplateManageView.showProgress(false);
                    }
                });

    }

    @Override
    public void getAndOpenHelpPage(Context context, String helpPageKey, String facilityId) {
        mFunctionHelpPresenter.getAndOpenHelpPage(context, helpPageKey, customerId, facilityId);
    }
}
