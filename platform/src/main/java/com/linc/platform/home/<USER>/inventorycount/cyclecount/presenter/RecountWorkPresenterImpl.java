package com.linc.platform.home.more.inventorycount.cyclecount.presenter;

import android.content.Context;
import android.text.TextUtils;

import com.annimon.stream.Stream;
import com.linc.platform.R;
import com.linc.platform.apiprocessor.IdAutoCompleteAPI;
import com.linc.platform.baseapp.api.LocationApi;
import com.linc.platform.baseapp.model.LocationBulkSearchEntry;
import com.linc.platform.baseapp.model.LocationEntry;
import com.linc.platform.common.TitleType;
import com.linc.platform.common.help.FunctionHelpPresenterImpl;
import com.linc.platform.common.step.StepBaseEntry;
import com.linc.platform.common.task.TaskStatusEntry;
import com.linc.platform.common.task.TaskTypeEntry;
import com.linc.platform.common.task.worktime.TaskWorkTimeDal;
import com.linc.platform.foundation.api.CustomerApi;
import com.linc.platform.foundation.api.OrganizationPartnershipApi;
import com.linc.platform.foundation.apiprocessor.SearchUnitAPI;
import com.linc.platform.foundation.model.CustomerViewEntry;
import com.linc.platform.foundation.model.ItemSpecEntry;
import com.linc.platform.foundation.model.ItemUnitSearchEntry;
import com.linc.platform.foundation.model.OrganizationPartnershipSearchEntry;
import com.linc.platform.foundation.model.OrganizationRelationshipTagEntry;
import com.linc.platform.foundation.model.UnitEntry;
import com.linc.platform.foundation.model.organization.common.base.BasicViewEntry;
import com.linc.platform.foundation.model.organization.common.base.OrganizationViewEntry;
import com.linc.platform.home.more.inventorycount.cyclecount.CountFlowHelper;
import com.linc.platform.home.more.inventorycount.cyclecount.api.CycleCountStepAPI;
import com.linc.platform.home.more.inventorycount.cyclecount.api.CycleCountTaskAPI;
import com.linc.platform.home.more.inventorycount.cyclecount.apiprocess.CloseRecountStepAPI;
import com.linc.platform.home.more.inventorycount.cyclecount.apiprocess.GetRecountStepAPI;
import com.linc.platform.home.more.inventorycount.cyclecount.apiprocess.GetRecountTaskAPI;
import com.linc.platform.home.more.inventorycount.cyclecount.apiprocess.SubmitCountAPI;
import com.linc.platform.home.more.inventorycount.cyclecount.cyclecountprocessor.handler.CountRecordHandler;
import com.linc.platform.home.more.inventorycount.cyclecount.cyclecountprocessor.handler.GetNextLocationHandler;
import com.linc.platform.home.more.inventorycount.cyclecount.cyclecountprocessor.handler.ItemBarcodeHandler;
import com.linc.platform.home.more.inventorycount.cyclecount.cyclecountprocessor.handler.RecountItemsHandler;
import com.linc.platform.home.more.inventorycount.cyclecount.cyclecountprocessor.handler.SearchLocationHandler;
import com.linc.platform.home.more.inventorycount.cyclecount.cyclecountprocessor.handler.SubmitNewItemHandler;
import com.linc.platform.home.more.inventorycount.cyclecount.cyclecountprocessor.handler.SubmitRecountHandler;
import com.linc.platform.home.more.inventorycount.cyclecount.cyclecountprocessor.validator.EmptyLocationValidator;
import com.linc.platform.home.more.inventorycount.cyclecount.cyclecountprocessor.validator.ItemValidator;
import com.linc.platform.home.more.inventorycount.cyclecount.cyclecountprocessor.validator.LPValidator;
import com.linc.platform.home.more.inventorycount.cyclecount.cyclecountprocessor.validator.LocationValidator;
import com.linc.platform.home.more.inventorycount.cyclecount.cyclecountprocessor.validator.NewItemValidator;
import com.linc.platform.home.more.inventorycount.cyclecount.cyclecountprocessor.validator.RecountValidator;
import com.linc.platform.home.more.inventorycount.cyclecount.model.CountAction;
import com.linc.platform.home.more.inventorycount.cyclecount.model.CountEntryCreateEntry;
import com.linc.platform.home.more.inventorycount.cyclecount.model.CountFlow;
import com.linc.platform.home.more.inventorycount.cyclecount.model.CountInfo;
import com.linc.platform.home.more.inventorycount.cyclecount.model.CountLocationEntry;
import com.linc.platform.home.more.inventorycount.cyclecount.model.CycleCountStepViewEntry;
import com.linc.platform.home.more.inventorycount.cyclecount.model.ItemInfo;
import com.linc.platform.home.more.inventorycount.cyclecount.model.NewItemInfo;
import com.linc.platform.home.more.inventorycount.cyclecount.model.RecountTaskUpdateEntry;
import com.linc.platform.home.more.inventorycount.cyclecount.model.RecountTaskViewEntry;
import com.linc.platform.home.more.inventorycount.cyclecount.view.CountWorkView;
import com.linc.platform.http.ErrorCode;
import com.linc.platform.http.HttpService;
import com.linc.platform.step.TaskStepProcessTimePresenter;
import com.linc.platform.toolset.task.model.Task;
import com.linc.platform.utils.CollectionUtil;
import com.linc.platform.utils.ConfigurationMapUtil;
import com.linc.platform.utils.ResUtil;
import com.linc.platform.utils.ToastUtil;

import java.util.Collections;
import java.util.Date;
import java.util.List;

import static com.linc.platform.utils.CustomerConfigUtil.allowManualEntry;
import static com.linc.platform.utils.ResUtil.getString;

/**
 * <AUTHOR>
 */
public class RecountWorkPresenterImpl extends TaskStepProcessTimePresenter implements CountWorkPresenter {
    private CountWorkView view;
    private CountAction action;

    private CustomerApi customerApi;
    private FunctionHelpPresenterImpl functionHelpPresenterImpl;
    private LocationApi mLocationApi;
    private CycleCountTaskAPI mCountTaskApi;
    private CycleCountStepAPI mCountStepApi;
    private OrganizationPartnershipApi organizationPartnershipApi;
    private TaskWorkTimeDal taskWorkTimeDal;

    public RecountWorkPresenterImpl(CountWorkView view, StepBaseEntry step) {
        super(step);
        this.view = view;
        this.action = new CountAction(step);
        this.customerApi = api(CustomerApi.class);
        functionHelpPresenterImpl = new FunctionHelpPresenterImpl();
        mLocationApi = HttpService.createService(LocationApi.class);
        mCountTaskApi = HttpService.createService(CycleCountTaskAPI.class);
        mCountStepApi = HttpService.createService(CycleCountStepAPI.class);
        organizationPartnershipApi = HttpService.createService(OrganizationPartnershipApi.class);
        taskWorkTimeDal = new TaskWorkTimeDal();
    }

    @Override
    public CountFlow countFlow() {
        return action.countFlow();
    }

    @Override
    public CountInfo countInfo() {
        return action.countInfo();
    }

    @Override
    public Task getTask() {
        return action.getTask();
    }

    @Override
    public CycleCountStepViewEntry getStep() {
        return action.getStep();
    }

    @Override
    public CustomerViewEntry getCustomer() {
        return action.getCustomerViewEntry();
    }

    @Override
    public void onViewCreated() {
        GetRecountStepAPI.newInstance().execute(view, action.getStep().id, action::setStep);
        GetRecountTaskAPI.newInstance().execute(view, action.getStep().taskId, task ->
        {
            action.setTask(task);
            view.onGetTaskSuccess(task);
            if (!action.getStep().isDone()) initTaskWorkTime();
            asyncExecute(customerApi.getCustomer(action.getCountRecord().customerId), view,
                    customer -> {
                        view.limitManualEntry(!allowManualEntry(customer));
                        action.setCustomerViewEntry(customer);
                    });
        });
    }

    @Override
    public void onLocationScanned(String location) {
        if (TextUtils.isEmpty(location)) {
            view.onScanLocationFailed(getString(R.string.msg_please_scan_or_input_location));
        } else {
            SearchLocationHandler.search(location, view, this::validateLocation);
        }
    }

    private void validateLocation(LocationEntry location) {
        String error = RecountValidator.validateLocation(location, action);
        if (TextUtils.isEmpty(error)) {
            RecountItemsHandler.buildProcessItemsByTaskItem(location, view, action);
        } else if (LocationValidator.isNeedShowHasCountedLocationDialog(location, error)) {
            view.showHasCountedLocationDialog(error, location,
                    locationEntry -> RecountItemsHandler.buildProcessItemsByTaskItem(locationEntry, view, action));
        } else {
            view.onScanLocationFailed(error);
        }
    }

    @Override
    public void onLocationConfirmed(LocationEntry location) {
        validateLocation(location);
    }

    @Override
    public void onLPScanned(String lp) {
        if (TextUtils.isEmpty(lp)) {
            view.onScanLPFailed(ResUtil.getString(R.string.msg_please_scan_or_input_lp));
            return;
        }
        if (LPValidator.hasCountedLP(action, lp)) {
            view.showHasCountedLpDialog(lp, this::lpScannedProceed);
            return;
        }
        lpScannedProceed(lp);
    }

    private void lpScannedProceed(String lp) {
        IdAutoCompleteAPI.newInstance().execute(view, Collections.singletonList(lp), lpIds ->
        {
            String error = LPValidator.validate(lpIds, action);
            if (TextUtils.isEmpty(error)) {
                action.initLPInfo(lpIds.get(0));
                view.onScanLPSuccess(action.countInfo().lpInfo);
            } else {
                view.onScanLPFailed(error);
            }
        });
    }

    @Override
    public void overwriteLPCounted(String lp) {

    }

    @Override
    public void onUpcScanned(String upc) {

    }

    @Override
    public void onItemScanned(String itemBarcode) {
        ItemBarcodeHandler.handle(itemBarcode, action.getCountRecord().customerId, view, this::onItemConfirmed);
    }

    @Override
    public void onItemConfirmed(ItemSpecEntry item) {
        String error = ItemValidator.validate(item, action);
        if (TextUtils.isEmpty(error)) {
            searchItemUnits(item);
        } else {
            view.onScanItemFailed(error);
        }
    }

    private void searchItemUnits(ItemSpecEntry item) {
        ItemUnitSearchEntry searchEntry = new ItemUnitSearchEntry();
        searchEntry.itemSpecId = item.id;
        SearchUnitAPI.newInstance().execute(view, searchEntry, units -> {
            action.countFlow().isRecount = false;
            action.initItemInfo(item.id, units);
            view.onScanItemSuccess(action.countInfo().itemInfo);
            if (!getCustomer().brandAsTitle) {
                loadTitleByCustomer(action.getCountRecord().customerId);
            }
        });
    }

    public void loadTitleByCustomer(String customerId) {
        OrganizationPartnershipSearchEntry searchEntry = new OrganizationPartnershipSearchEntry();
        searchEntry.organizationId = TextUtils.isEmpty(customerId) ? null : customerId;
        searchEntry.relationship = OrganizationRelationshipTagEntry.TITLE;
        asyncExecute(organizationPartnershipApi.search(searchEntry), view,
                errorResponse -> ToastUtil.showToast(errorResponse.getErrorMessage()),
                organizationViewEntries -> {
                    //默认添加customer本身作为title
                    if (CollectionUtil.isNullOrEmpty(organizationViewEntries)) {
                        view.loadCustomerTitle(Collections.singletonList(buildDefaultOrganization()));
                        return;
                    }

                    if (CollectionUtil.isNotNullOrEmpty(organizationViewEntries)) {
                        OrganizationViewEntry targetOrganization = Stream.of(organizationViewEntries).filter(organizationViewEntry -> organizationViewEntry.basic.id.equals(action.getCountRecord().customerId)).findFirst().orElse(null);
                        if (targetOrganization == null) {
                            organizationViewEntries.add(0, buildDefaultOrganization());
                        }
                        view.loadCustomerTitle(Stream.of(organizationViewEntries).filter(organizationViewEntry -> organizationViewEntry.extend == null || organizationViewEntry.extend.titleType == null
                                || organizationViewEntry.extend.titleType != TitleType.REVERSE).toList());
                    }
                });
    }

    private OrganizationViewEntry buildDefaultOrganization() {
        OrganizationViewEntry defaultOrganization = new OrganizationViewEntry();
        defaultOrganization.basic = new BasicViewEntry();
        defaultOrganization.basic.name = action.getCountRecord().customer.name;
        defaultOrganization.basic.id = action.getCountRecord().customer.id;
        return defaultOrganization;

    }

    @Override
    public void handleNewItem(NewItemInfo newItemInfo) {
        String error = NewItemValidator.validate(newItemInfo.item, action.countInfo());
        if (TextUtils.isEmpty(error)) {
            action.updateItemInfo(newItemInfo);
            view.onNewItemSuccess(action.countInfo().itemInfo);
        } else {
            view.onNewItemFailed(error);
        }
    }

    @Override
    public void onItemUnitSelected(UnitEntry unitEntry) {
        action.updateSelectedUnit(unitEntry);
    }

    @Override
    public void onItemTitleSelected(OrganizationViewEntry organizationViewEntry) {
        action.updateSelectedTitle(organizationViewEntry);
    }

    @Override
    public void submitCounted(String qty, String lotNo, Date mfgDate) {
        boolean isRequireCollectLotNo = CountFlowHelper.Companion.isRequireCollectLotNo(TaskTypeEntry.RECOUNT, getCustomer(), getItemSpecEntry());
        boolean isRequireCollectMfgDate = CountFlowHelper.Companion.isRequireCollectMfgDate(TaskTypeEntry.RECOUNT, getCustomer(), getItemSpecEntry());
        if (isRequireCollectLotNo && TextUtils.isEmpty(lotNo)) {
            view.onSubmitFailed(getString(R.string.hint_lot_no));
            return;
        }
        if (isRequireCollectMfgDate && null == mfgDate) {
            view.onSubmitFailed(getString(R.string.msg_please_select_mfg_date));
            return;
        }
        if (TextUtils.isEmpty(qty)) return;
        action.setMfgDate(mfgDate);
        SubmitRecountHandler.submit(view, action, Double.valueOf(qty), lotNo);
    }

    @Override
    public void markLocationIsEmpty() {
        unlockLocation();
        String error = EmptyLocationValidator.validate(action);
        if (TextUtils.isEmpty(error)) {
            action.countFlow().isEmptyLocation = true;
            SubmitCountAPI.newInstance().execute(view, buildSubmitEntry(), aVoid -> CountRecordHandler.updateCountedRecords(view, action, this::doLocationDone));
        } else {
            view.onEmptyLocationFailed(error);
        }
    }

    private CountEntryCreateEntry buildSubmitEntry() {
        CountEntryCreateEntry submitEntry = new CountEntryCreateEntry();
        submitEntry.recordId = action.getCountRecordId();
        submitEntry.taskId = getStep().taskId;
        submitEntry.qty = 0D;
        submitEntry.titleId = action.getCountRecord().titleId;
        submitEntry.customerId = action.getCountRecord().customerId;
        submitEntry.isEmptyLocation = true;
        submitEntry.locationId = countInfo().locationInfo.getLocation().id;

        return submitEntry;
    }

    @Override
    public void closeStep() {
        CloseRecountStepAPI.newInstance().execute(view, action.getStep().id, aVoid -> {
            view.onStepCloseSuccess();
            GetRecountStepAPI.newInstance().execute(view, action.getStep().id, action::setStep);
        });
    }

    @Override
    public void onEnableScanSNClick(boolean isChecked) {
        action.countInfo().itemInfo.setEnableSN(isChecked);
        view.onEnableScanSNSuccess(action.countInfo().itemInfo.isEnableSN());
    }

    @Override
    public void onScanSNClick() {
        view.showScanSNDialog(action.countInfo().getTargetCustomer().id, action.getCountItem(), action.countInfo().itemInfo.getSNList());
    }

    @Override
    public void onSNScanned(List<String> snList) {
        action.countInfo().itemInfo.setSNList(snList);
        view.onScanSNSuccess(snList);
    }

    @Override
    public void doneCurLocation() {
        view.showScanLocationView();
    }

    @Override
    public void getNextLocation() {
        GetNextLocationHandler.getNextLocation(view, action, TaskTypeEntry.RECOUNT);
    }

    @Override
    public void onItemSelected(ItemSpecEntry item) {

    }

    @Override
    public CountAction getCountAction() {
        return action;
    }

    @Override
    public void getAndOpenHelpPage(Context context, String facilityId) {
        String customerId = null;
        if (getCustomer() != null) {
            customerId = getCustomer().orgId;
        }
        String key = null;
        switch (view.getCurrentPartType()) {
            case SCAN_LOCATION:
                key = ConfigurationMapUtil.PAGE_KEY_RECOUNT_TASK_SCAN_LOCATION;
                break;
            case SCAN_LP:
                key = ConfigurationMapUtil.PAGE_KEY_RECOUNT_TASK_SCAN_LP;
                break;
            case SCAN_ITEM:
                key = ConfigurationMapUtil.PAGE_KEY_RECOUNT_TASK_SCAN_ITEM;
                break;
            case COUNT_QTY:
                key = ConfigurationMapUtil.PAGE_KEY_RECOUNT_TASK_COUNT_QTY;
                break;
            case COUNT_LP_QUANTITY:
                key = ConfigurationMapUtil.PAGE_KEY_RECOUNT_TASK_COUNT_LP_QTY;
                break;
            default:
        }
        functionHelpPresenterImpl.getAndOpenHelpPage(context, key, customerId, facilityId);
    }

    @Override
    public void searchLocationWithBulk(String locationName, String aisle, String bay) {
        LocationBulkSearchEntry searchEntry = new LocationBulkSearchEntry();
        if (!TextUtils.isEmpty(locationName)) {
            searchEntry.locationName = locationName;
        } else {
            searchEntry.aisle = aisle;
            searchEntry.bay = bay;
        }
        execute(mLocationApi.searchLocationListWithBulk(searchEntry), locationEntries ->
        {
            if (CollectionUtil.isNullOrEmpty(locationEntries)) {
                view.onScanLocationFailed(getString(R.string.msg_invalid_location_please_enter_again));
            } else if (locationEntries.size() == 1) {
                validateLocation(locationEntries.get(0));
            } else {
                view.onMultipleLocationFound(locationEntries);
            }
        }, errorResponse -> view.onScanLocationFailed(errorResponse.getErrorMessage()));
    }
    @Override
    public ItemSpecEntry getItemSpecEntry(){
        return action.getCountItem();
    }

    @Override
    public void getSuggestLocation() {
        if (((RecountTaskViewEntry) action.getTask()).status != TaskStatusEntry.IN_PROGRESS) {
            return;
        }
        CountLocationEntry locationEntry = new CountLocationEntry();
        execute(mCountStepApi.getReCountTaskNextLocation(action.getStep().taskId, locationEntry),
                location -> view.onGetNextLocationSuccess(action.findSuggestLocation(location)),
                errorResponse -> {
                    if (ErrorCode.CYCLE_COUNT_ALL_LOCATION_COUNTED.equals(errorResponse.code)) {
                        closeStep();
                        ToastUtil.showToast(errorResponse.getErrorMessage());
                    } else {
                        view.onScanLocationFailed(errorResponse.getErrorMessage());
                    }
                });
    }

    private void onItemDone() {
        view.onSubmitSuccess(action.countInfo());
    }

    @Override
    public void submitNewItem() {
        ItemInfo itemInfo = action.countInfo().itemInfo;
        itemInfo.lpId = action.countInfo().lpInfo.getLpId();
        itemInfo.locationId = action.countInfo().locationInfo.getLocation().id;
        SubmitNewItemHandler.submit(view, action, itemInfo.getCountQty(), true);
    }

    @Override
    public void submitOverride() {
        SubmitRecountHandler.submitOverride(view, action);
    }

    @Override
    public boolean hasCountLocationAtLeastOnce() {
        LocationEntry location = action.countInfo().locationInfo.getLocation();
        return LocationValidator.hasCountedLocation(action, location);
    }

    @Override
    public void doLocationDone() {
        unlockLocation();
        LocationEntry location = action.countInfo().locationInfo.getLocation();
        RecountTaskUpdateEntry updateEntry = new RecountTaskUpdateEntry();
        List<String> countedLocationIds = ((RecountTaskViewEntry) action.getTask()).getCountedLocationIds();
        if (!countedLocationIds.contains(location.id)) {
            countedLocationIds.add(location.id);
        }
        updateEntry.countedLocationIds = countedLocationIds;
        execute(mCountTaskApi.recountTaskUpdate(action.getStep().taskId, updateEntry), Void -> {
            getNextLocation();
            ToastUtil.showToast(R.string.msg_done_location_success);
        });
    }

    @Override
    public void doItemDone() {
        if (ItemValidator.hasCountedAtLeastOnce(action)) {
            action.updateToItemDone();
            onItemDone();
        } else {
            ToastUtil.showToast(R.string.msg_item_cant_be_done_for_no_count_record);
        }
    }

    @Override
    public void lockLocation() {
        if (action.getCountRecord().needLockInventory == true) {
            return;
        }
        LocationEntry location = action.countInfo().locationInfo.getLocation();
        if (LocationValidator.hasCountedLocation(action, location) && !action.isLocationDone()) {
            return;
        }
        execute(mCountTaskApi.prepareDynamicInventoryCount(action.getCountRecordId(), action.getStep().taskId, location.id), Void -> {
            GetRecountStepAPI.newInstance().execute(view, action.getStep().id, step -> {
                action.setStep(step);
                view.updateStepProcessView(action.countInfo());
            });

        });
    }

    @Override
    public void unlockLocation() {
        if (action.getCountRecord().needLockInventory == true) {
            return;
        }
        LocationEntry location = action.countInfo().locationInfo.getLocation();
        execute(mCountTaskApi.unLockLocation(action.getCountRecordId(), location.id), Void -> {
        });
    }

    @Override
    public void initTaskWorkTime() {
        view.initWorkTime();
    }

    @Override
    public void startTaskWorkTime() {
        taskWorkTimeDal.initStartTime((RecountTaskViewEntry) action.getTask(), false, (duration) -> {
            view.startWorkTime(duration);
        });
    }

    @Override
    public void resumeTaskWorkTime() {
        taskWorkTimeDal.resumeWorkTime(() -> {
            view.resumeWorkTime();
        });
    }

    @Override
    public void pauseTaskWorkTime() {
        taskWorkTimeDal.pauseWorkTime(() -> {
            view.pauseWorkTime();
        });
    }

}
