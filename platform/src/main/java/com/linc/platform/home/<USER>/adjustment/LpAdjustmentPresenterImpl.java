package com.linc.platform.home.more.adjustment;

import android.text.TextUtils;

import com.linc.platform.baseapp.api.LocationApi;
import com.linc.platform.baseapp.model.LocationEntry;
import com.linc.platform.baseapp.model.LocationSearchEntry;
import com.linc.platform.baseapp.model.LocationTypeEntry;
import com.linc.platform.common.apidal.BaseApiDal;
import com.linc.platform.foundation.api.CustomerApi;
import com.linc.platform.foundation.api.ItemSpecAPI;
import com.linc.platform.foundation.api.OrganizationPartnershipApi;
import com.linc.platform.foundation.api.UnitApi;
import com.linc.platform.foundation.model.ItemSpecEntry;
import com.linc.platform.foundation.model.ItemSpecSearchEntry;
import com.linc.platform.foundation.model.ItemUnitSearchEntry;
import com.linc.platform.foundation.model.OrganizationPartnershipSearchEntry;
import com.linc.platform.foundation.model.OrganizationRelationshipTagEntry;
import com.linc.platform.foundation.model.UnitEntry;
import com.linc.platform.foundation.model.organization.common.base.OrganizationViewEntry;
import com.linc.platform.home.more.adjustment.model.AdjustmentCreateEntry;
import com.linc.platform.http.ErrorCodeSubscriber;
import com.linc.platform.http.ErrorResponse;
import com.linc.platform.http.HttpService;
import com.linc.platform.http.IdResponse;
import com.linc.platform.utils.CollectionUtil;
import com.linc.platform.utils.Constant;
import com.linc.platform.utils.RxUtil;
import com.linc.platform.utils.ToastUtil;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import retrofit2.Response;

/**
 * <AUTHOR>
 */

public class LpAdjustmentPresenterImpl extends BaseApiDal implements LpAdjustmentPresenter {
    private LpAdjustmentView lpAdjustmentView;
    private UnitApi unitApi;
    private ItemSpecAPI itemSpecAPI;
    private LocationApi locationApi;
    private AdjustmentApi adjustmentApi;
    private CustomerApi customerApi;
    private OrganizationPartnershipApi organizationPartnershipApi;
    private List<OrganizationViewEntry> mOrganizationViewEntries;

    public LpAdjustmentPresenterImpl(LpAdjustmentView lpAdjustmentView) {
        this.lpAdjustmentView = lpAdjustmentView;
        unitApi = HttpService.createService(UnitApi.class);
        locationApi = HttpService.createService(LocationApi.class);
        itemSpecAPI = HttpService.createService(ItemSpecAPI.class);
        adjustmentApi = HttpService.createService(AdjustmentApi.class);
        customerApi = HttpService.createService(CustomerApi.class);
        organizationPartnershipApi = HttpService.createService(OrganizationPartnershipApi.class);
    }

    @Override
    public void loadItemByCustomer(String customerId) {
        lpAdjustmentView.showProgress(true);
        ItemSpecSearchEntry searchEntry = new ItemSpecSearchEntry();
        searchEntry.customerIds = Collections.singletonList(customerId);
        itemSpecAPI.search(searchEntry)
                .compose(RxUtil.asyncSchedulers())
                .subscribe(new ErrorCodeSubscriber<Response<List<ItemSpecEntry>>>() {
                    @Override
                    public void onSuccess(Response<List<ItemSpecEntry>> listResponse) {
                        lpAdjustmentView.refreshItemSpecEntries(listResponse.body());
                    }

                    @Override
                    public void onFailed(ErrorResponse errorResponse) {
                        ToastUtil.showErrorToast(errorResponse.getErrorMessage());
                    }

                    @Override
                    public void onDone() {
                        lpAdjustmentView.showProgress(false);
                    }
                });
    }

    @Override
    public void loadItemUnit(String itemSpecId) {
        lpAdjustmentView.showProgress(true);

        ItemUnitSearchEntry itemUnitSearchEntry = new ItemUnitSearchEntry();
        itemUnitSearchEntry.itemSpecId = itemSpecId;
        unitApi.search(itemUnitSearchEntry)
                .compose(RxUtil.asyncSchedulers())
                .subscribe(new ErrorCodeSubscriber<Response<List<UnitEntry>>>() {
                    @Override
                    public void onSuccess(Response<List<UnitEntry>> listResponse) {
                        lpAdjustmentView.refreshUnitEntries(listResponse.body());
                    }

                    @Override
                    public void onFailed(ErrorResponse errorResponse) {
                        ToastUtil.showErrorToast(errorResponse.getErrorMessage());
                    }

                    @Override
                    public void onDone() {
                        lpAdjustmentView.showProgress(false);
                    }
                });
    }

    @Override
    public void loadLocationByName(String locationName) {
        lpAdjustmentView.showProgress(true);

        LocationSearchEntry search = new LocationSearchEntry();
        search.type = LocationTypeEntry.LOCATION;
        if (!TextUtils.isEmpty(locationName)) {
            search.name = locationName;
        }

        locationApi.search(search)
                .compose(RxUtil.asyncSchedulers())
                .subscribe(new ErrorCodeSubscriber<Response<List<LocationEntry>>>() {
                    @Override
                    public void onSuccess(Response<List<LocationEntry>> listResponse) {
                        lpAdjustmentView.refreshLocations(listResponse.body());
                    }

                    @Override
                    public void onFailed(ErrorResponse errorResponse) {
                        ToastUtil.showErrorToast(errorResponse.getErrorMessage());
                    }

                    @Override
                    public void onDone() {
                        lpAdjustmentView.showProgress(false);
                    }
                });
    }

    @Override
    public void movement(AdjustmentCreateEntry createEntry) {
        lpAdjustmentView.showProgress(true);
        adjustmentApi.movement(createEntry)
                .compose(RxUtil.asyncSchedulers())
                .subscribe(new ErrorCodeSubscriber<Response<IdResponse>>() {
                    @Override
                    public void onSuccess(Response<IdResponse> listResponse) {
                        lpAdjustmentView.onMovementResult(true, listResponse.body());
                    }

                    @Override
                    public void onFailed(ErrorResponse errorResponse) {
                        ToastUtil.showErrorToast(errorResponse.getErrorMessage());
                    }

                    @Override
                    public void onDone() {
                        lpAdjustmentView.showProgress(false);
                    }
                });
    }

    @Override
    public void create(AdjustmentCreateEntry createEntry) {
        lpAdjustmentView.showProgress(true);
        adjustmentApi.create(createEntry)
                .compose(RxUtil.asyncSchedulers())
                .subscribe(new ErrorCodeSubscriber<Response<IdResponse>>() {
                    @Override
                    public void onSuccess(Response<IdResponse> listResponse) {
                        lpAdjustmentView.onCreateResult(true, listResponse.body());
                    }

                    @Override
                    public void onFailed(ErrorResponse errorResponse) {
                        ToastUtil.showErrorToast(errorResponse.getErrorMessage());
                    }

                    @Override
                    public void onDone() {
                        lpAdjustmentView.showProgress(false);
                    }
                });
    }

    @Override
    public void loadGoodsTypeByCustomer(String customerId) {
        if (TextUtils.isEmpty(customerId)) {
            return;
        }
        asyncExecute(customerApi.getCustomer(customerId), lpAdjustmentView, customerViewEntry -> {
            List<String> goodsTypes = null;
            if (customerViewEntry != null && CollectionUtil.isNotNullOrEmpty(customerViewEntry.allowedReceivingGoodsTypes)) {
                goodsTypes = customerViewEntry.getAllowedReceivingGoodsTypes();
            } else {
                goodsTypes = generalDefaultGoodsType();
            }
            lpAdjustmentView.refreshGoodsType(goodsTypes);
        });
    }

    @Override
    public void loadTitleByCustomer(String customerId) {
        OrganizationPartnershipSearchEntry searchEntry = new OrganizationPartnershipSearchEntry();
        searchEntry.organizationId = TextUtils.isEmpty(customerId) ? null : customerId;
        searchEntry.relationship = OrganizationRelationshipTagEntry.TITLE;
        asyncExecute(organizationPartnershipApi.search(searchEntry), lpAdjustmentView,
                errorResponse -> ToastUtil.showToast(errorResponse.getErrorMessage()),
                organizationViewEntries -> {
                    if (CollectionUtil.isNullOrEmpty(organizationViewEntries)) {
                        loadTitleByCustomer(null);
                    } else {
                        mOrganizationViewEntries = organizationViewEntries;
                        lpAdjustmentView.refreshTitle(organizationViewEntries);
                    }
                });
    }

    @Override
    public List<OrganizationViewEntry> getOrganizationViewEntries() {
        return mOrganizationViewEntries;
    }

    private List<String> generalDefaultGoodsType() {
        List<String> items = new ArrayList<>();
        items.add(Constant.GOODS_TYPE_GOOD);
        items.add(Constant.GOODS_TYPE_DAMAGE);
        items.add(Constant.GOODS_TYPE_NEAR_EXPIRY);
        items.add(Constant.GOODS_TYPE_EXPIRED);
        items.add(Constant.GOODS_TYPE_CONTAIN_DAMAGE);
        items.add(Constant.GOODS_TYPE_ON_HOLD);
        items.add(Constant.GOODS_TYPE_REWORK_NEEDED);
        items.add(Constant.GOODS_TYPE_QC);
        items.add(Constant.GOODS_TYPE_FDA);
        items.add(Constant.GOODS_TYPE_RETURN);
        items.add(Constant.GOODS_TYPE_B_GRADE);
        items.add(Constant.GOODS_TYPE_C_GRADE);
        return items;
    }
}
