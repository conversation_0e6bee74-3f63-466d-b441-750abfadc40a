package com.linc.platform.home.more.wifilocating.api;

import android.net.wifi.ScanResult;

import com.linc.platform.R;
import com.linc.platform.common.apidal.BaseApiDal;
import com.linc.platform.common.handler.SuccessHandler;
import com.linc.platform.home.more.wifilocating.model.LocatingAP;
import com.linc.platform.home.more.wifilocating.model.PositionWifi;
import com.linc.platform.home.more.wifilocating.model.PositioningChannel;
import com.linc.platform.home.more.wifilocating.model.WifiInfo;
import com.linc.platform.utils.Lists;
import com.linc.platform.utils.ResUtil;
import com.linc.platform.utils.ToastUtil;

import java.util.List;

/**
 * Created by Gavin
 */
public class WifiLocatingDal extends BaseApiDal {
    private WifiLocatingAPI wifiLocatingAPI;

    public WifiLocatingDal() {
        wifiLocatingAPI = api(WifiLocatingAPI.class);
    }

    public void create(List<ScanResult> results, String location) {
        PositionWifi positionWifi = new PositionWifi();
        positionWifi.location = location;
        positionWifi.wifiInfo = buildWifiInfo(results);
        executeWithoutShowProgress(wifiLocatingAPI.create(positionWifi), aVoid -> ToastUtil.showToast(ResUtil.getString(R.string.msg_success)));
    }

    public void create(List<ScanResult> results, String location, PositioningChannel channel) {
        PositionWifi positionWifi = new PositionWifi();
        positionWifi.location = location;
        positionWifi.wifiInfo = buildWifiInfo(results);
        positionWifi.channel = channel;
        executeWithoutShowProgress(wifiLocatingAPI.create(positionWifi), aVoid -> {
        });
    }

    public void position(List<ScanResult> results) {
        PositionWifi positionWifi = new PositionWifi();
        positionWifi.wifiInfo = buildWifiInfo(results);

        executeWithoutShowProgress(wifiLocatingAPI.position(positionWifi), aVoid -> {});
    }

    public void searchLocatingAP(SuccessHandler<List<LocatingAP>> successHandler) {
        execute(wifiLocatingAPI.searchLocatingAP(), successHandler);
    }

    public void updateLocatingAP(List<LocatingAP> locatingAPs) {
        execute(wifiLocatingAPI.createLocatingAP(locatingAPs), aVoid -> {

        });
    }

    public static List<WifiInfo> buildWifiInfo(List<ScanResult> wifiInfo) {
        List<WifiInfo> infos = Lists.newArrayList();
        for (ScanResult result : wifiInfo) {
            WifiInfo info = new WifiInfo();
            info.bssId = result.BSSID;
            info.level = String.valueOf(result.level);
            infos.add(info);
        }
        return infos;
    }
}
