package com.linc.platform.home.more.inventorycount.cyclecount.view;

import com.linc.platform.core.ProgressView;
import com.linc.platform.foundation.model.CustomerViewEntry;
import com.linc.platform.foundation.model.ItemSpecEntry;
import com.linc.platform.home.more.inventorycount.cyclecount.model.NewItemInfo;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface NewItemView extends ProgressView {
    void showScanItemView(boolean show);

    void initNewItemView(CustomerViewEntry customerEntry);

    void onSearchItemFailed(String error);

    void onMultipleNewItemFound(List<ItemSpecEntry> itemSpecEntries);

    void onItemSearchSuccess(ItemSpecEntry itemSpecEntry);

    void onItemCustomerNotMatch();

    void onNewItemFailed(String error);

    void onNewItemSuccess(NewItemInfo itemInfo);
}
