package com.linc.platform.home.more.inventorycount.cyclecount.model;

import android.text.TextUtils;

import com.annimon.stream.Collectors;
import com.annimon.stream.Stream;
import com.linc.platform.baseapp.model.LocationEntry;
import com.linc.platform.common.task.TaskTypeEntry;
import com.linc.platform.foundation.model.CustomerEntry;
import com.linc.platform.foundation.model.ItemRequiredDataTypeEntry;
import com.linc.platform.foundation.model.ItemSpecEntry;
import com.linc.platform.utils.CollectionUtil;
import com.linc.platform.utils.Lists;
import com.linc.platform.utils.LocationUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class CountInfo {
    public CustomerEntry targetCustomer;
    public LPInfo lpInfo;
    public ItemInfo itemInfo;
    public LocationInfo locationInfo;
    public TaskTypeEntry taskTypeEntry;

    public CountInfo() {
        lpInfo = new LPInfo();
        itemInfo = new ItemInfo();
        locationInfo = new LocationInfo();
    }

    public void setTaskTypeEntry(TaskTypeEntry taskTypeEntry) {
        this.taskTypeEntry = taskTypeEntry;
    }

    public void updateItemInfo(NewItemInfo newItemInfo) {
        itemInfo.updateByNewItem(newItemInfo);
    }

    public CustomerEntry getTargetCustomer() {
        return targetCustomer;
    }

    public void setTargetCustomer(CustomerEntry targetCustomer) {
        this.targetCustomer = targetCustomer;
    }

    public LocationEntry getLocation() {
        return locationInfo.getLocation();
    }

    public void setLocation(LocationEntry location) {
        locationInfo.setLocation(location);
    }

    public boolean isCountFromPickLocation() {
        return LocationUtil.isPickLocation(locationInfo.getLocation());
    }

    public boolean isLPItemUnfinished() {
        return Stream.of(lpInfo.getProcessItems()).filter(v -> (!v.isCounted && !v.isItemDone) && v.customerId.equals(getTargetCustomer().id)).count() > 0;
    }

    public boolean isLocationLPUnfinished() {
        return Stream.of(Lists.ensureNotNull(locationInfo.getProcessItems())).filter(v -> (!v.isCounted && !v.isItemDone) && v.customerId.equals(getTargetCustomer().id)).count() > 0;
    }

    public boolean isLocationCounted() {
        return Stream.of(Lists.ensureNotNull(locationInfo.getProcessItems())).filter(v -> !v.isCounted && v.customerId.equals(getTargetCustomer().id)).count() <= 0;
    }

    public void updateLPProcesses() {
        if (TextUtils.isEmpty(lpInfo.getLpId())) return;
        lpInfo.setProcessItems(Stream.of(locationInfo.getProcessItems()).filter(v -> v.lpId.equals(lpInfo.getLpId())).collect(Collectors.toList()));
    }

    public void updateItemProcesses() {
        if (TextUtils.isEmpty(itemInfo.getItemSpecId())) return;
        itemInfo.setProcessItems(Stream.of(lpInfo.getProcessItems()).filter(v -> v.itemSpecId.equals(itemInfo.getItemSpecId())).collect(Collectors.toList()));
    }

    public void updateLocationProcesses(List<CycleCountProcessItem> processItems, List<CountEntryViewEntry> countHistories) {
        markCountedInventories(processItems, countHistories);
        locationInfo.setProcessItems(processItems);

        if (LocationUtil.isPickLocation(locationInfo.getLocation())) {
            lpInfo.setLpId(locationInfo.getLocation().hlpId);
            lpInfo.setProcessItems(processItems);
        }

    }

    public void updateToItemDone(String itemSpecId) {
        if (!TextUtils.isEmpty(lpInfo.getLpId())) {
            lpInfo.updateToItemDone(itemSpecId);
        }
        if (!TextUtils.isEmpty(itemInfo.getItemSpecId())) {
            locationInfo.updateToItemDone(lpInfo.getLpId(), itemSpecId);
        }
    }

    private boolean isRequireCollectLotNo(ItemSpecEntry itemSpecEntry, boolean isRecount) {
        if (isRecount) {
            return itemSpecEntry != null && itemSpecEntry.requireCollectLotNoOnReceive;
        } else {
            return targetCustomer != null
                    && itemSpecEntry != null
                    && !(CollectionUtil.isNotNullOrEmpty(targetCustomer.ignoreCollectForCycleCount) && targetCustomer.ignoreCollectForCycleCount.contains(ItemRequiredDataTypeEntry.LOT_NO))
                    && itemSpecEntry.requireCollectLotNoOnReceive;
        }
    }

    private void markCountedInventories(List<CycleCountProcessItem> processItems, List<CountEntryViewEntry> countHistories) {
        if (CollectionUtil.isNullOrEmpty(processItems)) {
            return;
        }
        if (CollectionUtil.isNullOrEmpty(countHistories)) {
            for (CycleCountProcessItem processItem : processItems) {
                processItem.isCounted = false;
            }
            return;
        }
        boolean isRecount = taskTypeEntry == TaskTypeEntry.RECOUNT;

        List<String> keywords = new ArrayList<>();
        for (CountEntryViewEntry countHistory : countHistories) {
            keywords.add(countHistory.locationId + countHistory.lpId + countHistory.itemSpecId
                    + (isRequireCollectLotNo(countHistory.item, isRecount) ? countHistory.lotNo : ""));
        }

        if (isRecount) {
            for (CycleCountProcessItem processItem : processItems) {
                String processItemKey = processItem.location.id + processItem.lpId + processItem.itemSpecId;
                List<String> lotNosCountHistory = Stream.ofNullable(countHistories).filter(countHistory -> processItemKey.equals(countHistory.locationId + countHistory.lpId + countHistory.itemSpecId)).map(countHistory -> countHistory.lotNo).toList();
                if (isRequireCollectLotNo(processItem.itemSpecEntry, isRecount) && !CollectionUtil.isNullOrEmpty(lotNosCountHistory)) {
                    Stream.ofNullable(processItem.recountLotNos).forEach(recountLotNo -> recountLotNo.isCounted = lotNosCountHistory.contains(recountLotNo.lotNo));
                    processItem.isCounted = Stream.ofNullable(processItem.recountLotNos).allMatch(recountLotNo -> recountLotNo.isCounted);
                } else {
                    processItem.isCounted = keywords.contains(processItem.location.id + processItem.lpId + processItem.itemSpecId);
                }
            }
        } else {
            for (CycleCountProcessItem processItem : processItems) {
                processItem.isCounted = keywords.contains(processItem.location.id + processItem.lpId + processItem.itemSpecId
                        + (isRequireCollectLotNo(processItem.itemSpecEntry, isRecount) ? processItem.lotNo : ""));
            }
        }

         for (CountEntryViewEntry countHistory : countHistories) {
            if (countHistory.isNewItem &&
                    locationInfo.getLocation() != null
                    && locationInfo.getLocation().id.equals(countHistory.locationId)
                    && !hasContainCountHistoryForNewItem(processItems, countHistory, isRecount)) {
                if (isRecount && isRequireCollectLotNo(countHistory.item, isRecount)) {
                    List<CycleCountProcessItem> cycleCountProcessItems = Stream.ofNullable(processItems).filter(processItem -> {
                        String keywordProcessItem = processItem.location.id + processItem.lpId + processItem.itemSpecId;
                        String keywordCountHistory = countHistory.locationId + countHistory.lpId + countHistory.itemSpecId;
                        return keywordProcessItem.equals(keywordCountHistory);
                    }).toList();
                    if (CollectionUtil.isNullOrEmpty(cycleCountProcessItems)) {
                        return;
                    }
                    List<RecountLotNoEntry> recountLotNos = cycleCountProcessItems.get(0).recountLotNos;
                    if (recountLotNos == null) {
                        recountLotNos = new ArrayList<>();
                    }
                    recountLotNos.add(new RecountLotNoEntry(countHistory.lotNo, true));
                } else {
                    CycleCountProcessItem processItem = new CycleCountProcessItem();
                    processItem.unitId = countHistory.unitId;
                    processItem.itemSpecId = countHistory.itemSpecId;
                    processItem.itemSpecName = countHistory.item.name;
                    processItem.itemSpecDesc = countHistory.item.desc;
                    processItem.itemSpecEntry = countHistory.item;
                    processItem.lpId = countHistory.lpId;
                    processItem.locationId = countHistory.locationId;
                    processItem.location = countHistory.location;
                    processItem.qty = countHistory.qty;
                    processItem.customerId = getTargetCustomer().id;
                    processItem.customerView = getTargetCustomer();
                    processItem.isCounted = true;
                    processItem.isNewItem = true;
                    processItem.lotNo = countHistory.lotNo;
                    processItems.add(processItem);
                }
            }
        }
    }

    private boolean hasContainCountHistoryForNewItem(List<CycleCountProcessItem> processItems, CountEntryViewEntry countHistory, boolean isRecount) {
        List<CycleCountProcessItem> newItemProcessItems;
        if (isRecount) {
            newItemProcessItems = processItems;
        } else {
            newItemProcessItems = Stream.ofNullable(processItems).filter(processItem -> processItem.isNewItem).toList();
        }
        for (CycleCountProcessItem processItem : newItemProcessItems) {
            if (isRecount && isRequireCollectLotNo(processItem.itemSpecEntry, isRecount)) {
                String keywordProcessItem = processItem.location.id + processItem.lpId + processItem.itemSpecId;
                String keywordCountHistory = countHistory.locationId + countHistory.lpId + countHistory.itemSpecId;
                if (keywordProcessItem.equals(keywordCountHistory) && Stream.ofNullable(processItem.recountLotNos).anyMatch(recountLotNo -> recountLotNo.lotNo.equals(countHistory.lotNo))) {
                    return true;
                }
            } else {
                String keywordProcessItem = processItem.location.id + processItem.lpId + processItem.itemSpecId
                        + (isRequireCollectLotNo(processItem.itemSpecEntry, isRecount) ? processItem.lotNo : "");
                String keywordCountHistory = countHistory.locationId + countHistory.lpId + countHistory.itemSpecId
                        + (isRequireCollectLotNo(countHistory.item, isRecount) ? countHistory.lotNo : "");
                if (keywordProcessItem.equals(keywordCountHistory)) {
                    return true;
                }
            }
        }
        return false;
    }

    public List<CycleCountProcessItem> getLPUnCountProcessItems(String itemSpecId) {
        List<CycleCountProcessItem> processItems = getLPMatchedProcessItems(itemSpecId);
        return Stream.of(processItems).filter(v -> !v.isCounted).collect(Collectors.toList());
    }

    public List<CycleCountProcessItem> getLPMatchedProcessItems(String itemSpecId) {
        List<CycleCountProcessItem> processItems = lpInfo.getProcessItems();
        return Stream.of(processItems)
                .filter(v -> v.itemSpecId.equals(itemSpecId))
                .collect(Collectors.toList());
    }

}
