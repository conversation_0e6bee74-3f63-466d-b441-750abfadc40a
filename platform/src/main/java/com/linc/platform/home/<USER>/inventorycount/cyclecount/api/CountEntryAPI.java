package com.linc.platform.home.more.inventorycount.cyclecount.api;

import com.linc.platform.home.more.inventorycount.cyclecount.model.CountEntryCreateEntry;
import com.linc.platform.home.more.inventorycount.cyclecount.model.CountEntrySearchEntry;
import com.linc.platform.home.more.inventorycount.cyclecount.model.CountEntryViewEntry;
import com.linc.platform.home.more.inventorycount.cyclecount.model.InventorySimpleViewSearchEntry;
import com.linc.platform.inventory.model.InventorySimpleViewEntry;

import java.util.List;

import retrofit2.Response;
import retrofit2.http.Body;
import retrofit2.http.DELETE;
import retrofit2.http.POST;
import retrofit2.http.Path;
import rx.Observable;

/**
 * <AUTHOR>
 */
public interface CountEntryAPI {
    @POST("bam/wms-app/cycle-count/entry/search")
    Observable<Response<List<CountEntryViewEntry>>> search(@Body CountEntrySearchEntry search);

    @POST("wms-app/cycle-count/entry")
    Observable<Response<Void>> create(@Body CountEntryCreateEntry create);

    @POST("wms-app/inventory/location-item-lp-count")
    Observable<Response<InventorySimpleViewEntry>> getLocationItemLpCount(@Body InventorySimpleViewSearchEntry search);

    @POST("wms-app/cycle-count/delete-by-entryIds")
    Observable<Response<Void>> deleteCountEntryRecord(@Body List<Long> entryIds);

    @DELETE("wms-app/cycle-count/{taskId}/delete/{lpId}")
    Observable<Response<Void>> deleteCountEntryRecordByLpId(@Path("taskId") String taskId, @Path("lpId") String lpId);

}
