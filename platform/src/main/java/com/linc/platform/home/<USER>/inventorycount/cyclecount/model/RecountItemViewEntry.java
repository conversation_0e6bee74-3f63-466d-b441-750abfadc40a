package com.linc.platform.home.more.inventorycount.cyclecount.model;

import com.google.gson.annotations.SerializedName;
import com.linc.platform.baseapp.model.LocationEntry;
import com.linc.platform.foundation.model.ItemSpecEntry;
import com.linc.platform.foundation.model.UnitEntry;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class RecountItemViewEntry implements Serializable {
    @SerializedName("locationId")
    public String locationId;

    @SerializedName("location")
    public LocationEntry location;

    @SerializedName("itemSpecId")
    public String itemSpecId;

    @SerializedName("item")
    public ItemSpecEntry item;

    @SerializedName("unitId")
    public String unitId;

    @SerializedName("unit")
    public UnitEntry unit;

    @SerializedName("lpId")
    public String lpId;

    @SerializedName("lastCountQty")
    public Double lastCountQty;
}
