package com.linc.platform.packtopallet.presenter;

import com.linc.platform.baseapp.presenter.IBasePresenter;
import com.linc.platform.common.help.FunctionHelpPresenter;
import com.linc.platform.common.step.StepBaseEntry;
import com.linc.platform.idm.model.UserViewEntry;
import com.linc.platform.packtopallet.model.PackToPalletType;
import com.linc.platform.packtopallet.model.ParcelPackStepUpdateEntry;
import com.linc.platform.packtopallet.view.ParcelPackDataView;
import com.linc.platform.packtopallet.view.ParcelPackMainView;
import com.linc.platform.packtopallet.view.ParcelPackWorkView;

/**
 * Created by devinc on 2018/3/9.
 */

public interface ParcelPackPresenter extends IBasePresenter, FunctionHelpPresenter {
    void setTaskView(ParcelPackMainView mainView);

    void setDataView(ParcelPackDataView dataView);

    void setWorkView(ParcelPackWorkView workView);

    StepBaseEntry getStepEntry();

    void takeOverTask(UserViewEntry userViewEntry);

    void startStep();

    void closeStep();

    void packTrackingNo(String trackingNo, ParcelPackStepUpdateEntry updateEntry);

    void unpackTrackingNo(String trackingNo);

    void searchTrackingNoInfo(String trackingNo);

    void loadStepView();

    PackToPalletType getPackType();

    void doPack(String packData);

    void doUnPack(String packData);

    void initTaskWorkTime();

    void startTaskWorkTime();

    void resumeTaskWorkTime();

    void pauseTaskWorkTime();
}
