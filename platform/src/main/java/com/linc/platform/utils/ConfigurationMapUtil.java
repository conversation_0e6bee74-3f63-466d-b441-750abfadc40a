package com.linc.platform.utils;

import android.text.TextUtils;

import com.annimon.stream.Stream;
import com.linc.platform.foundation.model.ConfigurationMapViewEntry;
import com.linc.platform.receive.model.ReceiptEntry;

import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 */
public class ConfigurationMapUtil {
    //Collect COO
    public static final String TABLE_COLLECT_COO = "RequireCollectCOOOnReceive";
    private static final String KEY_COLLECT_COO = "HDR-dynTxtPropertyValue01";

    //Inventory Issue
    public static final String TABLE_INVENTORY_ISSUE_CODE = "InventoryIssueCode";

    //Hospital Result
    public static final String HOSPITAL_TASK_CONFIG = "HospitalTaskConfig";

    //SN Validation
    public static final String TABLE_SN_VALIDATION = "SNRule";
    public static final String KEY_SN_VALIDATION = "format";

    // HelpManagementPage
    public static final String TABLE_HELP_MANAGEMENT_PAGE = "AndroidHelpManagementPage";

    // Pick Task Config
    public static final String TABLE_PICK_TASK_CONFIG = "PickTaskConfig";
    public static final String KEY_BATCH_ORDER_PICK_ENABLE_COMBINE_ORDERS = "BatchOrderPickEnableCombineOrders";

    // GenerateUniqueProNOToOrder
    public static final String TABLE_GENERATE_UNIQUE_PRO_NO_TO_ORDER = "GenerateUniqueProNOToOrder";
    public static final String KEY_GENERATE_UNIQUE_PRO_NO_RETAILER = "Retailer";

    //Customized SN Scan Logic
    public static final String TABLE_INTERCEPT_SN_BY_CHARACTER = "InterceptSNByCharacter";
    public static final String KEY_SN_CHARACTER_SUFFIX = "delSuffix";
    public static final String KEY_SN_CHARACTER_PREFIX = "delPrefix";
    public static final String KEY_SN_CHARACTER_CHARACTER = "delCharacter";

    //Bluetooth Print Shipping Label On Submit Interval Time
    public static final String TABLE_BLUETOOTH_PRINT_SHIPPING_LABEL_ON_SUBMIT_INTERVAL_TIME = "BluetoothPrintShippingLabelOnSubmitIntervalTime";
    public static final String KEY_BLUETOOTH_PRINT_INTERVAL_TIME = "printInterval";

    //RFID
    public static final String TABLE_RFID_CONFIG = "RFIDConfiguration";
    public static final String KEY_RFID_COUNT = "COUNT";
    public static final String KEY_RFID_RSSI = "RSSI";
    public static final String KEY_RFID_TASK = "TASK";
    public static final String KEY_RFID_EQUIPMENT = "EQUIPMENT";
    public static final String TABLE_DISABLE_RFID_CONFIG = "DisableRFIDCollect";
    public static final String KEY_DISABLE_RFID_FACILITY = "FACILITY";
    public static final String KEY_DISABLE_RFID_TASK = "TASK";

    // <--inventory-movement start-->
    public static final String PAGE_KEY_INVENTORY_MOVEMENT_TASK = "android-inventory-movement-task"; //Inventory Movement Task
    public static final String PAGE_KEY_MOVEMENT_START_TASK = "android-movement-start-task";//Inventory Movement Task > Start Task
    public static final String PAGE_KEY_MOVEMENT_SCAN_EQUIPMENT = "android-movement-scan-equipment";//Inventory Movement Task > Scan Equipment
    public static final String PAGE_KEY_MOVEMENT_COLLECT_SCAN_LOCATION_LP = "android-movement-collect-scan-location/lp";//Inventory Movement Task > Collect Scan Location/lp
    public static final String PAGE_KEY_MOVEMENT_COLLECT_SCAN_ITEM = "android-movement-collect-scan-item";//Inventory Movement Task > Collect Scan Item
    public static final String PAGE_KEY_MOVEMENT_COLLECT_SUBMIT = "android-movement-collect-submit";//Inventory Movement Task > Collect Submit
    public static final String PAGE_KEY_MOVEMENT_DROP_SCAN_LP_ITEM = "android-movement-drop-scan-lp/item";//Inventory Movement Task > Drop Scan Lp/Item
    public static final String PAGE_KEY_MOVEMENT_DROP_SCAN_LOCATION = "android-movement-drop-scan-location";//Inventory Movement Task > Drop Scan Location
    public static final String PAGE_KEY_MOVEMENT_DROP_SUBMIT = "android-movement-drop-submit";//Inventory Movement Task > Drop Submit
    public static final String PAGE_KEY_MOVEMENT_BATCH = "android-movement-batch";//Inventory Movement Task > Movement Batch
    // <--inventory-movement end-->

    // <--receive start-->
    public static final String PAGE_KEY_RECEIVE_TASK = "android-receive-task";// Receive Task
    public static final String PAGE_KEY_RECEIVE_DOCK_RECEIVE_PHOTO = "android-receive-dock-receive-photo";// Receive Task > Dock Receive Photo
    public static final String PAGE_KEY_RECEIVE_OFFLOAD_STEP = "android-receive-offload-step";// Receive Task > Offload Step
    public static final String PAGE_KEY_RECEIVE_LP_SETUP_STEP = "android-receive-lp-setup-step";// Receive Task > LP Setup Step
    public static final String PAGE_KEY_RECEIVE_LP_VERIFY_STEP = "android-receive-lp-verify-step";// Receive Task > LP Verify Step
    public static final String PAGE_KEY_RECEIVE_SN_SCAN_STEP = "android-receive-sn-scan-step";// Receive Task > SN Scan Step
    public static final String PAGE_KEY_RECEIVE_PALLET_COUNT_STEP = "android-receive-pallet-count-step";// Receive Task > Pallet Count Step //TODO 8688待定
    public static final String PAGE_KEY_RECEIVE_LP_SETUP_STEP_SINGLE_ITEM = "android-receive-lp-setup-step-single-item";// Receive Task > LP Setup Step > Single Item
    public static final String PAGE_KEY_RECEIVE_LP_SETUP_STEP_MIX_ITEM = "android-receive-lp-setup-step-mix-item";// Receive Task > LP Setup Step > Mix Item
    public static final String PAGE_KEY_RECEIVE_LP_SETUP_STEP_RECEIVE_TO_PICK_LOCATION = "android-receive-lp-setup-step-receive-to-pick-location";// Receive Task > LP Setup Step > Receive To Pick Location
    public static final String PAGE_KEY_RECEIVE_LP_SETUP_STEP_ITEM_LINE = "android-receive-lp-setup-step-item-line";// Receive Task > LP Setup Step > Item Line
    public static final String PAGE_KEY_RECEIVE_LP_SETUP_STEP_MIX_ITEM_SELECTED_ITEM_LIST = "android-receive-lp-setup-step-mix-item-selected-item-list";// Receive Task > LP Setup Step > Mix Item >Selected Item List
    public static final String PAGE_KEY_RECEIVE_SN_SCAN_STEP_SCAN_ITEM = "android-receive-sn-scan-step-scan-item";// Receive Task > SN Scan Step > Scan Item
    public static final String PAGE_KEY_RECEIVE_LP_VERIFY_STEP_LP_SETUP_EDIT = "android-receive-lp-verify-step-lp-setup-edit";// Receive Task > LP Verify Step > LP Setup Edit
    // <--receive end-->

    //<--material center start-->
    public static final String PAGE_KEY_MATERIAL_CENTER = "android-material-center";// Material Center
    public static final String PAGE_KEY_MATERIAL_CENTER_SELECT_RECEIPT = "android-material-center-select-receipt";// Material Center > Select Receipt
    public static final String PAGE_KEY_MATERIAL_CENTER_SELECT_MATERIAL = "android-material-center-select-material";// Material Center > Select Material
    public static final String PAGE_KEY_MATERIAL_CENTER_MATERIAL_MANAGER = "android-material-center-material-manager";// Material Center > Material Manager
    //<--material center end-->

    //<--Lp template start-->
    public static final String PAGE_KEY_LP_TEMPLATE = "android-lp-template";// Lp Template
    public static final String PAGE_KEY_LP_TEMPLATE_NEW_ITEM_LP_TEMPLATE = "android-lp-template-new-item-lp-template";// Lp Template > New Item Lp Template
    public static final String PAGE_KEY_LP_TEMPLATE_LP_TEMPLATE_MANAGER = "android-lp-template-lp-template-manager";// Lp Template > Lp Template Manager
    public static final String PAGE_KEY_LP_TEMPLATE_LP_TEMPLATE_MANAGER_LP_TEMPLATE_SEARCH = "android-lp-template-lp-template-manager-lp-template-search";// Lp Template > Lp Template Manager > Lp Template Search
    public static final String PAGE_KEY_LP_TEMPLATE_LP_TEMPLATE_MANAGER_NEW_UPDATE_LP_TEMPLATE = "android-lp-template-lp-template-manager-new/update-lp-template";// Lp Template > Lp Template Manager >New/Update Lp Template
    public static final String PAGE_KEY_LP_TEMPLATE_MULTIPLE_LP_TEMPLATE = "android-lp-template-multiple-lp-template";// Lp Template > Multiple Lp Template
    //<--Lp template end-->

    //<--common start-->
    public static final String PAGE_KEY_SELECT_CUSTOMER = "android-select-customer";// Select Customer
    public static final String PAGE_KEY_DOCK_CHECKIN_SCAN = "android-dock-checkin-scan";// Dock Checkin Scan
    public static final String PAGE_KEY_LP_Detail = "android-lp-detail";// Lp Detail
    public static final String PAGE_KEY_MBOL_LIST = "android-mbol-list";// Mbol list
    public static final String PAGE_KEY_MBOL_LIST_LOAD_SIGNATURE = "android-mbol-list-load-signature";// Mbol list > Load Signature
    public static final String PAGE_KEY_ADD_PRO_NO = "android-add-pro-no";// Add Pro No
    public static final String PAGE_KEY_ADD_SEAL = "android-add-seal";// Add Seal
    public static final String PAGE_KEY_PRINT_ITEM_LABEL = "android-print-item-label";// Print Item Label
    public static final String PAGE_KEY_PRINT_PALLET_LABEL = "android-print-pallet-label";// Print Pallet Label
    public static final String PAGE_KEY_REPRINT_SOID = "android-print-reprint-soid";// Reprint Soid
    public static final String PAGE_KEY_GET_SUGGESTION = "android-get-suggestion";// Get Suggestion
    public static final String PAGE_KEY_ITEM_SELECTOR = "android-item-selector";// Item Selector
    public static final String PAGE_KEY_LABEL_BATCH_PRINT = "android-label-batch-print";// Label Batch Print
    public static final String PAGE_KEY_LP_PRINT = "android-label-lp-print";// LP Print
    public static final String PAGE_KEY_PACKAGE_TYPE = "android-package-type";// Package Type
    //<--common end-->

    //<--put away task start-->
    public static final String PAGE_KEY_PUT_AWAY_TASK = "android-put-away-task";// Put Away Task
    public static final String PAGE_KEY_PUT_AWAY_TASK_PUT_AWAY_STEP_BY_ITEM_SCAN_LP = "android-put-away-task-put-away-step-by-item-scan-lp";// Put Away Task > Put Away Step > By Item > Scan Lp
    public static final String PAGE_KEY_PUT_AWAY_TASK_PUT_AWAY_STEP_BY_ITEM_SCAN_ITEM = "android-put-away-task-put-away-step-by-item-scan-item";// Put Away Task > Put Away Step > By Item > Scan Item
    public static final String PAGE_KEY_PUT_AWAY_TASK_PUT_AWAY_STEP_BY_ITEM_SUBMIT = "android-put-away-task-put-away-step-by-item-submit";// Put Away Task > Put Away Step > By Item > Submit
    public static final String PAGE_KEY_PUT_AWAY_TASK_PUT_AWAY_PROGRESS = "android-put-away-task-put-away-progress";// Put Away Task > Put Away Progress
    public static final String PAGE_KEY_PUT_AWAY_TASK_START_STEP = "android-put-away-task-start-step";// Put Away Task > Start Step
    public static final String PAGE_KEY_PUT_AWAY_TASK_PUT_AWAY_STEP_BY_LP = "android-put-away-task-put-away-step-by-lp";// Put Away Task > Put Away Step > By Lp
    public static final String PAGE_KEY_PUT_AWAY_TASK_PUT_AWAY_MUL_ITEM_PROGRESS = "android-put-away-task-put-away-mul-item-progress";// Put Away Task > Put Away Mul Item Progress
    public static final String PAGE_KEY_PUT_AWAY_TASK_DIRECT_PUT_AWAY_SCAN_LOCATION = "android-put-away-task-direct-put-away-scan-location";// Put Away Task > Direct Put Away > Scan Location
    public static final String PAGE_KEY_PUT_AWAY_TASK_DIRECT_PUT_AWAY_SELECT_LP = "android-put-away-task-direct-put-away-select-lp";// Put Away Task > Direct Put Away > Select Lp
    public static final String PAGE_KEY_PUT_AWAY_TASK_DIRECT_PUT_AWAY_BATCH_LP = "android-put-away-task-direct-put-away-batch-lp";// Put Away Task > Direct Put Away > Batch Lp
    public static final String PAGE_KEY_PUT_AWAY_TASK_DIRECT_PUT_AWAY_SPLIT_LP = "android-put-away-task-direct-put-away-split-lp";// Put Away Task > Direct Put Away > Split Lp
    public static final String PAGE_KEY_PUT_AWAY_TASK_DIRECT_PUT_AWAY_PUT_AWAY_LP = "android-put-away-task-direct-put-away-put-away-lp";// Put Away Task > Direct Put Away > Put Away Lp
    //<--put away task end-->

    //<--load task start-->
    public static final String PAGE_KEY_GENERAL_LOAD_TASK = "android-general-load-task";//General Load Task
    public static final String PAGE_KEY_GENERAL_LOAD_TASK_LOAD_STEP_LOAD_DETAIL = "android-general-load-task-load-step-load-detail";//General Load Task > Load Step > Load Detail
    public static final String PAGE_KEY_GENERAL_LOAD_TASK_COUNT_UNSHIPPED_STEP_LOAD_DETAIL_COUNT_UNSHIPPED_LP = "android-general-load-task-count-unshipped-step-load-detail-count-unshipped-lp";//General Load Task > Count Unshipped Step > Load Detail > Count Unshipped Lp
    public static final String PAGE_KEY_GENERAL_LOAD_TASK_LOAD_DETAIL_ADD_SEAL = "android-general-load-task-load-detail-add-seal";//General Load Task > Load Detail > Add Seal
    public static final String PAGE_KEY_GENERAL_LOAD_TASK_LOAD_DETAIL_ADD_CTNR_TRAILER_NO = "android-general-load-task-load-detail-add-ctnr-trailer-no";//General Load Task > Load Detail > Add Ctnr Trailer No
    public static final String PAGE_KEY_GENERAL_LOAD_TASK_LOAD_DETAIL_COUNTING_SHEET = "android-general-load-task-load-detail-counting-sheet";//General Load Task > Load Detail > Counting Sheet
    public static final String PAGE_KEY_GENERAL_LOAD_TASK_LOAD_DETAIL_LOAD_LIST = "android-general-load-task-load-detail-load-list";//General Load Task > Load Detail > Load List
    public static final String PAGE_KEY_GENERAL_LOAD_TASK_LOAD_DETAIL_LOAD_LIST_LOAD = "android-general-load-task-load-detail-load-list-load";//General Load Task > Load Detail > Load List > Load
    //<--load task end-->

    //<--put back task start-->
    public static final String PAGE_KEY_PUT_BACK_TASK = "android-put-back-task";// Put Back Task
    public static final String PAGE_KEY_PUT_BACK_TASK_NEW_TASK = "android-put-back-task-new-task";// Put Back Task > New Task
    public static final String PAGE_KEY_PUT_BACK_TASK_EMPTY_PUT_BACK = "android-put-back-task-empty-put-back";// Put Back Task > Empty Put Back
    public static final String PAGE_KEY_PUT_BACK_TASK_EMPTY_PUT_BACK_PUT_BACK_PROCESS = "android-put-back-task-empty-put-back-put-back-process";// Put Back Task > Empty Put Back > Put Back Process
    public static final String PAGE_KEY_PUT_BACK_TASK_PUT_BACK_LIST = "android-put-back-task-put-back-list";// Put Back Task > Put Back List
    public static final String PAGE_KEY_PUT_BACK_TASK_PUT_BACK_LIST_PUT_BACK_DETAIL = "android-put-back-task-put-back-list-put-back-detail";// Put Back Task > Put Back List > Put Back Detail
    //<--put back task end-->
    //TODO 8688以上key重新命名：task+页面功能
    //<--pick task start-->
    public static final String PAGE_KEY_PICK_TASK = "android-pick-task";//Pick Task
    public static final String PAGE_KEY_PICK_TASK_RETURN_TO_INVENTORY = "android-pick-task-return-to-inventory";//Pick Task > Return To Inventory
    public static final String PAGE_KEY_PICK_TASK_ORDER_GROUP = "android-pick-task-order-group";//Pick Task > Order Group
    public static final String PAGE_KEY_PICK_TASK_BIND_ORDER_WITH_LP = "android-pick-task-bind-order-with-lp";//Pick Task > Bind Order With LP
    public static final String PAGE_KEY_PICK_TASK_ORDER_ALLOCATE_VIEW = "android-pick-task-order-allocate-view";//Pick Task > Order Allocate View

    public static final String PAGE_KEY_PICK_TASK_SCAN_LOCATION_TO_PICK = "android-pick-task-scan-location-to-pick";//Pick Task > Pick Step > Scan Location To Pick
    public static final String PAGE_KEY_PICK_TASK_SCAN_LP_TO_PICK = "android-pick-task-scan-lp-to-pick";//Pick Task > Pick Step > Scan Lp To Pick
    public static final String PAGE_KEY_PICK_TASK_PICK_PARTIA_LP_VIEW = "android-pick-task-pick-partia-lp-view";//Pick Task > Pick Step > Pick Partia LP View
    public static final String PAGE_KEY_PICK_TASK_STAGE_VIEW = "android-pick-task-pick-stage-view";//Pick Task > Stage Step > Stage View

    public static final String PAGE_KEY_PICK_TASK_NEW_PICK_WORK_DETAIL = "android-pick-task-new-pick-work-detail";//Pick Task(New) > Pick Step > Pick Work Detail
    public static final String PAGE_KEY_PICK_TASK_NEW_PICK_PROGRESS_VIEW = "android-pick-task-pick-new-progress-view";//Pick Task(New) > Pick Step > Pick Progress View

    public static final String PAGE_KEY_PICK_TASK_V1_PICK_WORK_DETAIL = "android-pick-task-v1-pick-work-detail";//Pick Task(v1) > Pick Step > Pick Work Detail
    public static final String PAGE_KEY_PICK_TASK_V1_PICK_PROGRESS_VIEW = "android-pick-task-v1-pick-progress-view";//Pick Task(V1) > Pick Step > Pick Progress View

    public static final String PAGE_KEY_PICK_TASK_MAKE_PALLET_PICK_WORK_DETAIL = "android-pick-task-make-pallet-pick-work-detail";//Pick Task(Make Pallet) > Pick Step > Pick Work Detail
    public static final String PAGE_KEY_PICK_TASK_MAKE_PALLET_PICK_PROGRESS_VIEW = "android-pick-task-make-pallet-pick-progress-view";//Pick Task(Make Pallet) > Pick Step > Pick Progress View

    public static final String PAGE_KEY_PICK_TASK_ALLOCATE = "android-pick-task-allocate";// Pick Task > Allocate
    public static final String PAGE_KEY_PICK_TASK_ALLOCATE_PROGRESS = "android-pick-task-allocate-progress";// Pick Task > Allocate Progress
    //<--pick task end-->

    //<--transload operation start-->
    public static final String PAGE_KEY_TRANSLOAD_OPERATION_SPLIT = "android-transload-operation-split";//Transload Operation > Split
    public static final String PAGE_KEY_TRANSLOAD_OPERATION_MERGE = "android-transload-operation-merge";//Transload Operation > Merge
    public static final String PAGE_KEY_TRANSLOAD_OPERATION_CHANGE_DESTINATION = "android-transload-operation-change-destination";//Transload Operation Split > Change Transload Pallet Destination
    //<--transload operation end-->

    //<--transload receive task start-->
    public static final String PAGE_KEY_TRANSLOAD_RECEIVE_TASK = "android-transload-receive-task";// Transload Receive Task
    public static final String PAGE_KEY_TRANSLOAD_RECEIVE_TASK_TRANSLOAD_RECEIVE_STEP = "android-transload-receive-task-transload-receive-step";// Transload Receive Task > Transload Receive Step
    public static final String PAGE_KEY_TRANSLOAD_RECEIVE_TASK_NEW_PALLET = "android-transload-receive-task-new-pallet";// Transload Receive Task > Receiving with New Pallet
    public static final String PAGE_KEY_TRANSLOAD_RECEIVE_TASK_EXISTING_PALLET = "android-transload-receive-task-existing-pallet";// Transload Receive Task > Receiving with Existing Pallet
    public static final String PAGE_KEY_TRANSLOAD_RECEIVE_TASK_BY_CARTON = "android-transload-receive-task-by-carton";// Transload Receive Task > Receiving with New Pallet By Carton
    public static final String PAGE_KEY_TRANSLOAD_RECEIVE_TASK_ADD_SEALPAGE_KEY_TRANSLOAD_RECEIVE_TASK_EXCEPTION_REPORT = "android-transload-receive-task-exception-report";// Transload Receive Task > Transload Receive Step > Exception Report
    //<--transload receive task end-->

    //<--transload load task start-->
    public static final String PAGE_KEY_TRANSLOAD_LOAD_TASK = "android-transload-load-task";// Transload Load Task
    public static final String PAGE_KEY_TRANSLOAD_LOAD_TASK_LOAD_DETAIL = "android-transload-load-task-load-detail";// Transload Load Task > Load Detail
    public static final String PAGE_KEY_TRANSLOAD_LOAD_TASK_ORDER_DETAIL = "android-transload-load-task-order-detail";// Transload Load Task > Load Detail > Order Detail
    //<--transload load task end-->

    //Lot No MFG Date Conversion
    public static final String TABLE_LOT_NO_MFG_DATE_CONVERSION = "Pepsi Lot No MFG Date Conversion";
    public static final String KEY_POPULATE_MFG_DATE_BY_LOT = "PopulateMFGDatebyLot";


    //<--cycle count task start-->
    public static final String PAGE_KEY_CYCLE_COUNT_TASK = "android-cycle-count-task";// Cycle Count Task
    public static final String PAGE_KEY_CYCLE_COUNT_TASK_SCAN_LOCATION = "android-cycle-count-task-scan_location";// Cycle Count Task > Scan Location
    public static final String PAGE_KEY_CYCLE_COUNT_TASK_SCAN_LP = "android-cycle-count-task-scan_lp";// Cycle Count Task > Scan LP
    public static final String PAGE_KEY_CYCLE_COUNT_TASK_SCAN_ITEM = "android-cycle-count-task-scan_item";// Cycle Count Task > Scan Item
    public static final String PAGE_KEY_CYCLE_COUNT_TASK_COUNT_QTY = "android-cycle-count-task-count_qty";// Cycle Count Task > Count Qty
    public static final String PAGE_KEY_CYCLE_COUNT_TASK_COUNT_HISTORY = "android-cycle-count-task-count_history";// Cycle Count Task > Count History
    public static final String PAGE_KEY_CYCLE_COUNT_TASK_NEW_ITEM = "android-cycle-count-task-new-item";// Cycle Count Task > New Item
    public static final String PAGE_KEY_CYCLE_COUNT_TASK_COUNT_LP_QTY = "android-cycle-count-task-count-lp-qty";// Cycle Count Task > Count Lp Qty
    //<--cycle count task end-->

    //<--recount task start-->
    public static final String PAGE_KEY_RECOUNT_TASK = "android-recount-task";// Recount Task
    public static final String PAGE_KEY_RECOUNT_TASK_SCAN_LOCATION = "android-recount-task-scan_location";// Recount Task > Scan Location
    public static final String PAGE_KEY_RECOUNT_TASK_SCAN_LP = "android-recount-task-scan_lp";// Recount Task > Scan LP
    public static final String PAGE_KEY_RECOUNT_TASK_SCAN_ITEM = "android-recount-task-scan_item";// Recount Task > Scan Item
    public static final String PAGE_KEY_RECOUNT_TASK_COUNT_QTY = "android-recount-task-count_qty";// Recount Task > Count Qty
    public static final String PAGE_KEY_RECOUNT_TASK_COUNT_HISTORY = "android-recount-task-count_history";// Recount Task > Count History
    public static final String PAGE_KEY_RECOUNT_TASK_NEW_ITEM = "android-recount-task-new-item";// Recount Task > New Item
    public static final String PAGE_KEY_RECOUNT_TASK_COUNT_LP_QTY = "android-recount-task-count-lp-qty";// Recount Task > Count Lp Qty
    //<--recount task end-->

    //<--pack task start-->
    public static final String PAGE_KEY_PACK_TASK = "android-pack-task";// Pack Task
    public static final String PAGE_KEY_PACK_TASK_RE_PALLETIZE = "android-pack-task-re-palletize";// Pack Task > RePalletize
    public static final String PAGE_KEY_PACK_TASK_WORK = "android-pack-task-work";// Pack Task > Work
    public static final String PAGE_KEY_PACK_TASK_WORK_ADD_ITEM_OR_LP = "android-pack-task-work-add-item-or-lp";// Pack Task > Work > Add Item Or Lp
    public static final String PAGE_KEY_PACK_TASK_ASSIGN_SLP_LOCATION = "android-pack-task-assign-slp-location";// Pack Task > Assign SLP Location
    public static final String PAGE_KEY_PACK_TASK_PACK_ALL_LP = "android-pack-task-pack-all-lp";// Pack Task > Pack All LP
    //<--pack task end-->

    //<--parcel pack task start-->
    public static final String PAGE_KEY_PARCEL_PACK_TASK = "android-parcel-pack-task";// Parcel Pack Task
    public static final String PAGE_KEY_PARCEL_PACK_TASK_WORK = "android-parcel-pack-task-work";// Parcel Pack Task > Work
    public static final String PAGE_KEY_PARCEL_PACK_TASK_LP_DETAIL = "android-parcel-pack-task-lp-detail";// Parcel Pack Task > Lp Detail
    //<--parcel pack task end-->

    //<--parcel load task start-->
    public static final String PAGE_KEY_PARCEL_LOAD_TASK = "android-parcel-load-task";// Parcel Load Task
    public static final String PAGE_KEY_PARCEL_LOAD_TASK_CREATE = "android-parcel-load-task-create";// Parcel Load Task > Create
    public static final String PAGE_KEY_PARCEL_LOAD_TASK_WORK = "android-parcel-load-task-work";// Parcel Load Task > Work
    //<--parcel load task end-->

    //<--configuration change task start-->
    public static final String PAGE_KEY_CONFIGURATION_CHANGE_TASK = "android-configuration-change-task";// Configuration Change Task
    public static final String PAGE_KEY_CONFIGURATION_CHANGE_TASK_SN_LIST = "android-configuration-change-task-sn-list";// Configuration Change Task > Sn List
    public static final String PAGE_KEY_CONFIGURATION_CHANGE_TASK_WORK = "android-configuration-change-task-work";// Configuration Change Task > Work
    public static final String PAGE_KEY_CONFIGURATION_CHANGE_TASK_WORK_TO_POOL = "android-configuration-change-task-work-to-pool";// Configuration Change Task > Work > To Pool(collect)
    public static final String PAGE_KEY_CONFIGURATION_CHANGE_TASK_WORK_TO_LP = "android-configuration-change-task-work-to-lp";// Configuration Change Task > Work > To Lp(build)
    public static final String PAGE_KEY_CONFIGURATION_CHANGE_TASK_WORK_TRADITIONAL_TO_POOL = "android-configuration-change-task-work-traditional-to-pool";// Configuration Change Task > Work > Traditional To Pool
    public static final String PAGE_KEY_CONFIGURATION_CHANGE_TASK_WORK_TRADITIONAL_TO_LP = "android-configuration-change-task-work-traditional-to-lp";// Configuration Change Task > Work > Traditional To Lp
    //<--configuration change task end-->

    //<--general task start-->
    public static final String PAGE_KEY_GENERAL_TASK = "android-general-task";// General Task
    public static final String PAGE_KEY_GENERAL_TASK_CREATE = "android-general-task-create";// General Task > Create
    public static final String PAGE_KEY_GENERAL_TASK_WORK = "android-general-task-work";// General Task > Work
    public static final String PAGE_KEY_GENERAL_TASK_WORK_VIEW_ALL = "android-general-task-work-view-all";// General Task > Work > View All
    //<--general task end-->

    //<--replenishment task start-->
    public static final String PAGE_KEY_REPLENISHMENT_TASK = "android-replenishment-task";// Replenishment Task
    public static final String PAGE_KEY_REPLENISHMENT_TASK_CREATE = "android-replenishment-task-create";// Replenishment Task > Create
    public static final String PAGE_KEY_REPLENISHMENT_TASK_BIND_EQUIPMENT = "android-replenishment-task-bind-equipment";// Replenishment Task > Bind Equipment
    public static final String PAGE_KEY_REPLENISHMENT_TASK_COLLECT_FROM = "android-replenishment-task-collect-from";// Replenishment Task > Collect From
    public static final String PAGE_KEY_REPLENISHMENT_TASK_COLLECT_ITEM = "android-replenishment-task-collect-item";// Replenishment Task > Collect Item
    public static final String PAGE_KEY_REPLENISHMENT_TASK_COLLECT_SUBMIT = "android-replenishment-task-collect-submit";// Replenishment Task > Collect Submit
    public static final String PAGE_KEY_REPLENISHMENT_TASK_DROP_FROM = "android-replenishment-task-drop-from";// Replenishment Task > Drop From
    public static final String PAGE_KEY_REPLENISHMENT_TASK_DROP_TO = "android-replenishment-task-drop-to";// Replenishment Task > Drop To
    public static final String PAGE_KEY_REPLENISHMENT_TASK_DROP_SUBMIT = "android-replenishment-task-drop-submit";// Replenishment Task > Drop Submit
    public static final String PAGE_KEY_REPLENISHMENT_TASK_CLOSE_TASK = "android-replenishment-task-close-task";// Replenishment Task > Close Task
    //<--replenishment task end-->

    //<--transload task start-->
    public static final String PAGE_KEY_TRANSLOAD_TASK = "android-transload-task";// Transload Task
    public static final String PAGE_KEY_TRANSLOAD_TASK_LPN = "android-transload-task-lpn";// Transload Task > Lpn(setup)
    public static final String PAGE_KEY_TRANSLOAD_TASK_LPN_DELETE = "android-transload-task-lpn-delete";// Transload Task > Lpn(setup) > Delete
    public static final String PAGE_KEY_TRANSLOAD_TASK_LPN_NEW_ITEM = "android-transload-task-lpn-new-item";// Transload Task > Lpn(setup) > New Item
    public static final String PAGE_KEY_TRANSLOAD_TASK_LPN_CREATE = "android-transload-task-lpn-create";// Transload Task > Lpn(setup) > Create
    public static final String PAGE_KEY_TRANSLOAD_TASK_PHOTO = "android-transload-task-photo";// Transload Task > Photo
    public static final String PAGE_KEY_TRANSLOAD_TASK_PUT_AWAY = "android-transload-task-put-away";// Transload Task > Put Away
    public static final String PAGE_KEY_TRANSLOAD_TASK_TRAILER_CHECKIN = "android-transload-task-trailer-checkin";// Transload Task > Trailer Checkin
    public static final String PAGE_KEY_TRANSLOAD_TASK_NEW_ENTRY = "android-transload-task-new-entry";// Transload Task > New Entry
    public static final String PAGE_KEY_TRANSLOAD_TASK_SELECT_ENTRY = "android-transload-task-select-entry";// Transload Task > Select Entry
    public static final String PAGE_KEY_TRANSLOAD_TASK_OFFLOAD = "android-transload-task-offload";// Transload Task > Offload
    public static final String PAGE_KEY_TRANSLOAD_TASK_RECEIVING = "android-transload-task-receiving";// Transload Task > Receiving
    public static final String PAGE_KEY_TRANSLOAD_TASK_RECEIVING_SCAN = "android-transload-task-receiving-scan";// Transload Task > Receiving > Scan
    public static final String PAGE_KEY_TRANSLOAD_TASK_RECEIVING_UNRECEIVING = "android-transload-task-receiving-unreceiving";// Transload Task > Receiving > Unreceiving
    public static final String PAGE_KEY_TRANSLOAD_TASK_PREVIEW = "android-transload-task-preview";// Transload Task > Preview
    public static final String PAGE_KEY_TRANSLOAD_TASK_SHIPPING = "android-transload-task-shipping";// Transload Task > Shipping
    public static final String PAGE_KEY_TRANSLOAD_TASK_SHIPPING_SCAN = "android-transload-task-shipping-scan";// Transload Task > Shipping > Scan
    public static final String PAGE_KEY_TRANSLOAD_TASK_SHIPPING_CARTON = "android-transload-task-shipping-carton";// Transload Task > Shipping > Carton(load/unload)
    public static final String PAGE_KEY_TRANSLOAD_TASK_SHIPPING_ADD_PRO_NO = "android-transload-task-shipping-add-pro-no";// Transload Task > Shipping > Add Pro No
    public static final String PAGE_KEY_TRANSLOAD_TASK_SCAN_SN = "android-transload-task-scan-sn";// Transload Task > Scan Sn
    //<--transload task end-->

    //<--qc task start-->
    public static final String PAGE_KEY_QC_TASK = "android-qc-task";// Qc Task
    public static final String PAGE_KEY_QC_TASK_TRACKING_NO_QC = "android-qc-task-tracking-no-qc";// Qc Task > Tracking No Qc
    public static final String PAGE_KEY_QC_TASK_REGULAR_QC = "android-qc-task-regular-qc";// Qc Task > Regularqc(longhaul)
    public static final String PAGE_KEY_QC_TASK_REGULAR_QC_SEARCH = "android-qc-task-regular-qc-search";// Qc Task > Regularqc(longhaul) > Search
    public static final String PAGE_KEY_QC_TASK_REGULAR_QC_DETAIL = "android-qc-task-regular-qc-detail";// Qc Task > Regularqc(longhaul) > Detail
    // <--qc task end-->

    //<--inventory count task start-->
    public static final String PAGE_KEY_INVENTORY_COUNT_TASK = "android-inventory-count-task";// Inventory Count Task
    public static final String PAGE_KEY_INVENTORY_COUNT_TASK_NEW_PALLET = "android-inventory-count-task-new-pallet";// Inventory Count Task > New Pallet
    //<--inventory count task end-->

    //<--inventory consolidation task start-->
    public static final String PAGE_KEY_INVENTORY_CONSOLIDATION_TASK = "android-inventory-consolidation-task";// Inventory Consolidation Task
    public static final String PAGE_KEY_INVENTORY_CONSOLIDATION_TASK_SCAN_INVENTORY = "android-inventory-consolidation-task-scan-inventory";// Inventory Consolidation Task > Scan Inventory
    public static final String PAGE_KEY_INVENTORY_CONSOLIDATION_TASK_OPERATE = "android-inventory-consolidation-task-operate";// Inventory Consolidation Task > Operate
    public static final String PAGE_KEY_INVENTORY_CONSOLIDATION_TASK_SUBMIT = "android-inventory-consolidation-task-submit";// Inventory Consolidation Task > Submit
    public static final String PAGE_KEY_INVENTORY_CONSOLIDATION_TASK_RESULT = "android-inventory-consolidation-task-result";// Inventory Consolidation Task > Result
    public static final String PAGE_KEY_INVENTORY_CONSOLIDATION_TASK_LIST = "android-inventory-consolidation-task-list";// Inventory Consolidation Task > List
    //<--inventory consolidation task end-->

    //<--transfer in task start-->
    public static final String PAGE_KEY_TRANSFER_IN_TASK = "android-transfer-in-task";// Transfer In Task
    public static final String PAGE_KEY_TRANSFER_IN_TASK_SCAN_LOCATION = "android-transfer-in-task-scan-location";// Transfer In Task > Scan Location
    public static final String PAGE_KEY_TRANSFER_IN_TASK_SCAN_TASK = "android-transfer-in-task-scan-task";// Transfer In Task > Scan Task
    public static final String PAGE_KEY_TRANSFER_IN_TASK_SCAN_LP = "android-transfer-in-task-scan-lp";// Transfer In Task > Scan Lp
    //<--transfer in task end-->

    //<--transfer out task start-->
    public static final String PAGE_KEY_TRANSFER_OUT_TASK = "android-transfer-out-task";// Transfer Out Task
    public static final String PAGE_KEY_TRANSFER_OUT_TASK_HISTORY = "android-transfer-out-task-history";// Transfer Out Task > History
    //<--transfer out task end-->

    //Filippo-PecoPallet
    public static final String TABLE_SUGGEST_PLT_TYPE_ON_LOAD_AND_ROUTE_856 = "SuggestPltTypeOnLoadAndRouteBy856";
    public static final String KEY_APPLY_TO_ALL_RETAILERS = "applyToAllRetailers";
    public static final String KEY_RETAILER_PALLET_TYPES = "retailerPalletTypes";


    public static boolean requireCollectCOOOnReceive(List<ConfigurationMapViewEntry> configs, ReceiptEntry receipt) {
        if (receipt == null || receipt.dynamicFields == null || TextUtils.isEmpty(receipt.dynamicFields.dynTxtPropertyValue01) || CollectionUtil.isNullOrEmpty(configs)) {
            return false;
        }

        List<ConfigurationMapViewEntry> customerConfigs = Stream.of(configs).filter(v -> v.customerId.equals(receipt.customerId)).toList();

        return Stream.of(Lists.ensureNotNull(customerConfigs)).anyMatch(v -> v.valueMapping.containsKey(KEY_COLLECT_COO) && v.valueMapping.get(KEY_COLLECT_COO).contains(receipt.dynamicFields.dynTxtPropertyValue01));
    }

    public static boolean validateSN(List<ConfigurationMapViewEntry> configs, String sn) {
        if (StringUtil.isEmpty(sn)) return false;

        if (configs == null || configs.isEmpty()) return true;

        Map<String, String> regexMap = configs.get(0).valueMapping;
        if (regexMap == null || !regexMap.containsKey(KEY_SN_VALIDATION)) return true;

        return sn.matches(regexMap.get(KEY_SN_VALIDATION));
    }

    public static boolean generateUniqueProNOToOrder(List<ConfigurationMapViewEntry> configs, String retailerName) {
        if (StringUtil.isEmpty(retailerName)) return false;
        if (configs == null || configs.isEmpty()) return false;
        return Stream.of(configs).anyMatch(config -> retailerName.equalsIgnoreCase(config.valueMapping.get(KEY_GENERATE_UNIQUE_PRO_NO_RETAILER)));
    }

    public static boolean enableCombineOrdersForBatchOrderPick(List<ConfigurationMapViewEntry> configs) {
        if (CollectionUtil.isNullOrEmpty(configs)) return false;
        Map<String, String> map = configs.get(0).valueMapping;
        if (map == null || !map.containsKey(KEY_BATCH_ORDER_PICK_ENABLE_COMBINE_ORDERS)) {
            return false;
        }
        return  "true".equals(map.get(KEY_BATCH_ORDER_PICK_ENABLE_COMBINE_ORDERS));
    }
}
