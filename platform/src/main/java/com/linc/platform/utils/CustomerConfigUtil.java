package com.linc.platform.utils;

import android.text.TextUtils;

import com.annimon.stream.Stream;
import com.linc.platform.common.customer.ManualInputRestrictionConfig;
import com.linc.platform.common.customer.ManualInputStepType;
import com.linc.platform.common.customer.ManualInputTaskType;
import com.linc.platform.common.customer.PermissionTag;
import com.linc.platform.core.PermissionManager;
import com.linc.platform.foundation.model.CustomerViewEntry;
import com.linc.platform.foundation.model.ListOperatorEntry;
import com.linc.platform.foundation.model.OrderTypeEntry;
import com.linc.platform.foundation.model.ReceiveMethodEntry;
import com.linc.platform.foundation.model.RequireProNoWhenCloseOrder;
import com.linc.platform.idm.model.PermissionEntry;
import com.linc.platform.pick.model.PickWayEntry;
import com.linc.platform.pick.model.ordergroup.OrderEntry;
import com.linc.platform.putaway.work.directputaway.PutAwayVersionEntry;

import java.util.List;

/**
 * <AUTHOR>
 */
public class CustomerConfigUtil {
    public static boolean isDirectedPutAway(CustomerViewEntry customer) {
        return customer != null && customer.putAwayVersion == PutAwayVersionEntry.DIRECTED_PUTAWAY;
    }

    public static boolean isPutAwayV2Version(CustomerViewEntry customer) {
        return customer != null && customer.putAwayVersion == PutAwayVersionEntry.V2;
    }

    public static boolean allowPickAutoSubmit(CustomerViewEntry customer) {
        return customer != null && customer.allowPickAutoSubmit;
    }

    public static boolean isNeedPrintSoId(CustomerViewEntry customer, OrderEntry order) {
        return order != null && customer != null && customer.requirePrintSOIDSubOrderTypes.contains(order.subOrderType);
    }

    public static boolean isNeedCollectSoId(CustomerViewEntry customer, OrderEntry order) {
        return order != null && customer != null && customer.requireCollectSOIDSubOrderTypes.contains(order.subOrderType);
    }

    public static boolean allowReceiveToStorageLocation(CustomerViewEntry customer) {
        return customer != null && customer.receiveMethods.contains(ReceiveMethodEntry.RECEIVE_TO_PICK_LOCATION);
    }

    public static boolean allowManualEntry(CustomerViewEntry customer) {
        return customer == null || !customer.interfaceManualEntryRestriction || PermissionManager.getInstance().hasPermission(PermissionEntry.User_ManualEntryUnrestricted);
    }

    public static boolean shouldRestrictInput(PermissionTag permissionTag) {
        if (CustomerConfigUtil.allowManualEntry(permissionTag.customer)) {
            // If User has 'ANDROID_INTERFACE_MANUAL_ENTRY_UNRESTRICTED' permission or interfaceManualEntryRestriction is false
            return false;
        }

        Stream<ManualInputRestrictionConfig> configStream = Stream.of(permissionTag.customer.manualInputRestrictionConfigs)
                .filter(config -> config.task != null && config.task.equals(permissionTag.task)
                        && config.step != null && config.step.equals(permissionTag.step));

        List<ManualInputRestrictionConfig> configList = configStream.toList();

        // None match task and step
        if (configList.isEmpty()) {
            return false;
        }

        // For Pick task & Picking step, read the same scenario if present
        if (permissionTag.task == ManualInputTaskType.PICK && permissionTag.step == ManualInputStepType.Pick_Picking
                && permissionTag.scenario != null) {
            Stream<ManualInputRestrictionConfig> pickScenarioStream = Stream.of(configList).filter(config -> permissionTag.scenario == config.scenario);
            List<ManualInputRestrictionConfig> pickScenarioList = pickScenarioStream.toList();
            if (!pickScenarioList.isEmpty()) {
                return pickScenarioList.get(0).operations.contains(permissionTag.operation);
            }
        }

        return Stream.of(configList).anyMatch(config -> config.operations.contains(permissionTag.operation));
    }

    public static boolean needCompatibleWithThirdPartyLP(CustomerViewEntry customer) {
        return customer != null && customer.needCompatibleWithThirdPartyLP;
    }

    public static boolean needReuseOtherFacilityLP(CustomerViewEntry customer) {
        return customer != null && customer.needReuseOtherFacilityLP;
    }

    public static boolean isNotAllowShortReceive(CustomerViewEntry customer) {
        return customer == null || !customer.allowShortReceive;
    }

    public static boolean requirePermission(CustomerViewEntry customer) {
        return customer != null && customer.requirePermissionCheckOnReceiveForceClose;
    }

    public static boolean requireVerification(CustomerViewEntry customer) {
        return customer != null && customer.requireVerificationOnReceiveTaskForceClose;
    }

    public static boolean needCheckStageToConveyorLine(CustomerViewEntry customer) {
        return customer != null && customer.needCheckStageToConveyorLine;
    }

    public static boolean isUseCustomerBarcode(CustomerViewEntry customer) {
        return customer != null && customer.useCustomerBarcode && !TextUtils.isEmpty(customer.customerBarcodeSeparator) && CollectionUtil.isNotNullOrEmpty(customer.customerBarcodeFields);
    }

    public static boolean allowPrintMultipleLPsForOrderWhenPicking(CustomerViewEntry customer) {
        return customer != null && customer.allowPrintMultipleLPsForOrderWhenPicking;
    }

    public static boolean allowRequestPutAway(CustomerViewEntry customer) {
        return customer != null && customer.allowRequestPutAway;
    }

    public static boolean displaySuggestSnOnPick(CustomerViewEntry customer) {
        return customer != null && customer.displaySuggestSnOnPick;
    }

    public static boolean requireProNo(CustomerViewEntry customerViewEntry, String retailerId) {
        RequireProNoWhenCloseOrder requireProNo;
        if (customerViewEntry == null || customerViewEntry.requireProNoWhenCloseOrder == null || TextUtils.isEmpty(retailerId)) {
            return false;
        }

        requireProNo = customerViewEntry.requireProNoWhenCloseOrder;
        return requireProNo.applyToAllRetailers || (ListOperatorEntry.IN.equals(requireProNo.operator) && requireProNo.retailerIds.contains(retailerId))
                || (ListOperatorEntry.NOT_IN.equals(requireProNo.operator) && !requireProNo.retailerIds.contains(retailerId));
    }

    public static boolean isPickToTracking(OrderTypeEntry orderType, CustomerViewEntry customerViewEntry, PickWayEntry pickWayEntry) {
        if (customerViewEntry == null || !customerViewEntry.pickToTrackingNo || CollectionUtil.isNullOrEmpty(customerViewEntry.pickToTrackingNoPickWays)) {
            return false;
        }
        if (null == orderType) return false;
        if (pickWayEntry == PickWayEntry.WAVE_PICK || pickWayEntry == PickWayEntry.WAVE_PICK_BY_ORDER) {
            pickWayEntry = PickWayEntry.WAVE_PICK_BY_ITEM;
        }
        PickWayEntry pickWay = pickWayEntry;
        return Stream.of(customerViewEntry.pickToTrackingNoPickWays).anyMatch(it -> it == pickWay) && orderType == OrderTypeEntry.DS;
    }

    public static boolean isTurtleBeach(CustomerViewEntry customerViewEntry) {
        return customerViewEntry != null && customerViewEntry.orgId.equals("ORG-43765");
    }
}
