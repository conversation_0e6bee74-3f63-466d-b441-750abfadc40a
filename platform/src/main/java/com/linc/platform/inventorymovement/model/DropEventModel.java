package com.linc.platform.inventorymovement.model;

import com.linc.platform.baseapp.model.LocationEntry;
import com.linc.platform.inventory.model.InventoryDetailEntry;
import com.linc.platform.inventory.model.InventoryEntry;

import java.io.Serializable;


/**
 * describe:
 * author: <PERSON>
 * createDate: 2020/10/15 16:54
 */
public class DropEventModel implements Serializable {
    public InventoryDetailEntry mInventoryDetailEntry;
    public InventoryMovementTaskEntry mInventoryMovementTaskEntry;
    public InventoryEntry mInventoryEntry;
    public String itemSpecId;
    public LocationEntry mLocationEntry;
    public String mDropItemOrLP;
    public boolean hasLoadedDropFromPage;
}
