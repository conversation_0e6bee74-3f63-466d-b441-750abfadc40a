package com.linc.platform.inventorymovement.view;

import com.linc.platform.core.ProgressView;
import com.linc.platform.inventorymovement.model.InventoryMovementTaskEntry;

/**
 * @Description:
 * @Author: Leo
 * @CreateDate: 2020/9/8 16:36
 */
public interface InventoryEquipmentView extends ProgressView {

    void equipmentNoFound();

    void updateTaskSuccess(InventoryMovementTaskEntry inventoryMovementTaskEntry);

}
