package com.linc.platform.inventorymovement.presenter;

import com.linc.platform.baseapp.model.LocationEntry;
import com.linc.platform.common.help.FunctionHelpPresenter;
import com.linc.platform.cyclecount.model.OnScreenCountResponseEntry;
import com.linc.platform.foundation.model.LpEntry;
import com.linc.platform.inventory.model.InventoryEntry;
import com.linc.platform.inventorymovement.model.DropParameter;
import com.linc.platform.inventorymovement.model.InventorySubmitModel;

import java.util.List;
import java.util.Map;

/**
 * @Description:
 * @Author: Owen
 * @CreateDate: 2020/9/14 17:48
 */
public interface InventoryDropSubmitPresenter extends FunctionHelpPresenter {

    boolean hasSerialNumber();

    void loadDropItem();

    InventoryEntry getInventoryEntry();

    LocationEntry getLocationEntry();

    void selectEntire();

    void cancelEntire();

    DropParameter getDropParameter();

    List<String> getLotNumberList();

    Map<String, LpEntry> getLpDetailMap();

    int getItemQTY();

    void searchScanSN(String scanValue);

    void searchLp(String scanValue);

    void dropSubmit(InventorySubmitModel inventorySubmitModel, boolean isCheckShippingRuleTime);

    void onStart();

    void checkOnScreenOpCount(LocationEntry locationEntry);

    void onScreenCountMatch(OnScreenCountResponseEntry onScreenCountResponseEntry);

    void onScreenCountNotMatch(OnScreenCountResponseEntry onScreenCountResponseEntry);

    boolean hasLoadedDropFromPage();
}
