package com.linc.platform.pack.view;

import com.linc.platform.foundation.model.OrderEntry;
import com.linc.platform.pack.model.ItemViewEntry;
import com.linc.platform.pack.model.LPViewEntry;
import com.linc.platform.pack.model.SLPViewEntry;

import java.util.List;

/**
 * Created by devinc on 2017/5/31.
 */

public interface PackDataView {
    void showProgress(boolean show);

    void setItems(List<ItemViewEntry> itemEntryList);

    void setFromLps(List<LPViewEntry> list);

    void setSlpItems(List<SLPViewEntry> slpViewEntries);

    void setOrders(List<OrderEntry> orderEntryList);
}
