package com.linc.platform.pack.view;

import com.linc.platform.pack.model.KittingComponentEntry;
import com.linc.platform.pack.model.KittingItemEntry;

/**
 * Created by devinc on 2016/11/3.
 */

public interface OperateWorkView {
    int calculateProgress(Double done, Double total);

    void onClickItem(KittingComponentEntry entry);

    void onFail(String message);

    void onPackingSuccess(KittingItemEntry itemEntry);
}
