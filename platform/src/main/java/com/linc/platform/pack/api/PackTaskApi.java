package com.linc.platform.pack.api;

import com.linc.platform.foundation.model.OrderEntry;
import com.linc.platform.http.IdResponse;
import com.linc.platform.load.model.FileEntry;
import com.linc.platform.pack.model.LPIdsRequest;
import com.linc.platform.pack.model.LpTemplateCheckResultEntry;
import com.linc.platform.pack.model.OrderIdsRequest;
import com.linc.platform.pack.model.PackAllPickedLpEntry;
import com.linc.platform.pack.model.PackItemRequestEntry;
import com.linc.platform.pack.model.PackItemSearchResultEntry;
import com.linc.platform.pack.model.PackSlpSearchResultEntry;
import com.linc.platform.pack.model.PackStepConsolidateLPIdsRequest;
import com.linc.platform.pack.model.PackTaskCreate;
import com.linc.platform.pack.model.PackTaskEntry;
import com.linc.platform.pack.model.PackTaskSearchEntry;
import com.linc.platform.pack.model.PickedLpSearchResultEntry;
import com.linc.platform.pack.model.RepalletizeOriginalLPDetail;
import com.linc.platform.pack.model.RepalletizeStageLPDetail;
import com.linc.platform.pack.model.RepalletizeTransferLpEntry;
import com.linc.platform.pack.model.UnpackItemRequestEntry;
import com.linc.platform.pack.model.ValidateMultipleShipToResultEntry;
import com.linc.platform.pick.model.LPBatchUpdateEntry;
import com.linc.platform.replenishment.model.TaskForceCloseEntry;

import java.util.List;

import retrofit2.Response;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.PUT;
import retrofit2.http.Path;
import rx.Observable;

/**
 * Created by devinc on 2017/6/12.
 */

public interface PackTaskApi {
    @PUT("wms-app/outbound/pack/step/{stepId}/lp/{lpId}/binding-item")
    Observable<Response<Void>> bindItemToLp(@Path("stepId") String stepId,
                                            @Path("lpId") String lpId, @Body PackItemRequestEntry packItem);

    @PUT("wms-app/outbound/pack/step/{stepId}/lp/{lpId}/consolidate/{fromLPId}")
    Observable<Response<Void>> bindLp(@Path("stepId") String stepId, @Path("lpId") String lpId,
                                      @Path("fromLPId") String fromLPId);

    @PUT("wms-app/outbound/pack/task/{taskId}/start")
    Observable<Response<Void>> startTask(@Path("taskId") String taskId);

    @PUT("wms-app/outbound/pack/task/{taskId}/complete")
    Observable<Response<Void>> completeTask(@Path("taskId") String taskId);

    @POST("bam/outbound/pack/step/pack-item/search")
    Observable<Response<PackItemSearchResultEntry>> searchItem(@Body PackTaskSearchEntry entry);

    @POST("bam/outbound/pack/step/lp-suggest")
    Observable<Response<PickedLpSearchResultEntry>> searchSuggestLp(@Body PackTaskSearchEntry entry);

    @POST("bam/outbound/pack/pack-task/{taskId}/packed-lp/search")
    Observable<Response<PackSlpSearchResultEntry>> searchSlp(@Path("taskId") String taskId);

    @PUT("wms-app/lp/batch")
    Observable<Response<Void>> updateLpBatch(@Body LPBatchUpdateEntry entry);

    @PUT("wms-app/outbound/pack/step/{stepId}/remove-lp/{lpId}/{toLpId}")
    Observable<Response<Void>> removePackedLps(@Path("stepId") String stepId, @Path("lpId") String fromLp,
                                               @Path("toLpId") String toLpId);

    @GET("bam/outbound/pack/task/lp/{slpId}/lp-view")
    Observable<Response<PackSlpSearchResultEntry>> loadSLpDetail(@Path("slpId") String slp);

    @PUT("wms-app/outbound/pack/step/{stepId}/lp/{lpId}/unbinding-item")
    Observable<Response<Void>> unbindingItemFromLp(@Path("stepId") String stepId, @Path("lpId") String slpId,
                                                   @Body UnpackItemRequestEntry unpackItem);

    @PUT("wms-app/outbound/pack/step/{stepId}/reopen")
    Observable<Response<Void>> reopenTask(@Path("stepId") String stepId);


    @PUT("wms-app/outbound/pack/step/{stepId}/lp/{lpId}/add-inner-lp/{innerLpId}")
    Observable<Response<Void>> addInnerLp(@Path("stepId") String stepId, @Path("lpId") String lpId,
                                          @Path("innerLpId") String innerLpId);

    @PUT("wms-app/outbound/pack/step/{stepId}/lp/{lpId}/move-inner-lp/{innerLpId}")
    Observable<Response<Void>> removeInnerLp(@Path("stepId") String stepId, @Path("lpId") String lpId,
                                             @Path("innerLpId") String innerLpId);

    @GET("wms-app/outbound/order/{orderId}/packing-list/print")
    Observable<Response<FileEntry>> createPackingListFileId(@Path("orderId") String orderId);

    @GET("bam/outbound/pack/task/{taskId}/order-view")
    Observable<Response<List<OrderEntry>>> searchOrder(@Path("taskId") String taskId);

    @GET("bam/lp/{lpId}/validate-package-type")
    Observable<Response<Void>> getPackagingType(@Path("lpId") String lpId);

    @PUT("wms-app/outbound/pack/step/{stepId}/close")
    Observable<Response<Void>> closeStep(@Path("stepId") String stepId);

    @PUT("wms-app/outbound/pack/step/{stepId}/force-close")
    Observable<Response<Void>> forceCloseStep(@Path("stepId") String stepId,
                                              @Body TaskForceCloseEntry closeEntry);

    @PUT("wms-app/outbound/pack/step/{stepId}/lp/{lpId}/batch-add-inner-lp")
    Observable<Response<Void>> batchAddInnerLp(@Path("stepId") String stepId, @Path("lpId") String lpId,
                                               @Body PackAllPickedLpEntry entry);

    @GET("bam/order/{orderId}/picked-inventory/lp-template/check")
    Observable<Response<LpTemplateCheckResultEntry>> orderLpTemplateCheck(@Path("orderId") String orderId);

    @POST("bam/order/picked-inventory/lp-template/check")
    Observable<Response<LpTemplateCheckResultEntry>> orderLpTemplateCheck(@Body OrderIdsRequest orderIds);

    @POST("bam/configuration-change/auto-cc/{orderId}")
    Observable<Response<Void>> autoCc(@Path("orderId") String orderId);

    //re-palletize
    @POST("bam/re-palletize/pack/task/create")
    Observable<Response<IdResponse>> createRepalletizePackTask(@Body PackTaskCreate create);

    @POST("bam/re-palletize/original/lp/detail")
    Observable<Response<List<RepalletizeOriginalLPDetail>>> getRepalletizeOriginalLPDetail(@Body LPIdsRequest request);

    @PUT("wms-app/outbound/pack/step/{stepId}/lp/{lpId}/consolidate")
    Observable<Response<Void>> consolidateLP(@Path("stepId") String stepId, @Path("lpId") String lpId, @Body PackStepConsolidateLPIdsRequest request);

    @GET("bam/re-palletize/stage/lp/{lpId}/detail")
    Observable<Response<RepalletizeStageLPDetail>> getRepalletizeStageLPDetail(@Path("lpId") String lpId);


    @PUT("bam/re-palletize/pack/step/{stepId}/task/{taskId}/close")
    Observable<Response<Void>> repalletizeTaskClose(@Path("stepId") String stepId, @Path("taskId") String taskId);

    @PUT("bam/re-palletize/pack/task/{taskId}/start")
    Observable<Response<PackTaskEntry>> repalletizeTaskStart(@Path("taskId") String taskId);

    @PUT("wms-app/rebuild-pallet/batch")
    Observable<Response<Void>> repalletizeTransferLp(@Body RepalletizeTransferLpEntry RepalletizeTransferLpEntry);

    @POST("wms-app/outbound/pack/task/orders-has-same-ship-to")
    Observable<Response<ValidateMultipleShipToResultEntry>> validateOrdersHasSameShipTo(@Body List<String> lpIds);

    @GET("wms-app/pallet-label/consolidate/lp/{lpId}")
    Observable<Response<IdResponse>> printConsolidatePalletLabel(@Path("lpId") String lpId);
}
