package com.linc.platform.gis.model;

import com.google.android.gms.maps.model.LatLng;
import com.google.gson.annotations.SerializedName;
import com.google.maps.android.clustering.ClusterItem;

import java.io.Serializable;

public class OsrTechEntry implements Serializable, ClusterItem {
    @SerializedName("zipcode")
    public String zipCode;
    @SerializedName("latlng")
    public String latlngStr;

    public LatLng latLng;

    @Override
    public LatLng getPosition() {
        return latLng;
    }

    @Override
    public String getTitle() {
        return null;
    }

    @Override
    public String getSnippet() {
        return null;
    }
}
