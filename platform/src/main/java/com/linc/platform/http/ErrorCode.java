package com.linc.platform.http;

/**
 * <AUTHOR>
 */

public final class ErrorCode {
    public static final String ERROR_CODE_UNKNOWN = "001";
    public static final String ERROR_CODE_BAD_REQUEST = "002";
    public static final String ERROR_CODE_TIME_OUT = "003";
    public static final String ERROR_CODE_INTERNAL_ERROR = "004";
    public static final String ERROR_CODE_REQUEST_NOT_FOUND = "005";
    public static final String ERROR_CODE_BAD_METHOD = "006";
    public static final String ERROR_CODE_DEFAULT = "-99";

    //WMS
    public static final String OFFLOAD_SEAL_NOT_MATCH = "44001";
    public static final String OFFLOAD_CONTAINER_NOT_MATCH = "44002";
    public static final String LP_SETUP_RECEIVED_QTY_NOT_MATCH_WITH_RN = "44003";
    public static final String INVALID_LP_ID = "44004";
    public static final String LP_SETUP_NOT_A_BLANK_LP = "44005";
    public static final String LP_SETUP_LP_ALREADY_ASSOCIATED_WITH_CURRENT_TASK = "44006";
    public static final String LP_SETUP_LP_TYPE_REQUIRED = "44007";
    public static final String LP_SETUP_ITEMLINE_REQUIRED = "44008";
    public static final String LP_SETUP_UNIT_REQUIRED = "44009";
    public static final String LP_SETUP_INVALID_QTY = "44010";
    public static final String LP_SETUP_PHOTO_REQUIRED_FOR_DAMAGE_GOOD = "44011";
    public static final String LP_SETUP_LP_ID_REQUIRED = "44012";
    public static final String LP_SETUP_INVALID_UNIT = "44013";
    public static final String PICK_ITEM_NOT_COMPLETED = "44014";
    public static final String SN_SCAN_EXPECTED_SNLIST = "44015";
    public static final String SN_SCAN_QTY_NOT_MATCH = "44016";
    public static final String PUT_AWAY_TASK_NOT_COMPLETE = "44017";
    public static final String PICK_TASK_NOT_COMPLETE = "44018";
    public static final String LP_CONFIGURATION_NOT_MATCH_WITH_ORDER_ITEMLINE = "40401";
    public static final String SN_NOT_EXIST_IN_ITEMLINE_EXPECT = "44015";
    public static final String LOCATION_ITEM_CHECK_NOT_MATCH = "44022";
    public static final String TASK_ALLOW_FORCE_CLOSE = "44024";
    public static final String LP_SETUP_QTY_EQUAL_ZERO = "44025";
    public static final String DOUBLE_SUBMISSION = "49001";
    public static final String PICK_TO_LP_NOT_FOUND = "NOT_FOUND";
    public static final String RECEIVING_PLATE_REPRINT_WITHOUT_RN = "44030";
    public static final String PICK_LP_ALREADY_BIND_ORDER = "44031";
    public static final String NEED_COLLECT_SN_BEFORE_LOAD = "48002";
    public static final String CYCLE_COUNT_ALL_LOCATION_COUNTED = "44032";
    public static final String FOUND_PENDING_LPS = "44034";
    public static final String LOCATION_ASSIGNED_TO_FIXED_ITEM = "44033";
    public static final String LP_SETUP_NEED_FORCE_CLOSE = "44034";
    public static final String TRANSLOAD_RECEIVE_NOT_MATCH_RECEIPT = "44040";
    public static final String CHECK_CAN_SKIP_AUTO_CC = "49005";

    public static final String NOT_ALLOW_SHORT_RECEIVE = "NOT_ALLOW_SHORT_RECEIVE";
    public static final String NOT_PERMISSION_FORCE_CLOSE = "NOT_PERMISSION_FORCE_CLOSE";
    public static final String FORCE_CLOSE_STEP = "FORCE_CLOSE_STEP";
    public static final String COLLECT_VERIFICATION = "COLLECT_VERIFICATION";
    public static final String OVER_RECEIVE = "50001";
    public static final String EXCEED_MAXIMUM_ALLOWED_PARTIAL_PALLET = "EXCEED_MAXIMUM_ALLOWED_PARTIAL_PALLET";
    public static final String TRANSLOAD_LOADING_COLLECT_PRO_NO = "48001";
    public static final String SLP_NO_CONTAIN_LOAD = "48007";
    // IDM
    public static final String USER_LOCKED = "46001";
    public static final String USER_DISABLED = "46002";
    public static final String PASSWORD_INVALID = "46003";
    public static final String USER_ALREADY_LOGGED_IN_ANOTHER_DEVICE = "46004";
    public static final String USER_INVALID = "46005";
    public static final String CARRIER_USER_FORBID_LOGIN = "46009";
    public static final String AUTHORIZATION_REQUIRED = "46006";
    public static final String INVALID_EMPLOYEE_ID = "46007";
    public static final String ERROR_CODE_NO_SUGGESTION_LOCATION = "44051";
    public static final String FORCE_COMPLETE_LOAD = "48003";
    public static final String CYCLE_COUNT_LPS_QTY_NO_MATCH = "44044";
    public static final String INVALID_CARRIER_NAME = "44047";
    public static final String LOAD_COMPLETE_ERROR_NEED_ADD_MATERIAL = "48004";
    public static final String PUT_AWAY_AUDITING_NOT_MATCH_TO_FORCE_CLOSE = "44049";
    public static final String LOAD_TASK_CLOSE_REQUIRE_PRINT_LABEL = "48005";
    public static final String OPPORTUNITY_PICK_STAGE_DATA_CHANGED = "49001";
    public static final String LOCATION_BAY_RESERVED = "44051";
    public static final String ASSET_NOT_RECEIVE = "81003";

    public static final String A_SCAN_ERROR_CODE = "22";

    public static final String CLP_BONDING_INVENTORY_SHORTAGE = "511";

    public static final String LOAD_TASK_HAS_NOT_LOADED = "48007";


    public static final String USER_NOT_EMPLOYEE_ID = "46020";

    public static final String ERROR_NEED_STAGE_TO_ONE_LOCATION_TOGETHER = "53001";
    public static final String ERROR_NOT_ALLOW_OVERRIDE_STAGE_SUGGESTED_LOCATION = "53002";
    public static final String ERROR_NOT_ALLOW_STAGE_MULTIPLE_LOAD = "53003";
    public static final String ERROR_REPLENISH_COLLECT_SUBMIT_NOT_MATCH_SHIPPING_RULE = "60001";
}
