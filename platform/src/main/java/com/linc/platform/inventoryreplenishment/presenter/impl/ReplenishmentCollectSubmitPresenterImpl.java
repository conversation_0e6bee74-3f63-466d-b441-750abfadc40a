package com.linc.platform.inventoryreplenishment.presenter.impl;

import android.text.TextUtils;

import com.annimon.stream.Stream;
import com.linc.platform.R;
import com.linc.platform.common.apidal.BaseApiDal;
import com.linc.platform.common.handler.FailedHandler;
import com.linc.platform.common.handler.SuccessHandler;
import com.linc.platform.common.step.StepBaseEntry;
import com.linc.platform.common.step.StepTypeEntry;
import com.linc.platform.foundation.model.ShippingRuleEntry;
import com.linc.platform.http.ErrorCode;
import com.linc.platform.http.ErrorCodeSubscriber;
import com.linc.platform.http.ErrorResponse;
import com.linc.platform.http.HttpService;
import com.linc.platform.inventory.model.InventoryEntry;
import com.linc.platform.inventoryreplenishment.ReplenishmentValidator;
import com.linc.platform.inventoryreplenishment.model.CollectTransmitData;
import com.linc.platform.inventoryreplenishment.model.ReplenishmentCollectModel;
import com.linc.platform.inventoryreplenishment.model.SuggestionLocationEntry;
import com.linc.platform.inventoryreplenishment.presenter.ReplenishmentCollectSubmitPresenter;
import com.linc.platform.inventoryreplenishment.view.ReplenishmentCollectFormView;
import com.linc.platform.pick.model.PickTypeEntry;
import com.linc.platform.replenish.collect.model.ReplenishCollectProcessEntry;
import com.linc.platform.replenishment.api.ReplenishmentStepApi;
import com.linc.platform.replenishment.api.ReplenishmentTaskApi;
import com.linc.platform.replenishment.model.AvailableReplenishmentSearchEntry;
import com.linc.platform.replenishment.model.PickStrategyEntry;
import com.linc.platform.utils.CollectionUtil;
import com.linc.platform.utils.RxUtil;
import com.linc.platform.utils.ToastUtil;

import java.util.List;

import kotlin.Function;
import kotlin.reflect.jvm.internal.impl.metadata.ProtoBuf;
import retrofit2.Response;

import static com.linc.platform.utils.ResUtil.getString;

/**
 * @Description:
 * @Author: Dennis
 * @CreateDate: 2020/12/25 14:15
 */
public class ReplenishmentCollectSubmitPresenterImpl extends BaseApiDal implements ReplenishmentCollectSubmitPresenter {

    private ReplenishmentStepApi mReplenishmentStepApi;
    private ReplenishmentTaskApi taskApi;
    private ReplenishmentCollectModel mReplenishmentCollectModel;
    private ReplenishmentCollectFormView mView;
    private StepBaseEntry mStepBaseEntry;

    public ReplenishmentCollectSubmitPresenterImpl(ReplenishmentCollectModel replenishmentCollectModel, ReplenishmentCollectFormView view) {
        mView = view;
        mReplenishmentCollectModel = replenishmentCollectModel;
        mStepBaseEntry = Stream.of(replenishmentCollectModel.getReplenishmentTaskEntry().stepEntries).filter(s -> s.type.equals(StepTypeEntry.REPLENISHMENT)).toList().get(0);
        mReplenishmentStepApi = HttpService.createService(ReplenishmentStepApi.class);
        taskApi = HttpService.createService(ReplenishmentTaskApi.class);
    }

    @Override
    public void onCollectSubmit(CollectTransmitData collectTransmitData) {
        String validateMsg = ReplenishmentValidator.validateCollectSubmitData(collectTransmitData, getReplenishmentCollectModel());
        if (!TextUtils.isEmpty(validateMsg)) {
            ToastUtil.showToast(validateMsg);
            return;
        }
        ReplenishCollectProcessEntry replenishCollectProcessEntry = assembleCollectSubmitData(collectTransmitData);

        asyncExecute(mReplenishmentStepApi.collectInventory(mStepBaseEntry.taskId, mStepBaseEntry.id, replenishCollectProcessEntry), mView,
                this::handleErrorCode,
                Void -> mView.onCollectSubmitSuccessful()
        );
    }

    @Override
    public void getSuggestCollectLP() {
        if (CollectionUtil.isNotNullOrEmpty(mReplenishmentCollectModel.getReplenishmentTaskEntry().replenishmentItems)) {
            asyncExecute(mReplenishmentStepApi.getSuggestionCollectLocation(mStepBaseEntry.taskId), mView,
                    suggestionLocationEntry -> {
                        if (suggestionLocationEntry != null && CollectionUtil.isNotNullOrEmpty(suggestionLocationEntry.lpIds)) {
                            mView.updateSuggestLocation(suggestionLocationEntry.location, suggestionLocationEntry.lpIds.get(0));
                        } else  {
                            ToastUtil.showToast(R.string.no_new_suggestion);
                        }
                    });
        } else {
            AvailableReplenishmentSearchEntry searchEntry = new AvailableReplenishmentSearchEntry();
            searchEntry.itemSpecId = mReplenishmentCollectModel.getSelectedInventory().itemSpecId;
            searchEntry.shippingRule = ShippingRuleEntry.FEFO;
            searchEntry.supportPickType = PickTypeEntry.PALLET_PICK;
            searchEntry.titleId = mReplenishmentCollectModel.getSelectedInventory().titleId;
            asyncExecute(taskApi.searchAvailableReplenishment(searchEntry), mView, pickStrategyEntries -> {
                if (CollectionUtil.isNotNullOrEmpty(pickStrategyEntries) && CollectionUtil.isNotNullOrEmpty(pickStrategyEntries.get(0).lpIds)) {
                    mView.updateSuggestLocation(pickStrategyEntries.get(0).location, pickStrategyEntries.get(0).lpIds.get(0));
                } else  {
                    ToastUtil.showToast(R.string.no_new_suggestion);
                }
            });
        }
    }

    /**
     * 处理特定错误码的特殊逻辑
     */
    private void handleErrorCode(ErrorResponse errorResponse) {
        if (ErrorCode.ERROR_REPLENISH_COLLECT_SUBMIT_NOT_MATCH_SHIPPING_RULE.equals(errorResponse.code)) {
            if (CollectionUtil.isNotNullOrEmpty(mReplenishmentCollectModel.getReplenishmentTaskEntry().replenishmentItems)) {
                asyncExecute(mReplenishmentStepApi.getSuggestionCollectLocation(mStepBaseEntry.taskId), mView,
                        error -> mView.onCollectSubmitNotMatchShippingRule("", ""),
                        suggestionLocationEntry -> {
                            if (suggestionLocationEntry != null && CollectionUtil.isNotNullOrEmpty(suggestionLocationEntry.lpIds)) {
                                mView.onCollectSubmitNotMatchShippingRule(suggestionLocationEntry.location, suggestionLocationEntry.lpIds.get(0));
                            } else  {
                                mView.onCollectSubmitNotMatchShippingRule("", "");
                            }
                        });
            } else {
                AvailableReplenishmentSearchEntry searchEntry = new AvailableReplenishmentSearchEntry();
                searchEntry.itemSpecId = mReplenishmentCollectModel.getSelectedInventory().itemSpecId;
                searchEntry.shippingRule = ShippingRuleEntry.FEFO;
                searchEntry.supportPickType = PickTypeEntry.PALLET_PICK;
                searchEntry.titleId = mReplenishmentCollectModel.getSelectedInventory().titleId;
                asyncExecute(taskApi.searchAvailableReplenishment(searchEntry), mView, error -> {
                    mView.onCollectSubmitNotMatchShippingRule("", "");
                }, pickStrategyEntries -> {
                    if (CollectionUtil.isNotNullOrEmpty(pickStrategyEntries) && CollectionUtil.isNotNullOrEmpty(pickStrategyEntries.get(0).lpIds)) {
                        mView.onCollectSubmitNotMatchShippingRule(pickStrategyEntries.get(0).location, pickStrategyEntries.get(0).lpIds.get(0));
                    } else  {
                        mView.onCollectSubmitNotMatchShippingRule("", "");
                    }
                });
            }
            return;
        }
        ToastUtil.showToast(errorResponse.getErrorMessage());
    }

    private ReplenishCollectProcessEntry assembleCollectSubmitData(CollectTransmitData collectTransmitData) {
        ReplenishmentCollectModel replenishmentCollectModel = getReplenishmentCollectModel();
        ReplenishCollectProcessEntry replenishCollectForm = new ReplenishCollectProcessEntry();
        InventoryEntry selectedInventory = replenishmentCollectModel.getSelectedInventory();
        replenishCollectForm.itemSpecId = selectedInventory.itemSpecId;
        replenishCollectForm.fromLPId = selectedInventory.lpId;
        replenishCollectForm.fromUnitId = selectedInventory.unitId;
        replenishCollectForm.fromLocationId = selectedInventory.location != null? selectedInventory.location.id : "";
        replenishCollectForm.titleId = selectedInventory.titleId;
        if (collectTransmitData.isEntire()) {
            replenishCollectForm.isEntireLPReplenish = true;
            replenishCollectForm.qty = selectedInventory.qty;
            replenishCollectForm.lotNo = replenishmentCollectModel.getLotNo();
            replenishCollectForm.snList = replenishmentCollectModel.getSnList();
        } else {
            String qty = collectTransmitData.getQty();
            String lotNo = collectTransmitData.getLotNo();
            List<String> snList = collectTransmitData.getSnList();
            replenishCollectForm.isEntireLPReplenish = false;
            replenishCollectForm.qty = Double.parseDouble(qty);
            replenishCollectForm.lotNo = lotNo;
            replenishCollectForm.snList = snList;
        }
        return replenishCollectForm;
    }

    @Override
    public void getSn(String scanVal) {
        List<String> snList = mReplenishmentCollectModel.getSnList();
        if (snList != null && !snList.isEmpty()) {
            if (snList.contains(scanVal)) {
                mView.getSnSuccessful(scanVal);
            } else {
                ToastUtil.showErrorToast(getString(R.string.sn_no_belong_item));
            }
        }
    }

    public ReplenishmentCollectModel getReplenishmentCollectModel() {
        return mReplenishmentCollectModel;
    }
}
