package com.linc.platform.inventoryreplenishment.view;

import com.linc.platform.core.ProgressView;

/**
 * @Description:
 * @Author: Dennis
 * @CreateDate: 2020/12/25 14:12
 */
public interface ReplenishmentCollectFormView extends ProgressView {

    void getSnSuccessful(String sn);

    void onCollectSubmitSuccessful();
    
    /**
     * 不符合出货规则(FEFO)的回调方法
     */
    void onCollectSubmitNotMatchShippingRule(String suggestLocationName, String suggestLpId);
    
    /**
     * 更新建议位置信息
     *
     * @param suggestLocationName
     * @param suggestLpId         建议的LP ID
     */
    void updateSuggestLocation(String suggestLocationName, String suggestLpId);
}
