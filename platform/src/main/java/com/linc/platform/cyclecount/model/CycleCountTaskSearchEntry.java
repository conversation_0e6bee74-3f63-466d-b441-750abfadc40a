package com.linc.platform.cyclecount.model;

import com.google.gson.annotations.SerializedName;
import com.linc.platform.generaltask.model.GeneralTaskSearchEntry;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by devinc on 2016/10/5.
 */
public class CycleCountTaskSearchEntry extends GeneralTaskSearchEntry implements Serializable {
    @SerializedName("taskTypes")
    public List<String> taskTypes = new ArrayList<>();

    @SerializedName("checkTypes")
    public List<String> checkTypes = new ArrayList<>();
}
