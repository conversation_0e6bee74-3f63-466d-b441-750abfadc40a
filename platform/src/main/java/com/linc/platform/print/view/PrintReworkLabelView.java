package com.linc.platform.print.view;

import com.linc.platform.core.ProgressView;
import com.linc.platform.foundation.model.ItemSpecEntry;
import com.linc.platform.print.model.PrinterEntry;

import java.util.List;

public interface PrintReworkLabelView extends ProgressView {
    void showPrinterInfo(PrinterEntry printerEntry);

    void hintScanUpc();

    void hintInputPrintCount();

    void hintToSelectPrinter();

    void startPrinterSettingAct();

    void showInvalidUpc();

    void toSelectItem(List<ItemSpecEntry> itemSpecEntries);

    void showItemInfo(ItemSpecEntry itemSpecEntry);

    void clearItemInfo();

    void onPrintSuccess();

    void onPrintFailed(String errorMessage, PrinterEntry printerEntry);
}
