package com.linc.platform.print.presenter.impl;

import com.linc.platform.http.ErrorCodeSubscriber;
import com.linc.platform.http.ErrorResponse;
import com.linc.platform.http.HttpService;
import com.linc.platform.http.IdResponse;
import com.linc.platform.print.api.LabelApi;
import com.linc.platform.print.model.LabelRequestEntry;
import com.linc.platform.print.presenter.PrintLabelPresenter;
import com.linc.platform.print.view.PrintLabelView;
import com.linc.platform.utils.RxUtil;
import com.linc.platform.utils.ToastUtil;
import retrofit2.Response;

/**
 * <AUTHOR>
 */

public class PrintLabelPresenterImpl implements PrintLabelPresenter {
    private LabelApi labelApi;
    private PrintLabelView printLabelView;

    public PrintLabelPresenterImpl(PrintLabelView printLabelView) {
        this.printLabelView = printLabelView;
        labelApi = HttpService.createService(LabelApi.class);
    }


    @Override
    public void loadPrintDetail(String lpId) {
        printLabelView.showProgress(true);
        labelApi.loadPrintDetail(lpId)
                .compose(RxUtil.asyncSchedulers())
                .subscribe(new ErrorCodeSubscriber<Response<LabelRequestEntry>>() {
                    @Override
                    public void onSuccess(Response<LabelRequestEntry> labelRequestEntryResponse) {
                        printLabelView.refreshRequestEntries(labelRequestEntryResponse.body());
                    }

                    @Override
                    public void onFailed(ErrorResponse errorResponse) {
                        ToastUtil.showErrorToast(errorResponse.getErrorMessage());
                    }

                    @Override
                    public void onDone() {
                        printLabelView.showProgress(false);
                    }
                });
    }

    @Override
    public void createPrintJob(LabelRequestEntry labelRequestEntry) {
        printLabelView.showProgress(true);
        labelApi.createLabelPrintJob(labelRequestEntry)
                .compose(RxUtil.asyncSchedulers())
                .subscribe(new ErrorCodeSubscriber<Response<IdResponse>>() {
                    @Override
                    public void onSuccess(Response<IdResponse> idResponseResponse) {
                        printLabelView.createJobSuccess(idResponseResponse.body().id);
                    }

                    @Override
                    public void onFailed(ErrorResponse errorResponse) {
                        ToastUtil.showErrorToast(errorResponse.getErrorMessage());
                    }

                    @Override
                    public void onDone() {
                        printLabelView.showProgress(false);
                    }
                });
    }
}
