package com.linc.platform.print.commonprintlp.printjobcreator;

import androidx.annotation.NonNull;

import com.linc.platform.common.apidal.BaseApiDal;
import com.linc.platform.common.handler.SuccessHandler;
import com.linc.platform.inventory.model.SNPrintJobCreateEntry;
import com.linc.platform.print.api.BarcodeApi;
import com.linc.platform.print.commonprintlp.PrintData;
import com.linc.platform.print.commonprintlp.PrintJobCreate;
import com.linc.platform.print.commonprintlp.PrintJobFactory;
import com.linc.platform.print.commonprintlp.PrintView;

/**
 * @Description:
 * @Author: Dennis
 * @CreateDate: 2022/3/9 14:16
 */
public class SnLabelJobCreator extends BaseApiDal implements PrintJobFactory.JobCreator<PrintJobCreate.SNLabel> {

    private BarcodeApi api;

    private SnLabelJobCreator() {
        api = api(BarcodeApi.class);
    }

    public static SnLabelJobCreator newInstance() {
        return new SnLabelJobCreator();
    }

    @Override
    public void create(PrintView view, @NonNull PrintData data, PrintJobCreate.SNLabel jobCreate, SuccessHandler<String> jobIdHandler) {
        SNPrintJobCreateEntry entry = new SNPrintJobCreateEntry();
        entry.itemSpecId = jobCreate.itemSpecId;
        entry.sn = jobCreate.sn;
        entry.customerId = jobCreate.customerId;
        asyncExecute(api.buildSNLabelPrintJob(entry), view,
                errorResponse -> view.onPrintFailed(errorResponse, null),
                idResponse -> jobIdHandler.onSuccess(idResponse.id));
    }
}
