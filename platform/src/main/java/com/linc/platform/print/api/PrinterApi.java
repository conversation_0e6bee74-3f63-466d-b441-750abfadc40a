package com.linc.platform.print.api;

import com.linc.platform.print.model.PrintServerEntry;
import com.linc.platform.print.model.PrintServerSearch;
import com.linc.platform.print.model.PrinterEntry;

import java.util.List;

import retrofit2.Response;
import retrofit2.http.Body;
import retrofit2.http.POST;
import rx.Observable;

/**
 * <AUTHOR>
 */
public interface PrinterApi {
    @POST("print-app/print-server/search")
    Observable<Response<List<PrintServerEntry>>> searchPrintServer(@Body PrintServerSearch search);

    @POST("print-app/printer/search")
    Observable<Response<List<PrinterEntry>>> searchPrinters(@Body PrinterEntry printerEntry);
}
