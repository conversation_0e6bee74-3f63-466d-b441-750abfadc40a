package com.linc.platform.localconfig;

import org.json.JSONObject;

import java.util.List;

/**
 * <AUTHOR>
 */

public interface TakeOverMessagePresenter {
    void saveMessage(String idmUserId, JSONObject messageJsonObject);

    List<TakeOverMessageEntity> getTakeOverMessage(String idmUserId);

    List<TakeOverMessageEntity> getMessageGroupEntries(String userId);

    List<TakeOverMessageEntity> getMessageWindowEntries(String userId, String applicantName);

    void updateMessageStatus(String userId, String applicantName);
}
