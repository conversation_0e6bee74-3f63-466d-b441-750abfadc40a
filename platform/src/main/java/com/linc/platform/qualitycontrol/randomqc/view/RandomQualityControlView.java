package com.linc.platform.qualitycontrol.randomqc.view;

import com.linc.platform.qualitycontrol.randomqc.model.QCItemDetailEntry;
import com.linc.platform.qualitycontrol.randomqc.model.RandomQCEntry;

/**
 * Created by Gavin
 */

public interface RandomQualityControlView {
    void showRandomQcDetail(RandomQCEntry randomQCEntry);

    void onRandomQcDetailNotFound();

    void showQCItemDetail(QCItemDetailEntry qcItemDetailEntry);

    void onQcSuccess();

    void setLpScannerEdtFocus();

    void setItemScannerEdtFocus();
}
