package com.linc.platform.qualitycontrol.regularqc.apiprocessor;

import com.linc.platform.common.handler.APIProcess;
import com.linc.platform.common.handler.SuccessHandler;
import com.linc.platform.core.ProgressView;
import com.linc.platform.qualitycontrol.regularqc.api.QcTaskApi;
import com.linc.platform.qualitycontrol.regularqc.model.QCTaskSearchEntry;
import com.linc.platform.qualitycontrol.regularqc.model.QcTaskViewEntry;

import java.util.List;

/**
 * <AUTHOR>
 */
public class SearchQCTaskAPI extends APIProcess<QCTaskSearchEntry, List<QcTaskViewEntry>> {

    public static SearchQCTaskAPI newInstance() {
        return new SearchQCTaskAPI();
    }

    @Override
    public void execute(ProgressView progress, QCTaskSearchEntry entry, SuccessHandler<List<QcTaskViewEntry>> successHandler) {
        super.onObserve(progress, api(QcTaskApi.class).search(entry), successHandler);
    }
}