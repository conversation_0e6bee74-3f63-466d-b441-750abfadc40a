package com.linc.platform.putaway.progress;

import com.linc.platform.common.handler.ProcessorAPI;
import com.linc.platform.common.handler.SuccessHandler;
import com.linc.platform.http.ErrorCodeSubscriber;
import com.linc.platform.http.ErrorResponse;
import com.linc.platform.http.HttpService;
import com.linc.platform.toolset.lpputaway.api.PutAwayStepApi;
import com.linc.platform.toolset.lpputaway.model.PutAwayResultEntry;
import com.linc.platform.utils.RxUtil;
import com.linc.platform.utils.ToastUtil;

import java.util.List;

import retrofit2.Response;

/**
 * <AUTHOR>
 */

public class GetPutAwayLPProgressAPI implements ProcessorAPI<String, PutAwayProgressView, SuccessHandler<List<PutAwayResultEntry>>> {
    private static GetPutAwayLPProgressAPI api;
    private String companyId;

    public GetPutAwayLPProgressAPI(String companyId) {
        this.companyId = companyId;
    }

    public static GetPutAwayLPProgressAPI getInstance(String companyId) {
        if (api == null) api = new GetPutAwayLPProgressAPI(companyId);
        return api;
    }

    @Override
    public void execute(String stepId, PutAwayProgressView view, SuccessHandler<List<PutAwayResultEntry>> successHandler) {

        ErrorCodeSubscriber<Response<List<PutAwayResultEntry>>> subscriber = new ErrorCodeSubscriber<Response<List<PutAwayResultEntry>>>() {
            @Override
            public void onSuccess(Response<List<PutAwayResultEntry>> response) {
                successHandler.onSuccess(response.body());
            }

            @Override
            public void onFailed(ErrorResponse errorResponse) {
                ToastUtil.showErrorToast(errorResponse.getErrorMessage());
            }

            @Override
            public void onDone() {

            }

            @Override
            public boolean useDefaultProgress() {
                return true;
            }
        };

        getAPI().getLpView(stepId).compose(RxUtil.asyncSchedulers()).subscribe(subscriber);
    }

    private PutAwayStepApi getAPI() {
        return HttpService.createService(PutAwayStepApi.class);
    }
}
