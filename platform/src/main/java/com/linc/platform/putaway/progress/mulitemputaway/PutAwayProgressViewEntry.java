package com.linc.platform.putaway.progress.mulitemputaway;

import android.text.format.DateFormat;

import com.google.gson.annotations.SerializedName;
import com.linc.platform.baseapp.model.FacilityEquipmentView;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 */
public class PutAwayProgressViewEntry implements Serializable {
    @SerializedName("id")
    public String id;

    @SerializedName("taskId")
    public String taskId;

    @SerializedName("stepId")
    public String stepId;

    @SerializedName("lpId")
    public String lpId;

    @SerializedName("lpTote")
    public FacilityEquipmentView lpTote;

    @SerializedName("toLPId")
    public String toLPId;

    @SerializedName("itemSpecId")
    public String itemSpecId;

    @SerializedName("itemName")
    public String itemName;

    @SerializedName("itemDesc")
    public String itemDesc;

    @SerializedName("unitId")
    public String unitId;

    @SerializedName("unitName")
    public String unitName;

    @SerializedName("toLocationId")
    public String toLocationId;

    @SerializedName("toLocationName")
    public String toLocationName;

    @SerializedName("qty")
    public double qty;

    @SerializedName("putAwayBy")
    public String putAwayBy;

    @SerializedName("putAwayWhen")
    public Date putAwayWhen;

    public String putAwayWhen() {
        return putAwayWhen == null ? "" : DateFormat.format("MM/dd/yyyy hh:mm:ss", putAwayWhen).toString();
    }
}
