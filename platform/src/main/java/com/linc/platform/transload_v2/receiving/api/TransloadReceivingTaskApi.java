package com.linc.platform.transload_v2.receiving.api;

import com.linc.platform.common.task.TaskAssignmentEntry;
import com.linc.platform.transload_v2.receiving.model.OversizeHandlingEntity;
import com.linc.platform.transload_v2.receiving.model.TransloadReceivingTaskEntity;
import com.linc.platform.transload_v2.receiving.model.TransloadReceivingTaskPagingEntry;
import com.linc.platform.transload_v2.receiving.model.TransloadReceivingTaskSearchEntity;
import com.linc.platform.transload_v2.receiving.model.TransloadReceivingTaskViewEntity;

import java.util.List;

import retrofit2.Response;
import retrofit2.http.Body;
import retrofit2.http.DELETE;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.PUT;
import retrofit2.http.Path;
import rx.Observable;

/**
 * <AUTHOR>
 * @date 2020/12/25
 * @desc
 */
public interface TransloadReceivingTaskApi {

    @GET("bam/transload/receive-task/{taskId}")
    Observable<Response<TransloadReceivingTaskViewEntity>> get(@Path("taskId") String taskId);

    @PUT("wms-app/transload/receive-task/{taskId}")
    Observable<Response<Void>> update(@Path("taskId") String taskId,@Body TransloadReceivingTaskEntity taskEntity);

    @POST("bam/transload/receive-task/search-by-paging")
    Observable<Response<TransloadReceivingTaskPagingEntry>> searchByPaging(@Body TransloadReceivingTaskSearchEntity searchEntity);

    @POST("wms-app/transload/receive-task/search")
    Observable<Response<List<TransloadReceivingTaskEntity>>> search(@Body TransloadReceivingTaskSearchEntity search);

    @PUT("wms-app/transload/receive-task/{taskId}/assignment")
    Observable<Response<Void>> assign(@Path("taskId") String taskId, @Body TaskAssignmentEntry search);

    @PUT("wms-app/transload/receive-task/{taskId}/start")
    Observable<Response<Void>> start(@Path("taskId") String taskId);

    @PUT("wms-app/transload/receive-task/{taskId}/close")
    Observable<Response<Void>> close(@Path("taskId") String taskId);

    @PUT("wms-app/transload/receive-task/{taskId}/force-close")
    Observable<Response<Void>> forceClose(@Path("taskId") String taskId);

    @POST("shared/last-mile-app/last-mile/oversize-package")
    Observable<Response<Void>> createOversizePackage(@Body OversizeHandlingEntity entity);

    @POST("shared/last-mile-app/last-mile/oversize-package/search")
    Observable<Response<List<OversizeHandlingEntity>>> searchOversizePackage(@Body OversizeHandlingEntity entity);

    @DELETE("/shared/last-mile-app/last-mile/oversize-package/{id}")
    Observable<Response<Void>> deleteOversizePackage(@Path("id") String id);
}
