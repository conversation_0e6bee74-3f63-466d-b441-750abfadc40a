package com.linc.platform.damagegood.presenter.impl;

import androidx.annotation.NonNull;

import com.linc.platform.damagegood.model.DamageEntry;
import com.linc.platform.damagegood.presenter.DamagePresenter;
import com.linc.platform.damagegood.view.DamageView;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class DamagePresenterImpl implements DamagePresenter {
    private DamageView damageView;
    private List<DamageEntry> damageEntryList = new ArrayList<>();

    public DamagePresenterImpl(@NonNull DamageView damageView) {
        this.damageView = damageView;
    }

    @Override
    public void loadDamageList() {
        initTestData();
        damageView.refreshData(damageEntryList);
    }

    private void initTestData() {
        DamageEntry data = new DamageEntry();
        data.item = "vivo";
        data.status = DamageEntry.DAMAGE_STATUS_NEW;
        data.zone = "A";
        data.location = "100";
        data.quantity = 1.0;
        damageEntryList.add(data);

        data = new DamageEntry();
        data.item = "Oppo";
        data.status = DamageEntry.DAMAGE_STATUS_PROGRESS;
        data.zone = "B";
        data.location = "101";
        data.quantity = 2.0;
        damageEntryList.add(data);

        data = new DamageEntry();
        data.item = "Huawei";
        data.status = DamageEntry.DAMAGE_STATUS_DONE;
        data.zone = "C";
        data.location = "103";
        data.quantity = 3.0;
        damageEntryList.add(data);
    }
}
