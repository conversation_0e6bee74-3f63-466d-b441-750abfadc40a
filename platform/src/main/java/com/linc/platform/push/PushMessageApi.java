package com.linc.platform.push;

import java.util.List;

import retrofit2.Response;
import retrofit2.http.Body;
import retrofit2.http.DELETE;
import retrofit2.http.POST;
import retrofit2.http.Path;
import rx.Observable;

/**
 * <AUTHOR>
 */

public interface PushMessageApi {
    @POST("push-app/register")
    Observable<Response<Void>> register(@Body RegisterPushEntry registerPushEntry);

    @POST("shared/bam/open-api/push-app/register")
    Observable<Response<Void>> registerByEmployeeId(@Body RegisterPushEntry registerPushEntry);

    @DELETE("push-app/register/{userId}/{registrationId}")
    Observable<Response<Void>> unregister(@Path("userId") String userId,
                                          @Path("registrationId") String registrationId);

    @POST("push-app/message")
    Observable<Response<Void>> push(@Body MessagePushEntry messagePush);

    @POST("push-app/message/batch")
    Observable<Response<Void>> batchPush(@Body List<MessagePushEntry> messagePush);
}
