package com.linc.platform.load.model;

import com.google.gson.annotations.SerializedName;
import com.linc.platform.baseapp.model.LocationEntry;
import com.linc.platform.foundation.model.CustomerViewEntry;
import com.linc.platform.foundation.model.OrderEntry;
import com.linc.platform.idm.model.UserViewEntry;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class LoadDetailEntry implements Serializable {
    public static final String TAG = LoadDetailEntry.class.getSimpleName();
    public static final String STEP_LOADING = "Loading";
    public static final String STEP_COUNT_UNSHIPPED = "Count Unshipped";

    @SerializedName("id")
    public String id;

    @SerializedName("type")
    public String type;

    @SerializedName("description")
    public String description;

    @SerializedName("companyId")
    public String companyId;

    @SerializedName("status")
    public LoadStatusEntry status;

    @SerializedName("shipToId")
    public String shipTo;

    @SerializedName("shipToAddress")
    public ShipAddressEntry shipToAddress;

    @SerializedName("carrierName")
    public String carrierName;

    @SerializedName("proNo")
    public String proNo;

    @SerializedName("customer")
    public CustomerViewEntry customerViewEntry;

    @SerializedName("loadNo")
    public String loadNo;

    @SerializedName("titleNames")
    public List<String> titleList = new ArrayList<>();

    @SerializedName("orderIds")
    public List<String> orderIds = new ArrayList<>();

    @SerializedName("stagingAreas")
    public List<String> stagingAreas = new ArrayList<>();

    @SerializedName("requireLoadLps")
    public List<String> requireLoadLps = new ArrayList<>();

    @SerializedName("countingSheetPhotos")
    public List<String> countingSheetPhotos = new ArrayList<>();

    @SerializedName("customerId")
    public String customerId;

    @SerializedName("customerName")
    public String customerName;

    @SerializedName("loads")
    public List<LoadEntry> loads;

    @SerializedName("orderAmount")
    public int orderAmount;

    @SerializedName("dock")
    public LocationEntry dock;

    @SerializedName("assignees")
    public List<String> assigneeIds;

    @SerializedName("assigneeDetail")
    public List<UserViewEntry> assigneeDetail;

    @SerializedName("lps")
    public List<LpOrderEntry> lps;

    @SerializedName("entryId")
    public String entryId;

    @SerializedName("dockId")
    public String dockId;

    @SerializedName("shipperSignature")
    public String shipperSignature;

    @SerializedName("carrierSignature")
    public String carrierSignature;

    @SerializedName("isNeedProNo")
    public boolean isNeedProNo = false;

    @SerializedName("driverLoadBar")
    public MaterialCheckedStatusView driverLoadBar;

    @SerializedName("driverStrap")
    public MaterialCheckedStatusView driverStrap;

    @SerializedName("driverAirbag")
    public MaterialCheckedStatusView driverAirbag;

    @SerializedName("unisLoadBar")
    public MaterialCheckedStatusView unisLoadBar;

    @SerializedName("unisStrap")
    public MaterialCheckedStatusView unisStrap;

    @SerializedName("unisAirbag")
    public MaterialCheckedStatusView unisAirbag;

    @SerializedName("sequence")
    public Integer sequence;

    @SerializedName("createdBy")
    public String createdBy;

    @SerializedName("loadItemSpecNames")
    public String loadItemSpecNames;

    @SerializedName("note")
    public String note;

    @SerializedName("trailerPickUpMode")
    public TrailerPickUpModeEntry trailerPickUpMode;

    @SerializedName("carrierId")
    public String carrierId;

    public List<OrderEntry> orderList;

    public Double palletQty;

    public boolean printable = false;

    public boolean isLoadCompleted() {
        return LoadStatusEntry.LOADED == status || LoadStatusEntry.SHIPPED == status;
    }
}
