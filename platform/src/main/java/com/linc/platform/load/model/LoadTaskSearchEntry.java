package com.linc.platform.load.model;

import com.google.gson.annotations.SerializedName;
import com.linc.platform.generaltask.model.GeneralTaskSearchEntry;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */

public class LoadTaskSearchEntry extends GeneralTaskSearchEntry implements Serializable {
    @SerializedName("fuzzyTaskId")
    public String fuzzyTaskId;

    @SerializedName("entryId")
    public String entryId;

    @SerializedName("entryIds")
    public List<String> entryIds;

    @SerializedName("dockIds")
    public List<String> dockIds;

    @SerializedName("dockId")
    public String dockId;

    @SerializedName("loadIds")
    public List<String> loadIds;

    @SerializedName("subscribedDockIds")
    public String subscribedDockIds;
}
