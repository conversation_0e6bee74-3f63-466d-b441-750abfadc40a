package com.linc.platform.dockcheckin.presenter.impl;


import android.content.Context;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import android.text.TextUtils;

import com.linc.platform.R;
import com.linc.platform.baseapp.api.EntryTicketApi;
import com.linc.platform.baseapp.model.DockOperateEntry;
import com.linc.platform.baseapp.model.EntryTicketSearchEntry;
import com.linc.platform.baseapp.model.LocationEntry;
import com.linc.platform.common.EntryTicketEntry;
import com.linc.platform.common.EntryTicketPagingResult;
import com.linc.platform.common.EntryTicketStatusEntry;
import com.linc.platform.common.apidal.BaseApiDal;
import com.linc.platform.common.help.FunctionHelpPresenterImpl;
import com.linc.platform.common.task.TaskTypeEntry;
import com.linc.platform.dockcheckin.model.ItemSearchEntryIdEnum;
import com.linc.platform.dockcheckin.presenter.DockCheckInPresenter;
import com.linc.platform.dockcheckin.view.DockCheckInView;
import com.linc.platform.foundation.api.FacilityApi;
import com.linc.platform.foundation.model.organization.common.facility.FacilityEntry;
import com.linc.platform.generaltask.PagingParamEntry;
import com.linc.platform.generaltask.model.GeneralTaskViewEntry;
import com.linc.platform.http.ErrorCodeSubscriber;
import com.linc.platform.http.ErrorResponse;
import com.linc.platform.http.HttpService;
import com.linc.platform.load.model.LoadTaskViewEntry;
import com.linc.platform.localconfig.FacilityConfigPresenterImpl;
import com.linc.platform.receive.api.ReceiveTaskApi;
import com.linc.platform.receive.model.ReceiveTaskEntry;
import com.linc.platform.utils.CollectionUtil;
import com.linc.platform.utils.ResUtil;
import com.linc.platform.utils.RxUtil;
import com.linc.platform.utils.ToastUtil;
import com.linc.platform.yms.checkin.model.EntryTicketUpdateEntry;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import retrofit2.Response;
import rx.Observable;

/**
 * Created by devinc.
 */
public class DockCheckInPresenterImpl extends BaseApiDal implements DockCheckInPresenter {

    private DockCheckInView dockCheckInView;

    private EntryTicketApi entryTicketApi;
    private EntryTicketEntry entryTicketEntry;
    private GeneralTaskViewEntry generalTaskViewEntry;
    private FacilityApi facilityApi;
    private LocationEntry dock;
    private final FunctionHelpPresenterImpl mFunctionHelpPresenter;
    private ReceiveTaskApi receiptTaskApi;


    public DockCheckInPresenterImpl(@NonNull DockCheckInView dockCheckInView,
                                    @NonNull GeneralTaskViewEntry taskViewEntry,
                                    @Nullable LocationEntry dock) {
        this.dockCheckInView = dockCheckInView;
        this.dock = dock;
        entryTicketApi = HttpService.createService(EntryTicketApi.class);
        generalTaskViewEntry = taskViewEntry;
        facilityApi = HttpService.createService(FacilityApi.class);
        receiptTaskApi = HttpService.createService(ReceiveTaskApi.class);
        mFunctionHelpPresenter = new FunctionHelpPresenterImpl();
    }



    @Override
    public String getCurrentEntryId(String searchEntryIdWay, String inputContent, String entryId) {
        if (TextUtils.isEmpty(searchEntryIdWay)) return "";
        if (searchEntryIdWay.equals(ItemSearchEntryIdEnum.ENTRYID.getName())) {
            return inputContent;
        } else if (searchEntryIdWay.equals(ItemSearchEntryIdEnum.Equipment.getName())) {
            return entryId;
        }
        return "";
    }

    @Override
    public boolean canShowEntryIDLayout(String searchEntryIdWay) {
        return searchEntryIdWay.equals(ItemSearchEntryIdEnum.Equipment.getName());
    }

    private boolean isTransloadTaskType(){
        TaskTypeEntry taskType = generalTaskViewEntry.taskType;
        boolean isTransloadTaskType = (taskType == TaskTypeEntry.TRANSLOAD_RECEIVING
                || taskType == TaskTypeEntry.TRANSLOAD_LOADING);
        return isTransloadTaskType;
    }

    @Override
    public void setDock(LocationEntry dock) {
        if(isTransloadTaskType()){
            this.dock = dock;
        }
    }

    private String getCurrentDockId(){
        switch(generalTaskViewEntry.taskType){
            case TRANSLOAD:
            case TRANSLOAD_LOADING:
            case TRANSLOAD_RECEIVING:
                return dock.id;
            default:
                LoadTaskViewEntry loadTaskViewEntry = (LoadTaskViewEntry) generalTaskViewEntry;
                return loadTaskViewEntry.dockId;
        }
    }

    @Override
    public void checkDock(String dockName) {
        if (entryTicketEntry == null) {
            dockCheckInView.onSubmitError("Please input entry id");
            return;
        }
        //transloadReceiving 和 transloadLoading task 需要 支持dock 修改
        if (TextUtils.isEmpty(dockName) || (!dockName.equals(entryTicketEntry.dock.name) && !isTransloadTaskType())) {
            dockCheckInView.onDockError();
        } else {
            doOccupy();
        }
    }

    @Override
    public void checkCheckingNo(String checkingNo) {
        if (entryTicketEntry == null) {
            dockCheckInView.onSubmitError("Please input entry id");
            return;
        }

        if (TextUtils.isEmpty(checkingNo) || !checkingNo.equals(entryTicketEntry.dock.checkingNo)) {
            dockCheckInView.onCheckingNoError();
        } else {
            doOccupy();
        }
    }

    @Override
    public void onSearchEntryIdByEquipment(String searchEntryIdWay, String keyWord) {
        if (TextUtils.isEmpty(keyWord)) {
            return;
        }
        if (searchEntryIdWay.equals(ItemSearchEntryIdEnum.Equipment.getName())) {
            dockCheckInView.showProgress(true);
            List<String> tickets = new ArrayList<>();
            tickets.clear();
            Observable.zip(searchByTrailer(keyWord), searchByContainerNo(keyWord), (Response<EntryTicketPagingResult> trailerResult, Response<EntryTicketPagingResult> containerNoResult) -> {
                if (CollectionUtil.isNotNullOrEmpty(trailerResult.body().entryTickets)) {
                    for (EntryTicketEntry entryTicket : trailerResult.body().entryTickets) {
                        tickets.add(entryTicket.id);
                    }
                }
                if (CollectionUtil.isNotNullOrEmpty(containerNoResult.body().entryTickets)) {
                    for (EntryTicketEntry entryTicket : containerNoResult.body().entryTickets) {
                        tickets.add(entryTicket.id);
                    }

                }
                return trailerResult;
            }).compose(RxUtil.asyncSchedulers()).subscribe(new ErrorCodeSubscriber<Response<EntryTicketPagingResult>>() {
                @Override
                public void onSuccess(Response<EntryTicketPagingResult> result) {
                    if (CollectionUtil.isNullOrEmpty(tickets)) {
                        ToastUtil.showErrorToast(ResUtil.getString(R.string.toast_not_found_entry_id));
                        return;
                    }
                    if (CollectionUtil.isSingleItemCollection(tickets)) {
                        dockCheckInView.showEntryId(tickets.get(0));
                        return;
                    }

                    dockCheckInView.showSelectEntryIdDialog(tickets);
                }

                @Override
                public void onFailed(ErrorResponse errorResponse) {
                    ToastUtil.showErrorToast(errorResponse.getErrorMessage());
                }

                @Override
                public void onDone() {
                    dockCheckInView.showProgress(false);
                }
            });
        } else {
            dockCheckInView.DockNameEdtFocuse();
        }
    }

    private Observable<Response<EntryTicketPagingResult>> searchByTrailer(String keyword) {
        EntryTicketSearchEntry searchEntry = new EntryTicketSearchEntry();
        searchEntry.trailer = keyword;
        searchEntry.paging = new PagingParamEntry();
        searchEntry.paging.limit = 10L;
        return entryTicketApi.searchEntryTicket(searchEntry);
    }

    private Observable<Response<EntryTicketPagingResult>> searchByContainerNo(String keyword) {
        EntryTicketSearchEntry searchEntry = new EntryTicketSearchEntry();
        searchEntry.containerNO = keyword;
        searchEntry.paging = new PagingParamEntry();
        searchEntry.paging.limit = 10L;
        return entryTicketApi.searchEntryTicket(searchEntry);
    }

    @Override
    public void loadEntryDetail(String entryId, String idmUserId) {
        if (TextUtils.isEmpty(entryId)) {
            return;
        }

        dockCheckInView.showProgress(true);
        facilityApi.get(FacilityConfigPresenterImpl
                .getInstance().getFacility(idmUserId).id)
                .compose(RxUtil.asyncSchedulers())
                .subscribe(new ErrorCodeSubscriber<Response<FacilityEntry>>() {
                    @Override
                    public void onSuccess(Response<FacilityEntry> facilityEntryResponse) {
                        loadEntry(entryId);
                        dockCheckInView.updateCheckingNo(facilityEntryResponse.body().useDockCheckingNo);
                    }

                    @Override
                    public void onFailed(ErrorResponse errorResponse) {
                        ToastUtil.showErrorToast(errorResponse.getErrorMessage());
                    }

                    @Override
                    public void onDone() {
                        dockCheckInView.showProgress(false);
                    }
                });
    }

    private void loadEntry(String entryId) {
        dockCheckInView.showProgress(true);
        entryTicketApi.getEntryTicketDetail(entryId)
                .compose(RxUtil.asyncSchedulers())
                .subscribe(new ErrorCodeSubscriber<Response<EntryTicketEntry>>() {
                    @Override
                    public void onSuccess(Response<EntryTicketEntry> entryTicketEntryResponse) {
                        entryTicketEntry = entryTicketEntryResponse.body();
                        if (dock != null) {
                            entryTicketEntry.dock = dock;
                        }
                        dockCheckInView.updateEntryDetail(entryTicketEntry);
                    }

                    @Override
                    public void onFailed(ErrorResponse errorResponse) {
                        ToastUtil.showErrorToast(errorResponse.getErrorMessage());
                    }

                    @Override
                    public void onDone() {
                        dockCheckInView.showProgress(false);
                    }
                });
    }

    @Override
    public void onStart() {
        validateMaxInProgress();
        initSearchEntryIDSpData();
    }

    private void initSearchEntryIDSpData() {
        List<String> list = new ArrayList<>();
        list.clear();
        list.add(ItemSearchEntryIdEnum.ENTRYID.getName());
        list.add(ItemSearchEntryIdEnum.Equipment.getName());
        dockCheckInView.showSpData(list);
    }

    @Override
    public String getEntryIdError(String searchEntryIdWay) {
        if (searchEntryIdWay.equals(ItemSearchEntryIdEnum.ENTRYID.getName())) {
            return ResUtil.getString(R.string.toast_please_input_entry_id);
        } else if (searchEntryIdWay.equals(ItemSearchEntryIdEnum.Equipment.getName())) {
            return ResUtil.getString(R.string.toast_please_input_equipment_to_search);
        }
        return "";
    }

    private void doOccupy() {
        if (generalTaskViewEntry.taskType == TaskTypeEntry.RECEIVE) {
            dockCheckInView.onReceiveTaskCheck((ReceiveTaskEntry) generalTaskViewEntry);
            return;
        }

        dockCheckInView.showProgress(true);
        DockOperateEntry operateEntry = new DockOperateEntry();
        operateEntry.locationId = getCurrentDockId();
        operateEntry.entryId = entryTicketEntry.id;
        operateEntry.taskType = generalTaskViewEntry.taskType;
        operateEntry.taskIds = Collections.singletonList(generalTaskViewEntry.id);
        entryTicketApi.dockCheckin(operateEntry.entryId, operateEntry)
                .compose(RxUtil.asyncSchedulers())
                .subscribe(new ErrorCodeSubscriber<Response<Void>>() {
                    @Override
                    public void onSuccess(Response<Void> voidResponse) {
                        dockCheckInView.onCheckSuccess(generalTaskViewEntry);
                        updateEntryTicketStatus(operateEntry.entryId);
                    }

                    @Override
                    public void onFailed(ErrorResponse errorResponse) {
                        ToastUtil.showErrorToast(errorResponse.getErrorMessage());
                    }

                    @Override
                    public void onDone() {
                        dockCheckInView.showProgress(false);
                    }
                });
    }

    private void updateEntryTicketStatus(String entryId) {
        EntryTicketUpdateEntry entry = new EntryTicketUpdateEntry();
        entry.statusEntry = EntryTicketStatusEntry.DOCK_CHECKED_IN;
        entryTicketApi.updateStatus(entryId, entry)
                .compose(RxUtil.asyncSchedulers())
                .subscribe(new ErrorCodeSubscriber<Response<Void>>() {
                    @Override
                    public void onSuccess(Response<Void> voidResponse) {
                    }

                    @Override
                    public void onFailed(ErrorResponse errorResponse) {
                        ToastUtil.showErrorToast(errorResponse.getErrorMessage());
                    }

                    @Override
                    public void onDone() {
                    }
                });
    }

    @Override
    public void getAndOpenHelpPage(Context context, String helpPageKey, String facilityId) {
        mFunctionHelpPresenter.getAndOpenHelpPage(context, helpPageKey, generalTaskViewEntry.customerId, facilityId);
    }

    private void validateMaxInProgress(){
        receiptTaskApi.validateMaxInProgress(generalTaskViewEntry.id)
                .compose(RxUtil.asyncSchedulers())
                .subscribe(new ErrorCodeSubscriber<Response<Void>>() {
                    @Override
                    public void onSuccess(Response<Void> voidResponse) {}
                    @Override
                    public void onFailed(ErrorResponse errorResponse) {
                        dockCheckInView.onValidateMaxInProgressError(errorResponse.getErrorMessage());
                    }
                    @Override
                    public void onDone() {}
                });
    }

}
