package com.linc.platform.replenishment.presenter.impl;

import androidx.annotation.NonNull;
import android.text.TextUtils;

import com.annimon.stream.Stream;
import com.linc.platform.baseapp.model.LocationEntry;
import com.linc.platform.common.lp.LpApi;
import com.linc.platform.common.step.StepTypeEntry;
import com.linc.platform.foundation.api.FacilityApi;
import com.linc.platform.foundation.api.UnitApi;
import com.linc.platform.foundation.model.ItemSpecSearchEntry;
import com.linc.platform.foundation.model.ItemUnitSearchEntry;
import com.linc.platform.foundation.model.UnitEntry;
import com.linc.platform.foundation.model.organization.common.facility.FacilityEntry;
import com.linc.platform.http.ErrorCodeSubscriber;
import com.linc.platform.http.ErrorResponse;
import com.linc.platform.http.HttpService;
import com.linc.platform.http.IdResponse;
import com.linc.platform.inventory.model.InventoryEntry;
import com.linc.platform.itemlocation.ItemLocationApi;
import com.linc.platform.itemlocation.ItemLocationSearchEntry;
import com.linc.platform.itemlocation.ItemLocationViewEntry;
import com.linc.platform.replenishment.api.ReplenishmentStepApi;
import com.linc.platform.replenishment.model.ReplenishmentStepProcessCreateEntry;
import com.linc.platform.replenishment.model.ReplenishmentTaskEntry;
import com.linc.platform.replenishment.presenter.ReplenishmentProcessPresenter;
import com.linc.platform.replenishment.view.ReplenishmentProcessView;
import com.linc.platform.utils.CollectionUtil;
import com.linc.platform.utils.RxUtil;
import com.linc.platform.utils.StringUtil;
import com.linc.platform.utils.ToastUtil;

import java.util.List;

import retrofit2.Response;

/**
 * <AUTHOR>
 */

public class ReplenishmentProcessPresenterImpl implements ReplenishmentProcessPresenter {
    private InventoryEntry inventoryEntry;
    private ReplenishmentTaskEntry taskEntry;
    private ReplenishmentProcessView processView;
    private ReplenishmentStepApi stepApi;
    private UnitApi unitApi;

    private FacilityApi facilityApi;
    private List<UnitEntry> unitEntries;
    private LpApi lpApi;
    private ItemLocationApi itemLocationApi;

    public ReplenishmentProcessPresenterImpl(@NonNull InventoryEntry inventoryEntry,
                                             @NonNull ReplenishmentTaskEntry taskEntry,
                                             @NonNull ReplenishmentProcessView processView) {
        this.inventoryEntry = inventoryEntry;
        this.processView = processView;
        this.taskEntry = taskEntry;
        stepApi = HttpService.createService(ReplenishmentStepApi.class);
        unitApi = HttpService.createService(UnitApi.class);
        facilityApi = HttpService.createService(FacilityApi.class);
        lpApi = HttpService.createService(LpApi.class);
        itemLocationApi = HttpService.createService(ItemLocationApi.class);
    }

    @Override
    public void loadProcessData(String processId) {
//        processView.showProgress(true);

    }

    @Override
    public void submit(String fromQty, String toLpId, String toUnitId, String toLocationId, boolean isEntire) {
        processView.showProgress(true);
        ReplenishmentStepProcessCreateEntry createEntry = new ReplenishmentStepProcessCreateEntry();
        createEntry.fromLPId = inventoryEntry.lpId;
        createEntry.fromUnitId = inventoryEntry.unitId;
        createEntry.itemSpecId = inventoryEntry.itemSpecId;
        createEntry.toUnitId = toUnitId;
        createEntry.toLPId = toLpId;
        createEntry.toLocationId = toLocationId;
        createEntry.qty = fromQty;
        createEntry.isEntire = isEntire;
        stepApi.replenishment(taskEntry.id,
                taskEntry.getStep(StepTypeEntry.REPLENISHMENT).id,
                createEntry)
                .compose(RxUtil.asyncSchedulers())
                .subscribe(new ErrorCodeSubscriber<Response<IdResponse>>() {
                    @Override
                    public void onSuccess(Response<IdResponse> idResponseResponse) {
                        processView.replenishmentSuccess();
                    }

                    @Override
                    public void onFailed(ErrorResponse errorResponse) {
                        ToastUtil.showErrorToast(errorResponse.getErrorMessage());
                    }

                    @Override
                    public void onDone() {
                        processView.showProgress(false);
                    }
                });
    }

    @Override
    public void loadItemUom(String itemSpecId) {
        processView.showProgress(true);
        ItemUnitSearchEntry searchEntry = new ItemUnitSearchEntry();
        searchEntry.itemSpecId = itemSpecId;
        unitApi.search(searchEntry)
                .compose(RxUtil.asyncSchedulers())
                .subscribe(new ErrorCodeSubscriber<Response<List<UnitEntry>>>() {
                    @Override
                    public void onSuccess(Response<List<UnitEntry>> listResponse) {
                        unitEntries = listResponse.body();
                        processView.updateUoms(unitEntries);
                    }

                    @Override
                    public void onFailed(ErrorResponse errorResponse) {
                        ToastUtil.showErrorToast(errorResponse.getErrorMessage());
                    }

                    @Override
                    public void onDone() {
                        processView.showProgress(false);
                        processView.toLoadFacility();
                    }
                });

    }

    @Override
    public void loadFacility(String facilityId) {
        processView.showProgress(true);

        facilityApi.get(facilityId)
                .compose(RxUtil.asyncSchedulers())
                .subscribe(new ErrorCodeSubscriber<Response<FacilityEntry>>() {
                    @Override
                    public void onSuccess(Response<FacilityEntry> facilityEntryResponse) {
                        processView.refreshFacility(facilityEntryResponse.body());
                    }

                    @Override
                    public void onFailed(ErrorResponse errorResponse) {
                        ToastUtil.showToast(errorResponse.error);
                    }

                    @Override
                    public void onDone() {
                        processView.showProgress(false);
                    }
                });
    }

    @Override
    public void findSuggestToLocation(String itemSpecId, String unitId) {
        ItemSpecSearchEntry entry = new ItemSpecSearchEntry();
        entry.itemSpecId = itemSpecId;
        entry.replenishToUnitId = unitId;
        stepApi.findSuggestToLocation(entry)
                .compose(RxUtil.asyncSchedulers())
                .subscribe(new ErrorCodeSubscriber<Response<LocationEntry>>() {
                    @Override
                    public void onSuccess(Response<LocationEntry> response) {
                        if (response.body() != null) {
                            processView.setSuggestPutToLocation(response.body());
                        }
                    }

                    @Override
                    public void onFailed(ErrorResponse errorResponse) {
                        ToastUtil.showErrorToast(errorResponse.error);
                    }

                    @Override
                    public void onDone() {

                    }
                });
    }

    @Override
    public boolean isAllowUomDowngradeSameLp(String toLP, String toUnitId) {

        if (inventoryEntry == null) {
            return true;
        }

        String toLPNumber = StringUtil.getLPNumber(toLP);
        String fromLPNumber = StringUtil.getLPNumber(inventoryEntry.lpId);

        if (TextUtils.equals(toLPNumber, fromLPNumber)
                && CollectionUtil.isNotNullOrEmpty(unitEntries)) {

            UnitEntry toUnitEntry = Stream.of(unitEntries)
                    .filter(u -> TextUtils.equals(u.id, toUnitId)).findFirst().get();
            UnitEntry fromUnitEntry = Stream.of(unitEntries)
                    .filter(u -> TextUtils.equals(u.id, inventoryEntry.unitId)).findFirst().get();

            return !(toUnitEntry != null && fromUnitEntry != null
                    && toUnitEntry.baseQty < fromUnitEntry.baseQty);
        } else {
            return true;
        }
    }

    @Override
    public void loadItemLocation(String itemSpecId, String productId) {
        ItemLocationSearchEntry searchEntry = new ItemLocationSearchEntry();
        searchEntry.itemSpecId = itemSpecId;
        searchEntry.productId = productId;
        itemLocationApi.search(searchEntry)
                .compose(RxUtil.asyncSchedulers())
                .subscribe(new ErrorCodeSubscriber<Response<List<ItemLocationViewEntry>>>() {
                    @Override
                    public void onSuccess(Response<List<ItemLocationViewEntry>> listResponse) {
                        if (CollectionUtil.isNotNullOrEmpty(listResponse.body())
                                && listResponse.body().get(0).firstLocation != null) {
                            processView.setSuggestPutToLocation(listResponse.body().get(0).firstLocation);
                        }
                    }

                    @Override
                    public void onFailed(ErrorResponse errorResponse) {
                        ToastUtil.showErrorToast(errorResponse.getErrorMessage());
                    }

                    @Override
                    public void onDone() {

                    }

                    @Override
                    public boolean useDefaultProgress() {
                        return true;
                    }
                });
    }

}
