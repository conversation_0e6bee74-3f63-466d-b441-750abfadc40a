package com.linc.platform.replenishment.model;

import com.google.gson.annotations.SerializedName;
import com.linc.platform.foundation.model.ShippingRuleEntry;
import com.linc.platform.pick.model.PickTypeEntry;

import java.io.Serializable;

/**
 * <AUTHOR>
 */

public class AvailableReplenishmentSearchEntry implements Serializable {
    @SerializedName("itemSpecId")
    public String itemSpecId;

    @SerializedName("productId")
    public String productId;

    @SerializedName("unitId")
    public String unitId;

    @SerializedName("titleId")
    public String titleId;

//    @SerializedName("shippingRule")
//    public ShippingRule shippingRule = ShippingRule.FIFO;

    @SerializedName("limit")
    public Integer limit = 10;

    @SerializedName("qty")
    public Double qty;

    @SerializedName("shippingRule")
    public ShippingRuleEntry shippingRule;

    @SerializedName("supportPickType")
    public PickTypeEntry supportPickType;
}
