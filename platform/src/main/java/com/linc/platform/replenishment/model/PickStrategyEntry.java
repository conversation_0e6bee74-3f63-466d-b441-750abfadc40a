package com.linc.platform.replenishment.model;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */

public class PickStrategyEntry implements Serializable {
    public static final String TAG = PickStrategyEntry.class.getSimpleName();

    @SerializedName("id")
    public Long id;

    @SerializedName("orderId")
    public String orderId;

    @SerializedName("taskId")
    public String taskId;

    @SerializedName("orderItemLineId")
    public String orderItemLineId;

    @SerializedName("itemSpecId")
    public String itemSpecId;

    @SerializedName("itemSpec")
    public String itemSpec;

    @SerializedName("productId")
    public String productId;

    @SerializedName("unitId")
    public String unitId;

    @SerializedName("unit")
    public String unit;

    @SerializedName("lpId")
    public String lpId;

    @SerializedName("titleId")
    public String titleId;

    @SerializedName("title")
    public String title;

    @SerializedName("locationId")
    public String locationId;

    @SerializedName("location")
    public String location;

    @SerializedName("baseQty")
    public Double baseQty;

    @SerializedName("qty")
    public Double qty;

    @SerializedName("state")
    public String state;

    @SerializedName("confId")
    public String confId;

    @SerializedName("orderPlanningId")
    public String orderPlanningId;

    @SerializedName("packagingTypeSpecId")
    public String packagingTypeSpecId;

    @SerializedName("packagingTypeProductId")
    public String packagingTypeProductId;

    @SerializedName("shouldToCc")
    public Integer shouldToCc;

    @SerializedName("updatedBy")
    public String updatedBy;

    @SerializedName("lpIds")
    public List<String> lpIds;

//    @SerializedName("pickMode")
//    public PickMode pickMode;
//
//    @SerializedName("strategyType")
//    public StrategyType strategyType;
//
//    @SerializedName("pickType")
//    public PickType pickType;
}
