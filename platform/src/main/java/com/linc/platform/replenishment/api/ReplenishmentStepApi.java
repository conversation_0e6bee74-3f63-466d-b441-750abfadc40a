package com.linc.platform.replenishment.api;

import com.linc.platform.baseapp.model.LocationEntry;
import com.linc.platform.foundation.model.ItemSpecSearchEntry;
import com.linc.platform.http.IdResponse;
import com.linc.platform.inventory.model.InventoryEntry;
import com.linc.platform.inventoryreplenishment.model.ReplenishmentClosable;
import com.linc.platform.inventoryreplenishment.model.SuggestionLocationEntry;
import com.linc.platform.replenish.collect.model.CollectSuggestQtySearchEntry;
import com.linc.platform.replenish.collect.model.CollectSuggestQtyViewEntry;
import com.linc.platform.replenish.collect.model.ReplenishCollectDropLocationEntry;
import com.linc.platform.replenish.collect.model.ReplenishCollectProcessEntry;
import com.linc.platform.replenish.replenish.model.ReplenishSuggestEntry;
import com.linc.platform.replenishment.model.ReplenishmentStepProcessCreateEntry;
import com.linc.platform.replenishment.model.ReplenishmentStepProcessViewEntry;
import com.linc.platform.replenishment.model.ReplenishmentStepSearchEntry;
import com.linc.platform.replenishment.model.ReplenishmentStepUpdateEntry;
import com.linc.platform.suggestiondrawer.SuggestionTypeEntry;

import java.util.List;

import retrofit2.Response;
import retrofit2.http.Body;
import retrofit2.http.GET;
import retrofit2.http.POST;
import retrofit2.http.PUT;
import retrofit2.http.Path;
import rx.Observable;

/**
 * <AUTHOR>
 */

public interface ReplenishmentStepApi {
    @POST("wms-app/replenishment-task/{taskId}/step/{stepId}/process")
    Observable<Response<IdResponse>> replenishmentProcess(@Path("taskId") String taskId,
                                                          @Path("stepId") String stepId,
                                                          @Body ReplenishmentStepProcessCreateEntry create);

    @PUT("wms-app/replenishment-task/{taskId}/step/{stepId}/process/{processId}/update")
    Observable<Response<Void>> upateProcess(@Path("taskId") String taskId,
                                            @Path("stepId") String stepId,
                                            @Path("processId") String processId,
                                            @Body ReplenishmentStepUpdateEntry update);

    @POST("wms-app/replenishment-task/{taskId}/step/{stepId}/process/collect")
    Observable<Response<Void>> collectInventory(@Path("taskId") String taskId,
                                                @Path("stepId") String stepId,
                                                @Body ReplenishCollectProcessEntry collectProcessEntry);

    @POST("wms-app/replenishment-task/{taskId}/step/{stepId}/process/drop-location")
    Observable<Response<Void>> dropLocation(@Path("taskId") String taskId,
                                            @Path("stepId") String stepId,
                                            @Body ReplenishCollectDropLocationEntry dropLocationEntry);

    @PUT("wms-app/replenishment-task/{taskId}/step/{stepId}/process/{processId}/apply")
    Observable<Response<Void>> applyToInventory(@Path("taskId") String taskId,
                                                @Path("stepId") String stepId,
                                                @Path("processId") String processId);

    @PUT("wms-app/replenishment-task/{taskId}/step/{stepId}/start")
    Observable<Response<Void>> startStep(@Path("taskId") String taskId,
                                         @Path("stepId") String stepId);

    @PUT("wms-app/replenishment-task/{taskId}/step/{stepId}/close")
    Observable<Response<Void>> closeStep(@Path("taskId") String taskId,
                                         @Path("stepId") String stepId);

    @PUT("wms-app/replenishment-task/{taskId}/step/{stepId}/force-close")
    Observable<Response<Void>> forceClose(@Path("taskId") String taskId,
                                          @Path("stepId") String stepId);

    @POST("bam/inventory/replenishment-task/process/search")
    Observable<Response<List<ReplenishmentStepProcessViewEntry>>> searchReplenishmentStepProcess(
            @Body ReplenishmentStepSearchEntry search);

    @POST("wms-app/replenishment-task/{taskId}/step/{stepId}/create-and-apply")
    Observable<Response<IdResponse>> replenishment(@Path("taskId") String taskId,
                                                   @Path("stepId") String stepId,
                                                   @Body ReplenishmentStepProcessCreateEntry create);

    @POST("bam/inventory/replenishment-task/put-to-location/suggest")
    Observable<Response<LocationEntry>> findSuggestToLocation(@Body ItemSpecSearchEntry search);

    @GET("bam/wms-app/inventories/multi-criteria-search/{criteria}")
    Observable<Response<List<InventoryEntry>>> getInventory(@Path("criteria") String criteria);

    @GET("bam/inventory/replenishment-task/{taskId}/suggestionType/{type}/suggestion")
    Observable<Response<List<ReplenishSuggestEntry>>> getSuggestions(@Path("taskId") String taskId,
                                                                     @Path("type") SuggestionTypeEntry suggestionTypeEntry);

    @POST("bam/inventory/replenishment-task/suggestion/qty")
    Observable<Response<CollectSuggestQtyViewEntry>> getSuggestionQty(@Body CollectSuggestQtySearchEntry searchEntry);

    @GET("bam/inventory/replenishment-task/{taskId}/suggestion/collect-location")
    Observable<Response<SuggestionLocationEntry>> getSuggestionCollectLocation(@Path("taskId") String taskId);



    @GET("bam/replenishment/task/{taskId}/item-spec/{itemSpecId}/title/{titleId}/suggestion-drop-location")
    Observable<Response<LocationEntry>> getSuggestionDropLocation(@Path("taskId") String taskId, @Path("itemSpecId") String itemSpecId, @Path("titleId") String titleId);

    @GET("bam/replenishment/task/{taskId}/check-replenishment-closable")
    Observable<Response<ReplenishmentClosable>> checkReplenishmentClosable(@Path("taskId") String taskId);
}
