package com.linc.platform.replenishment.model;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */

public class ReplenishmentStepSearchEntry implements Serializable {
    @SerializedName("taskId")
    public String taskId;

    @SerializedName("itemSpecId")
    public String itemSpecId;

    @SerializedName("productId")
    public String productId;

    @SerializedName("snList")
    public List<String> snList;

    @SerializedName("fromLPId")
    public String fromLPId;

    @SerializedName("toLPId")
    public String toLPId;

    @SerializedName("toLocationId")
    public String toLocationId;

    @SerializedName("fromLocationName")
    public String fromLocationName;

    @SerializedName("replenishStatus")
    public ReplenishStatusEntry replenishStatus;
}
