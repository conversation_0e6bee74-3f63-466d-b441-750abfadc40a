package com.linc.platform.common;

import com.linc.platform.foundation.model.UnitEntry;

import java.util.LinkedHashMap;
import java.util.List;

/**
 * @Description:
 * @Author: Dennis
 * @CreateDate: 2021/1/20 21:24
 */
public class UnitLruCache {

    public static final int MAX_DEFAULT_CACHE_SIZE = 50;
    private static final float DEFAULT_LOAD_FACTOR = 0.75f;
    private final int mMaxCacheSize;

    private LinkedHashMap<String, List<UnitEntry>> mUnitCacheMap;

    public UnitLruCache() {
        this(MAX_DEFAULT_CACHE_SIZE);
    }

    public UnitLruCache(int maxCacheSize) {
        mMaxCacheSize = maxCacheSize;
        int initialCapacity = (int) Math.ceil(MAX_DEFAULT_CACHE_SIZE / DEFAULT_LOAD_FACTOR) + 1;
        mUnitCacheMap = new LinkedHashMap<String, List<UnitEntry>>(initialCapacity, DEFAULT_LOAD_FACTOR,true){
            @Override
            protected boolean removeEldestEntry(Entry<String, List<UnitEntry>> eldest) {
                return size() > mMaxCacheSize;
            }
        };
    }

    public void put(String key, List<UnitEntry> unitEntry) {
        mUnitCacheMap.put(key, unitEntry);
    }

    public List<UnitEntry> get(String key) {
       return mUnitCacheMap.get(key);
    }
}
