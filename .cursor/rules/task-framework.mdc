---
description: Task Framework
globs: 
alwaysApply: false
---

!!! MAIN OBJECTIVES !!!
1. Read the "User Input" at the bottom
2. Read through this entire prompt carefully
3. Follow the "Steps to Follow" section
4. Stop at each verification point for human confirmation
5. MAINTAIN THE TASK FILE AS THE CENTRAL SOURCE OF TRUTH
!!! END MAIN OBJECTIVES !!!

> VERIFICATION REQUIRED:
> You, the AI, must reply with "I acknowledge the 'MAIN OBJECTIVES' above and will follow the steps in order".

# General rules:
- You are allowed to run any terminal commands you need to get the job done, such as, but not limited to:
  - Look for files and directories in the project root
  - Inspect the codebase for any relevant files or directories
  - Use the terminal to run any commands you need to get the job done
  - If the user references this framework file without explicitly providing `[TASK]` and `[BACKGROUND INFO]` using the standard "User Input" format, infer the `[TASK]` and `[BACKGROUND INFO]` (if any) from the user's message content.
  - Whenever the user references this framework file, you MUST follow the defined steps, starting with Task Analysis and leading to the creation of a [TASK FILE].

# Processing Rules:
1. Process the task methodically according to the template sections
2. __never say__ "i see the issue..." or "i found the problem" or similar
3. Ask for clarification if any aspect is unclear
4. For any missing context, search files using `tree` or request specific information
5. Before starting each new section (analysis/solution/implementation/etc.):
   - Recap what's been done so far
   - List what's about to be done
6. After completing each section:
   - Summarize what was completed
   - Show completed checkmarks
   - State what's coming next
7. After each implementation step:
   - Mark the step as complete [x]
   - Show the full implementation list with progress
   - Ask for confirmation before proceeding
8. Before proceeding to verification:
   - Show complete implementation checklistbn
   - Confirm all steps are done
   - Get user confirmation to proceed
9. After verification:
    - Show all completed verification steps
    - Get final confirmation

# Placeholder Definitions:
- **[TASK]:** The specific task or issue being addressed (e.g., "fix-cache-manager")
- **[TASK_IDENTIFIER]:** A unique, human-readable identifier for the task (e.g., "fix-cache-manager")
- **[TASK_FILE_NAME]:** The name of the task file
- **[TASK_DATE_AND_NUMBER]:** A timestamped and sequential identifier for the task file (e.g., "2025-01-14_1")
- **[TASK FILE]:** The Markdown file created to document and track the task's progress
- **[DATETIME]:** A timestamp that needs to be obtained by executing a command in the console each time it is used
- **[JIRA_ISSUE_NUMBER]:** The Jira issue number for the task provided by the user in the "User Input" section. (e.g., "WISE-1", "TMS-1")

# Placeholder Information Retrieval:
- Obtain the following information as needed to fill in placeholders. Follow the suggested methods in order.

  - **[DATETIME]:** The current date and time, formatted as `yyyy-MM-dd_HH:mm:ss`.
    1.  **Attempt Command:** Try executing a standard system command to get the current date and time (e.g., `Get-Date -Format "yyyy-MM-dd_HH:mm:ss"` on Windows PowerShell, or `date +'%Y-%m-%d_%H:%M:%S'` on Linux/macOS).
    2.  **Fallback:** If the command fails or is unavailable, generate the timestamp in the required format using your internal knowledge of the current date and time.

  - **[DATE]:** The current date, formatted as `yyyy-MM-dd`.
    1.  **Attempt Command:** Try executing a standard system command to get the current date (e.g., `Get-Date -Format "yyyy-MM-dd"` on Windows PowerShell, or `date +'%Y-%m-%d'` on Linux/macOS).
    2.  **Fallback:** If the command fails or is unavailable, generate the date string in the required format using your internal knowledge of the current date.

  - **[TIME]:** The current time, formatted as `HH:mm:ss`.
    1.  **Attempt Command:** Try executing a standard system command to get the current time (e.g., `Get-Date -Format "HH:mm:ss"` on Windows PowerShell, or `date +'%H:%M:%S'` on Linux/macOS).
    2.  **Fallback:** If the command fails or is unavailable, generate the time string in the required format using your internal knowledge of the current time.

  - **[USER_NAME]:** The name of the current user initiating the task.
    1.  **Attempt Git Command:** Try executing `git config user.name` to retrieve the username configured in Git.
    2.  **Ask User:** If automatic retrieval fails, ask the user to provide their name.

# Task File Template:
```markdown
# Context
Task file name: [TASK_FILE_NAME]
Created at: [DATETIME]
Created by: [USER_NAME]
Issue: [JIRA_ISSUE_NUMBER]

# Task Description
[A detailed description based on the [TASK] given by the user.]

# Background Info (Optional)
[A detailed background information about the task and related context. If none, add "—".]

# Task Analysis
- Purpose of the [TASK].
- Issues identified, including:
  - Problems caused.
  - Why it needs resolution.
  - Implementation details and goals.
- Other useful reference details.

# Steps to take
[List of actionable steps for the task]
- Add "DO NOT REMOVE" to the bottom of this section (for reference if needed).

# Current step: [The number of the current step]

# Original task template
[The ENTIRE unedited "Task File Template"]
- Copy and paste the ENTIRE unedited "Task File Template" into this section, __including all the details__
- Add "DO NOT REMOVE" to the bottom of this section (for reference if needed).

# Original steps
[The ENTIRE unedited "Steps to Follow" section]
- Copy and paste the ENTIRE unedited "Steps to Follow" section into this section, __including all the details under each step__
- Add "DO NOT REMOVE" to the bottom of this section (for reference if needed).

# Notes
[Iteration notes during the task. If none, add "—".]

# Task Progress
- Updates must include:
  - Mandatory:
    - [DATETIME].
    - SUCCESSFUL/UNSUCCESSFUL, after user confirmation
  - Optional:
    - Findings, solutions, blockers, and results.
    - All updates must be logged chronologically.

# Final Review
[To be filled in only after task completion.]
```

# Steps to Follow:

### **1. Task Analysis**
1. Examine the [TASK], related code, and functionality step-by-step.
2. Identify issues and document findings in "Task Analysis."
3. Confirm with the user before proceeding.
   - Combine any iterative changes here.

> BEFORE CONTINUING:
> 1. Update the [TASK FILE] with your current progress
> 2. Show the updated [TASK FILE] content
> 3. Wait for user confirmation that your analysis is complete, if not, iterate on this

---

### **2. Task File Creation**
1.  **Determine Project Root:** Identify the root directory of the project associated with this `task-framework.mdc` file. Let this be `[PROJECT_ROOT]`.
   * IMPORTANT: Always use the current working directory where the `task-framework.mdc` file is located as the project root.
   * The task file MUST be created within the SAME project where this `task-framework.mdc` file is located.
   * DO NOT create task files in any other project directory that might be open.
   * Before proceeding, verify the project path with `pwd` to confirm you're in the correct project directory.
2.  **Determine Directory Name and Path:**
    *   Use the [DATETIME] value (format: `yyyy-MM-dd_HH:mm:ss`) to extract the year and month in `YYYY-MM` format by taking the first 7 characters of [DATETIME] (e.g., `2024-07`). This value is `[DIRECTORY_NAME]`.
    *   Construct the target directory path: `[PROJECT_ROOT]/.tasks/[DIRECTORY_NAME]`.
    *   Ensure this directory exists. If it does not exist, **you must create it**.
3.  **Determine Task File Name Prefix (`[TASK_FILE_NAME]`):**
    *   Use the [DATETIME] value to extract the current date in `YYYY-MM-DD` format by taking the first 10 characters of [DATETIME] (e.g., `2024-07-19`).
    *   Determine the next sequential number for tasks created *on this date* within the target directory (`[PROJECT_ROOT]/.tasks/[DIRECTORY_NAME]`). To achieve this:
        *   List the contents of the target directory (`[PROJECT_ROOT]/.tasks/[DIRECTORY_NAME]`).
        *   Count how many existing file names within that directory begin with the current date prefix (`YYYY-MM-DD_`).
        *   The sequential number for the new file is this count plus 1.
    *   Combine the date extracted from [DATETIME] and the sequential number using an underscore: `YYYY-MM-DD_SEQUENTIAL_NUMBER` (e.g., `2024-07-19_1`). This combined value is `[TASK_FILE_NAME]`.
4.  **Determine User Name (`[USER_NAME]`):** Obtain the user's name (e.g., by checking Git configuration via `git config user.name` or asking the user if necessary). This value is `[USER_NAME]`.
5.  **Construct Full File Path:** Assemble the final, complete path for the task file using the determined components:
    `[PROJECT_ROOT]/.tasks/[DIRECTORY_NAME]/[TASK_FILE_NAME]_[TASK_IDENTIFIER]_[USER_NAME].md`
    *(Example: `/path/to/project/.tasks/2024-07/2024-07-19_1_fix-login-bug_JaneDoe.md`)*
6.  **Create and Populate File:**
    *   Create an empty file at the constructed path.
    *   Populate this new file using the "Task File Template". Fill in all placeholders accurately, using the information gathered ([DATETIME], [USER_NAME], [JIRA_ISSUE_NUMBER], [TASK_FILE_NAME] itself for the context section) and incorporating the analysis from Step 1 and user input ([TASK] description, [BACKGROUND INFO]). **Crucially, ensure the 'Context' section is fully populated with:**
        *   `Task file name: [TASK_FILE_NAME]`
        *   `Created at: [DATETIME]`
        *   `Created by: [USER_NAME]`
        *   `Issue: [JIRA_ISSUE_NUMBER]`
7.  **Confirm with User:** Present the fully constructed file path and the proposed initial content of the task file to the user for confirmation.

> BEFORE CONTINUING:
> 1.  Show the proposed full path for the [TASK FILE].
> 2.  Show the proposed initial content for the [TASK FILE].
> 3.  Wait for user confirmation on both the path and the content before proceeding.

---

### **3. Iterate on the Task**
1. Analyze code context fully before changes.
2. Log all progress under "Task Progress" in the [TASK FILE].
3. For each change:
   - Seek user confirmation on updates.
   - Mark changes as SUCCESSFUL or UNSUCCESSFUL in the log.
4. Analyze updates under "Task Progress" to ensure you don't repeat previous mistakes or unsuccessful changes.

> BEFORE CONTINUING:
> 1. Update the [TASK FILE] with your current progress
> 2. Show the updated [TASK FILE] content
> 3. If you believe that you have finished the task, consult the user before continuing to the next step

---

### **4. Final Review**
1. Complete the "Final Review" in the [TASK FILE].
2. Summarize all successful changes.

> BEFORE CONTINUING:
> 1. Update the [TASK FILE] with your current progress
> 2. Show the updated [TASK FILE] content
> 3. Confirm with the user before concluding the task

---

> # User Input:
> **[TASK]:** `<DESCRIBE YOUR TASK>`
> **[BACKGROUND INFO]:** `<ENTER BACKGROUND INFORMATION OR LINK TO FILE CONTAINING THE DETAILS (OPTIONAL)>`
